<template>
  <cmp-row>
    <cmp-card size="small" :span="4" bordered title="API清单">
      <template #actions>
        <!-- <t-radio-group v-model="apiType" variant="primary-filled" :default-value="1">
          <t-radio-button :value="1">系统API</t-radio-button>
          <t-radio-button :value="3">转发API</t-radio-button>
        </t-radio-group> -->
      </template>
      <api-list :data="apiDataList" @add="onAddApi" @click="onApiClick"></api-list>
    </cmp-card>
    <cmp-card :span="8" bordered>
      <system-api-info @save="handleSaveApi" @delete="handleDeleteApi"></system-api-info>
    </cmp-card>
  </cmp-row>
</template>
<script lang="ts">
export default {
  name: 'SystemApiPanel',
};
</script>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { api, Services } from '@/api/system';

import ApiList from '../components/ApiList.vue';
import SystemApiInfo from '../components/SystemApiInfo.vue';
import { ApiInfoDto, useApiStore } from '../store';

const apiStore = useApiStore();
onMounted(() => {
  fetchApiDataList();
});

const apiType = ref(1);
watch(apiType, () => {
  fetchApiDataList();
  apiStore.setCurrentApi(null);
});

const apiDataList = ref<ApiInfoDto[]>([]);
const fetchApiDataList = async () => {
  await api.run(Services.apiGetAll, { apiType: apiType.value }).then((data) => {
    apiDataList.value = data;
  });
};
const onAddApi = () => {
  if (apiDataList.value.length > 0 && apiDataList.value.some((item) => item.id === null)) return;
  const newApi = {
    id: null,
    state: 1,
    apiType: apiType.value,
    requestType: 'GET',
    httpUrl: '',
    apiName: '接口名称 *',
  } as ApiInfoDto;
  apiDataList.value.push(newApi);
  apiStore.setCurrentApi(newApi);
};

const onApiClick = (apiInfo) => {
  apiStore.setCurrentApi(apiInfo);
};

const handleSaveApi = async (apiInfo: ApiInfoDto) => {
  await apiStore.saveApi(apiInfo);
  fetchApiDataList();
};

const handleDeleteApi = async (id) => {
  await apiStore.deleteApi(id);
  fetchApiDataList();
};
</script>
<style lang="less" scoped></style>
