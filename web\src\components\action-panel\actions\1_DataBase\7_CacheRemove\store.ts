import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfo } from './model';

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      description: '',
      cacheKey: null,
    } as ArgsInfo),
    ...(actionFlowStore.currentStep.args || {}),
  }) as ArgsInfo;
};

export const useCacheRemoveStore = defineStore('cacheRemove', {
  state: () => {
    const state = { args: null } as { args: ArgsInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);
    },
  },
});
