<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="数据源" prop="dataSource">
              <data-source-select v-model="formData.dataSource" />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <data-source-table
          v-model:data="formData.configureInfo"
          operation-type="Delete"
          :data-source-id="formData.dataSource"
        ></data-source-table>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'DataDeleteActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { watch } from 'vue';

import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import DataSourceSelect from '@/components/action-panel/DataSourceSelect.vue';
import DataSourceTable from '@/components/action-panel/DataSourceTable.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { useDataDeleteStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataDeleteStore = useDataDeleteStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataDeleteStore.updateState();
  },
  {
    immediate: true,
  },
);
const { args: formData } = storeToRefs(dataDeleteStore);

watch(
  () => formData.value,
  (newValue) => {
    dataDeleteStore.setArgs(newValue);
  },
  {
    deep: true,
  },
);
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}
</style>
