using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.FunctionPool.Flow.Services;
using GCP.Functions.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Collections;
using Xunit;

namespace GCP.Core.Tests.FunctionPool.Flow.Services
{
    public class DataSaveCurrentRowTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly DbContext _dbContext;
        private readonly FunctionContext _functionContext;

        public DataSaveCurrentRowTests()
        {
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            
            _serviceProvider = services.BuildServiceProvider();
            
            // 创建测试数据库上下文
            _dbContext = new DbContext();
            _dbContext.SetDefaultConnection("Data Source=test_datasave_currentrow.db", "SQLite");
            
            // 创建测试表
            CreateTestTable();
            
            // 创建功能上下文
            _functionContext = new FunctionContext
            {
                db = _dbContext,
                globalData = new Dictionary<string, object>(),
                Current = new StepInfo { StepName = "TestStep" },
                SqlLog = new TestSqlLog()
            };
        }

        private void CreateTestTable()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS test_users (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    email TEXT,
                    age INTEGER,
                    created_at TEXT,
                    updated_at TEXT
                );
                
                DELETE FROM test_users;
                
                INSERT INTO test_users (id, name, email, age, created_at) VALUES 
                (1, 'Alice', '<EMAIL>', 25, '2023-01-01'),
                (2, 'Bob', '<EMAIL>', 30, '2023-01-02'),
                (3, 'Charlie', '<EMAIL>', 35, '2023-01-03');
            ";
            
            _dbContext.ExecuteNonQuery(sql);
        }

        [Fact]
        public async Task UpdateData_WithCurrentRowScript_ShouldUseCorrectCurrentRowForEachRecord()
        {
            // Arrange
            var dataSource = "test-datasource";
            var sourceData = new ArrayList
            {
                new Dictionary<string, object> { { "id", 1 }, { "name", "Alice Updated" } },
                new Dictionary<string, object> { { "id", 2 }, { "name", "Bob Updated" } },
                new Dictionary<string, object> { { "id", 3 }, { "name", "Charlie Updated" } }
            };

            var dataSaveData = new DataSaveData
            {
                Name = "TestUpdate",
                DataSource = dataSource,
                OperateType = DataSaveOperationType.Update,
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "sourceData" },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "test_users",
                    Columns = new List<ColumnInfo>
                    {
                        new ColumnInfo
                        {
                            ColumnName = "id",
                            DataType = "int",
                            IsCondition = true,
                            IsPrimaryKey = true,
                            Required = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.id" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "name",
                            DataType = "string",
                            IsUpdate = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "email",
                            DataType = "string",
                            IsQuery = true
                        },
                        new ColumnInfo
                        {
                            ColumnName = "age",
                            DataType = "int",
                            IsQuery = true
                        },
                        new ColumnInfo
                        {
                            ColumnName = "updated_at",
                            DataType = "string",
                            IsUpdate = true,
                            ColumnValue = new DataValue 
                            { 
                                Type = "script", 
                                ScriptValue = "currentRow.email + '_' + currentRow.age + '_updated'" 
                            }
                        }
                    }
                }
            };

            // 设置全局数据
            _functionContext.globalData["sourceData"] = sourceData;

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            // Act
            var result = await dataSaveService.SaveData(dataSaveData);

            // Assert
            Assert.NotNull(result);
            
            // 验证每条记录的 updated_at 字段是否使用了正确的 currentRow 数据
            var updatedRecords = _dbContext.Query<dynamic>("SELECT id, name, updated_at FROM test_users ORDER BY id");
            
            Assert.Equal(3, updatedRecords.Count());
            
            var recordsList = updatedRecords.ToList();
            
            // 验证第一条记录
            Assert.Equal("Alice Updated", recordsList[0].name);
            Assert.Equal("alice@test.com_25_updated", recordsList[0].updated_at);
            
            // 验证第二条记录
            Assert.Equal("Bob Updated", recordsList[1].name);
            Assert.Equal("bob@test.com_30_updated", recordsList[1].updated_at);
            
            // 验证第三条记录
            Assert.Equal("Charlie Updated", recordsList[2].name);
            Assert.Equal("charlie@test.com_35_updated", recordsList[2].updated_at);
        }

        [Fact]
        public async Task UpdateData_WithCurrentRowScript_ShouldNotMixDataBetweenRows()
        {
            // Arrange - 测试更复杂的场景，确保不会出现数据混乱
            var sourceData = new ArrayList
            {
                new Dictionary<string, object> { { "id", 2 }, { "name", "Bob Modified" } },
                new Dictionary<string, object> { { "id", 1 }, { "name", "Alice Modified" } },
                new Dictionary<string, object> { { "id", 3 }, { "name", "Charlie Modified" } }
            };

            var dataSaveData = new DataSaveData
            {
                Name = "TestUpdateOrder",
                DataSource = "test-datasource",
                OperateType = DataSaveOperationType.Update,
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "sourceData" },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "test_users",
                    Columns = new List<ColumnInfo>
                    {
                        new ColumnInfo
                        {
                            ColumnName = "id",
                            DataType = "int",
                            IsCondition = true,
                            IsPrimaryKey = true,
                            Required = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.id" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "name",
                            DataType = "string",
                            IsUpdate = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "email",
                            DataType = "string",
                            IsQuery = true
                        },
                        new ColumnInfo
                        {
                            ColumnName = "updated_at",
                            DataType = "string",
                            IsUpdate = true,
                            ColumnValue = new DataValue 
                            { 
                                Type = "script", 
                                ScriptValue = "item.id + '_' + currentRow.email" 
                            }
                        }
                    }
                }
            };

            _functionContext.globalData["sourceData"] = sourceData;

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            // Act
            var result = await dataSaveService.SaveData(dataSaveData);

            // Assert
            var updatedRecords = _dbContext.Query<dynamic>("SELECT id, name, updated_at FROM test_users ORDER BY id");
            var recordsList = updatedRecords.ToList();
            
            // 验证每条记录使用的是自己的 currentRow 数据，而不是其他行的数据
            Assert.Equal("<EMAIL>", recordsList[0].updated_at);
            Assert.Equal("<EMAIL>", recordsList[1].updated_at);
            Assert.Equal("<EMAIL>", recordsList[2].updated_at);
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
            _serviceProvider?.Dispose();
            
            // 清理测试数据库文件
            if (File.Exists("test_datasave_currentrow.db"))
            {
                File.Delete("test_datasave_currentrow.db");
            }
        }
    }

    public class TestSqlLog : ISqlLog
    {
        public Task Info(string message, bool isImportant = false) => Task.CompletedTask;
        public Task Warn(string message, bool isImportant = false) => Task.CompletedTask;
        public Task Error(string message, bool isImportant = false) => Task.CompletedTask;
    }
}
