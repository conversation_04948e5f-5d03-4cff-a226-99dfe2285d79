﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20221109000001, "初始化索引")]
    public class AddIndex : Migration
    {
        public override void Up()
        {
            Create.Index("TREE_CODE_IDX")
                .OnTable("LC_DIR_TREE")
                .OnColumn("DIR_CODE").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .WithOptions().Unique();

            Create.Index("MAPPING_OBJ_IDX")
                .OnTable("LC_TAG_MAPPING")
                .OnColumn("OBJECT_ID").Ascending()
                .OnColumn("OBJECT_TYPE").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();

            // mysql同一个会话, 同一个表, 不能同时创建多个索引
        }

        public override void Down()
        {
            Delete.Index("MAPPING_OBJ_IDX").OnTable("LC_TAG_MAPPING");
            Delete.Index("TREE_CODE_IDX").OnTable("LC_DIR_TREE");
        }
    }
}
