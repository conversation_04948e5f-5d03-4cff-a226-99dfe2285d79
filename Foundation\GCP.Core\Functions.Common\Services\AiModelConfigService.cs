using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using LinqToDB;
using Serilog;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// AI模型配置服务
    /// </summary>
    [Function("aiModelConfig", "AI模型配置服务")]
    public class AiModelConfigService : BaseService
    {
        /// <summary>
        /// 创建模型配置
        /// </summary>
        [Function("createModelConfiguration", "创建模型配置")]
        public async Task<ChatModelConfiguration> CreateModelConfiguration(ChatModelConfiguration config)
        {
            await using var db = this.GetDb();

            await using (var transaction = await db.BeginTransactionAsync())
            {
                try
                {
                    // 如果设置为默认，则清除其他默认
                    if (config.IsDefault)
                    {
                        this.UpdateData(db.LcChatModelConfigs
                            .Where(c => c.IsDefault && c.Id != config.Id)
                            .Set(c => c.IsDefault, false));
                    }

                    // 保存配置
                    var dbConfig = new LcChatModelConfig
                    {
                        Id = config.Id,
                        Name = config.Name,
                        Type = config.Type,
                        ApiKey = config.ApiKey,
                        ChatModelId = config.ChatModelID,
                        EmbeddingModelId = config.EmbeddingModelID,
                        BaseUrl = config.BaseURL,
                        IsDefault = config.IsDefault,
                        State = 1
                    };

                    await db.InsertAsync(dbConfig);
                    await transaction.CommitAsync();

                    return config;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    Log.Error("创建模型配置失败: {ExMessage}", ex.Message);
                    throw new CustomException($"创建模型配置失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 更新模型配置
        /// </summary>
        [Function("updateModelConfiguration", "更新模型配置")]
        public async Task<ChatModelConfiguration> UpdateModelConfiguration(ChatModelConfiguration config)
        {
            await using var db = this.GetDb();

            await using (var transaction = await db.BeginTransactionAsync())
            {
                try
                {
                    // 如果设置为默认，则清除其他默认
                    if (config.IsDefault)
                    {
                        this.UpdateData(db.LcChatModelConfigs
                            .Where(c => c.IsDefault && c.Id != config.Id)
                            .Set(c => c.IsDefault, false));
                    }

                    // 更新配置
                    this.UpdateData(db.LcChatModelConfigs
                        .Where(c => c.Id == config.Id)
                        .Set(c => c.Name, config.Name)
                        .Set(c => c.Type, config.Type)
                        .Set(c => c.ApiKey, config.ApiKey)
                        .Set(c => c.ChatModelId, config.ChatModelID)
                        .Set(c => c.EmbeddingModelId, config.EmbeddingModelID)
                        .Set(c => c.BaseUrl, config.BaseURL)
                        .Set(c => c.IsDefault, config.IsDefault));

                    await transaction.CommitAsync();
                    return config;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    Log.Error("更新模型配置失败: {ExMessage}", ex.Message);
                    throw new CustomException($"更新模型配置失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取模型配置
        /// </summary>
        [Function("getModelConfiguration", "获取模型配置")]
        public async Task<ChatModelConfiguration> GetModelConfiguration(string configId)
        {
            await using var db = this.GetDb();

            var dbConfig = await db.LcChatModelConfigs
                .FirstOrDefaultAsync(c => c.Id == configId);

            if (dbConfig == null)
            {
                throw new CustomException($"找不到ID为{configId}的模型配置");
            }

            return new ChatModelConfiguration
            {
                Id = dbConfig.Id,
                Name = dbConfig.Name,
                Type = dbConfig.Type,
                ApiKey = dbConfig.ApiKey,
                ChatModelID = dbConfig.ChatModelId,
                EmbeddingModelID = dbConfig.EmbeddingModelId,
                BaseURL = dbConfig.BaseUrl,
                IsDefault = dbConfig.IsDefault
            };
        }

        /// <summary>
        /// 获取默认模型配置
        /// </summary>
        [Function("getDefaultModelConfiguration", "获取默认模型配置")]
        public async Task<ChatModelConfiguration> GetDefaultModelConfiguration()
        {
            await using var db = this.GetDb();

            var dbConfig = await db.LcChatModelConfigs
                .FirstOrDefaultAsync(c => c.IsDefault);

            if (dbConfig == null)
            {
                throw new CustomException("未找到默认模型配置");
            }

            return new ChatModelConfiguration
            {
                Id = dbConfig.Id,
                Name = dbConfig.Name,
                Type = dbConfig.Type,
                ApiKey = dbConfig.ApiKey,
                ChatModelID = dbConfig.ChatModelId,
                EmbeddingModelID = dbConfig.EmbeddingModelId,
                BaseURL = dbConfig.BaseUrl,
                IsDefault = dbConfig.IsDefault
            };
        }

        /// <summary>
        /// 获取所有模型配置
        /// </summary>
        [Function("getAllModelConfigurations", "获取所有模型配置")]
        public async Task<List<ChatModelConfiguration>> GetAllModelConfigurations()
        {
            await using var db = this.GetDb();

            var dbConfigs = await db.LcChatModelConfigs.ToListAsync();
            
            return dbConfigs.Select(c => new ChatModelConfiguration
            {
                Id = c.Id,
                Name = c.Name,
                Type = c.Type,
                ApiKey = c.ApiKey,
                ChatModelID = c.ChatModelId,
                EmbeddingModelID = c.EmbeddingModelId,
                BaseURL = c.BaseUrl,
                IsDefault = c.IsDefault
            }).ToList();
        }
    }
}