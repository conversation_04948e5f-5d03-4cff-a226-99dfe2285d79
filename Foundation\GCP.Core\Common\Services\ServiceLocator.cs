﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.Common
{
    public class ServiceLocator
    {
        public static IServiceProvider Current { get; private set; }
        public static void SetServices(IServiceProvider services)
        {
            Current = services;
        }

        public static T GetScopedService<T>() where T : class
        {
            var httpContext = Current?.GetService<IHttpContextAccessor>()?.HttpContext;
            return httpContext?.RequestServices.GetService<T>();
        }
    }
}
