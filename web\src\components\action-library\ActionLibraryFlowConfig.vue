<template>
  <div class="action-library-flow-config">
    <div class="form-header">
      <h2>函数流配置</h2>
      <p>直接在此处创建和配置动作库的执行流程</p>
    </div>

    <div class="form-body">
      <!-- 函数流基础信息 -->
      <div class="flow-info-section">
        <t-card title="基础信息" :bordered="false">
          <t-form ref="flowInfoFormRef" :data="flowInfo" layout="vertical">
            <t-row :gutter="24">
              <t-col :span="8">
                <t-form-item label="函数流名称" name="name">
                  <t-input v-model="flowInfo.name" placeholder="请输入函数流名称" />
                </t-form-item>
              </t-col>
              <t-col :span="8">
                <t-form-item label="版本" name="version">
                  <t-input-number v-model="flowInfo.version" :min="1" readonly />
                </t-form-item>
              </t-col>
              <t-col :span="8">
                <t-form-item label="持久化" name="persistence">
                  <t-switch v-model="flowInfo.persistence" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-form-item label="描述" name="description">
              <t-textarea
                v-model="flowInfo.description"
                placeholder="请输入函数流描述"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </t-form-item>
          </t-form>
        </t-card>
      </div>

      <!-- 动作配置区域 -->
      <div class="action-config-section">
        <t-card title="动作配置" :bordered="false">
          <div class="action-configure-container">
            <div class="action-list-panel" :style="{ width: leftPanelWidth + 'px' }">
              <div class="panel-header">
                <h4>动作列表</h4>
                <t-space>
                  <span class="step-count">{{ flowInfo.body?.length || 0 }} 个步骤</span>
                </t-space>
              </div>
              <div class="action-list-content">
                <template v-if="flowInfo.body && flowInfo.body.length > 0">
                  <action-step-item
                    v-for="(step, index) in flowInfo.body"
                    :key="step.id"
                    :step="step"
                    :index="index"
                    :active="currentStepId === step.id"
                    @select="selectStep"
                    @delete="deleteStep"
                    @move-up="moveStepUp"
                    @move-down="moveStepDown"
                  />
                </template>
                <div v-else class="empty-actions">
                  <t-empty description="暂无动作，点击下方按钮添加" />
                </div>
                <div class="add-action-section">
                  <action-type-dropdown @add-action="addAction">
                    <t-button block variant="dashed">
                      <template #icon>
                        <add-icon />
                      </template>
                      添加动作
                    </t-button>
                  </action-type-dropdown>
                </div>
              </div>

              <!-- 拖拽分隔线 -->
              <div class="resize-handle" @mousedown="startResize">
                <div class="resize-handle-line"></div>
              </div>
            </div>

            <div class="action-form-panel" :style="{ width: `calc(100% - ${leftPanelWidth}px)` }">
              <div class="panel-header">
                <h4>{{ currentStep ? `配置动作: ${currentStep.name || currentStep.function}` : '动作配置' }}</h4>
                <t-space v-if="currentStep">
                  <t-button size="small" theme="default" @click="duplicateStep">
                    <template #icon>
                      <copy-icon />
                    </template>
                    复制
                  </t-button>
                  <t-button size="small" theme="danger" @click="deleteCurrentStep">
                    <template #icon>
                      <delete-icon />
                    </template>
                    删除
                  </t-button>
                </t-space>
              </div>
              <div class="action-form-content">
                <div v-if="currentStep" class="step-form">
                  <action-step-form v-model="currentStep" @update="updateCurrentStep" />
                </div>
                <div v-else class="no-selection">
                  <t-empty description="请选择一个动作进行配置" />
                </div>
              </div>
            </div>
          </div>
        </t-card>
      </div>

      <!-- 变量配置区域 -->
      <div class="variables-config-section">
        <t-card title="变量配置" :bordered="false">
          <div class="variables-container">
            <div class="variables-header">
              <h4>输入变量</h4>
              <t-button size="small" theme="primary" @click="addVariable">
                <template #icon>
                  <add-icon />
                </template>
                添加变量
              </t-button>
            </div>
            <div class="variables-content">
              <template v-if="flowInfo.data && flowInfo.data.length > 0">
                <variable-item
                  v-for="(variable, index) in flowInfo.data"
                  :key="variable.key"
                  :variable="variable"
                  :index="index"
                  @update="updateVariable"
                  @delete="deleteVariable"
                />
              </template>
              <div v-else class="empty-variables">
                <t-empty description="暂无变量，点击上方按钮添加" />
              </div>
            </div>
          </div>
        </t-card>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryFlowConfig',
};
</script>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';
import { AddIcon, CopyIcon, DeleteIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import ActionStepItem from './ActionStepItem.vue';
import ActionTypeDropdown from './ActionTypeDropdown.vue';
import ActionStepForm from './ActionStepForm.vue';
import VariableItem from './VariableItem.vue';

interface ActionLibraryData {
  id?: string;
  name?: string;
  functionFlowId?: string;
  functionFlowVersion?: number;
}

interface FlowStep {
  id: string;
  name?: string;
  function: string;
  description?: string;
  args?: any;
  result?: any[];
}

interface FlowVariable {
  key: string;
  value: {
    type: string;
    textValue?: string;
    numberValue?: number;
    booleanValue?: boolean;
    objectValue?: any;
  };
  description?: string;
}

interface FlowInfo {
  id?: string;
  name: string;
  description?: string;
  version: number;
  persistence: boolean;
  data: FlowVariable[];
  body: FlowStep[];
}

const props = defineProps<{
  modelValue: ActionLibraryData;
}>();

const emits = defineEmits<{
  'update:modelValue': [value: ActionLibraryData];
}>();

const flowInfoFormRef = ref();
const currentStepId = ref<string>('');
const currentStep = ref<FlowStep | null>(null);

// 函数流信息
const flowInfo = ref<FlowInfo>({
  name: '',
  description: '',
  version: 1,
  persistence: false,
  data: [],
  body: [],
});

// 左侧面板宽度配置
const STORAGE_KEY = 'action-library-flow-panel-width';
const DEFAULT_WIDTH = 350;
const minWidth = 250;
const maxWidth = 600;

const getSavedWidth = (): number => {
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) {
    const width = parseInt(saved, 10);
    if (width >= minWidth && width <= maxWidth) {
      return width;
    }
  }
  return DEFAULT_WIDTH;
};

const leftPanelWidth = ref(getSavedWidth());

// 拖拽调整宽度
const isResizing = ref(false);
const startX = ref(0);
const startWidth = ref(0);

const startResize = (event: MouseEvent) => {
  isResizing.value = true;
  startX.value = event.clientX;
  startWidth.value = leftPanelWidth.value;

  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
  document.body.style.userSelect = 'none';
  document.body.style.cursor = 'col-resize';
};

const handleResize = (event: MouseEvent) => {
  if (!isResizing.value) return;

  const deltaX = event.clientX - startX.value;
  const newWidth = startWidth.value + deltaX;

  if (newWidth >= minWidth && newWidth <= maxWidth) {
    leftPanelWidth.value = newWidth;
  }
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
  document.body.style.userSelect = '';
  document.body.style.cursor = '';

  // 保存宽度到localStorage
  localStorage.setItem(STORAGE_KEY, leftPanelWidth.value.toString());
};

// 生成唯一ID
const generateId = () => {
  return 'step_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// 步骤管理
const selectStep = (step: FlowStep) => {
  currentStepId.value = step.id;
  currentStep.value = { ...step };
};

const addAction = (actionType: string) => {
  const newStep: FlowStep = {
    id: generateId(),
    name: `新动作 ${flowInfo.value.body.length + 1}`,
    function: actionType,
    description: '',
    args: {},
    result: [],
  };

  flowInfo.value.body.push(newStep);
  selectStep(newStep);
  updateModelValue();
};

const deleteStep = (stepId: string) => {
  const index = flowInfo.value.body.findIndex((step) => step.id === stepId);
  if (index > -1) {
    flowInfo.value.body.splice(index, 1);
    if (currentStepId.value === stepId) {
      currentStepId.value = '';
      currentStep.value = null;
    }
    updateModelValue();
  }
};

const deleteCurrentStep = () => {
  if (currentStep.value) {
    deleteStep(currentStep.value.id);
  }
};

const duplicateStep = () => {
  if (currentStep.value) {
    const newStep: FlowStep = {
      ...currentStep.value,
      id: generateId(),
      name: `${currentStep.value.name} (副本)`,
    };

    const currentIndex = flowInfo.value.body.findIndex((step) => step.id === currentStep.value!.id);
    flowInfo.value.body.splice(currentIndex + 1, 0, newStep);
    selectStep(newStep);
    updateModelValue();
  }
};

const moveStepUp = (stepId: string) => {
  const index = flowInfo.value.body.findIndex((step) => step.id === stepId);
  if (index > 0) {
    const step = flowInfo.value.body.splice(index, 1)[0];
    flowInfo.value.body.splice(index - 1, 0, step);
    updateModelValue();
  }
};

const moveStepDown = (stepId: string) => {
  const index = flowInfo.value.body.findIndex((step) => step.id === stepId);
  if (index < flowInfo.value.body.length - 1) {
    const step = flowInfo.value.body.splice(index, 1)[0];
    flowInfo.value.body.splice(index + 1, 0, step);
    updateModelValue();
  }
};

const updateCurrentStep = (updatedStep: FlowStep) => {
  const index = flowInfo.value.body.findIndex((step) => step.id === updatedStep.id);
  if (index > -1) {
    flowInfo.value.body[index] = { ...updatedStep };
    currentStep.value = { ...updatedStep };
    updateModelValue();
  }
};

// 变量管理
const addVariable = () => {
  const newVariable: FlowVariable = {
    key: `variable_${flowInfo.value.data.length + 1}`,
    value: {
      type: 'text',
      textValue: '',
    },
    description: '',
  };

  flowInfo.value.data.push(newVariable);
  updateModelValue();
};

const updateVariable = (index: number, updatedVariable: FlowVariable) => {
  if (index >= 0 && index < flowInfo.value.data.length) {
    flowInfo.value.data[index] = { ...updatedVariable };
    updateModelValue();
  }
};

const deleteVariable = (index: number) => {
  if (index >= 0 && index < flowInfo.value.data.length) {
    flowInfo.value.data.splice(index, 1);
    updateModelValue();
  }
};

// 数据同步
const updateModelValue = () => {
  // 生成函数流JSON
  const flowJson = JSON.stringify({
    id: flowInfo.value.id || generateId(),
    name: flowInfo.value.name,
    description: flowInfo.value.description,
    version: flowInfo.value.version,
    persistence: flowInfo.value.persistence,
    data: flowInfo.value.data,
    body: flowInfo.value.body,
  });

  // 更新父组件数据
  const updatedData = {
    ...props.modelValue,
    functionFlowId: flowInfo.value.id || generateId(),
    functionFlowVersion: flowInfo.value.version,
    // 可以在这里添加其他需要同步的字段
  };

  emits('update:modelValue', updatedData);
};

// 监听flowInfo变化
watch(
  () => flowInfo.value,
  () => {
    updateModelValue();
  },
  { deep: true },
);

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue.name && !flowInfo.value.name) {
      flowInfo.value.name = `${newValue.name}_函数流`;
    }
  },
  { deep: true, immediate: true },
);

// 清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
});

// 验证表单
const validate = async () => {
  try {
    await flowInfoFormRef.value?.validate();
    return true;
  } catch (error) {
    return false;
  }
};

defineExpose({
  validate,
  getFlowData: () => flowInfo.value,
});
</script>

<style lang="less" scoped>
.action-library-flow-config {
  height: 100%;
  display: flex;
  flex-direction: column;

  .form-header {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--td-text-color-secondary);
    }
  }

  .form-body {
    flex: 1;
    overflow-y: auto;

    .flow-info-section,
    .action-config-section,
    .variables-config-section {
      margin-bottom: 24px;

      .t-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border-radius: 12px;

        :deep(.t-card__header) {
          padding: 20px 24px 16px;
          border-bottom: 1px solid var(--td-border-level-1-color);

          .t-card__title {
            font-size: 16px;
            font-weight: 600;
          }
        }

        :deep(.t-card__body) {
          padding: 24px;
        }
      }
    }

    .action-configure-container {
      display: flex;
      height: 600px;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 8px;
      overflow: hidden;

      .action-list-panel {
        display: flex;
        flex-direction: column;
        background: var(--td-bg-color-container);
        border-right: 1px solid var(--td-border-level-1-color);
        position: relative;

        .panel-header {
          padding: 16px 20px;
          border-bottom: 1px solid var(--td-border-level-1-color);
          background: var(--td-bg-color-container-hover);
          display: flex;
          justify-content: space-between;
          align-items: center;

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--td-text-color-primary);
          }

          .step-count {
            font-size: 12px;
            color: var(--td-text-color-secondary);
            background: var(--td-bg-color-component);
            padding: 2px 8px;
            border-radius: 12px;
          }
        }

        .action-list-content {
          flex: 1;
          overflow-y: auto;
          padding: 16px;

          .empty-actions {
            padding: 40px 20px;
            text-align: center;
          }

          .add-action-section {
            margin-top: 16px;
          }
        }

        .resize-handle {
          position: absolute;
          top: 0;
          right: -3px;
          bottom: 0;
          width: 6px;
          cursor: col-resize;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover .resize-handle-line {
            background: var(--td-brand-color);
          }

          .resize-handle-line {
            width: 2px;
            height: 40px;
            background: var(--td-border-level-2-color);
            border-radius: 1px;
            transition: background 0.2s ease;
          }
        }
      }

      .action-form-panel {
        display: flex;
        flex-direction: column;
        background: var(--td-bg-color-page);

        .panel-header {
          padding: 16px 20px;
          border-bottom: 1px solid var(--td-border-level-1-color);
          background: var(--td-bg-color-container);
          display: flex;
          justify-content: space-between;
          align-items: center;

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--td-text-color-primary);
          }
        }

        .action-form-content {
          flex: 1;
          overflow-y: auto;
          padding: 24px;

          .no-selection {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
          }
        }
      }
    }

    .variables-container {
      .variables-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
      }

      .variables-content {
        .empty-variables {
          padding: 40px 20px;
          text-align: center;
        }
      }
    }
  }
}
</style>
