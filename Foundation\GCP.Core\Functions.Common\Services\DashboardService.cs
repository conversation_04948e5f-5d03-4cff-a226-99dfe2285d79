﻿using GCP.Common;
using GCP.DataAccess;

namespace GCP.Functions.Common.Services
{
    [Function("dashboard", "仪表盘服务")]
    internal class DashboardService : BaseService
    {
        private List<T> FillMissingTimeData<T>(
    List<KeyValuePair<DateTime, T>> sourceData,
    DateTime startTime,
    DateTime endTime,
    Func<DateTime, DateTime> normalizeTime,
    Func<DateTime, DateTime> nextTimePoint,
    T defaultValue)
        {
            var result = new List<T>();
            var currentTime = normalizeTime(startTime);

            // 创建查找字典，方便根据日期查找对应的值
            var dataDict = sourceData.ToDictionary(x => normalizeTime(x.Key), x => x.Value);

            // 遍历时间范围内的每个时间点
            while (currentTime <= endTime)
            {
                if (dataDict.TryGetValue(currentTime, out var value))
                {
                    result.Add(value);
                }
                else
                {
                    result.Add(defaultValue);
                }

                currentTime = nextTimePoint(currentTime);
            }

            return result;
        }

        [Function("getTopPanelStats", "获取驾驶舱顶部面板统计数据")]
        public object GetTopPanelStats(string triggerType)
        {
            using var db = this.GetFlowDb();

            // 获取当前日期时间信息
            var now = DateTime.Now;
            var today = now.Date;
            var yesterday = today.AddDays(-1);

            // 本周开始和结束
            var dayOfWeek = (int)now.DayOfWeek;
            dayOfWeek = dayOfWeek == 0 ? 7 : dayOfWeek;
            var thisWeekStart = today.AddDays(-(dayOfWeek - 1));
            var thisWeekEnd = thisWeekStart.AddDays(7);

            // 上周开始和结束
            var lastWeekStart = thisWeekStart.AddDays(-7);
            var lastWeekEnd = thisWeekStart;

            // 获取十分钟数据的开始结束时间
            var minuteNow = now.Minute;
            var currentTenMinStart = now.Date.AddHours(now.Hour).AddMinutes((minuteNow / 10) * 10).AddMinutes(-10);
            var lastTenMinStart = currentTenMinStart.AddMinutes(-10);

            // 1. 获取运行总数（按周汇总）及比较
            var thisWeekTotalStats = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= thisWeekStart && p.StatDate < thisWeekEnd && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => 1)
                .Select(g => new
                {
                    TotalCount = g.Sum(p => p.TotalCount),
                    SuccessCount = g.Sum(p => p.SuccessCount),
                    FailCount = g.Sum(p => p.FailCount)
                })
                .FirstOrDefault() ?? new { TotalCount = 0, SuccessCount = 0, FailCount = 0 };

            var lastWeekTotalStats = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= lastWeekStart && p.StatDate < lastWeekEnd && p.ArchivePeriod == 1 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => 1)
                .Select(g => new
                {
                    TotalCount = g.Sum(p => p.TotalCount)
                })
                .FirstOrDefault() ?? new { TotalCount = 0 };

            // 计算同比变化率
            decimal weekChangeRate = 0;
            if (lastWeekTotalStats.TotalCount > 0)
            {
                weekChangeRate = Math.Round(((decimal)thisWeekTotalStats.TotalCount / lastWeekTotalStats.TotalCount - 1) * 100, 1);
            }

            // 2. 获取今日调用次数及与昨日对比
            var todayStats = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= today && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => 1)
                .Select(g => new
                {
                    TotalCount = g.Sum(p => p.TotalCount)
                })
                .FirstOrDefault() ?? new { TotalCount = 0 };

            var yesterdayStats = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate == yesterday && p.ArchivePeriod == 1 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => 1)
                .Select(g => new
                {
                    TotalCount = g.Sum(p => p.TotalCount)
                })
                .FirstOrDefault() ?? new { TotalCount = 0 };

            // 计算同比变化率
            decimal dayChangeRate = 0;
            if (yesterdayStats.TotalCount > 0)
            {
                dayChangeRate = Math.Round(((decimal)todayStats.TotalCount / yesterdayStats.TotalCount - 1) * 100, 1);
            }

            // 3. 获取当前十分钟流量统计和平均耗时及与前十分钟对比
            var currentTenMinStatsResult = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate == currentTenMinStart && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => 1)
                .Select(g => new
                {
                    TotalTraffic = g.Sum(p => p.TotalTraffic ?? 0),
                    AvgDuration = (int)g.Average(p => p.AvgDuration)
                })
                .FirstOrDefault();

            decimal currentTotalTraffic = 0;
            int currentAvgDuration = 0;

            if (currentTenMinStatsResult != null)
            {
                currentTotalTraffic = currentTenMinStatsResult.TotalTraffic;
                currentAvgDuration = currentTenMinStatsResult.AvgDuration;
            }

            var lastTenMinStatsResult = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate == lastTenMinStart && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => 1)
                .Select(g => new
                {
                    TotalTraffic = g.Sum(p => p.TotalTraffic ?? 0),
                    AvgDuration = (int)g.Average(p => p.AvgDuration)
                })
                .FirstOrDefault();

            decimal lastTotalTraffic = 0;
            int lastAvgDuration = 0;

            if (lastTenMinStatsResult != null)
            {
                lastTotalTraffic = lastTenMinStatsResult.TotalTraffic;
                lastAvgDuration = lastTenMinStatsResult.AvgDuration;
            }

            // 计算同比变化率
            decimal trafficChangeRate = 0;
            decimal durationChangeRate = 0;

            if (lastTotalTraffic > 0)
            {
                trafficChangeRate = Math.Round((currentTotalTraffic / lastTotalTraffic - 1) * 100, 1);
            }

            if (lastAvgDuration > 0)
            {
                durationChangeRate = Math.Round(((decimal)currentAvgDuration / lastAvgDuration - 1) * 100, 1);
            }

            // 获取各指标趋势图数据
            var endDate = today;

            // 运行总数趋势 - 按周统计（最近4周）
            var weekEnd = thisWeekEnd.AddDays(-7); // 本周不算，取前4周
            var weekStart = weekEnd.AddDays(-28);  // 往前4周

            // 获取周的开始日期
            DateTime GetWeekStart(DateTime date)
            {
                int dayOfWeek = (int)date.DayOfWeek;
                if (dayOfWeek == 0) dayOfWeek = 7;
                return date.Date.AddDays(-(dayOfWeek - 1));
            }

            // 获取原始周数据
            var runTotalWeeklyData = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= weekStart && p.StatDate < weekEnd && p.ArchivePeriod == 1 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .ToList()
                .GroupBy(p => GetWeekStart(p.StatDate))
                .Select(g => new KeyValuePair<DateTime, int>(g.Key, g.Sum(p => p.TotalCount)))
                .ToList();

            // 填充缺失的周数据
            var runTotalTrend = FillMissingTimeData(
                runTotalWeeklyData,
                weekStart,
                weekEnd.AddDays(-7), // 结束日期为最后一周的开始日期
                dt => GetWeekStart(dt), // 标准化为周开始日期
                dt => dt.AddDays(7),    // 下一个时间点为下一周
                0                        // 默认值为0
            );
            runTotalTrend.Add(thisWeekTotalStats.TotalCount);

            // 调用次数趋势 - 按天统计（最近7天）
            var dayStart = today.AddDays(-7);
            var dayEnd = today;

            var callCountDailyData = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= dayStart && p.StatDate < dayEnd && p.ArchivePeriod == 1 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => p.StatDate.Date)
                .Select(g => new KeyValuePair<DateTime, int>(g.Key, g.Sum(p => p.TotalCount)))
                .ToList();

            var callCountTrend = FillMissingTimeData(
                callCountDailyData,
                dayStart,
                dayEnd.AddDays(-1),
                dt => dt.Date,       // 标准化为日期（去掉时间部分）
                dt => dt.AddDays(1), // 下一个时间点为下一天
                0                     // 默认值为0
            );
            callCountTrend.Add(todayStats.TotalCount);

            // 流量趋势 - 按10分钟统计（最近24个点）
            var minuteEnd = currentTenMinStart;
            var minuteStart = minuteEnd.AddMinutes(-230); // 往前23个10分钟段 (-10*23 = -230)

            // 标准化为10分钟间隔的开始时间
            DateTime NormalizeTenMinutes(DateTime dt)
            {
                return dt.Date.AddHours(dt.Hour).AddMinutes((dt.Minute / 10) * 10);
            }

            var trafficMinuteData = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= minuteStart && p.StatDate <= minuteEnd && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => p.StatDate)
                .Select(g => new KeyValuePair<DateTime, decimal>(
                    g.Key,
                    Math.Round(g.Sum(p => p.TotalTraffic ?? 0) / 1024, 2)
                ))
                .ToList();

            var trafficTrend = FillMissingTimeData(
                trafficMinuteData,
                minuteStart,
                minuteEnd,
                dt => NormalizeTenMinutes(dt), // 标准化为10分钟间隔
                dt => dt.AddMinutes(10),       // 下一个时间点为10分钟后
                0m                             // 默认值为0
            );
            trafficTrend.Add(currentTotalTraffic);

            // 平均耗时趋势 - 按10分钟统计（最近24个点）
            var durationMinuteData = db.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= minuteStart && p.StatDate <= minuteEnd && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => p.StatDate)
                .Select(g => new KeyValuePair<DateTime, int>(
                    g.Key,
                    (int)g.Average(p => p.AvgDuration)
                ))
                .ToList();

            var durationTrend = FillMissingTimeData(
                durationMinuteData,
                minuteStart,
                minuteEnd,
                dt => NormalizeTenMinutes(dt), // 标准化为10分钟间隔
                dt => dt.AddMinutes(10),       // 下一个时间点为10分钟后
                0                              // 默认值为0
            );
            durationTrend.Add(currentAvgDuration);

            // 构建返回结果
            string GetUpTrend(decimal rate)
            {
                if (rate > 0) return rate.ToString("0.##") + "%";
                if (rate == 0) return "0%";
                return null;
            }
            string GetDownTrend(decimal rate)
            {
                if (rate < 0) return Math.Abs(rate).ToString("0.##") + "%";
                if (rate == 0) return "0%";
                return null;
            }
            return new
            {
                Base = new object[]
                {
                    new {
                        title = "运行总数",
                        number = thisWeekTotalStats.TotalCount.ToString("N0"),
                        upTrend = GetUpTrend(weekChangeRate),
                        downTrend = GetDownTrend(weekChangeRate),
                        trentName = "较上周",
                        chartData = runTotalTrend
                    },
                    new {
                        title = "调用次数",
                        number = todayStats.TotalCount.ToString("N0"),
                        upTrend = GetUpTrend(dayChangeRate),
                        downTrend = GetDownTrend(dayChangeRate),
                        trentName = "较前日",
                        chartData = callCountTrend
                    },
                    new {
                        title = "流量统计(KB)",
                        number = Math.Round(currentTotalTraffic / 1024, 2).ToString("0.##"),
                        upTrend = GetUpTrend(trafficChangeRate),
                        downTrend = GetDownTrend(trafficChangeRate),
                        trentName = "十分钟",
                        chartData = trafficTrend
                    },
                    new {
                        title = "平均耗时(ms)",
                        number = currentAvgDuration.ToString(),
                        upTrend = GetUpTrend(durationChangeRate),
                        downTrend = GetDownTrend(durationChangeRate),
                        trentName = "十分钟",
                        chartData = durationTrend
                    }
                }
            };
        }

        [Function("getServiceResources", "获取驾驶舱服务资源统计")]
        public object GetServiceResources(string triggerType)
        {
            using var db = this.GetDb();

            var apiCount = db.LcApis.Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.State == 1).Count();
            var jobCount = db.LcJobs.Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.State == 1).Count();
            var eventCount = db.LcMessageEvents.Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.State == 1).Count();
            var pointCount = db.LcIotEquipmentVariables.Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId).Count();

            using var flowDb = this.GetFlowDb();

            var now = DateTime.Now;
            var today = now.Date;

            // 获取服务资源统计
            var resourceStats = flowDb.LcFhiStats
                .Where(p => p.SolutionId == SolutionId && p.ProjectId == ProjectId && p.StatDate >= today.AddDays(-7) && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => p.TriggerType)
                .Select(g => new
                {
                    TriggerType = g.Key,
                    Count = g.Sum(p => p.TotalCount)
                })
                .ToList();

            return new
            {
                Base = new
                {
                    apiCount,
                    jobCount,
                    eventCount,
                    pointCount,
                    rateData = new List<object> {
                        new { name = "定时任务", value = resourceStats.FirstOrDefault(x => x.TriggerType == "JOB")?.Count ?? 0 },
                        new { name = "API", value = resourceStats.FirstOrDefault(x => x.TriggerType == "API")?.Count ?? 0 },
                        new { name = "消息队列", value = resourceStats.FirstOrDefault(x => x.TriggerType == "EVENT")?.Count ?? 0 },
                    },
                }
            };
        }


        [Function("getChartData", "获取驾驶舱统计图表数据")]
        public object GetChartData(string triggerType, DateTime? startDate = null, DateTime? endDate = null)
        {
            using var db = this.GetFlowDb();

            var now = DateTime.Now;
            var today = now.Date;

            // 默认显示最近7天数据
            if (!endDate.HasValue)
            {
                endDate = today;
            }

            if (!startDate.HasValue)
            {
                startDate = endDate.Value.AddDays(-6);
            }

            var chartData = db.LcFhiStats
                .Where(p => p.StatDate >= startDate.Value && p.StatDate <= endDate.Value && p.ArchivePeriod == 1 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => p.StatDate)
                .Select(g => new
                {
                    Date = g.Key,
                    SuccessCount = g.Sum(p => p.SuccessCount),
                    FailCount = g.Sum(p => p.FailCount),
                    TotalCount = g.Sum(p => p.TotalCount)
                })
                .OrderBy(x => x.Date)
                .ToList();

            return chartData.Select(item => new
            {
                Date = item.Date.ToString("yyyy-MM-dd"),
                Success = item.SuccessCount,
                Fail = item.FailCount,
                Other = item.TotalCount - item.SuccessCount - item.FailCount,
                Total = item.TotalCount
            }).ToList();
        }

        [Function("getCallRank", "获取驾驶舱调用次数排行")]
        public object GetCallRank(string triggerType, string timeRange = "today")
        {
            using var db = this.GetFlowDb();

            var now = DateTime.Now;
            var today = now.Date;
            DateTime startDate;
            DateTime endDate = today.AddDays(1);

            // 根据时间范围参数确定开始时间
            switch (timeRange.ToLower())
            {
                case "yesterday":
                    startDate = today.AddDays(-1);
                    endDate = today;
                    break;
                case "week":
                    var dayOfWeek = (int)now.DayOfWeek;
                    dayOfWeek = dayOfWeek == 0 ? 7 : dayOfWeek;
                    startDate = today.AddDays(-(dayOfWeek - 1));
                    break;
                case "today":
                default:
                    startDate = today;
                    break;
            }

            var callRankList = db.LcFhiStats
                .Where(p => p.StatDate >= startDate && p.StatDate < endDate && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType))
                .GroupBy(p => new { p.FunctionId })
                .Select(g => new
                {
                    FunctionId = g.Key.FunctionId,
                    TotalCount = g.Sum(p => p.TotalCount),
                    SuccessRate = Math.Round(g.Sum(p => p.SuccessCount) * 100m / (g.Sum(p => p.TotalCount) > 0 ? g.Sum(p => p.TotalCount) : 1), 1)
                })
                .OrderByDescending(x => x.TotalCount)
                .OrderBy(x => x.SuccessRate)
                .Take(5)
                .ToList();

            // 查询服务名
            var funcIds = callRankList.Select(x => x.FunctionId).ToList();
            using (var dbMain = new GcpDb())
            {
                var funcNames = dbMain.LcFunctions.Where(f => funcIds.Contains(f.Id)).ToDictionary(f => f.Id, f => f.FunctionName);
                return new
                {
                    Base = callRankList.Select(x => new
                    {
                        serviceName = funcNames.ContainsKey(x.FunctionId) ? funcNames[x.FunctionId] : x.FunctionId,
                        count = x.TotalCount,
                        successRate = x.SuccessRate
                    }).ToList()
                };
            }
        }

        [Function("getErrorRank", "获取驾驶舱错误次数排行")]
        public object GetErrorRank(string triggerType, string timeRange = "today")
        {
            using var db = this.GetFlowDb();

            var now = DateTime.Now;
            var today = now.Date;
            DateTime startDate;
            DateTime endDate = today.AddDays(1);

            // 根据时间范围参数确定开始时间
            switch (timeRange.ToLower())
            {
                case "yesterday":
                    startDate = today.AddDays(-1);
                    endDate = today;
                    break;
                case "week":
                    var dayOfWeek = (int)now.DayOfWeek;
                    dayOfWeek = dayOfWeek == 0 ? 7 : dayOfWeek;
                    startDate = today.AddDays(-(dayOfWeek - 1));
                    break;
                case "today":
                default:
                    startDate = today;
                    break;
            }

            var errorRankList = db.LcFhiStats
                .Where(p => p.StatDate >= startDate && p.StatDate < endDate && p.ArchivePeriod == 2 && (string.IsNullOrEmpty(triggerType) || p.TriggerType == triggerType) && p.FailCount > 0)
                .GroupBy(p => new { p.FunctionId, p.TriggerType })
                .Select(g => new
                {
                    g.Key.FunctionId,
                    g.Key.TriggerType,
                    ErrorCount = g.Sum(p => p.FailCount)
                })
                .OrderByDescending(x => x.ErrorCount)
                .Take(5)
                .ToList();

            // 查询服务名
            var funcIds = errorRankList.Select(x => x.FunctionId).ToList();
            using (var dbMain = new GcpDb())
            {
                var funcNames = dbMain.LcFunctions.Where(f => funcIds.Contains(f.Id)).ToDictionary(f => f.Id, f => f.FunctionName);
                return new
                {
                    Base = errorRankList.Select(x => new
                    {
                        functionId = x.FunctionId,
                        triggerType = x.TriggerType,
                        serviceName = funcNames.ContainsKey(x.FunctionId) ? funcNames[x.FunctionId] : x.FunctionId,
                        count = x.ErrorCount
                    }).ToList()
                };
            }
        }
    }
}