﻿using GCP.Common;

namespace GCP.FunctionPool.Flow.Models
{
    public class DataApiRequest
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ApiId { get; set; }

        /// <summary>
        /// 响应体配置ID，用于响应验证
        /// </summary>
        public string ResponseId { get; set; }

        public List<FlowData> Inputs { get; set; }

        public DataValue Headers { get; set; }
        public DataValue Params { get; set; }
        public DataValue Body { get; set; }
    }

    public class DataApiForwarder
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ApiId { get; set; }
        public DataValue Url { get; set; }
        /// <summary>
        /// 1-选择第三方ApiId，2-指定Url
        /// </summary>
        public short Mode { get; set; }
        public List<FlowData> Params { get; set; } = new List<FlowData>();
        public List<FlowData> Headers { get; set; } = new List<FlowData>();
    }

    /// <summary>
    /// 新版本API请求数据模型
    /// </summary>
    public class DataApiRequestV2
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ApiId { get; set; }

        /// <summary>
        /// 响应体配置ID，用于响应验证
        /// </summary>
        public string ResponseId { get; set; }

        /// <summary>
        /// 请求头配置
        /// </summary>
        public List<FlowData> Headers { get; set; } = new List<FlowData>();

        /// <summary>
        /// 请求参数配置
        /// </summary>
        public List<FlowData> Params { get; set; } = new List<FlowData>();

        /// <summary>
        /// 请求体配置
        /// </summary>
        public List<FlowData> Body { get; set; } = new List<FlowData>();

        /// <summary>
        /// 输出配置
        /// </summary>
        public List<FlowData> Outputs { get; set; } = new List<FlowData>();

        /// <summary>
        /// 版本标识
        /// </summary>
        public string Version { get; set; } = "v2";

        /// <summary>
        /// 是否使用根节点
        /// </summary>
        public bool? UseRoot { get; set; }
    }
}
