﻿using LinqToDB.Data;
using LinqToDB.SchemaProvider;

namespace GCP.DataAccess
{
    public class DbBase : DataConnection
    {
        public DbBase() : base(DbSettings.DefaultLinqDbProvider, DbSettings.DefaultConnectionString)
        {
        }

        public DbBase(string providerName, string connectionString): base(DbSettings.GetLinqDbProvider(providerName), connectionString)
        {
        }

        public DatabaseSchema GetSchema(Func<LoadTableData, bool> tableOrViewFilter = null, string[] includedSchemas = null, bool getProcedures = false)
        {
            var schemaProvider = this.DataProvider.GetSchemaProvider();
            var options = new GetSchemaOptions
            {
                IncludedSchemas = includedSchemas,
                GetProcedures = getProcedures,
                GetForeignKeys = false,
                LoadTable = tableOrViewFilter
            };

            return schemaProvider.GetSchema(this, options);
        }

        public TableSchema GetTableSchema(string tableName, string[] includedSchemas = null)
        {
            var schema = GetSchema(tableOrViewFilter: td => td.Name == tableName, includedSchemas);
            return schema.Tables.FirstOrDefault();
        }
    }
}
