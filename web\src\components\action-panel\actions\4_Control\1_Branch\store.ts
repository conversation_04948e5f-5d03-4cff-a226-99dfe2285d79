import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfo } from './model';

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};
  const isNewAction = !currentArgs.name; // 判断是否为新增动作

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      description: '',
      type: 'conditions',
      conditions: null,
      script: '',
      useRoot: isNewAction ? true : (currentArgs.useRoot ?? false), // 新增动作默认true，历史配置默认false
    } as ArgsInfo),
    ...currentArgs,
  }) as ArgsInfo;

  // 设置结果变量
  if (actionFlowStore.currentStep) {
    const resultVariable = {
      id: 'result',
      key: 'result',
      description: '判断结果',
      type: 'boolean',
      value: {
        type: 'variable' as const,
        variableType: 'current' as const,
        variableValue: 'result',
      },
    };

    actionFlowStore.currentStep.result = [resultVariable];
  }
};

export const useDataBranchStore = defineStore('DataBranch', {
  state: () => {
    const state = { args: null } as { args: ArgsInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 设置结果变量
      const resultVariable = {
        id: 'result',
        key: 'result',
        description: '判断结果',
        type: 'boolean',
        value: {
          type: 'variable' as const,
          variableType: 'current' as const,
          variableValue: 'result',
        },
      };

      actionFlowStore.setStepResultData([resultVariable]);
    },
  },
});
