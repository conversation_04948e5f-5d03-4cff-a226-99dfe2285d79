<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="12">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="Url类型" prop="mode">
              <t-radio-group v-model="formData.mode" name="mode" :options="modeOptions"></t-radio-group>
            </t-form-item>
          </t-col>
          <t-col v-show="formData.mode === 1" :span="6">
            <t-form-item label="转发API" prop="apiId">
              <t-select v-model="formData.apiId" placeholder="请选择转发API" :options="apiOptions"></t-select>
            </t-form-item>
          </t-col>
          <t-col v-show="formData.mode === 2" :span="6">
            <t-form-item label="Url地址" prop="apiId">
              <value-input
                v-model:data-value="formData.url"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
                placeholder="http://www.example.com/api/test"
              ></value-input>
              <!-- <t-input v-model="formData.url" placeholder="http://www.example.com/api/test" /> -->
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <t-tabs :default-value="1">
          <t-tab-panel :value="1" label="参数">
            <variable-list :data="formData.params" style="margin: 16px 0" />
          </t-tab-panel>
          <t-tab-panel :value="2" label="请求头">
            <variable-list :data="formData.headers" style="margin: 16px 0" />
          </t-tab-panel>
        </t-tabs>

        <action-form-title title="输出参数"> </action-form-title>
        <variable-list :data="currentStep.result" :show-root-node="false"></variable-list>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ApiForwarderActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onMounted, ref, watch } from 'vue';

import { api, Services } from '@/api/system';
import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';

import { useApiForwarderStore } from './store';

const actionFlowStore = useActionFlowStore();
const apiForwarderStore = useApiForwarderStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    apiForwarderStore.updateState();
  },
  {
    immediate: true,
  },
);
const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData } = storeToRefs(apiForwarderStore);

watch(
  () => formData.value,
  (newValue) => {
    apiForwarderStore.setArgs(newValue);
  },
  {
    deep: true,
    immediate: true,
  },
);

const modeOptions = [
  { label: '第三方Api', value: 1 },
  { label: '指定Url', value: 2 },
];

const apiOptions = ref([]);
const apiList = ref([]);
onMounted(() => {
  api
    .run(Services.apiGetAll, {
      apiType: 2,
    })
    .then((res) => {
      apiList.value = res;
      apiOptions.value = res.map((item) => ({ label: `${item.apiName}`, value: item.id }));
    });
});
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 720px;
  padding: 16px;
}
</style>
