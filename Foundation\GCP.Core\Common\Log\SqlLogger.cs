﻿using GCP.DataAccess;
using Serilog;

namespace GCP.Common
{
    public enum SqlType
    {
        Insert,
        Update,
    }
    public class SqlLogger : LoggerBase<Func<Tuple<SqlType, object>>>, IFunctionService
    {
        private readonly DbContext _db;

        public SqlLogger()
        {
            _db = new DbContext();
            if (SqlLogSettings.EnableLocalDatabase)
            {
                _db.SetDefaultConnection(SqlLogSettings.ConnectionString, DbProviderType.DuckDB);
            }

            this.Initialize(SqlLogSettings.ChannelCapacity);
        }

        public async Task Info(FunctionContext ctx, string message, bool isFlowLog = false)
        {
            await WriteLog(ctx, "INFO", message, isFlowLog: isFlowLog);
        }

        public async Task Warn(FunctionContext ctx, string message, bool isFlowLog = false)
        {
            await WriteLog(ctx, "WARN", message, isFlowLog: isFlowLog);
        }

        public async Task Error(FunctionContext ctx, Exception ex, string message, bool isFlowLog = false)
        {
            await WriteLog(ctx, "ERROR", message, ex, isFlowLog: isFlowLog);
        }

        public async Task Debug(FunctionContext ctx, string message, bool isFlowLog = false)
        {
            await WriteLog(ctx, "DEBUG", message, isFlowLog: isFlowLog);
        }

        internal async Task WriteLog(FunctionContext ctx, string level, string message, Exception ex = null, bool isFlowLog = false)
        {
            var id = TUID.NewTUID().ToString();
            var procId = ctx.trackId;
            var stepId = ctx.Current?.StepId;
            await this.Write(() =>
            {
                var log = new LcFruLog
                {
                    Id = id,
                    ProcId = procId,
                    StepId = stepId,
                    Category = isFlowLog ? "FLOW" : "ACTION",
                    Timestamp = DateTime.Now,
                    Level = level,
                    Message = message,
                };

                if (ex != null)
                {
                    log.Exception = ex.Message + Environment.NewLine + ex.StackTrace;
                }
                return new Tuple<SqlType, object>(SqlType.Insert, log);
            });
        }

        public async Task AddFunctionOutputVariable(FunctionContext ctx, object output)
        {
            if (!ctx.Persistence || output == null) return;
            var id = TUID.NewTUID().ToString();
            var procId = ctx.trackId;
            var stepId = ctx.Current.StepId;
            var outputStr = JsonHelper.Serialize(output);
            await this.Write(() => new Tuple<SqlType, object>(SqlType.Insert, new LcFruVariable
            {
                Id = id,
                ProcId = procId,
                StepId = stepId,
                VarType = "OUTPUT",
                VarName = "outputs",
                VarValue = outputStr,
            }));
        }

        public async Task AddFunctionContextVariable(FunctionContext ctx)
        {
            if (!ctx.Persistence) return;
            var id = TUID.NewTUID().ToString();
            var procId = ctx.trackId;
            var stepId = ctx.Current.StepId;
            ctx.globalData.Remove("$_Request");

            var ctxData = JsonHelper.Serialize(ctx);
            await this.Write(() => new Tuple<SqlType, object>(SqlType.Insert, new LcFruVariable
            {
                Id = id,
                ProcId = procId,
                StepId = stepId,
                VarType = "OUTPUT",
                VarName = "context",
                VarValue = ctxData,
            }));
        }

        public async Task AddFunctionInputVariable(FunctionContext ctx)
        {
            if (!ctx.Persistence) return;
            var procId = ctx.trackId;
            var stepId = ctx.Current.StepId;
            if (ctx.Current.Args != null)
            {
                var id = TUID.NewTUID().ToString();
                var ctxArgs = ctx.Current.Args;
                var currentArgs = ctx.Current.ArgsBuilder;
                var hasError = ctx.Current.HasError;
                await this.Write(() =>
                {
                    string argsStr = "";
                    if (hasError)
                    {
                        argsStr = JsonHelper.Serialize(ctxArgs);
                    }
                    else if (currentArgs is { Length: > 0 })
                    {
                        argsStr = currentArgs.ToString();
                    }
                    else
                    {
                        argsStr = JsonHelper.Serialize(ctxArgs);
                    }

                    return new Tuple<SqlType, object>(SqlType.Insert, new LcFruVariable
                    {
                        Id = id,
                        ProcId = procId,
                        StepId = stepId,
                        VarType = "INPUT",
                        VarName = "inputs",
                        VarValue = argsStr,
                    });
                });
            }

            var result = ctx.Current.Result;
            if (result != null && result is not IAsyncEnumerable<List<object>>)
            {
                var id = TUID.NewTUID().ToString();
                await this.Write(() => new Tuple<SqlType, object>(SqlType.Insert, new LcFruVariable
                {
                    Id = id,
                    ProcId = procId,
                    StepId = stepId,
                    VarType = "RESULT",
                    VarName = "result",
                    VarValue = JsonHelper.Serialize(result),
                }));
            }
        }

        private static string SqlLogPipelineKey => "$SqlLog";

        public async Task BatchProcess()
        {
            this.BufferSize = SqlLogSettings.EnableLocalDatabase ? 3400 : 340;

            ResiliencePipelineManager.TryAdd(SqlLogPipelineKey, () =>
            {
                var handle = new ResilienceHandle<object>(SqlLogPipelineKey)
                {
                    RetryCount = 5,
                    RetryDelaysInSeconds = 5
                };

                return handle.Build((ex) =>
                {
                    Log.Error("SqlLog 处理失败, 异常信息：{Exception}", ex.Message);
                    return Task.CompletedTask;
                });
            });

            await ResiliencePipelineManager.ExecuteAsync(SqlLogPipelineKey, async (token) =>
            {
                await StartConsumer(BatchExecute);
            });
        }

        private async Task BatchExecute(List<Func<Tuple<SqlType, object>>> items)
        {
            SqlList sqlList = new SqlList(_db);
            foreach (var func in items)
            {
                var item = func();
                if (item.Item1 == SqlType.Insert)
                {
                    sqlList.AddInsert(item.Item2);
                }
                else if (item.Item1 == SqlType.Update)
                {
                    sqlList.AddUpdate(item.Item2);
                }
            }

            await sqlList.BatchExecute(isRowByRow: false, commandTimeout: 120, openTransaction: false, exceptionSkip: true, onException: (ex, item) =>
            {
                Log.Error(ex, "写入日志失败, Item: {item}", item);
                return Task.CompletedTask;
            });
        }
    }
}
