﻿using EasyNotice;
using EasyNotice.Dingtalk;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class DingtalkExtension
    {
        public static EasyNoticeOptions UseDingTalk(
            this EasyNoticeOptions options,
            Action<DingtalkOptions> configure
            )
        {
            if (configure == null)
            {
                throw new ArgumentNullException(nameof(configure));
            }

            options.RegisterExtension(new DingtalkOptionsExtension(configure));
            return options;
        }
    }
}
