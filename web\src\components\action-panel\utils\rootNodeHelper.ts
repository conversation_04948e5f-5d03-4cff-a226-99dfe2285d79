import { FlowData } from '@/components/action-panel/model';

/**
 * 创建根节点绑定的工具函数
 * 当动作只有一个输出结果时，直接绑定到根节点，避免创建额外的子节点
 */

/**
 * 根节点固定名称常量
 * 所有根节点的名称都必须使用此常量，避免歧义
 */
export const ROOT_NODE_KEY = 'ROOT';

/**
 * 创建根节点 FlowData
 * 根节点名称固定为 "ROOT"，不可修改
 * @param type 数据类型 ('object', 'array', 'string', 'number', 'boolean')
 * @param description 描述
 * @param children 子节点（可选）
 * @returns 根节点 FlowData
 */
export function createRootNode(type: string, description: string, children?: FlowData[]): FlowData {
  return {
    id: ROOT_NODE_KEY,
    key: ROOT_NODE_KEY, // 固定为 ROOT，避免歧义
    description,
    type,
    value: {
      type: 'variable',
      variableType: 'current',
      variableValue: 'result',
    },
    children,
  };
}

/**
 * 创建对象类型的根节点
 * @param description 描述
 * @param children 子节点
 * @returns 对象类型的根节点
 */
export function createObjectRootNode(description: string = '结果', children?: FlowData[]): FlowData {
  return createRootNode('object', description, children);
}

/**
 * 创建数组类型的根节点
 * @param description 描述
 * @param children 子节点
 * @returns 数组类型的根节点
 */
export function createArrayRootNode(description: string = '结果', children?: FlowData[]): FlowData {
  return createRootNode('array', description, children);
}

/**
 * 创建字符串类型的根节点
 * @param description 描述
 * @returns 字符串类型的根节点
 */
export function createStringRootNode(description: string = '结果'): FlowData {
  return createRootNode('string', description);
}

/**
 * 创建数字类型的根节点
 * @param description 描述
 * @returns 数字类型的根节点
 */
export function createNumberRootNode(description: string = '结果'): FlowData {
  return createRootNode('number', description);
}

/**
 * 创建布尔类型的根节点
 * @param description 描述
 * @returns 布尔类型的根节点
 */
export function createBooleanRootNode(description: string = '结果'): FlowData {
  return createRootNode('boolean', description);
}

/**
 * 判断是否应该使用根节点绑定
 * 当只有一个输出结果且不需要复杂嵌套时，建议使用根节点绑定
 * @param outputCount 输出结果数量
 * @param hasComplexStructure 是否有复杂的嵌套结构
 * @returns 是否应该使用根节点绑定
 */
export function shouldUseRootNodeBinding(outputCount: number, hasComplexStructure: boolean = false): boolean {
  return outputCount === 1 && !hasComplexStructure;
}

/**
 * 为动作组件设置根节点结果数据
 * @param actionFlowStore 动作流程存储
 * @param rootNode 根节点数据
 */
export function setRootNodeResult(actionFlowStore: any, rootNode: FlowData): void {
  actionFlowStore.setStepResultData([rootNode]);
}
