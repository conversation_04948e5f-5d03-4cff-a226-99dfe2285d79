export default {
  header: {
    code: 'Code Repository',
    help: 'Document',
    user: 'Profile',
    signOut: 'Sign Out',
    setting: 'Setting',
  },
  notice: {
    title: 'Notification Center',
    clear: 'Clear',
    setRead: 'Set Read',
    empty: 'Empty',
    emptyNotice: 'No Notice',
    viewAll: 'View All',
  },
  tagTabs: {
    closeOther: 'close other',
    closeLeft: 'close left',
    closeRight: 'close right',
    refresh: 'refresh',
  },
  searchPlaceholder: 'Enter search content',
  setting: {
    title: 'Setting',
    theme: {
      mode: 'Theme Mode',
      color: 'Theme Color',
      options: {
        light: 'Light',
        dark: 'Dark ',
        auto: 'Follow System',
      },
    },
    navigationLayout: 'Navigation Layout',
    sideMode: 'Side Menu Mode',
    splitMenu: 'Split Menu（Only Mix mode）',
    fixedSidebar: 'Fixed Sidebar',
    element: {
      title: 'Element Switch',
      showHeader: 'Show Header',
      showBreadcrumb: 'Show Breadcrumb',
      showFooter: 'Show Footer',
      useTagTabs: 'Use Tag Tabs',
      menuAutoCollapsed: 'Menu Auto Collapsed',
    },
    tips: 'Please copy and manually modify the configuration file: /src/config/style.ts',
    copy: {
      title: 'Copy',
      success: 'copied',
      fail: 'fail to copy',
    },
  },
};
