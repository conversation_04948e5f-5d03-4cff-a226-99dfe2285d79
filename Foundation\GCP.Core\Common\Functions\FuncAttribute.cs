﻿namespace GCP.Common
{
    [AttributeUsage(AttributeTargets.Assembly | AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class FunctionAttribute : Attribute
    {
        /// <summary>
        /// 唯一路径
        /// </summary>
        public string Path { get; set; }
        /// <summary>
        /// 间隔字符
        /// </summary>
        public string SpaceCharacters { get; set; } = ".";
        /// <summary>
        /// 是否自动生成API
        /// </summary>
        public bool AutoGenerateApi { get; set; } = false;
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 程序启动就执行
        /// </summary>
        public bool StartupRun { get; set; }

        public FunctionAttribute() { }

        public FunctionAttribute(string path)
        {
            Path = path;
        }

        public FunctionAttribute(string path, string desc, bool startupRun = false)
        {
            Path = path;
            Description = desc;
            StartupRun = startupRun;
        }
    }
}
