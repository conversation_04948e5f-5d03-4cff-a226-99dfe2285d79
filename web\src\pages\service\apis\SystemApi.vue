<template>
  <div style="height: 100%">
    <action-panel
      ref="actionPanelRef"
      v-model:visible="isShowActionPanel"
      :action-id="functionId"
      :data="actionData"
      @back="onClickBackForm"
      @save="onClickSaveAction"
    ></action-panel>
    <cmp-container v-show="!(isShowForm || isShowActionPanel)" full>
      <system-api-panel></system-api-panel>
    </cmp-container>
  </div>
</template>
<script lang="ts">
export default {
  name: 'SystemApi',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onActivated, onMounted, ref } from 'vue';

import ActionPanel from '@/components/action-panel/ActionPanel.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import SystemApiPanel from './panels/SystemApiPanel.vue';
import { useApiStore } from './store';

const actionFlowStore = useActionFlowStore();

const apiStore = useApiStore();
const { isShowActionPanel, actionData, functionId } = storeToRefs(apiStore);

onActivated(() => {
  isShowActionPanel.value = false;
  actionFlowStore.flowType = 'API';
});

const isShowForm = ref(false);
const onClickBackForm = () => {
  isShowForm.value = false;
  actionFlowStore.setCurrentApiStep();
};

const onClickSaveAction = () => {
  // isShowActionPanel.value = false;
};

const actionPanelRef = ref<InstanceType<typeof ActionPanel>>();
onMounted(() => {
  apiStore.actionPanelRef = actionPanelRef;
});
</script>
<style lang="less" scoped>
.tabs-card {
  height: 100%;
}
</style>
