using Serilog;
using GCP.Iot.Models;

namespace GCP.Iot.Interfaces
{
    public interface IDriver : IDisposable
    {
        /// <summary>
        /// 驱动是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 日志记录器
        /// </summary>
        ILogger Logger { get; set; }

        /// <summary>
        /// 驱动名称
        /// </summary>
        string DriverCode { get; }

        /// <summary>
        /// 最小采集周期(毫秒)
        /// </summary>
        int MinSamplingPeriod { get; set; }

        /// <summary>
        /// 数据归档周期（毫秒）
        /// </summary>
        int? ArchivePeriod { get; set; }

        /// <summary>
        /// 是否支持批量读取
        /// </summary>
        bool SupportsBatchReading { get; }

        /// <summary>
        /// 连接设备
        /// </summary>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync();

        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns>断开是否成功</returns>
        Task<bool> DisconnectAsync();

        /// <summary>
        /// 读取数据
        /// </summary>
        /// <param name="address">地址</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>读取结果</returns>
        Task<DriverOperationResult> ReadAsync(string address, DataTypeEnum dataType);
        /// <summary>
        /// 批量读取数据
        /// </summary>
        /// <param name="addresses">地址和数据类型</param>
        /// <returns></returns>
        Task<DriverOperationResult> BatchReadAsync(Dictionary<string, DataTypeEnum> addresses);

        /// <summary>
        /// 写入数据
        /// </summary>
        /// <param name="address">地址</param>
        /// <param name="value">值</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>写入结果</returns>
        Task<DriverOperationResult> WriteAsync(string address, object value, DataTypeEnum dataType);
    }
}