﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20221108121803, "初始化标签表")]
    public class AddTagTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_TAG").WithDescription("标签")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("TAG_TYPE").AsFixedLengthAnsiString(3).WithColumnDescription("标签类型 E:环境 B:业务域 O:优化 N:新功能 U:其他")
               .WithColumn("TAG_NAME").AsAnsiString(200).WithColumnDescription("标签名称")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("TAG_COLOR").AsAnsiString(36).Nullable().WithColumnDescription("标签颜色")
               ;

            Create.Index("TAG_TYPE_IDX")
                .OnTable("LC_TAG")
                .OnColumn("TAG_NAME").Ascending()
                .OnColumn("TAG_TYPE").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();

            Create.Table("LC_TAG_MAPPING").WithDescription("标签映射")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("TAG_ID").AsAnsiString(36).WithColumnDescription("标签 ID")
               .WithColumn("OBJECT_ID").AsAnsiString(36).WithColumnDescription("映射对象 ID")
               .WithColumn("OBJECT_TYPE").AsAnsiString(50).WithColumnDescription("映射对象类型 M:模型 F:函数 D:目录 P:项目")
               ;

            Create.Index("MAPPING_TAG_IDX")
                .OnTable("LC_TAG_MAPPING")
                .OnColumn("TAG_ID").Ascending()
                .OnColumn("OBJECT_ID").Ascending()
                .OnColumn("OBJECT_TYPE").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();

            //Create.Index("MAPPING_OBJ_IDX")
            //    .OnTable("LC_TAG_MAPPING")
            //    .OnColumn("OBJECT_ID").Ascending()
            //    .OnColumn("OBJECT_TYPE").Ascending()
            //    .OnColumn("SOLUTION_ID").Ascending()
            //    .OnColumn("PROJECT_ID").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_TAG");
            Delete.Table("LC_TAG_MAPPING");
        }
    }
}
