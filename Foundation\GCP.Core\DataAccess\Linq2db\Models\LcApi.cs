// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// API定义
	/// </summary>
	[Table("lc_api")]
	public class LcApi : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"                , CanBeNull = false, IsPrimaryKey = true)] public string    Id               { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                               )] public DateTime  TimeCreate       { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"           , CanBeNull = false                     )] public string    Creator          { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                             )] public DateTime? TimeModified     { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                                  )] public string?   Modifier         { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                     )] public short     State            { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"       , CanBeNull = false                     )] public string    SolutionId       { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"        , CanBeNull = false                     )] public string    ProjectId        { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:请求类型
		/// </summary>
		[Column("REQUEST_TYPE"                                              )] public string?   RequestType      { get; set; } // varchar(50)
		/// <summary>
		/// Description:请求地址
		/// </summary>
		[Column("HTTP_URL"                                                  )] public string?   HttpUrl          { get; set; } // varchar(200)
		/// <summary>
		/// Description:API名称
		/// </summary>
		[Column("API_NAME"          , CanBeNull = false                     )] public string    ApiName          { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                               )] public string?   Description      { get; set; } // varchar(200)
		/// <summary>
		/// Description:函数ID
		/// </summary>
		[Column("FUNCTION_ID"                                               )] public string?   FunctionId       { get; set; } // varchar(36)
		/// <summary>
		/// Description:集群ID
		/// </summary>
		[Column("CLUSTER_ID"                                                )] public string?   ClusterId        { get; set; } // varchar(36)
		/// <summary>
		/// Description:匹配主机名，支持多个逗号分隔
		/// </summary>
		[Column("HOSTS"                                                     )] public string?   Hosts            { get; set; } // varchar(200)
		[Column("HEADERS"                                                   )] public string?   Headers          { get; set; } // varchar(2000)
		[Column("QUERY_PARAMETERS"                                          )] public string?   QueryParameters  { get; set; } // varchar(2000)
		/// <summary>
		/// Description:API类型，1-系统API，2-第三方API，3-网关API
		/// </summary>
		[Column("API_TYPE"                                                  )] public short     ApiType          { get; set; } // smallint
		/// <summary>
		/// Description:API请求体
		/// </summary>
		[Column("BODY"                                                      )] public string?   Body             { get; set; } // varchar(4000)
		/// <summary>
		/// Description:API基础地址
		/// </summary>
		[Column("BASE_URL"                                                  )] public string?   BaseUrl          { get; set; } // varchar(200)
		/// <summary>
		/// Description:API请求体
		/// </summary>
		[Column("RESPONSE"                                                  )] public string?   Response         { get; set; } // varchar(4000)
		/// <summary>
		/// Description:响应体配置ID
		/// </summary>
		[Column("RESPONSE_ID"                                               )] public string?   ResponseId       { get; set; } // varchar(36)
		/// <summary>
		/// 目录编码
		/// </summary>
		[Column("DIR_CODE"                                                  )] public string?   DirCode          { get; set; } // varchar(50)
		/// <summary>
		/// 超时时间（秒）
		/// </summary>
		[Column("TIMEOUT_IN_SECONDS"                                        )] public short?    TimeoutInSeconds { get; set; } // smallint
	}
}
