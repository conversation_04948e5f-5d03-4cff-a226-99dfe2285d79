﻿using GCP.Common;
using GCP.DataAccess;
using Jint;
using Jint.Native;
using Jint.Runtime.Interop;

namespace GCP.Functions.Common
{
    public static class JavascriptEngine
    {
        public static Engine CreateEngine(string basePath = null, DbContext db = null)
        {
            var engine = new Engine(opts =>
            {
                //禁用严格模式
                //opts.Strict();

                //极限递归限制
                opts.LimitRecursion(200);

                //添加扩展方法
                //opts.AddExtensionMethods(typeof(JavascriptRunExtensions));

                //设置时区
                opts.LocalTimeZone(TimeZoneHelper.ChinaTimeZone);

                //设置模块路径
                if (!string.IsNullOrWhiteSpace(basePath))
                    opts.EnableModules(basePath);
            });
            
            //注册自定义异常类型
            engine.SetValue("CustomError", TypeReference.CreateTypeReference<CustomException>(engine));

            return engine;
        }


        #region js引擎参数准备
        /// <summary>
        /// javascript 脚本执行之前准备
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="ctx"></param>
        public static void JavascriptFuncBeforeExec(this Engine engine, FunctionContext ctx)
        {
            engine.SetValue("useContext", () => ctx);
            engine.SetValue("useDatabase", () => ctx.db);
            engine.SetValue("console", new JavascriptConsole(engine, ctx));
        }
        #endregion


        public static object GetObject(this JsValue jsValue)
        {
            if (jsValue.IsUndefined() || jsValue.IsNull())
            {
                return null;
            }
            else if (jsValue.IsString())
            {
                return jsValue.AsString();
            }
            else if (jsValue.IsDate())
            {
                return jsValue.AsDate().ToDateTime().ToLocalTime();
            }
            else if (jsValue.IsBoolean())
            {
                return jsValue.AsBoolean();
            }
            //else if (jsValue.IsNumber())
            //{
            //    return jsValue.AsNumber();
            //}
            else if (jsValue.IsArray())
            {
                var arr = new List<object>();
                foreach (var item in jsValue.AsArray())
                {
                    arr.Add(item.ToObject());
                }
                return arr;
            }
            //else if (jsValue.IsObject())
            //{
            //    var oi = jsValue.AsObject();
            //    var obj = new Dictionary<string, object>();
            //    var len = (uint)TypeConverter.ToLength(oi.Get(new JsString("length")));
            //    var k = 0;
            //    while (k < len)
            //    {
            //        var prop = new JsString(k.ToString());
            //        var v = oi.Get(prop);
            //        obj.Add(prop.ToString(), v.ToObject());
            //        k++;
            //    }
            //    return obj;
            //}
            else
            {
                return jsValue.ToObject();
            }
        }
    }
}
