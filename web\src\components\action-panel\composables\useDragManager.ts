import { ref, computed } from 'vue';
import { FlowStep } from '../model';
import { useActionFlowStore } from '../store/index';

// 全局拖拽状态
const isDragging = ref(false);
const draggedItem = ref<FlowStep | null>(null);
const draggedFromParentId = ref<string | null>(null);
const dropZones = ref<Map<string, HTMLElement>>(new Map());
const currentHoveringZone = ref<string | null>(null);

export interface DropZoneInfo {
  parentId?: string;
  position: 'before' | 'inside' | 'after';
  targetItem?: FlowStep;
  element: HTMLElement;
}

export function useDragManager() {
  const actionFlowStore = useActionFlowStore();

  // 开始拖拽
  const startDrag = (item: FlowStep, parentId?: string) => {
    isDragging.value = true;
    draggedItem.value = item;
    draggedFromParentId.value = parentId || null;

    // 不再自动高亮所有区域，只在悬停时高亮
  };

  // 结束拖拽
  const endDrag = () => {
    isDragging.value = false;
    draggedItem.value = null;
    draggedFromParentId.value = null;
    currentHoveringZone.value = null;
  };

  // 设置当前悬停的放置区域
  const setHoveringZone = (zoneId: string | null) => {
    currentHoveringZone.value = zoneId;
  };

  // 检查是否是当前悬停的区域
  const isCurrentHoveringZone = (zoneId: string) => {
    return currentHoveringZone.value === zoneId;
  };

  // 注册放置区域
  const registerDropZone = (id: string, element: HTMLElement) => {
    dropZones.value.set(id, element);
  };

  // 注销放置区域
  const unregisterDropZone = (id: string) => {
    dropZones.value.delete(id);
  };

  // 处理放置
  const handleDrop = (dropInfo: DropZoneInfo) => {
    if (!draggedItem.value) return;

    const sourceItem = draggedItem.value;
    const { targetItem, position, parentId } = dropInfo;

    // 如果是放置到控制块内部
    if (position === 'inside' && targetItem?.controlType) {
      actionFlowStore.moveStep(sourceItem, targetItem, 'inside');
    }
    // 如果是放置到某个项目的前面或后面
    else if (targetItem && (position === 'before' || position === 'after')) {
      actionFlowStore.moveStep(sourceItem, targetItem, position);
    }
    // 如果是放置到空的容器中
    else if (position === 'inside' && parentId) {
      // 将项目移动到指定父容器的内部
      const parentItem = actionFlowStore.flowInfo.body.find((item) => item.id === parentId);
      if (parentItem?.controlType) {
        actionFlowStore.moveStep(sourceItem, parentItem, 'inside');
      }
    }

    endDrag();
  };

  // 检查是否可以放置
  const canDrop = (dropInfo: DropZoneInfo): boolean => {
    if (!draggedItem.value) return false;

    const sourceItem = draggedItem.value;
    const { targetItem, position, parentId } = dropInfo;

    // 不能拖拽到自己
    if (targetItem && sourceItem.id === targetItem.id) return false;

    // 如果是inside位置，目标必须有controlType
    if (position === 'inside' && targetItem && !targetItem.controlType) return false;

    // 不能将父节点拖拽到自己的子节点中
    if (targetItem && isDescendant(sourceItem, targetItem)) return false;

    // 检查是否拖拽到自己的父容器中的相同位置
    if (position === 'inside' && parentId && parentId === draggedFromParentId.value) {
      // 如果是拖拽到同一个父容器内部，允许重新排序
      return true;
    }

    // 检查是否拖拽到自己的直接父节点
    if (targetItem && draggedFromParentId.value === targetItem.id && position === 'inside') {
      return true; // 允许在同一容器内重新排序
    }

    return true;
  };

  // 检查是否是后代节点
  const isDescendant = (ancestor: FlowStep, descendant: FlowStep): boolean => {
    if (!ancestor.controlType) return false;

    const allData = actionFlowStore.flowInfo.body;

    // 获取ancestor的所有子节点ID
    const getAllSubIds = (item: FlowStep): string[] => {
      const list: string[] = [];
      getAllSubIdsByRecursion(item, list);
      return list;
    };

    const getAllSubIdsByRecursion = (item: FlowStep, list: string[]) => {
      list.push(item.id);

      // 获取下一个节点ID
      let nextId: string | null = null;
      if (item.controlType) {
        // 对于控制类型，获取其内部的第一个子节点
        switch (item.controlType) {
          case 'branch':
            nextId = item.control?.branch?.hasData?.nextId;
            break;
          case 'forEach':
            nextId = item.control?.forEach?.nextId;
            break;
          case 'while':
            nextId = item.control?.while?.nextId;
            break;
          case 'try':
            nextId = item.control?.try?.nextId;
            break;
        }
      } else {
        nextId = item.nextId;
      }

      if (nextId) {
        const nextItem = allData.find((t) => t.id === nextId);
        if (nextItem) {
          getAllSubIdsByRecursion(nextItem, list);
        }
      }
    };

    const subIds = getAllSubIds(ancestor);
    return subIds.includes(descendant.id);
  };

  return {
    isDragging: computed(() => isDragging.value),
    draggedItem: computed(() => draggedItem.value),
    currentHoveringZone: computed(() => currentHoveringZone.value),
    startDrag,
    endDrag,
    registerDropZone,
    unregisterDropZone,
    handleDrop,
    canDrop,
    setHoveringZone,
    isCurrentHoveringZone,
  };
}
