using System.Text;
using FluentMigrator.Expressions;
using FluentMigrator.Runner.Generators;
using FluentMigrator.Runner.Generators.Generic;

using Microsoft.Extensions.Options;

namespace GCP.Core.DataAccess.Migrations.Generators.DuckDb
{
    public class DuckDbGenerator : GenericGenerator
    {
        private static readonly HashSet<string> _supportedAdditionalFeatures = new HashSet<string>
        {
        };

        public DuckDbGenerator(
            DuckDbQuoter quoter)
            : this(quoter, new OptionsWrapper<GeneratorOptions>(new GeneratorOptions()))
        {
        }

        public DuckDbGenerator(
            DuckDbQuoter quoter,
            IOptions<GeneratorOptions> generatorOptions)
            : base(new DuckDbColumn(quoter, new DuckDbTypeMap()), quoter, null, generatorOptions)
        {
        }

        protected DuckDbGenerator(
            DuckDbQuoter quoter,
            IOptions<GeneratorOptions> generatorOptions,
            IDuckDbTypeMap typeMap)
            : base(new DuckDbColumn(quoter, typeMap), quoter, null, generatorOptions)
        {
        }

        protected DuckDbGenerator(
            IColumn column,
            DuckDbQuoter quoter,
            IOptions<GeneratorOptions> generatorOptions)
            : base(column, quoter, null, generatorOptions)
        {
        }

        /// <inheritdoc />
        public override bool IsAdditionalFeatureSupported(string feature) =>
            _supportedAdditionalFeatures.Contains(feature)
         || base.IsAdditionalFeatureSupported(feature);

        public override string CreateTable { get { return "CREATE TABLE IF NOT EXISTS {0} ({1})"; } }
        public override string DropTable { get { return "DROP TABLE {0};"; } }

        public override string AddColumn { get { return "ALTER TABLE {0} ADD {1};"; } }
        public override string DropColumn { get { return "ALTER TABLE {0} DROP {1};"; } }
        public override string AlterColumn { get { return "ALTER TABLE {0} {1};"; } }
        public override string RenameColumn { get { return "ALTER TABLE {0} RENAME COLUMN {1} TO {2};"; } }

        public override string GeneratorId => "DuckDB";

        public override List<string> GeneratorIdAliases => ["DuckDB"];

        public override string Generate(AlterTableExpression expression)
        {
            var alterStatement = new StringBuilder();

            alterStatement.Append(base.Generate(expression));

            return alterStatement.ToString();
        }

        public override string Generate(CreateSchemaExpression expression)
        {
            return string.Format("CREATE SCHEMA IF NOT EXISTS {0};", Quoter.QuoteSchemaName(expression.SchemaName));
        }

        public override string Generate(DeleteSchemaExpression expression)
        {
            return string.Format("DROP SCHEMA {0};", Quoter.QuoteSchemaName(expression.SchemaName));
        }

        public override string Generate(CreateTableExpression expression)
        {
            var createStatement = new StringBuilder();
            createStatement.AppendFormat(
                CreateTable,
                Quoter.QuoteTableName(expression.TableName, expression.SchemaName),
                Column.Generate(expression.Columns, Quoter.Quote(expression.TableName)));

            createStatement.Append(";");

            return createStatement.ToString();
        }

        public override string Generate(AlterColumnExpression expression)
        {
            var alterStatement = new StringBuilder();
            alterStatement.AppendFormat(
                AlterColumn,
                Quoter.QuoteTableName(expression.TableName, expression.SchemaName),
                ((DuckDbColumn)Column).GenerateAlterClauses(expression.Column));

            return alterStatement.ToString();
        }

        public override string Generate(CreateColumnExpression expression)
        {
            var createStatement = new StringBuilder();
            createStatement.Append(base.Generate(expression));

            return createStatement.ToString();
        }

        public override string Generate(DeleteColumnExpression expression)
        {
            StringBuilder builder = new StringBuilder();
            foreach (string columnName in expression.ColumnNames)
            {
                if (expression.ColumnNames.First() != columnName) builder.AppendLine("");
                builder.AppendFormat(DropColumn,
                    Quoter.QuoteTableName(expression.TableName, expression.SchemaName),
                    Quoter.QuoteColumnName(columnName));
            }
            return builder.ToString();
        }

        public override string Generate(CreateForeignKeyExpression expression)
        {
            return null;
        }

        public override string Generate(DeleteForeignKeyExpression expression)
        {
            return null;
        }

        public override string Generate(CreateIndexExpression expression)
        {
            var result = new StringBuilder("CREATE");

            if (expression.Index.IsUnique)
            {
                result.Append(" UNIQUE");
            }

            result.AppendFormat(" INDEX IF NOT EXISTS {0} ON {1} (",
                Quoter.QuoteIndexName(expression.Index.Name),
                Quoter.QuoteTableName(expression.Index.TableName, expression.Index.SchemaName));

            var first = true;
            foreach (var column in expression.Index.Columns)
            {
                if (first)
                {
                    first = false;
                }
                else
                {
                    result.Append(",");
                }

                result.Append(Quoter.QuoteColumnName(column.Name));
            }

            result.Append(")")
                .Append(";");

            return result.ToString();
        }

        public override string Generate(DeleteIndexExpression expression)
        {
            var quotedSchema = Quoter.QuoteSchemaName(expression.Index.SchemaName);
            var quotedIndex = Quoter.QuoteIndexName(expression.Index.Name);
            var indexName = string.IsNullOrEmpty(quotedSchema) ? quotedIndex : $"{quotedSchema}.{quotedIndex}";
            return string.Format("DROP INDEX {0};", indexName);
        }

        public override string Generate(DeleteTableExpression expression)
        {
            return
                $"DROP TABLE{(expression.IfExists ? " IF EXISTS" : "")} {Quoter.QuoteTableName(expression.TableName, expression.SchemaName)};";
        }

        public override string Generate(RenameTableExpression expression)
        {
            return string.Format("ALTER TABLE {0} RENAME TO {1};", Quoter.QuoteTableName(expression.OldName, expression.SchemaName), Quoter.Quote(expression.NewName));
        }

        public override string Generate(RenameColumnExpression expression)
        {
            return string.Format(
                RenameColumn,
                Quoter.QuoteTableName(expression.TableName, expression.SchemaName),
                Quoter.QuoteColumnName(expression.OldName),
                Quoter.QuoteColumnName(expression.NewName));
        }

        public override string Generate(InsertDataExpression expression)
        {
            var result = new StringBuilder();
            foreach (var row in expression.Rows)
            {
                var columnNames = new List<string>();
                var columnData = new List<object>();
                foreach (var item in row)
                {
                    columnNames.Add(item.Key);
                    columnData.Add(item.Value);
                }

                var columns = GetColumnList(columnNames);
                var data = GetDataList(columnData);
                result.AppendFormat("INSERT INTO {0} ({1}) VALUES ({2});",
                    Quoter.QuoteTableName(expression.TableName, expression.SchemaName),
                    columns,
                    data);
            }
            return result.ToString();
        }

        public override string Generate(AlterDefaultConstraintExpression expression)
        {
            return null;
        }

        public override string Generate(DeleteDataExpression expression)
        {
            var result = new StringBuilder();

            if (expression.IsAllRows)
            {
                result.AppendFormat("DELETE FROM {0};", Quoter.QuoteTableName(expression.TableName, expression.SchemaName));
            }
            else
            {
                foreach (var row in expression.Rows)
                {
                    var where = string.Empty;
                    var i = 0;

                    foreach (var item in row)
                    {
                        if (i != 0)
                        {
                            where += " AND ";
                        }

                        var op = item.Value == null || item.Value == DBNull.Value ? "IS" : "=";
                        where += string.Format("{0} {1} {2}", Quoter.QuoteColumnName(item.Key), op, Quoter.QuoteValue(item.Value));
                        i++;
                    }

                    result.AppendFormat("DELETE FROM {0} WHERE {1};", Quoter.QuoteTableName(expression.TableName, expression.SchemaName), where);
                }
            }

            return result.ToString();
        }

        public override string Generate(UpdateDataExpression expression)
        {
            var updateItems = new List<string>();
            var whereClauses = new List<string>();

            foreach (var item in expression.Set)
            {
                updateItems.Add(string.Format("{0} = {1}", Quoter.QuoteColumnName(item.Key), Quoter.QuoteValue(item.Value)));
            }

            if (expression.IsAllRows)
            {
                whereClauses.Add("1 = 1");
            }
            else
            {
                foreach (var item in expression.Where)
                {
                    var op = item.Value == null || item.Value == DBNull.Value ? "IS" : "=";
                    whereClauses.Add(string.Format("{0} {1} {2}", Quoter.QuoteColumnName(item.Key),
                                                   op, Quoter.QuoteValue(item.Value)));
                }
            }

            return string.Format(
                "UPDATE {0} SET {1} WHERE {2};",
                Quoter.QuoteTableName(expression.TableName, expression.SchemaName),
                string.Join(", ", updateItems.ToArray()),
                string.Join(" AND ", whereClauses.ToArray()));
        }

        public override string Generate(AlterSchemaExpression expression)
        {
            return null;
        }

        public override string Generate(DeleteDefaultConstraintExpression expression)
        {
            return string.Format("ALTER TABLE {0} ALTER COLUMN {1} DROP DEFAULT;", Quoter.QuoteTableName(expression.TableName, expression.SchemaName), Quoter.Quote(expression.ColumnName));
        }

        public override string Generate(DeleteConstraintExpression expression)
        {
            return string.Format("DROP INDEX {0};", Quoter.Quote(expression.Constraint.ConstraintName));
        }

        public override string Generate(CreateConstraintExpression expression)
        {
            string[] columns = new string[expression.Constraint.Columns.Count];

            for (int i = 0; i < expression.Constraint.Columns.Count; i++)
            {
                columns[i] = Quoter.QuoteColumnName(expression.Constraint.Columns.ElementAt(i));
            }

            if (expression.Constraint.IsPrimaryKeyConstraint)
            {
                    return string.Format(
                        "ALTER TABLE {0} ADD PRIMARY KEY ({1});",
                        Quoter.QuoteTableName(expression.Constraint.TableName, expression.Constraint.SchemaName),
                        string.Join(", ", columns));
            }
            else
            {
                return string.Format(
                    "CREATE UNIQUE INDEX {1} ON {0} ({2});",
                    Quoter.QuoteTableName(expression.Constraint.TableName, expression.Constraint.SchemaName),
                    Quoter.QuoteConstraintName(expression.Constraint.ConstraintName),
                    string.Join(", ", columns));
            }
        }

        protected string GetColumnList(IEnumerable<string> columns)
        {
            var result = "";
            foreach (var column in columns)
            {
                result += Quoter.QuoteColumnName(column) + ",";
            }
            return result.TrimEnd(',');
        }

        protected string GetDataList(List<object> data)
        {
            var result = "";
            foreach (var column in data)
            {
                result += Quoter.QuoteValue(column) + ",";
            }
            return result.TrimEnd(',');
        }

        public override string Generate(CreateSequenceExpression expression)
        {
            var result = new StringBuilder("CREATE SEQUENCE ");
            var seq = expression.Sequence;
            result.AppendFormat(Quoter.QuoteSequenceName(seq.Name, seq.SchemaName));

            if (seq.Increment.HasValue)
            {
                result.AppendFormat(" INCREMENT BY {0}", seq.Increment);
            }

            if (seq.MinValue.HasValue)
            {
                result.AppendFormat(" MINVALUE {0}", seq.MinValue);
            }

            if (seq.MaxValue.HasValue)
            {
                result.AppendFormat(" MAXVALUE {0}", seq.MaxValue);
            }

            if (seq.StartWith.HasValue)
            {
                result.AppendFormat(" START WITH {0}", seq.StartWith);
            }

            if (seq.Cycle)
            {
                result.Append(" CYCLE");
            }

            return string.Format("{0};", result.ToString());
        }

        public override string Generate(DeleteSequenceExpression expression)
        {
            return string.Format("{0};", base.Generate(expression));
        }
    }
}
