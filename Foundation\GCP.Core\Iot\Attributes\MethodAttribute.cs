﻿using GCP.Iot.Models;

namespace GCP.Iot
{
    [AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = false)]
    public class DriverMethodAttribute : Attribute
    {
        public string Name { get;  }        
        public ProtectTypeEnum Protect_Type { get;  }
        public string Description { get;  }

        public DriverMethodAttribute(string name, ProtectTypeEnum protect_Type = ProtectTypeEnum.ReadOnly, string description = "")
        {
            Name = name;
            Protect_Type = protect_Type;
            Description = description;
        }
    }
}
