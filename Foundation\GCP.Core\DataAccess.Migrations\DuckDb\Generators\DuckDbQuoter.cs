using FluentMigrator;
using FluentMigrator.Runner.Generators.Generic;
using GCP.Core.DataAccess.Migrations.Processors.DuckDb;

namespace GCP.Core.DataAccess.Migrations.Generators.DuckDb
{
    public class DuckDbQuoter : GenericQuoter
    {
        public DuckDbQuoter(DuckDbOptions options)
        {
            Options = options ?? new DuckDbOptions();
        }

        public DuckDbOptions Options { get; }

        public override string FormatBool(bool value) { return value ? "true" : "false"; }

        public override string QuoteSchemaName(string schemaName)
        {
            if (string.IsNullOrEmpty(schemaName))
                schemaName = "main";
            return base.QuoteSchemaName(schemaName);
        }

        public override string QuoteSequenceName(string sequenceName, string schemaName)
        {
            return CreateSchemaPrefixedQuotedIdentifier(
                string.IsNullOrEmpty(schemaName) ? string.Empty : Quote(schemaName),
                IsQuoted(sequenceName) ? sequenceName : Quote(sequenceName));
        }

        protected override string FormatByteArray(byte[] array)
        {
            var arrayAsHex = array.Select(b => b.ToString("X2")).ToArray();
            return @"E'\\x" + string.Concat(arrayAsHex) + "'";
        }

        public string UnQuoteSchemaName(string quoted)
        {
            if (string.IsNullOrEmpty(quoted))
                return "main";

            return UnQuote(quoted);
        }

        public override string FormatSystemMethods(SystemMethods value)
        {
            switch (value)
            {
                case SystemMethods.NewGuid:
                    return "uuid()";
                case SystemMethods.NewSequentialId:
                    return "uuid()";
                case SystemMethods.CurrentDateTime:
                    return "now()";
                case SystemMethods.CurrentUTCDateTime:
                    return "(now() at time zone 'UTC')";
                case SystemMethods.CurrentDateTimeOffset:
                    return "current_timestamp";
            }

            return base.FormatSystemMethods(value);
        }
    }
}
