﻿using GCP.Common;
using GCP.FunctionPool.Models;

namespace GCP.FunctionPool.Services
{
    class LicenseService : IFunctionService
    {

        [Function("/gcp/license", "获取许可证数据")]
        public object GetLicenseInfo()
        {
            return new LicenseDTO
            {
                IsActivated = LicenseManager.IsActivated == true,
                RegistrationCode = LicenseManager.GenerateRegistrationCode(),
                ExpireDate = LicenseManager.ActivationData?.ValidTo?.ToString("yyyy-MM-dd"),
            };
        }

        [Function("/gcp/license/activate", "激活许可证")]
        public async Task ActivateLicense(string registrationCode)
        {
            try
            {
                await LicenseManager.ActivateLicense(registrationCode);
            }
            finally
            {
                if(LicenseManager.IsActivated != true)
                {
                    throw new Exception("激活失败，请检查注册码是否正确");
                }
            }
        }
    }
}
