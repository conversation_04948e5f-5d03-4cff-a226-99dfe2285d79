import uniq from 'lodash-es/uniq';
import {
  createRouter,
  createWebHashHistory,
  createWebHistory,
  RouteLocationNormalizedLoaded,
  RouteRecordRaw,
} from 'vue-router';

const env = import.meta.env.MODE || 'development';

// 导入homepage相关固定路由
const homepageModules = import.meta.glob('./modules/**/homepage.ts', { eager: true });

// 导入modules非homepage相关固定路由
const fixedModules = import.meta.glob('./modules/**/!(homepage).ts', { eager: true });

// 其他固定路由
const defaultRouterList: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/pages/login/index.vue'),
  },
  {
    path: '/',
    redirect: '/service/base',
  },
];
// 存放固定路由
export const homepageRouterList: Array<RouteRecordRaw> = mapModuleRouterList(homepageModules);
export const fixedRouterList: Array<RouteRecordRaw> = mapModuleRouterList(fixedModules);

function flattenRoutes(routes: Array<RouteRecordRaw>, parentPath: string[] = []): Array<RouteRecordRaw> {
  const flat: Array<RouteRecordRaw> = [];
  routes.forEach((route) => {
    // 跳过重定向、登录等
    if (route.path === '/' || route.path === '/login') return;
    const currentPath = [...parentPath, route.path.replace(/^\//, '')].filter(Boolean);
    if (route.component && typeof route.component !== 'string') {
      flat.push({
        path: currentPath.join('-'), // 用 - 连接多级路径
        name: route.name ? `blank-${String(route.name)}` : undefined,
        component: route.component,
        meta: { ...(route.meta || {}), layout: 'blank' },
        props: route.props,
        beforeEnter: route.beforeEnter,
      });
    }
    if (route.children && route.children.length > 0) {
      flat.push(...flattenRoutes(route.children, currentPath));
    }
  });
  return flat;
}

// --- 生成并整合空白路由 ---
const routesToWrapInBlank = [...homepageRouterList, ...fixedRouterList];
const blankChildren = flattenRoutes(routesToWrapInBlank);

const blankLayoutRoute: RouteRecordRaw = {
  path: '/blank',
  name: 'blankLayout',
  component: () => import('@/layouts/blank.vue'),
  children: blankChildren,
};

// export const allRoutes = [...homepageRouterList, ...fixedRouterList, ...defaultRouterList];
export const allRoutes = [...homepageRouterList, ...fixedRouterList, ...defaultRouterList, blankLayoutRoute];

// 固定路由模块转换为路由
export function mapModuleRouterList(modules: Record<string, unknown>): Array<RouteRecordRaw> {
  const routerList: Array<RouteRecordRaw> = [];
  Object.keys(modules).forEach((key) => {
    // @ts-ignore
    const mod = modules[key].default || {};
    const modList = Array.isArray(mod) ? [...mod] : [mod];
    routerList.push(...modList);
  });
  return routerList;
}

export const getRoutesExpanded = () => {
  const expandedRoutes: Array<string> = [];

  fixedRouterList.forEach((item) => {
    if (item.meta && item.meta.expanded) {
      expandedRoutes.push(item.path);
    }
    if (item.children && item.children.length > 0) {
      item.children
        .filter((child) => child.meta && child.meta.expanded)
        .forEach((child: RouteRecordRaw) => {
          expandedRoutes.push(item.path);
          expandedRoutes.push(`${item.path}/${child.path}`);
        });
    }
  });
  return uniq(expandedRoutes);
};

export const getActive = (route: RouteLocationNormalizedLoaded | null | undefined, maxLevel = 3): string => {
  if (!route || !route.path) {
    return '';
  }

  return route.path
    .split('/')
    .filter((_item: string, index: number) => index <= maxLevel && index > 0)
    .map((item: string) => `/${item}`)
    .join('');
};

const router = createRouter({
  // history: createWebHistory(env === 'site' ? '/starter/vue-next/' : import.meta.env.VITE_BASE_URL),
  history: createWebHashHistory(env === 'site' ? '/starter/vue-next/' : import.meta.env.VITE_BASE_URL),
  routes: allRoutes,
  scrollBehavior() {
    return {
      el: '#app',
      top: 0,
      behavior: 'smooth',
    };
  },
});

export default router;
