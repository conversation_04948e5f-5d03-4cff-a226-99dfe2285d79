<template>
  <div>
    <single-logs-dialog
      v-model:visible="isShowDetail"
      :is-run="isRun"
      :process-id="currentRow?.id"
      :process-name="currentRow?.functionName"
    ></single-logs-dialog>
    <CmpContainer full>
      <CmpCard>
        <t-tabs :default-value="1">
          <t-tab-panel :value="1" label="历史日志" :destroy-on-hide="false">
            <history-logs @show-detail="onShowDetail" />
          </t-tab-panel>
          <t-tab-panel :value="2" label="运行日志" :destroy-on-hide="false">
            <run-logs @show-detail="onShowDetail" />
          </t-tab-panel>
        </t-tabs>
      </CmpCard>
    </CmpContainer>
  </div>
</template>
<script lang="ts">
export default {
  name: 'SystemLog',
};
</script>
<script setup lang="ts">
import { ref } from 'vue';

import HistoryLogs from './HistoryLogs.vue';
import RunLogs from './RunLogs.vue';
import SingleLogsDialog from './SingleLogsDialog.vue';

defineProps<{
  keyword?: string;
}>();

const isShowDetail = ref(false);
const isRun = ref(false);
const currentRow = ref(null);
const onShowDetail = ({ row, state }) => {
  isRun.value = state === 'error';
  isShowDetail.value = true;
  currentRow.value = row;
};
</script>
<style lang="less" scoped></style>
