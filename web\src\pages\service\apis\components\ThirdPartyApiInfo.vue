<template>
  <div class="api-info">
    <div>
      <t-space v-if="noCurrentApi" direction="vertical" align="center" style="width: 100%; margin-top: 60px">
        <t-empty size="large" title="请选择一个接口" />
      </t-space>
    </div>
    <div v-if="!noCurrentApi">
      <div class="api-action-bar">
        <div class="api-action-bar-breadcrumb">
          <t-breadcrumb>
            <!-- <t-breadcrumb-item>一级目录</t-breadcrumb-item>
          <t-breadcrumb-item>二级目录</t-breadcrumb-item> -->
            <t-breadcrumb-item>
              <div class="api-title">
                {{ currentThirdPartyApi.apiName }}
              </div>
            </t-breadcrumb-item>
          </t-breadcrumb>
        </div>
        <div class="api-action-bar-right">
          <t-popconfirm content="确认删除吗" @confirm="onClickDelete">
            <t-button theme="default">删除</t-button>
          </t-popconfirm>
          <t-button @click="onClickSave">保存</t-button>
        </div>
      </div>
      <div class="api-info-main">
        <div class="api-info-form">
          <t-form>
            <t-space direction="vertical" style="width: 100%">
              <t-row :gutter="[32, 24]">
                <t-col :span="6">
                  <t-form-item label="名称">
                    <t-input v-model="currentThirdPartyApi.apiName" placeholder="请输入接口名称" />
                  </t-form-item>
                </t-col>
                <t-col :span="6">
                  <t-form-item label="服务地址">
                    <CmpDict v-model="currentThirdPartyApi.baseUrl" group-code="API_BASE_URL"></CmpDict>
                  </t-form-item>
                </t-col>
                <t-col :span="12">
                  <t-form-item label="请求">
                    <t-input-adornment>
                      <template #prepend>
                        <t-select
                          v-model="currentThirdPartyApi.requestType"
                          auto-width
                          :options="methodOptions"
                          default-value="GET"
                        ></t-select>
                      </template>
                      <t-input v-model="currentThirdPartyApi.httpUrl" placeholder="请输入API地址，例如/api/sys/user" />
                    </t-input-adornment>
                  </t-form-item>
                </t-col>
                <t-col :span="12">
                  <t-form-item label="标签">
                    <t-select
                      v-model="initData.tagVal"
                      creatable
                      filterable
                      multiple
                      clearable
                      :options="tagOptions"
                      placeholder="请输入标签"
                      @create="handleTagCreate"
                    />
                  </t-form-item>
                </t-col>
                <t-col :span="12">
                  <t-form-item label="描述">
                    <t-textarea
                      v-model="currentThirdPartyApi.description"
                      placeholder="请输入接口描述"
                      :autosize="{ minRows: 2, maxRows: 5 }"
                    />
                  </t-form-item>
                </t-col>
              </t-row>
            </t-space>
          </t-form>
        </div>
      </div>
      <div class="api-query-params">
        <action-form-title title="请求参数"> </action-form-title>
        <t-tabs v-model="activeTab" :default-value="1">
          <t-tab-panel :value="1" label="参数">
            <variable-list v-model:data="initData.params" :show-root-node="false" style="margin: 16px 0" />
          </t-tab-panel>
          <t-tab-panel :value="2" label="请求头">
            <variable-list v-model:data="initData.headers" :show-root-node="false" style="margin: 16px 0" />
          </t-tab-panel>
          <t-tab-panel :value="3" label="请求体">
            <variable-list v-model:data="initData.body" style="margin: 16px 0" />
          </t-tab-panel>
          <t-tab-panel :value="4" label="授权"> </t-tab-panel>
          <!-- <t-tab-panel :value="4" label="授权"> </t-tab-panel>
        <t-tab-panel :value="5" label="中间件"> </t-tab-panel> -->
        </t-tabs>
      </div>
      <div class="api-response">
        <action-form-title title="返回响应">
          <t-button size="small" variant="outline" @click="onManageResponse">
            <template #icon><t-icon name="setting" /></template>
            管理响应体
          </t-button>
        </action-form-title>
        <t-form-item label="响应体配置">
          <t-select
            v-model="currentThirdPartyApi.responseId"
            :options="responseOptions"
            placeholder="选择响应体配置（可选）"
            clearable
            @change="onResponseConfigChange"
          />
        </t-form-item>
        <div v-if="selectedResponseConfig" class="selected-response-info">
          <t-alert theme="info" :message="`已选择响应体: ${selectedResponseConfig.responseName}`" />
        </div>
        <variable-list v-model:data="initData.response" />
      </div>
    </div>
    <value-dialog></value-dialog>
    <variable-batch-dialog></variable-batch-dialog>
    <api-response-dialog v-model="showResponseDialog" :api-id="currentThirdPartyApi?.id" @select="onResponseSelected" />
  </div>
</template>
<script lang="tsx">
export default {
  name: 'ThirdPartyApiInfo',
};
</script>
<script setup lang="tsx">
import { cloneDeep, isEmpty } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { computed, onActivated, onMounted, ref, watch } from 'vue';

import { api, Services } from '@/api/system';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { FlowData, FlowStep } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueDialog from '@/components/action-panel/ValueDialog.vue';
import VariableBatchDialog from '@/components/action-panel/VariableBatchDialog.vue';
import VariableList from '@/components/action-panel/VariableList.vue';

import { useApiStore } from '../store';
import ApiResponseDialog from './ApiResponseDialog.vue';

const actionFlowStore = useActionFlowStore();

const apiStore = useApiStore();
const { currentThirdPartyApi } = storeToRefs(apiStore);
const defaultStep: FlowStep = {
  id: '__default__api_info__',
  name: '接口信息',
  function: 'apiInfo',
};

onMounted(() => {
  // fetchBaseUrlOptions();
  fetchResponseOptions();
});

onActivated(() => {
  actionFlowStore.setCurrentStep(cloneDeep(defaultStep));
});

watch(
  () => currentThirdPartyApi.value?.id,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      actionFlowStore.setCurrentStep(cloneDeep(defaultStep));
      activeTab.value = currentThirdPartyApi.value?.requestType === 'POST' ? 3 : 1;
      initData.value = {
        tagVal: [], // currentThirdPartyApi.value?.tags?.split(',')
        params: JSON.parse(currentThirdPartyApi.value?.queryParameters || '[]'),
        body: JSON.parse(currentThirdPartyApi.value?.body || '[]'),
        headers: JSON.parse(currentThirdPartyApi.value?.headers || '[]'),
        response: [], // JSON.parse(currentThirdPartyApi.value.response)
      };
    }
  },
  { deep: true },
);

const noCurrentApi = computed(() => isEmpty(currentThirdPartyApi.value));

const emits = defineEmits(['save', 'delete']);
const methods = ref(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']);
const methodOptions = computed(() => methods.value.map((value) => ({ label: value, value })));
const baseUrlList = ref([]);
const baseUrlOptions = computed(() => [{ label: '继承', value: '' }, ...baseUrlList.value]);
const tagOptions = ref([]);
const activeTab = ref(1);
const showResponseDialog = ref(false);
const selectedResponseConfig = ref(null);
const responseOptions = ref([]);
const initData = ref<{
  tagVal: string[];
  params: FlowData[];
  body: FlowData[];
  headers: FlowData[];
  response: FlowData[];
}>({
  tagVal: [],
  params: [],
  body: [],
  headers: [],
  response: [],
});

// const fetchBaseUrlOptions = async () => {
//   await api.run(Services.getBaseUrlList).then((data) => {
//     baseUrlList.value = data;
//   });
// };

const handleTagCreate = (value: string) => {
  tagOptions.value.push({ label: value, value });
  initData.value.tagVal.push(value);
};

const onClickSave = () => {
  currentThirdPartyApi.value.queryParameters = JSON.stringify(initData.value.params);
  currentThirdPartyApi.value.body = JSON.stringify(initData.value.body);
  currentThirdPartyApi.value.headers = JSON.stringify(initData.value.headers);
  // currentApi.value.response = JSON.stringify(response.value);
  // currentApi.value.tags = tagVal.value;
  emits('save', currentThirdPartyApi.value);
};

const onClickDelete = () => {
  emits('delete', currentThirdPartyApi.value.id);
};

const onManageResponse = () => {
  showResponseDialog.value = true;
};

const onResponseSelected = (response: any) => {
  selectedResponseConfig.value = response;
  currentThirdPartyApi.value.responseId = response.id;
  // 可以选择是否自动应用响应体结构到当前API
  if (response.responseData) {
    try {
      initData.value.response = JSON.parse(response.responseData);
    } catch (error) {
      console.error('解析响应体数据失败:', error);
    }
  }
};

const onResponseConfigChange = (responseId: string) => {
  if (responseId) {
    // 根据选择的响应体ID查找对应的配置
    const selectedConfig = responseOptions.value.find((option: any) => option.value === responseId);
    if (selectedConfig) {
      selectedResponseConfig.value = selectedConfig;
    }
  } else {
    selectedResponseConfig.value = null;
  }
};

// 加载响应体选项
const fetchResponseOptions = async () => {
  try {
    const data = await api.run(Services.apiGetResponseList);
    responseOptions.value = data.map((item: any) => ({
      label: item.responseName,
      value: item.id,
      ...item,
    }));
  } catch (error) {
    console.error('获取响应体列表失败:', error);
  }
};
</script>
<style lang="less" scoped>
.api-action-bar {
  height: var(--td-comp-size-m);
  margin-bottom: 8px;
  position: relative;

  > div {
    display: inline-block;
  }

  .api-action-bar-breadcrumb {
    position: relative;
    top: 8px;
  }

  .api-action-bar-right {
    float: right;
  }
}

.api-info {
  // height: 500px;
  // overflow-y: scroll;

  .api-info-main {
    margin-top: 16px;

    .api-info-form {
      margin-top: 16px;

      :deep(.t-input-adornment) {
        width: 100%;
      }
    }
  }
}

.api-query-params {
  // margin-top: 16px;
}

.selected-response-info {
  margin-bottom: 16px;
}
</style>
