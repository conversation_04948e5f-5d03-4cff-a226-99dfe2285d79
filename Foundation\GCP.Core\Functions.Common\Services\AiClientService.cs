using GCP.Common;
using GCP.Core.Ai;
using GCP.Functions.Common.Models;
using Microsoft.Extensions.AI;
using ModelContextProtocol.Client;
using OpenAI;
using Serilog;
using System.ClientModel;
using System.Collections.Concurrent;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// AI客户端服务
    /// </summary>
    [Function("aiClient", "AI客户端服务")]
    internal class AiClientService : BaseService
    {
        private readonly ConcurrentDictionary<string, (IChatClient Client, IList<McpClientTool> Tools)> _clientContexts 
            = new ConcurrentDictionary<string, (IChatClient, IList<McpClientTool>)>();
        
        private readonly AiModelConfigService _modelConfigService;
        private IList<McpClientTool> _tools;

        public AiClientService()
        {
            _modelConfigService = new AiModelConfigService();
            InitializeTools();
        }

        private async void InitializeTools()
        {
            try
            {
                // 获取MCP工具
                _tools = await GetMcpTools();
                
                // 初始化所有配置的客户端
                var configs = await _modelConfigService.GetAllModelConfigurations();
                foreach (var config in configs)
                {
                    InitializeClientContext(config);
                }
            }
            catch (Exception ex)
            {
                Log.Error("初始化AI工具失败: {ExMessage}", ex.Message);
            }
        }
        
        [Function("getMcpTools", "获取MCP工具")]
        public static async Task<IList<McpClientTool>> GetMcpTools()
        {
            var tools = await McpService.GetToolsAsync();
            return tools;
        }

        [Function("getClientContext", "获取AI客户端上下文")]
        public async Task<(IChatClient Client, IList<McpClientTool> Tools)> GetClientContext(string modelConfigId)
        {
            if (string.IsNullOrEmpty(modelConfigId))
            {
                // 获取默认配置
                var defaultConfig = await _modelConfigService.GetDefaultModelConfiguration();
                modelConfigId = defaultConfig.Id;
            }

            if (_clientContexts.TryGetValue(modelConfigId, out var context))
            {
                return context;
            }

            // 如果客户端上下文不存在，重新初始化
            var config = await _modelConfigService.GetModelConfiguration(modelConfigId);
            return InitializeClientContext(config);
        }

        private (IChatClient Client, IList<McpClientTool> Tools) InitializeClientContext(ChatModelConfiguration config)
        {
            try
            {
                IChatClient chatClient;

                if (config.Type == "OpenAI")
                {
                    // OpenAI客户端
                    var apiKeyCredential = new ApiKeyCredential(config.ApiKey);
                    var openAIClientOptions = new OpenAIClientOptions
                    {
                        Endpoint = new Uri(config.BaseURL)
                    };

                    var openaiClient = new OpenAIClient(apiKeyCredential, openAIClientOptions);
                    chatClient = openaiClient
                        .AsChatClient(config.ChatModelID)
                        .AsBuilder()
                        .UseFunctionInvocation()
                        .Build();
                }
                else if (config.Type == "Ollama")
                {
                    // Ollama客户端
                    chatClient = new OllamaChatClient(config.BaseURL, config.ChatModelID);
                }
                else
                {
                    throw new NotSupportedException($"不支持的AI模型类型: {config.Type}");
                }

                var context = (chatClient, _tools);
                _clientContexts[config.Id] = context;
                return context;
            }
            catch (Exception ex)
            {
                Log.Error("初始化模型配置 {ConfigName} 失败: {ExMessage}", config.Name, ex.Message);
                throw;
            }
        }

        [Function("refreshClient", "刷新AI客户端")]
        public async Task RefreshClient(string modelConfigId)
        {
            if (string.IsNullOrEmpty(modelConfigId))
                return;

            _clientContexts.TryRemove(modelConfigId, out _);
            var config = await _modelConfigService.GetModelConfiguration(modelConfigId);
            InitializeClientContext(config);
        }

        [Function("trainDatabaseSchema", "训练数据库Schema")]
        public async Task TrainDatabaseSchemaAsync(string dataSourceId)
        {
            await using var db = this.GetDb();
            var dataSource = db.LcDataSources
                .FirstOrDefault(t => t.Id == dataSourceId);

            if (dataSource == null)
            {
                throw new CustomException($"找不到ID为{dataSourceId}的数据源");
            }

            var schemas = new List<string>();
            if (!string.IsNullOrEmpty(dataSource.Database))
            {
                schemas.Add(dataSource.Database);
            }

            // 获取默认模型配置和客户端
            var modelConfig = await _modelConfigService.GetDefaultModelConfiguration();
            var (client, _) = await GetClientContext(modelConfig.Id);

            // 需要实现嵌入生成器
            // 这部分需要单独实现，这里仅作示例
            Log.Information("开始训练数据库Schema: {DataSourceDatabase}", dataSource.Database);
        }
    }
} 