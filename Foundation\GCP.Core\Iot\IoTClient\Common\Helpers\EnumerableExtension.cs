﻿using System;
using System.Collections.Generic;
using System.Text;

namespace IoTClient.Common.Helpers
{
    /// <summary>
    /// 
    /// </summary>
    //public static class EnumerableExtension
    //{
    //    /// <summary>
    //    /// 去重
    //    /// </summary>
    //    /// <typeparam name="TSource"></typeparam>
    //    /// <typeparam name="TKey"></typeparam>
    //    /// <param name="source"></param>
    //    /// <param name="keySelector"></param>
    //    /// <returns></returns>
    //    public static IEnumerable<TSource> DistinctBy<TSource, TKey>(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector)
    //    {
    //        HashSet<TKey> seenKeys = new HashSet<TKey>();
    //        foreach (TSource element in source)
    //        {
    //            if (seenKeys.Add(keySelector(element)))
    //            {
    //                yield return element;
    //            }
    //        }
    //    }
    //}
}
