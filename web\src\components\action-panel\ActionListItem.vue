<template>
  <t-list-item
    :title="props.item.name"
    :class="actionListItemClass"
    :draggable="draggable && !isEndItem"
    @click="handleClick"
    @contextmenu.prevent="handleContextMenu"
    @dragstart="onDragStart"
    @dragend="onDragEnd"
    @dragover.prevent="onDragOver"
    @dragleave="onDragLeave"
    @drop.prevent="onDrop"
    style="cursor: grab"
  >
    <t-list-item-meta :title="props.item.name" :description="props.item.description">
      <template #image>
        <icon :name="itemIcon" />
      </template>
    </t-list-item-meta>
    <template #action>
      <t-tooltip content="删除">
        <t-button
          class="hide-action-btn remove-action-btn"
          shape="square"
          variant="text"
          @click.prevent.stop="onClickDelete"
        >
          <template #icon>
            <minus-circle-filled-icon></minus-circle-filled-icon>
          </template>
        </t-button>
      </t-tooltip>

      <t-tooltip v-if="!draggable" content="移动">
        <t-button class="hide-action-btn action-btn" shape="square" variant="text" @click.prevent="onClickMove">
          <template #icon>
            <move-icon />
          </template>
        </t-button>
      </t-tooltip>

      <t-button v-if="item.controlType" shape="square" variant="text" @click.prevent.stop="onClickCollapse">
        <template #icon>
          <chevron-right-icon :class="collapseIconClass"></chevron-right-icon>
        </template>
      </t-button>
    </template>
  </t-list-item>

  <!-- 控制类型项目的内部放置区域 -->
  <template v-if="item.controlType">
    <t-list-item v-show="!isCollapse" class="action-sub-list">
      <action-list
        :start-id="subStartId"
        :parent-id="item.id"
        :active-id="activeId"
        :draggable="draggable"
      ></action-list>
    </t-list-item>
    <t-list-item v-show="!isCollapse" class="action-list-item action-list-item-end">
      <t-list-item-meta :title="item.config.endStepName">
        <template #image>
          <icon :name="itemIcon" />
        </template>
      </t-list-item-meta>
    </t-list-item>
  </template>
</template>
<script lang="tsx">
export default {
  name: 'ActionListItem',
};
</script>
<script setup lang="tsx">
import ContextMenu from '@imengyu/vue3-context-menu';
import { useClipboard } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import {
  ChevronRightIcon,
  CopyIcon,
  CutIcon,
  Delete1Icon,
  Icon,
  MinusCircleFilledIcon,
  Move1Icon,
  MoveIcon,
  PasteIcon,
} from 'tdesign-icons-vue-next';
import { ListItemMetaProps, MessagePlugin } from 'tdesign-vue-next';
import { computed, ref } from 'vue';
import type { DropZoneInfo } from './composables/useDragManager';

import ActionList from './ActionList.vue';
import { useDragManager } from './composables/useDragManager';
import { FlowStep } from './model';
import { useActionFlowStore } from './store/index';
import { getRandomId, getSubNextId, setNextIdByParent } from './utils';

export interface ActionListItemProps extends Omit<ListItemMetaProps, 'options'> {
  item: FlowStep;
  activeId?: string;
  isCollapse?: boolean;
  draggable?: boolean;
  parentId?: string;
  isEndItem?: boolean;
}

const actionFlowStore = useActionFlowStore();
const { startDrag, endDrag, canDrop, handleDrop, setHoveringZone, isCurrentHoveringZone } = useDragManager();

const itemIcon = computed(() => {
  const config = props.item?.config;
  if (config?.icon) {
    return config?.icon;
  }
  return 'file-unknown';
});
const props = withDefaults(defineProps<ActionListItemProps>(), {
  isCollapse: false,
  draggable: true,
  isEndItem: false,
});

const isCollapse = ref(props.isCollapse);

const collapseIconClass = computed(() => {
  return `icon-arrow${isCollapse.value ? '' : ' icon-arrow-active'}`;
});

const actionListItemClass = computed(() => {
  const classes = ['action-list-item'];

  if (props.item.id === props.activeId) {
    classes.push('action-list-item-active');
  }

  if (isItemHovering.value && dropPosition.value) {
    classes.push('action-list-item-drop-hover');
    classes.push(`action-list-item-drop-${dropPosition.value}`);
  }

  return classes.join(' ');
});

const subStartId = computed(() => {
  let startId = null;
  if (props.item.controlType) {
    startId = getSubNextId(props.item);
  }
  return startId;
});
const emits = defineEmits(['update:isCollapse', 'add-step']);
const handleClick = () => {
  actionFlowStore.setCurrentStep(props.item);
};
const onClickDelete = () => {
  actionFlowStore.removeFlowStep(props.item.id);
  actionFlowStore.setCurrentStep(null);
};
const onClickCollapse = () => {
  isCollapse.value = !isCollapse.value;
  emits('update:isCollapse', isCollapse.value);
};
const onClickMove = () => {
  actionFlowStore.showMoveActionDialog = true;
};

// 拖拽事件处理
const onDragStart = (event: DragEvent) => {
  if (!props.draggable || props.isEndItem) {
    event.preventDefault();
    return;
  }

  startDrag(props.item, props.parentId);

  // 添加拖拽源样式
  const target = event.target as HTMLElement;
  const listItem = target.closest('.action-list-item');
  if (listItem) {
    listItem.classList.add('dragging-source');
  }

  // 设置拖拽数据
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', props.item.id);
    event.dataTransfer.setData('application/json', JSON.stringify(props.item));
  }
};

const onDragEnd = (event: DragEvent) => {
  // 移除拖拽源样式
  const target = event.target as HTMLElement;
  const listItem = target.closest('.action-list-item');
  if (listItem) {
    listItem.classList.remove('dragging-source');
  }

  endDrag();
};

// 列表项拖拽悬停处理
const isItemHovering = ref(false);
const dropPosition = ref<'before' | 'after' | null>(null);

const onDragOver = (event: DragEvent) => {
  if (props.isEndItem) return;

  event.preventDefault();
  event.dataTransfer!.dropEffect = 'move';

  // 计算拖拽位置
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const { clientY } = event;
  const centerY = rect.top + rect.height / 2;

  const position = clientY < centerY ? 'before' : 'after';

  // 检查是否可以放置
  const dropInfo: DropZoneInfo = {
    parentId: props.parentId,
    targetItem: props.item,
    position: position as 'before' | 'after',
    element: event.currentTarget as HTMLElement,
  };

  if (canDrop(dropInfo)) {
    isItemHovering.value = true;
    dropPosition.value = position;
    setHoveringZone(`item-${props.item.id}-${position}`);
  }
};

const onDragLeave = (event: DragEvent) => {
  // 检查是否真的离开了元素
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const { clientX, clientY } = event;

  if (clientX < rect.left || clientX > rect.right || clientY < rect.top || clientY > rect.bottom) {
    isItemHovering.value = false;
    dropPosition.value = null;
    setHoveringZone(null);
  }
};

const onDrop = (event: DragEvent) => {
  if (props.isEndItem || !dropPosition.value) return;

  event.preventDefault();

  const dropInfo: DropZoneInfo = {
    parentId: props.parentId,
    targetItem: props.item,
    position: dropPosition.value as 'before' | 'after',
    element: event.currentTarget as HTMLElement,
  };

  if (canDrop(dropInfo)) {
    handleDrop(dropInfo);
  }

  isItemHovering.value = false;
  dropPosition.value = null;
  setHoveringZone(null);
};

const { isSupported, copy } = useClipboard();

const onClickCopy = () => {
  const sourceText = JSON.stringify(props.item);
  copy(sourceText)
    .then(() => {
      if (!isSupported.value) {
        sessionStorage.setItem('copyText', sourceText);
      }
      MessagePlugin.success(`复制【${props.item.name}】步骤成功`);
    })
    .catch(() => {
      MessagePlugin.error('复制失败');
    });
};

const onClickCut = () => {
  const sourceText = JSON.stringify(props.item);
  copy(sourceText)
    .then(() => {
      if (!isSupported.value) {
        sessionStorage.setItem('copyText', sourceText);
      }
      onClickDelete();
      MessagePlugin.success(`剪切【${props.item.name}】步骤成功`);
    })
    .catch(() => {
      MessagePlugin.error('剪切失败');
    });
};

const { flowInfo } = storeToRefs(actionFlowStore);
const targetStep = computed<FlowStep>(() => {
  return flowInfo.value.body.find((item) => item.id === props.item.id);
});
const onClickPaste = async (position: 'before' | 'inside' | 'after') => {
  const sourceText = isSupported.value ? await navigator.clipboard.readText() : sessionStorage.getItem('copyText');
  let item: FlowStep;
  try {
    item = JSON.parse(sourceText) as FlowStep;
  } catch (e) {
    MessagePlugin.error('粘贴内容格式错误');
    console.error('粘贴内容格式错误: ', sourceText);
    return;
  }

  if (flowInfo.value.body.some((step) => step.id === item.id)) {
    item.id = getRandomId();
  }

  setNextIdByParent(item, '', false);

  emits('add-step', {
    sourceStep: item,
    targetStep: targetStep.value,
    position,
  });
};

const handleContextMenu = (e: MouseEvent) => {
  ContextMenu.showContextMenu({
    theme: 'mac',
    x: e.x,
    y: e.y,
    items: [
      {
        label: '删除',
        icon: <Delete1Icon />,
        onClick: () => {
          onClickDelete();
        },
      },
      {
        label: '复制',
        icon: <CopyIcon />,
        onClick: () => {
          onClickCopy();
        },
      },
      {
        label: '剪切',
        icon: <CutIcon />,
        onClick: () => {
          onClickCut();
        },
      },
      {
        label: '粘贴到前面',
        icon: <PasteIcon />,
        onClick: () => {
          onClickPaste('before');
        },
      },
      ...(props.item.controlType
        ? [
            {
              label: '粘贴到内部',
              icon: <PasteIcon />,
              onClick: () => {
                onClickPaste('inside');
              },
            },
          ]
        : []),
      {
        label: '粘贴到后面',
        icon: <PasteIcon />,
        onClick: () => {
          onClickPaste('after');
        },
      },
      {
        label: '移动',
        icon: <Move1Icon />,
        onClick: () => {
          onClickMove();
        },
      },
    ],
  });
};
</script>
<style lang="less" scoped>
.icon-arrow {
  transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);

  &.icon-arrow-active {
    transform: rotate(90deg);
  }
}

.action-sub-list {
  padding-right: 0 !important;
  padding-left: 30px !important;
  position: relative;
  z-index: 0;
  :deep(> .t-list-item-main > .t-list > .t-list__inner) {
    padding-top: 2px;
  }
}

.action-list-item {
  cursor: grab;
  transition: background 0.3s ease;
  :deep(.t-list-item__meta) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.action-list-item-end {
    cursor: not-allowed;
    color: var(--td-text-color-secondary);
    :deep(.t-list-item__meta-title) {
      color: var(--td-text-color-secondary);
    }
  }

  &:hover:not(.action-list-item-end) {
    background-color: var(--td-bg-color-container-hover);

    :deep(.t-list-item__action) {
      .hide-action-btn {
        display: inline-flex;
      }
    }
  }

  &.action-list-item-active {
    background-color: var(--td-bg-color-container-hover);
    color: var(--td-brand-color);

    :deep(.t-list-item__meta-title) {
      color: var(--td-brand-color);
      font-weight: bold !important;
    }
  }

  // 整行拖拽样式
  &:active {
    cursor: grabbing;
  }

  :deep(.t-list-item__meta-content) {
    .t-list-item__meta-title {
      font-size: 14px;
      margin-bottom: 2px;
      font-weight: normal;
    }

    .t-list-item__meta-description {
      font-size: 12px;
    }

    .t-list-item__meta-title:last-child {
      position: relative;
      top: 7px;
    }
  }

  :deep(.t-list-item__meta-avatar) {
    width: 38px;
    height: 38px;
    position: relative;
    top: 5px;

    &:has(+ .t-list-item__meta-content > .t-list-item__meta-title:last-child) {
      top: 0;
    }

    > .t-icon {
      font-size: 16px;
      position: relative;
      left: 11px;
      top: 6px;
    }
  }

  :deep(.t-list-item__action) {
    .hide-action-btn {
      display: none;
    }
    .remove-action-btn {
      color: var(--td-error-color);
    }
    .action-btn {
      color: #999;
    }
    .drag-handle {
      cursor: grab;
      &:active {
        cursor: grabbing;
      }
    }
  }

  // 拖拽源样式
  &.dragging-source {
    opacity: 0.5;
    background-color: transparent;
    border: 1px dashed var(--td-brand-color);
    border-radius: 4px;
    transform: scale(0.98);
    transition: all 0.15s ease;
    position: relative;
    z-index: 1;
  }

  // 拖拽悬停样式
  &.action-list-item-drop-hover {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      height: 3px;
      background-color: var(--td-brand-color);
      border-radius: 2px;
      z-index: 10;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    &.action-list-item-drop-before::before {
      top: -2px;
    }

    &.action-list-item-drop-after::before {
      bottom: -2px;
    }
  }
}
</style>
