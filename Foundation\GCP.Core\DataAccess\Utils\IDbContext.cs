﻿using System.Data.Common;

namespace GCP.DataAccess
{
    public interface IDbContext
    {
        int CommandTimeout { get; set; }
        string ConnectionString { get; }
        IDbProvider DbProvider { get; }
        DbProviderFactory GetFactory();
        DbConnection CreateConnection(string connectionString = null);
        DbConnection CreateOpenConnection(string connectionString = null);
        Task<DbConnection> CreateOpenConnectionAsync(string connectionString = null);
        void RegisterProvider(string name, IDbProvider dbProvider);
    }
}
