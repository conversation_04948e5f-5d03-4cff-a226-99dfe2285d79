using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.FunctionPool.Flow.Services;
using GCP.Functions.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Collections;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace GCP.Core.Tests.FunctionPool.Flow.Services
{
    public class DataSavePerformanceTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly DbContext _dbContext;
        private readonly FunctionContext _functionContext;
        private readonly ITestOutputHelper _output;

        public DataSavePerformanceTests(ITestOutputHelper output)
        {
            _output = output;
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            
            _serviceProvider = services.BuildServiceProvider();
            
            // 创建测试数据库上下文
            _dbContext = new DbContext();
            _dbContext.SetDefaultConnection("Data Source=test_datasave_performance.db", "SQLite");
            
            // 创建测试表
            CreateTestTable();
            
            // 创建功能上下文
            _functionContext = new FunctionContext
            {
                db = _dbContext,
                globalData = new Dictionary<string, object>(),
                Current = new StepInfo { StepName = "PerformanceTestStep" },
                SqlLog = new TestSqlLog()
            };
        }

        private void CreateTestTable()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS perf_test_table (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    email TEXT,
                    age INTEGER,
                    score REAL,
                    created_at TEXT,
                    updated_at TEXT
                );
                
                DELETE FROM perf_test_table;
            ";
            
            _dbContext.ExecuteNonQuery(sql);
        }

        [Theory]
        [InlineData(100)]
        [InlineData(500)]
        [InlineData(1000)]
        public async Task DataSave_LargeBatch_ShouldPerformWell(int recordCount)
        {
            // Arrange
            var sourceData = GenerateTestData(recordCount);
            
            var dataSaveData = new DataSaveData
            {
                Name = $"PerformanceTest_{recordCount}",
                DataSource = "test-datasource",
                OperateType = DataSaveOperationType.Insert,
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "sourceData" },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "perf_test_table",
                    Columns = new List<ColumnInfo>
                    {
                        new ColumnInfo
                        {
                            ColumnName = "id",
                            DataType = "int",
                            IsCondition = true,
                            IsPrimaryKey = true,
                            IsInsert = true,
                            Required = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.id" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "name",
                            DataType = "string",
                            IsInsert = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "email",
                            DataType = "string",
                            IsInsert = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.email" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "age",
                            DataType = "int",
                            IsInsert = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.age" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "score",
                            DataType = "decimal",
                            IsInsert = true,
                            ColumnValue = new DataValue 
                            { 
                                Type = "script", 
                                ScriptValue = "item.age * 1.5 + Math.random() * 10" 
                            }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "created_at",
                            DataType = "string",
                            IsInsert = true,
                            ColumnValue = new DataValue 
                            { 
                                Type = "script", 
                                ScriptValue = "new Date().toISOString()" 
                            }
                        }
                    }
                }
            };

            _functionContext.globalData["sourceData"] = sourceData;

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await dataSaveService.SaveData(dataSaveData);
            stopwatch.Stop();

            // Assert
            Assert.NotNull(result);
            
            var insertedCount = _dbContext.QuerySingle<int>("SELECT COUNT(*) FROM perf_test_table");
            Assert.Equal(recordCount, insertedCount);
            
            // 性能断言：每1000条记录应该在10秒内完成
            var maxExpectedMs = (recordCount / 1000.0) * 10000;
            
            _output.WriteLine($"处理 {recordCount} 条记录耗时: {stopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"平均每条记录耗时: {(double)stopwatch.ElapsedMilliseconds / recordCount:F2}ms");
            
            Assert.True(stopwatch.ElapsedMilliseconds < maxExpectedMs, 
                $"性能测试失败：处理 {recordCount} 条记录耗时 {stopwatch.ElapsedMilliseconds}ms，超过预期的 {maxExpectedMs}ms");
        }

        [Fact]
        public async Task DataSave_WithCurrentRowScript_ShouldNotHaveMemoryLeak()
        {
            // Arrange - 测试内存使用情况
            var recordCount = 500;
            var sourceData = GenerateTestDataWithUpdate(recordCount);
            
            // 先插入一些基础数据
            await InsertBaseData(recordCount);
            
            var dataSaveData = new DataSaveData
            {
                Name = "MemoryLeakTest",
                DataSource = "test-datasource",
                OperateType = DataSaveOperationType.Update,
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "sourceData" },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "perf_test_table",
                    Columns = new List<ColumnInfo>
                    {
                        new ColumnInfo
                        {
                            ColumnName = "id",
                            DataType = "int",
                            IsCondition = true,
                            IsPrimaryKey = true,
                            Required = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.id" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "name",
                            DataType = "string",
                            IsUpdate = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "email",
                            DataType = "string",
                            IsQuery = true
                        },
                        new ColumnInfo
                        {
                            ColumnName = "age",
                            DataType = "int",
                            IsQuery = true
                        },
                        new ColumnInfo
                        {
                            ColumnName = "updated_at",
                            DataType = "string",
                            IsUpdate = true,
                            ColumnValue = new DataValue 
                            { 
                                Type = "script", 
                                ScriptValue = "currentRow.email + '_' + currentRow.age + '_' + new Date().getTime()" 
                            }
                        }
                    }
                }
            };

            _functionContext.globalData["sourceData"] = sourceData;

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            // Act
            var initialMemory = GC.GetTotalMemory(true);
            
            var result = await dataSaveService.SaveData(dataSaveData);
            
            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;

            // Assert
            Assert.NotNull(result);
            
            _output.WriteLine($"初始内存: {initialMemory / 1024 / 1024:F2}MB");
            _output.WriteLine($"最终内存: {finalMemory / 1024 / 1024:F2}MB");
            _output.WriteLine($"内存增长: {memoryIncrease / 1024 / 1024:F2}MB");
            
            // 内存增长不应该超过50MB（这是一个合理的阈值）
            Assert.True(memoryIncrease < 50 * 1024 * 1024, 
                $"内存使用过多：增长了 {memoryIncrease / 1024 / 1024:F2}MB");
        }

        private ArrayList GenerateTestData(int count)
        {
            var data = new ArrayList();
            for (int i = 1; i <= count; i++)
            {
                data.Add(new Dictionary<string, object>
                {
                    { "id", i },
                    { "name", $"User_{i}" },
                    { "email", $"user{i}@test.com" },
                    { "age", 20 + (i % 50) }
                });
            }
            return data;
        }

        private ArrayList GenerateTestDataWithUpdate(int count)
        {
            var data = new ArrayList();
            for (int i = 1; i <= count; i++)
            {
                data.Add(new Dictionary<string, object>
                {
                    { "id", i },
                    { "name", $"UpdatedUser_{i}" }
                });
            }
            return data;
        }

        private async Task InsertBaseData(int count)
        {
            var sourceData = GenerateTestData(count);
            _functionContext.globalData["sourceData"] = sourceData;

            var dataSaveData = new DataSaveData
            {
                Name = "BaseDataInsert",
                DataSource = "test-datasource",
                OperateType = DataSaveOperationType.Insert,
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "sourceData" },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "perf_test_table",
                    Columns = new List<ColumnInfo>
                    {
                        new ColumnInfo
                        {
                            ColumnName = "id",
                            DataType = "int",
                            IsCondition = true,
                            IsPrimaryKey = true,
                            IsInsert = true,
                            Required = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.id" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "name",
                            DataType = "string",
                            IsInsert = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "email",
                            DataType = "string",
                            IsInsert = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.email" }
                        },
                        new ColumnInfo
                        {
                            ColumnName = "age",
                            DataType = "int",
                            IsInsert = true,
                            ColumnValue = new DataValue { Type = "variable", VariableValue = "item.age" }
                        }
                    }
                }
            };

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            await dataSaveService.SaveData(dataSaveData);
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
            _serviceProvider?.Dispose();
            
            // 清理测试数据库文件
            if (File.Exists("test_datasave_performance.db"))
            {
                File.Delete("test_datasave_performance.db");
            }
        }
    }

    public class TestSqlLog : ISqlLog
    {
        public Task Info(string message, bool isImportant = false) => Task.CompletedTask;
        public Task Warn(string message, bool isImportant = false) => Task.CompletedTask;
        public Task Error(string message, bool isImportant = false) => Task.CompletedTask;
    }
}
