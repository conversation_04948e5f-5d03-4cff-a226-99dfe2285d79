import CmpDict from './cmp-dict/CmpDict.vue';
import CmpCard from './cmp-layout/CmpCard.vue';
import CmpContainer from './cmp-layout/CmpContainer.vue';
import CmpRow from './cmp-layout/CmpRow.vue';

export default {
  install: (app) => {
    // 布局
    app.component('CmpContainer', CmpContainer);
    app.component('CmpRow', CmpRow);
    app.component('CmpCard', CmpCard);

    app.component('CmpDict', CmpDict);
  },
};

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    CmpContainer: typeof CmpContainer;
    CmpRow: typeof CmpRow;
    CmpCard: typeof CmpCard;

    CmpDict: typeof CmpDict;
  }
}
