<template>
  <t-form ref="formRef" :data="formData" :rules="rules" class="mb-4">
    <t-form-item name="eventName" label="事件名称">
      <t-input v-model="formData.eventName" placeholder="请输入事件名称" />
    </t-form-item>

    <t-form-item name="sourceType" label="事件源类型">
      <t-radio-group variant="primary-filled" v-model="formData.sourceType">
        <t-radio-button :value="1">设备</t-radio-button>
        <t-radio-button :value="2">MQTT</t-radio-button>
        <t-radio-button :value="3">RabbitMQ</t-radio-button>
        <t-radio-button :value="4">Kafka</t-radio-button>
        <t-radio-button :value="5">Redis</t-radio-button>
        <t-radio-button :value="6">Tcp</t-radio-button>
        <t-radio-button :value="0">InMemory</t-radio-button>
      </t-radio-group>
    </t-form-item>

    <t-form-item v-if="formData.sourceType === 1" name="eventType" label="事件类型">
      <t-radio-group variant="primary-filled" v-model="formData.eventType">
        <t-radio-button :value="1">测点数据变化</t-radio-button>
        <t-radio-button :value="2">设备数据变化</t-radio-button>
        <t-radio-button :value="3">设备类型数据变化</t-radio-button>
      </t-radio-group>
    </t-form-item>

    <t-form-item name="isEnabled" label="是否启用">
      <t-switch v-model="formData.isEnabled" :custom-value="[1, 0]" />
    </t-form-item>

    <t-form-item name="description" label="事件描述">
      <t-textarea v-model="formData.description" placeholder="请输入事件描述" :autosize="{ minRows: 2, maxRows: 5 }" />
    </t-form-item>

    <div v-if="formData.id">
      <!-- 设备事件 -->
      <event-equipment-form
        ref="equipmentFormRef"
        v-if="formData.sourceType == 1"
        :event-type="formData.eventType"
        :event-id="formData.id"
      />
      <!-- Tcp事件 -->
      <event-tcp-form ref="tcpFormRef" v-if="formData.sourceType == 6" :data="formData" />
    </div>
  </t-form>
</template>

<script lang="ts">
export default {
  name: 'EventForm',
};
</script>

<script setup lang="ts">
import { FormRule } from 'tdesign-vue-next';
import { ref, toRef } from 'vue';

import { LcMessageEvent } from '@/composables/services/messageEvent';
import EventEquipmentForm from './EventEquipmentForm.vue';
import EventTcpForm from './EventTcpForm.vue';

export interface EventFormProps {
  data: LcMessageEvent;
}

const props = defineProps<EventFormProps>();

const formData = toRef(props, 'data');

const rules = {
  eventName: [{ required: true, message: '请输入事件名称', trigger: 'blur' }],
  sourceType: [{ required: true, message: '请选择事件源类型', trigger: 'change' }],
} as Record<string, FormRule[]>;

const formRef = ref();
const equipmentFormRef = ref();
const tcpFormRef = ref();

// 综合验证方法
const validateAllForms = async () => {
  // 验证主表单
  const mainFormResult = await formRef.value?.validate();
  const isMainFormValid = mainFormResult?.validateResult === true;

  let subFormValid = true;
  let subFormErrors = null;

  // 根据事件源类型验证相应的子表单
  if (formData.value.id) {
    if (formData.value.sourceType === 1 && equipmentFormRef.value) {
      // 验证设备事件表单
      const equipmentResult = await equipmentFormRef.value.validate?.();
      subFormValid = equipmentResult?.validateResult === true;
      if (!subFormValid) {
        subFormErrors = equipmentResult?.firstError || '设备事件配置验证失败';
      }
    } else if (formData.value.sourceType === 6 && tcpFormRef.value) {
      // 验证TCP事件表单
      const tcpResult = await tcpFormRef.value.validate?.();
      subFormValid = tcpResult?.validateResult === true;
      if (!subFormValid) {
        subFormErrors = tcpResult?.firstError || 'TCP事件配置验证失败';
      }
    }
    // 可以在这里添加其他事件源类型的验证
    // else if (formData.value.sourceType === 2) { // MQTT
    // else if (formData.value.sourceType === 3) { // RabbitMQ
    // else if (formData.value.sourceType === 4) { // Kafka
    // else if (formData.value.sourceType === 5) { // Redis
  }

  const allValid = isMainFormValid && subFormValid;

  return {
    validateResult: allValid,
    firstError: !isMainFormValid ? mainFormResult?.firstError : !subFormValid ? subFormErrors : null,
  };
};

// 暴露方法
defineExpose({
  validate: validateAllForms,
  formRef,
  equipmentFormRef,
  tcpFormRef,
  getData: () => formData.value,
});
</script>

<style lang="less" scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
