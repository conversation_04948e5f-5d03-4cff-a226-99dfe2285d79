<template>
  <t-row :gutter="[16, 16]">
    <t-col v-for="(item, index) in PANE_LIST" :key="item.title" :xs="6" :xl="3">
      <t-card
        :title="item.title"
        :bordered="false"
        :class="{ 'dashboard-item': true, 'dashboard-item--main-color': index == 0 }"
      >
        <div class="dashboard-item-content">
          <div class="dashboard-item-top">
            <span :style="{ fontSize: `${resizeTime * 28}px` }">{{ item.number }}</span>
          </div>
          <div class="dashboard-item-chart">
            <div :id="item.chartId" class="dashboard-chart-container" :style="{ width: '120px', height: '40px' }"></div>
          </div>
        </div>
        <template #footer>
          <div class="dashboard-item-bottom">
            <div class="dashboard-item-block">
              {{ item.trentName }}
              <trend
                class="dashboard-item-trend"
                :type="item.upTrend ? 'up' : 'down'"
                :is-reverse-color="index === 0"
                :describe="item.upTrend || item.downTrend"
              />
            </div>
            <t-icon name="chevron-right" />
          </div>
        </template>
      </t-card>
    </t-col>
  </t-row>
</template>

<script lang="ts">
export default {
  name: 'TopPanel',
};
</script>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core';
import { BarChart, LineChart } from 'echarts/charts';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { nextTick, onMounted, ref, watch, onUnmounted } from 'vue';

// 导入样式
import Trend from '@/components/trend/index.vue';
import { useSettingStore } from '@/store';
import { changeChartsTheme } from '@/utils/color';
import { useDashboardService, TopPanelStatsBaseItem } from '@/composables/services/dashboard';

const store = useSettingStore();
const resizeTime = ref(1);

const dashboardService = useDashboardService();

const props = defineProps<{
  triggerType: string;
}>();

// 组件顶部
const chartIdMap: Record<string, string> = {
  运行总数: 'serviceCountChart',
  调用次数: 'serviceCallChart',
  '流量统计(KB)': 'serviceFlowChart',
  '平均耗时(ms)': 'serviceResponseChart',
};

interface PaneItemExt extends TopPanelStatsBaseItem {
  chartId: string;
  leftType: string;
  downTrend?: string;
}

const PANE_LIST = ref<PaneItemExt[]>([]);
let timer: number | undefined;

const fetchTopPanel = async () => {
  const res = await dashboardService.getTopPanelStats(props.triggerType);
  if (res.base && Array.isArray(res.base)) {
    PANE_LIST.value = res.base.map((item) => ({
      ...item,
      chartId: chartIdMap[item.title] || '',
      leftType: 'echarts-line',
    }));
  } else {
    PANE_LIST.value = [];
  }
  nextTick(() => {
    renderCharts();
  });
};

echarts.use([LineChart, BarChart, CanvasRenderer]);

// 图表实例容器
const charts: Record<string, echarts.ECharts> = {};

const renderCharts = () => {
  PANE_LIST.value.forEach((item, index) => {
    const container = document.getElementById(item.chartId);
    if (!container) return;

    const chart = echarts.init(container);
    chart.setOption({
      color: [index === 0 ? '#ffffff' : '#0052D9'],
      grid: {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        show: false,
      },
      yAxis: {
        type: 'value',
        show: false,
      },
      series: [
        {
          data: item.chartData,
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
          },
          areaStyle:
            index === 0
              ? undefined
              : {
                  opacity: 0.3,
                },
        },
      ],
    });
    charts[item.chartId] = chart;
  });
};

// chartSize update
const updateContainer = () => {
  if (document.documentElement.clientWidth >= 1400 && document.documentElement.clientWidth < 1920) {
    resizeTime.value = Number((document.documentElement.clientWidth / 2080).toFixed(2));
  } else if (document.documentElement.clientWidth < 1080) {
    resizeTime.value = Number((document.documentElement.clientWidth / 1080).toFixed(2));
  } else {
    resizeTime.value = 1;
  }

  // 更新所有图表大小
  Object.values(charts).forEach((chart) => {
    chart.resize();
  });
};

watch(
  () => props.triggerType,
  () => {
    fetchTopPanel();
  },
);

onMounted(() => {
  fetchTopPanel();
  timer = window.setInterval(fetchTopPanel, 5 * 60 * 1000);
  nextTick(() => {
    updateContainer();
  });
});

const { width, height } = useWindowSize();
watch([width, height], () => {
  updateContainer();
});

watch(
  () => store.brandTheme,
  () => {
    changeChartsTheme(Object.values(charts));
  },
);

watch(
  () => store.mode,
  () => {
    Object.values(charts).forEach((item) => {
      item.dispose();
    });

    renderCharts();
  },
);

onUnmounted(() => {
  if (timer) clearInterval(timer);
});
</script>

<style lang="less" scoped>
.dashboard-item {
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xxl);

  :deep(.t-card__header) {
    padding: 0;
  }

  :deep(.t-card__footer) {
    padding: 0;
  }

  :deep(.t-card__title) {
    font: var(--td-font-body-medium);
    color: var(--td-text-color-secondary);
  }

  :deep(.t-card__body) {
    padding: var(--td-comp-paddingTB-s) 0;
  }

  :deep(.t-card__footer) {
    padding: var(--td-comp-paddingTB-s) 0 0 0;
  }

  &--main-color {
    background: var(--td-brand-color);

    :deep(.t-card__title) {
      color: rgba(255, 255, 255, 0.7);
    }

    .dashboard-item-top span {
      color: #fff;
    }

    .dashboard-item-bottom {
      color: rgba(255, 255, 255, 0.7);
    }

    .dashboard-item-block {
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

.dashboard-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-item-top {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  span {
    font-weight: 500;
    color: var(--td-text-color-primary);
  }
}

.dashboard-item-chart {
  margin-right: -10px;
}

.dashboard-item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--td-text-color-placeholder);
}

.dashboard-item-block {
  display: flex;
  align-items: center;
  font-size: var(--td-font-size-body-small);
}

.dashboard-item-trend {
  margin-left: var(--td-comp-margin-xxs);
}

.dashboard-chart-container {
  position: relative;
}
</style>
