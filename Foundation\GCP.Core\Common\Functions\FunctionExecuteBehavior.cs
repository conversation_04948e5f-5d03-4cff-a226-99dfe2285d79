﻿using System.Runtime.Serialization;

namespace GCP.Common
{
    /// <summary>
    /// 函数执行行为
    /// </summary>
    [DataContract]
    [Serializable]
    public sealed class FunctionExecuteBehavior
    {
        #region 降级
        /// <summary>
        /// 降级函数, 如果相同则使用上一个版本
        /// </summary>
        [DataMember(Name = "fallbackFunction")]
        public string FallbackFunction { get; set; }
        /// <summary>
        /// 降级函数参数, 如果相同函数则不用配置
        /// </summary>
        [DataMember(Name = "fallbackArgs")]
        public Dictionary<string, object> FallbackArgs { get; set; } 
        #endregion

        #region 重试
        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        [DataMember(Name = "timeout")]
        public int? Timeout { get; set; }
        /// <summary>
        /// 重试次数
        /// </summary>
        [DataMember(Name = "retryCount")]
        public int? RetryCount { get; set; }
        /// <summary>
        /// 重试延迟秒数
        /// </summary>
        [DataMember(Name = "retryDelaysInSeconds")]
        public int? RetryDelaysInSeconds { get; set; }
        /// <summary>
        /// 是否使用次方（例如重试5次, 延迟2秒, 如果为true：2,4,8,16,32, 如果为false：2,4,6,8,10。）
        /// true：主要策略使用指数退避, 允许最初快速进行重试, 但随后以逐渐延长的时间间隔进行重试, 高负载情况下给服务一些时间来稳定
        /// false：默认, 固定时间进行重试
        /// </summary>
        [DataMember(Name = "retryUsePower")]
        public bool? RetryUsePower { get; set; } 
        #endregion

        #region 约束每个服务最大并行
        /// <summary>
        /// 最大并行数量
        /// </summary>
        [DataMember(Name = "maxParallelization")]
        public int? MaxParallelization { get; set; }
        /// <summary>
        /// 随时可能正在排队的最大操作数
        /// </summary>
        [DataMember(Name = "maxQueuingActions")]
        public int? MaxQueuingActions { get; set; } 
        #endregion

        #region 全局速率限制（一般按照用户或客户端限制速率）
        /// <summary>
        /// 每个时间跨度允许的执行次数
        /// </summary>
        [DataMember(Name = "rateLimitNumberOfExecutions")]
        public int? RateLimitNumberOfExecutions { get; set; }
        /// <summary>
        /// 允许执行的频率（秒）
        /// </summary>
        [DataMember(Name = "rateLimitPerTimeSpan")]
        public int? RateLimitPerTimeSpan { get; set; }
        /// <summary>
        /// 允许突发最大执行次数
        /// </summary>
        [DataMember(Name = "rateLimitMaxBurst")]
        public int? RateLimitMaxBurst { get; set; } 
        #endregion

        #region 熔断
        /// <summary>
        /// 熔断故障比例
        /// </summary>
        [DataMember(Name = "circuitBreakerFailureThreshold")]
        public double? CircuitBreakerFailureThreshold { get; set; }
        /// <summary>
        /// 熔断持续时间, 之后会恢复（秒）
        /// </summary>
        [DataMember(Name = "circuitBreakerDurationOfBreak")]
        public int? CircuitBreakerDurationOfBreak { get; set; }
        /// <summary>
        /// 滚动时间（秒）
        /// </summary>
        [DataMember(Name = "circuitBreakerSamplingDuration")]
        public int? CircuitBreakerSamplingDuration { get; set; }
        /// <summary>
        /// 最小吞吐量
        /// </summary>
        [DataMember(Name = "circuitBreakerMinimumThroughput")]
        public int? CircuitBreakerMinimumThroughput { get; set; } 
        #endregion
    }
}
