﻿using GCP.Common;
using System.Collections.Concurrent;
using System.Data;
using System.Data.Common;
using System.Diagnostics;
using System.Text;

namespace GCP.DataAccess
{
    public static class DbDebug
    {
        /// <summary>
        /// 是否开启SQL调试(默认false)
        /// </summary>
        public static bool IsOpen { get; set; }
        /// <summary>
        /// 最大记录数量(默认30条)
        /// </summary>
        private static int maxCount;

        public static int MaxCount
        {
            get { return maxCount; }
            set
            {
                SqlQueue = new BlockingCollection<SqlDebug>(value);
                maxCount = value;
            }
        }

        /// <summary>
        /// SQL运行记录
        /// </summary>
        public static List<SqlDebug> SqlHistory
        {
            get
            {
                return SqlQueue.ToList();
            }
        }
        /// <summary>
        /// SQL运行之前调用的方法
        /// </summary>
        public static Func<IDbCommand, Task> SqlExecuteBeforeAction { get; set; }
        public static Func<DbBatchCommand, Task> SqlBatchExecuteBeforeAction { get; set; }
        /// <summary>
        /// SQL运行完成时调用的方法
        /// </summary>
        public static Func<SqlDebug, object, Task> SqlExecuteCompleteAction { get; set; }

        private static BlockingCollection<SqlDebug> SqlQueue { get; set; }

        static DbDebug()
        {
            MaxCount = 30;
        }

        internal static T Run<T>(IDbConnection connection, DbCommand cmd, bool wasClosed, Func<DbCommand, bool, T> function)
        {
            SqlDebug sqlDebug = new SqlDebug(connection, cmd);

            sqlDebug.Start();
            T result = function(cmd, wasClosed);
            sqlDebug.End();

            SqlQueue.Add(sqlDebug);

            if (SqlExecuteCompleteAction != null)
            {
                _ = SqlExecuteCompleteAction.Invoke(sqlDebug, result);
            }
            return result;
        }

        internal static async Task<T> RunAsync<T>(IDbConnection connection, DbCommand cmd, bool wasClosed, Func<DbCommand, bool, Task<T>> function)
        {
            SqlDebug sqlDebug = new SqlDebug(connection, cmd);

            sqlDebug.Start();
            T result = await function(cmd, wasClosed);
            sqlDebug.End();

            SqlQueue.Add(sqlDebug);

            if (SqlExecuteCompleteAction != null) await SqlExecuteCompleteAction.Invoke(sqlDebug, result);
            return result;
        }
    }

    /// <summary>
    /// SQL运行调试信息
    /// </summary>
    public class SqlDebug
    {
        public IDbConnection DbConnection { get; internal set; }
        public IDbCommand DbCommand { get; internal set; }
        public IDbProvider DbProvider { get; internal set; }
        public string ConnectionString
        {
            get
            {
                return this.DbConnection.ConnectionString;
            }
        }
        public string SqlString
        {
            get
            {
                return this.DbCommand.CommandText;
            }
        }

        private IDataParameter[] parameters;
        internal IDataParameter[] Parameters
        {
            get
            {
                if (this.parameters == null)
                {
                    this.parameters = this.DbCommand.Parameters.Cast<IDataParameter>().ToArray();
                }
                return this.parameters;
            }
        }

        public DateTime StartTime { get; internal set; }
        public TimeSpan ExecuteTime { get; internal set; }
        public bool IsAppendExecutableSql { get; set; }

        public string ExecutableSql
        {
            get
            {
                return GetExecutableSql(this.DbProvider, this.SqlString, this.Parameters);
            }
        }

        private Stopwatch stopwatch = new Stopwatch();

        public SqlDebug(IDbConnection connection, IDbCommand cmd)
        {
            this.DbConnection = connection;
            this.DbProvider = connection.GetDbProvider();
            this.DbCommand = cmd;
        }

        public void Start()
        {
            this.StartTime = DateTime.Now;
            this.stopwatch.Start();
        }

        public void End()
        {
            this.stopwatch.Stop();
            this.ExecuteTime = stopwatch.Elapsed;
        }

        internal static string GetExecutableSql(IDbProvider dbProvider, string sql, IDataParameterCollection parms)
        {
            return GetExecutableSql(dbProvider, sql, parms.Cast<IDataParameter>().ToArray());
        }

        internal static string GetExecutableSql(IDbProvider dbProvider, string sql, IEnumerable<IDataParameter> parms)
        {
            if (parms == null || !parms.Any())
            {
                return sql.Trim();
            }

            var parameterNames = new List<string>();
            var parameterValues = new List<string>();

            foreach (var parm in parms)
            {
                string value = "''";
                if (parm.Value != null)
                {
                    value = parm.Value switch
                    {
                        DateTime dateTime => dbProvider.GetTimeFormatStr(dateTime),
                        bool boolValue => boolValue ? "'1'" : "'0'",
                        Enum enumValue => Convert.ToInt32(enumValue).ToString(),
                        _ => $"'{parm.Value.ToString().Replace("'", "''")}'",
                    };
                }

                parameterNames.Add(dbProvider.GetParameterName(parm.ParameterName));
                parameterValues.Add(value);
            }

            var builder = new StringBuilder(sql);
            for (int i = parameterNames.Count - 1; i >= 0; i--)
            {
                builder.Replace(parameterNames[i], parameterValues[i]);
            }

            return builder.ToString().Trim();
        }

        private void FormatSql(StringBuilder sb, string sql, IEnumerable<IDataParameter> parms)
        {
            sb.AppendLine(sql);
            sb.AppendLine();
            if (parms.Any())
            {
                sb.AppendLine("Parameters: ");
                foreach (var item in parms)
                {
                    var parm = item;
                    sb.AppendFormat("\t({1} {2}) {0} = {3}", parm.ParameterName, parm.Direction, parm.DbType, parm.Value);
                    sb.AppendLine();
                }
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (!string.IsNullOrEmpty(this.ConnectionString))
            {
                sb.AppendLine("-------------------------BaseInfo--------------------------------");
                sb.AppendLine("ExecuteTime:      [" + ExecuteTime.FormatString() + "] ");
                sb.AppendLine("StartTime:        [" + StartTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "] ");
                sb.AppendLine("ConnectionString: [" + this.ConnectionString + "] ");
            }

            sb.AppendLine("-------------------------Generated SQL----------------------------");
            FormatSql(sb, this.SqlString, this.Parameters);

            if (IsAppendExecutableSql)
            {
                sb.AppendLine("-------------------------Executable SQL--------------------------");
                sb.AppendLine(GetExecutableSql(this.DbProvider, this.SqlString, this.Parameters));
            }
            sb.AppendLine();
            return sb.ToString();
        }
    }
}
