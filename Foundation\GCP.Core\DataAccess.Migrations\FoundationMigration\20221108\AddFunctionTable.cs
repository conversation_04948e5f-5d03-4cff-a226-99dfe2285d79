﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20221108121801, "初始化函数池表")]
    public class AddFunctionTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_FUNCTION").WithDescription("函数池")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("FUNCTION_NAME").AsAnsiString(200).WithColumnDescription("函数名称")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("FUNCTION_TYPE").AsAnsiString(50).Nullable().WithColumnDescription("函数类型 MIDDLEWARE、FLOW、QUERY、COMMAND")
               .WithColumn("USE_VERSION").AsInt64().WithColumnDescription("使用版本")
               ;

            Create.Table("LC_FUNCTION_CODE").WithDescription("函数代码")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("函数 ID")
               .WithColumn("VERSION").AsInt64().WithColumnDescription("代码版本")
               .WithColumn("CODE_LANGUAGE").AsAnsiString(36).WithColumnDescription("函数代码语言 csharp、javascript、json")
               .WithColumn("CODE").AsAnsiString(int.MaxValue).WithColumnDescription("函数代码")
               ;

            Create.Index("FUNC_CODE_VER_IDX")
                .OnTable("LC_FUNCTION_CODE")
                .OnColumn("FUNCTION_ID").Ascending()
                .OnColumn("VERSION").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();

            Create.Table("LC_FUNCTION_ATTR").WithDescription("函数代码属性")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("函数 ID")
               .WithColumn("ATTR_NAME").AsAnsiString(50).WithColumnDescription("属性名称")
               .WithColumn("ATTR_VALUE").AsAnsiString(200).WithColumnDescription("属性值")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               ;

            Create.Index("FUNCTION_ATTR_ID_IDX")
                .OnTable("LC_FUNCTION_ATTR")
                .OnColumn("FUNCTION_ID").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_FUNCTION");
            Delete.Table("LC_FUNCTION_CODE");
            Delete.Table("LC_FUNCTION_ATTR");
        }
    }
}
