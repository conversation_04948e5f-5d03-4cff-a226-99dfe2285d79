import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowControl, FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfo } from './model';

const control: FlowControl = {
  tryCatch: {
    allowRetry: false,
    retryCount: 3,
    retryDelaysInSeconds: 10,
    nextId: '',
  },
};
export type tryCatchInfo = typeof control.tryCatch;

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      description: '',
    } as ArgsInfo),
    ...(actionFlowStore.currentStep.args || {}),
  }) as ArgsInfo;

  state.tryCatchData = cloneDeep(actionFlowStore.currentStep.control?.tryCatch || control.tryCatch) as tryCatchInfo;
};

export const useTryCatchStore = defineStore('TryCatch', {
  state: () => {
    const state = { args: null, tryCatchData: null } as { args: ArgsInfo; tryCatchData: tryCatchInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs() {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(this.args.name);

      actionFlowStore.setCurrentControl({ tryCatch: this.tryCatchData });
    },
  },
});
