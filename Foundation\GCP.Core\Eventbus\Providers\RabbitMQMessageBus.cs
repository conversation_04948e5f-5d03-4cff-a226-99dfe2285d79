using System.Text.Json;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Serilog;

namespace GCP.Eventbus.Providers
{
    class RabbitMqMessageBus : MessageBusBase
    {
        private IConnection _connection;
        private IChannel _channel;
        private readonly Dictionary<string, string> _consumerTags;
        private readonly SemaphoreSlim _connectionLock = new(1, 1);

        public override bool IsConnected => _connection?.IsOpen == true && _channel?.IsOpen == true;

        public RabbitMqMessageBus(MessageBusOptions options)
            : base(options)
        {
            _consumerTags = new Dictionary<string, string>();
        }

        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (IsConnected) return;

                var uri = new Uri(Options.Settings.GetValueOrDefault("ConnectionString", "amqp://guest:guest@localhost:5672/"));

                var factory = new ConnectionFactory
                {
                    Uri = uri
                };

                _connection = await factory.CreateConnectionAsync(cancellationToken);
                _channel = await _connection.CreateChannelAsync(cancellationToken: cancellationToken);

                Log.Information("Connected to RabbitMQ: {ConnectionString}",
                    $"amqp://{factory.UserName}@{factory.HostName}:{factory.Port}/{factory.VirtualHost}");
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected) return;

                await _channel!.CloseAsync(cancellationToken: cancellationToken);
                await _connection!.CloseAsync(cancellationToken: cancellationToken);

                _channel?.Dispose();
                _channel = null;

                _connection?.Dispose();
                _connection = null;

                Log.Information("Disconnected from RabbitMQ");
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var envelope = new MessageEnvelope
            {
                Payload = message,
            };

            var properties = new BasicProperties();
            if (headers is not null)
            {
                envelope.Headers = headers;
                properties.Headers = headers;
            }
            properties.MessageId = envelope.MessageId;
            properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());

            var body = JsonHelper.SerializeToUtf8Bytes(envelope);

            await _channel!.ExchangeDeclareAsync(topic, ExchangeType.Fanout, true, cancellationToken: cancellationToken);
            await _channel!.BasicPublishAsync(topic, "", mandatory: true, basicProperties: properties, body: body, cancellationToken: cancellationToken);

            Log.Debug("Published message {MessageId} to {Topic}", envelope.MessageId, topic);
            await Task.CompletedTask;
        }

        public override async Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            await _channel!.ExchangeDeclareAsync(topic, ExchangeType.Fanout, true, cancellationToken: cancellationToken);

            var msgArr = messages.ToArray();
            foreach (var message in msgArr)
            {
                var envelope = new MessageEnvelope
                {
                    Payload = message,
                };

                var body = JsonSerializer.SerializeToUtf8Bytes(envelope);
                var properties = new BasicProperties();
                if (headers is not null)
                {
                    envelope.Headers = headers;
                    //properties.Headers = headers;
                }
                properties.MessageId = envelope.MessageId;
                properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());

                await _channel!.BasicPublishAsync(topic, "", mandatory: true, basicProperties: properties, body: body, cancellationToken: cancellationToken);
            }

            Log.Debug("Published {Count} messages to {Topic}", msgArr.Count(), topic);
            await Task.CompletedTask;
        }

        public override async Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var queueName = $"{topic}_{options.Name}";
            await _channel!.ExchangeDeclareAsync(topic, ExchangeType.Fanout, true, cancellationToken: cancellationToken);
            await _channel!.QueueDeclareAsync(queueName, true, false, false, cancellationToken: cancellationToken);
            await _channel!.QueueBindAsync(queueName, topic, "", cancellationToken: cancellationToken);

            var consumer = new AsyncEventingBasicConsumer(_channel);
            consumer.ReceivedAsync += async (_, args) =>
            {
                try
                {
                    var json = System.Text.Encoding.UTF8.GetString(args.Body.Span);
                    var envelope = JsonSerializer.Deserialize<MessageEnvelope>(json);
                    if (envelope != null)
                    {
                        await handler(envelope, cancellationToken).ConfigureAwait(false);
                        await _channel!.BasicAckAsync(args.DeliveryTag, false, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error processing message from {Topic}", topic);
                    await _channel!.BasicNackAsync(args.DeliveryTag, false, true, cancellationToken);
                }
            };

            var consumerTag = await _channel!.BasicConsumeAsync(queueName, false, consumer, cancellationToken: cancellationToken);
            _consumerTags[topic] = consumerTag;

            Log.Information("Subscribed to {Topic}", topic);
        }

        public override async Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (_consumerTags.TryGetValue(topic, out var consumerTag))
            {
                await _channel!.BasicCancelAsync(consumerTag, cancellationToken: cancellationToken);
                _consumerTags.Remove(topic);
                Log.Information("Unsubscribed from {Topic}", topic);
            }
        }

        protected override async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            try
            {
                foreach (var consumerTag in _consumerTags.Values)
                {
                    await _channel!.BasicCancelAsync(consumerTag);
                }
                _consumerTags.Clear();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error disposing consumers");
            }

            await base.DisposeAsyncCore();
        }
    }
}