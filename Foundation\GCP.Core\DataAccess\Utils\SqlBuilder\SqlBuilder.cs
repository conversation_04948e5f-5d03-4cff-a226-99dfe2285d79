﻿using System.Data;
using System.Data.Common;
using System.Collections;
using GCP.Common;

namespace GCP.DataAccess
{
    public enum ConditionOperatorType
    {
        AND,
        OR,
        Equals,
        NotEquals,
        GreaterThan,
        LessThan,
        GreaterThanOrEqual,
        LessThanOrEqual,
        In,
        NotIn,
        Like,
        LeftLike,
        RightLike,
        NotLike,
    }

    public sealed class SqlBuilder<T> : SqlBuilderBase, ISqlBuilder<T>
    {
        /// <summary>
        /// 参数后缀
        /// </summary>
        public string ParamSuffix { get; set; } = "";
        /// <summary>
        /// sql类型 如：SELECT、UPDATE、DELETE、INSERT
        /// </summary>
        public string SqlType { get; set; } = "SELECT";


        public SqlBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public SqlBuilder(DbConnection connection) : base(connection)
        {
        }

        internal SqlBuilder(SqlSession session) : base(session)
        {
        }

        public SqlBuilder<T> Init(string sqlString, object parms = null)
        {
            this.SqlString = sqlString;
            this.AddParameters(parms);
            this.CanExecute = true;
            return this;
        }

        public static SqlBuilder<T> Init(DbConnection connection, string sqlString, object parms = null)
        {
            return new SqlBuilder<T>(connection).Init(sqlString, parms);
        }

        public SqlBuilder<T> Append(string sqlString, params object[] args)
        {
            if (args.Length > 0)
            {
                this.dbParameters.SetParametersByObjects(args, ParamSuffix);
                this.sqlBuilder.AppendFormat(" " + sqlString, this.dbParameters.replaceParmNameMap.Select(t => t.Value).ToArray<object>());
            }
            else
            {
                this.sqlBuilder.Append(" " + sqlString);
            }
            return this;
        }

        private void ReplaceParameter()
        {
            if (this.dbParameters.replaceParmNameMap != null)
            {
                string sqlString = this.sqlBuilder.ToString();
                foreach (var item in this.dbParameters.replaceParmNameMap)
                {
                    sqlString = sqlString.Replace(item.Key, item.Value);
                }
                this.SqlString = sqlString;
            }
        }

        public SqlBuilder<T> AddParameters(object parms)
        {
            this.dbParameters.Add(parms);
            ReplaceParameter();
            return this;
        }

        public SqlBuilder<T> SetParameters(DbParameters parameters)
        {
            this.dbParameters = parameters;
            ReplaceParameter();
            return this;
        }

        public SqlBuilder<T> AddParameters(params IDataParameter[] parms)
        {
            this.dbParameters.Add(parms);
            return this;
        }

        /// <summary>
        /// 开启过滤（开启之后如果查询条件为空, 则不附加到sql语句中）
        /// </summary>
        public SqlBuilder<T> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public SqlBuilder<T> Where(string name, object value)
        {
            return this.Append(" WHERE 1=1").And(name, value);
        }

        public SqlBuilder<T> Where(object whereObj)
        {
            return this.Append(" WHERE 1=1").And(whereObj);
        }

        public SqlBuilder<T> AppendCondition(string name, ConditionOperatorType operatorType, object value)
        {
            var isNull = DbHelper.IsNull(value);
            if (isFilter && isNull) return this;

            switch (operatorType)
            {
                case ConditionOperatorType.Equals:
                    if (isNull)
                    {
                        this.Append(name + " IS NULL");
                    }
                    else
                    {
                        if (value is ICollection list)
                        {
                            this.In(name, list);
                        }
                        else
                        {
                            this.Append(name + " = {0}", value);
                        }
                    }
                    break;
                case ConditionOperatorType.NotEquals:
                    if (isNull)
                    {
                        this.Append(name + " IS NOT NULL");
                    }
                    else
                    {
                        if (value is ICollection list)
                        {
                            this.In(name + " NOT", list);
                        }
                        else
                        {
                            this.Append(name + " <> {0}", value);
                        }
                    }
                    break;
                case ConditionOperatorType.GreaterThan:
                    this.Append(name + " > {0}", value);
                    break;
                case ConditionOperatorType.LessThan:
                    this.Append(name + " > {0}", value);
                    break;
                case ConditionOperatorType.GreaterThanOrEqual:
                    this.Append(name + " >= {0}", value);
                    break;
                case ConditionOperatorType.LessThanOrEqual:
                    this.Append(name + " <= {0}", value);
                    break;
                case ConditionOperatorType.In:
                    this.In(name, value as ICollection);
                    break;
                case ConditionOperatorType.NotIn:
                    this.NotIn(name, value as ICollection);
                    break;
                case ConditionOperatorType.Like:
                    this.Like(name, value, 3);
                    break;
                case ConditionOperatorType.NotLike:
                    this.Like(name + " NOT", value, 3);
                    break;
                case ConditionOperatorType.LeftLike:
                    this.Like(name, value, 1);
                    break;
                case ConditionOperatorType.RightLike:
                    this.Like(name, value, 2);
                    break;
                default:
                    throw new ArgumentException("Invalid operator type: " + operatorType);
            }
            return this;
        }

        /// <summary>
        /// Equal: and name = value
        /// </summary>
        public SqlBuilder<T> And(string name, object value)
        {
            AppendCondition("AND " + name, ConditionOperatorType.Equals, value);
            return this;
        }

        /// <summary>
        /// Equal: and parmName1 = parmValue1 and parmName2 = parmValue3
        /// </summary>
        /// <param name="parms">new {a=b,c=d}</param>
        /// <returns></returns>
        public SqlBuilder<T> And(object parms)
        {
            foreach (var item in EntityCache.GetFields(parms.GetType()))
            {
                this.And(item.Key, item.Value.GetValue(parms));
            }
            return this;
        }

        public SqlBuilder<T> AndId(object whereObj, string idColumnName)
        {
            if (whereObj == null) return this;
            var fields = EntityCache.GetFields(whereObj.GetType());
            if (!string.IsNullOrEmpty(idColumnName) && fields.TryGetValue(idColumnName.ToLower(), out var idField))
            {
                var idObj = idField.GetValue(whereObj);
                if (idObj != null)
                {
                    this.And(idColumnName, idObj);
                    return this;
                }
            }

            return this.And(whereObj);
        }

        private SqlBuilder<T> NotIn(string name, ICollection value)
        {
            if (value == null) return this;
            var arr = new ArrayList(value).ToArray();
            int count = arr.Length;
            if (count == 0)
            {
                this.Append(name + " IS NOT NULL");
                return this;
            }
            return In(name + " NOT", value);
        }

        private SqlBuilder<T> In(string name, ICollection value)
        {
            if (value == null) return this;
            var arr = new ArrayList(value).ToArray();
            int count = arr.Length;
            if (count == 0)
            {
                this.Append(name + " IS NULL");
                return this;
            }

            string str = name + " IN {0}";
            this.Append(str, [arr]);
            //int maxCount = 1000;
            //if (count > maxCount)
            //{
            //    int length = (int)Math.Ceiling((decimal)count / maxCount);
            //    for (int i = 1; i <= length; i++)
            //    {
            //        if (i != 1)
            //        {
            //            this.Append(" OR");
            //        }
            //        else
            //        {
            //            this.Append("(");
            //        }

            //        this.Append(str, new object[] { arr.Skip((i - 1) * maxCount).Take(maxCount) });
            //    }
            //    this.Append(")");
            //}
            //else
            //{
            //    this.Append(str, new object[] { arr });
            //}

            return this;
        }

        /// <summary>
        /// and name in (v1,v2,v3)
        /// </summary>
        public SqlBuilder<T> AndIn(string name, ICollection value)
        {
            var arr = new ArrayList(value).ToArray();
            int count = arr.Length;
            if (this.isFilter && count == 0) return this;
            return In("AND " + name, value);
        }

        public SqlBuilder<T> AndIn(string name, params object[] values)
        {
            return this.AndIn(name, values.ToList());
        }

        /// <summary>
        /// and name not in (v1,v2,v3)
        /// </summary>
        public SqlBuilder<T> AndNotIn(string name, ICollection value)
        {
            return this.AndIn(name + " NOT", value);
        }

        public SqlBuilder<T> AndNotIn(string name, params object[] values)
        {
            return this.AndNotIn(name, values.ToList());
        }

        private SqlBuilder<T> Like(string name, object value, int status)
        {
            if (this.isFilter && DbHelper.IsNull(value)) return this;

            string tmpStr = "";
            if ((status & 1) == 1) tmpStr += "%";
            tmpStr += value;
            if ((status & 2) == 2) tmpStr += "%";

            this.Append(name + " LIKE {0}", tmpStr);
            return this;
        }

        private SqlBuilder<T> AndLike(string name, object value, int status)
        {
            if (this.isFilter && DbHelper.IsNull(value)) return this;

            return Like("AND " + name, value, status);
        }

        /// <summary>
        /// and name like '%' + value + '%'
        /// </summary>
        public SqlBuilder<T> AndLike(string name, object value)
        {
            return this.AndLike(name, value, 3);
        }

        /// <summary>
        /// and name like '%' + value
        /// </summary>
        public SqlBuilder<T> AndLikeL(string name, object value)
        {
            return this.AndLike(name, value, 1);
        }

        /// <summary>
        /// and name like value + '%'
        /// </summary>
        public SqlBuilder<T> AndLikeR(string name, object value)
        {
            return this.AndLike(name, value, 2);
        }

    }
}
