/**
 * 自定义字典
 */
export default class Dictionary<TValue> {
  items: { [key: string]: TValue };

  constructor() {
    this.items = {};
  }

  /**
   * 查找字典中的元素
   * @param key 关键字
   */
  has(key: string): boolean {
    // eslint-disable-next-line no-prototype-builtins
    return this.items.hasOwnProperty(key);
  }

  /**
   * 向字典添加一个元素
   * @param key 关键字
   * @param val 值
   */
  add(key: string, val: TValue): void {
    this.items[key] = val;
  }

  /**
   * 通过关键字删除字典项，返回是否删除成功
   * @param key 关键字
   */
  remove(key: string): boolean {
    if (this.has(key)) {
      delete this.items[key];
      return true;
    }
    return false;
  }

  /**
   * 清空字典
   */
  clear(): void {
    this.items = {};
  }

  /**
   * 查找字典中的元素，没有则是undefined
   * @param key 关键字
   */
  get(key: string): TValue | undefined {
    return this.has(key) ? this.items[key] : undefined;
  }

  /**
   * 返回一个包含所有value数组
   */
  values(): TValue[] {
    const values: TValue[] = [];
    for (const k in this.items) {
      if (this.has(k)) {
        values.push(this.items[k]);
      }
    }
    return values;
  }

  /**
   * 循环字典子元素
   * @param func 入参为key和value
   */
  map(func: (key: string, value: TValue) => void): void {
    for (const key in this.items) {
      if (this.has(key)) {
        const item = this.items[key];
        func(key, item);
      }
    }
  }

  /**
   * 查看字典中元素的个数
   */
  count(): number {
    let num = 0;
    for (const key in this.items) {
      if (this.has(key)) num++;
    }
    return num;
  }
}
