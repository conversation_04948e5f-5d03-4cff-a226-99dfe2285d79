// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	[Table("lc_job_log")]
	public class LcJobLog : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"            , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                           )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"       , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                         )] public DateTime? TimeModified { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                              )] public string?   Modifier     { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                 )] public short     State        { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"   , CanBeNull = false                     )] public string    SolutionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"    , CanBeNull = false                     )] public string    ProjectId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:任务ID
		/// </summary>
		[Column("JOB_ID"        , CanBeNull = false                     )] public string    JobId        { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:任务名称
		/// </summary>
		[Column("JOB_NAME"      , CanBeNull = false                     )] public string    JobName      { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:任务组
		/// </summary>
		[Column("JOB_GROUP"     , CanBeNull = false                     )] public string    JobGroup     { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:请求类型
		/// </summary>
		[Column("REQUEST_TYPE"                                          )] public string?   RequestType  { get; set; } // varchar(50)
		/// <summary>
		/// Description:请求地址
		/// </summary>
		[Column("HTTP_URL"                                              )] public string?   HttpUrl      { get; set; } // varchar(200)
		/// <summary>
		/// Description:请求参数
		/// </summary>
		[Column("HTTP_PARAMS"                                           )] public string?   HttpParams   { get; set; } // varchar(2000)
		/// <summary>
		/// Description:任务结果
		/// </summary>
		[Column("JOB_RESULT"                                            )] public string?   JobResult    { get; set; } // longtext
		/// <summary>
		/// Description:开始时间
		/// </summary>
		[Column("JOB_BEGIN_TIME"                                        )] public DateTime  JobBeginTime { get; set; } // datetime
		/// <summary>
		/// Description:结束时间
		/// </summary>
		[Column("JOB_END_TIME"                                          )] public DateTime? JobEndTime   { get; set; } // datetime
		/// <summary>
		/// Description:状态 0：失败 1：成功
		/// </summary>
		[Column("STATUS"                                                )] public short?    Status       { get; set; } // smallint
	}
}
