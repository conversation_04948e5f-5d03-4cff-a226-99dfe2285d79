﻿using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    /// <summary>
    /// IDbConnection Extension（基础扩展）
    /// </summary>
    public static class DbConnectionAsyncExtension
    {
        
        internal static async Task<T> RunSqlAsync<T>(this DbConnection connection, Func<DbCommand, bool, Task<T>> function)
        {
            var wasClosed = connection.State == ConnectionState.Closed;
            DbCommand cmd = null;
            try
            {
                if (wasClosed) await connection.OpenAsync();
                cmd = connection.CreateCommand();
                cmd.Connection = connection;

                T obj;
                if (DbDebug.IsOpen)
                {
                    obj = await DbDebug.RunAsync<T>(connection, cmd, wasClosed, function).ConfigureAwait(false);
                }
                else
                {
                    obj = await function(cmd, wasClosed).ConfigureAwait(false);
                }

                if (obj is IDataReader)
                {
                    wasClosed = false;
                }
                return obj;
            }
            finally
            {
                if (cmd != null)
                {
                    cmd.Parameters.Clear();
                    cmd.Dispose();
                }
                if (wasClosed) await connection.CloseAsync();
            }
        }


        /// <summary>
        /// 执行非查询SQL语句, 返回受影响行数
        /// </summary>
        public static async Task<int> ExecuteAsync(this DbConnection connection, DbTransaction tran, string sqlString, CommandType cmdType, int commandTimeout, params IDataParameter[] parms)
        {
            return await connection.RunSqlAsync(async (cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = cmdType;
                cmd.CommandTimeout = commandTimeout;
                await cmd.PrepareCommand(sqlString, parms).ConfigureAwait(false);
                return await cmd.ExecuteNonQueryAsync();
            }).ConfigureAwait(false);
        }
        public static async Task<int> ExecuteAsync(this DbConnection connection, string sqlString, CommandType cmdType, int commandTimeout, params IDataParameter[] parms)
        {
            return await ExecuteAsync(connection, null, sqlString, cmdType, commandTimeout, parms).ConfigureAwait(false);
        }
        public static async Task<int> ExecuteAsync(this DbConnection connection, string sqlString, params IDataParameter[] parms)
        {
            return await ExecuteAsync(connection, null, sqlString, CommandType.Text, DbSettings.CommandTimeout, parms).ConfigureAwait(false);
        }
        public static async Task<int> ExecuteAsync(this DbTransaction tran, string sqlString, CommandType cmdType, int commandTimeout, params IDataParameter[] parms)
        {
            return await ExecuteAsync(tran.Connection, tran, sqlString, cmdType, commandTimeout, parms).ConfigureAwait(false);
        }
        public static async Task<int> ExecuteAsync(this DbTransaction tran, string sqlString, params IDataParameter[] parms)
        {
            return await ExecuteAsync(tran.Connection, tran, sqlString, CommandType.Text, DbSettings.CommandTimeout, parms).ConfigureAwait(false);
        }


        /// <summary>
        /// 执行查询, 返回第一行的第一列数据
        /// </summary>
        public static async Task<object> ExecuteScalarAsync(this DbConnection connection, DbTransaction tran, string sqlString, int commandTimeout, params IDataParameter[] parms)
        {
            return await connection.RunSqlAsync(async (cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = CommandType.Text;
                cmd.CommandTimeout = commandTimeout;
                await cmd.PrepareCommand(sqlString, parms).ConfigureAwait(false);
                object result = await cmd.ExecuteScalarAsync();
                return Convert.IsDBNull(result) ? null : result;
            }).ConfigureAwait(false);
        }


        /// <summary>
        /// 执行查询语句, 返回DataReader（使用完DataReader必须Close掉）
        /// </summary>
        public static async Task<DbDataReader> ExecuteReaderAsync(this DbConnection connection, DbTransaction tran, string sqlString, CommandType cmdType = CommandType.Text, CommandBehavior behavior = CommandBehavior.Default, params IDataParameter[] parms)
        {
            return await connection.RunSqlAsync(async (cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = cmdType;
                await cmd.PrepareCommand(sqlString, parms).ConfigureAwait(false);
                return await cmd.ExecuteReaderAsync(wasClosed ? behavior | CommandBehavior.CloseConnection : behavior);
            }).ConfigureAwait(false);
        }
        public static async Task<DbDataReader> ExecuteReaderAsync(this DbConnection connection, string sqlString, params IDataParameter[] parms)
        {
            return await ExecuteReaderAsync(connection, null, sqlString, CommandType.Text, CommandBehavior.Default, parms);
        }
    }
}
