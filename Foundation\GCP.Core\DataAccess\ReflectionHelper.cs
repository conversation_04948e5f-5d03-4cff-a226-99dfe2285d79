﻿using System.Reflection;
using System.Linq.Expressions;
using System.Dynamic;
using System.Collections;
using System.Collections.Concurrent;

namespace GCP.Common
{
    static class EntityCache
    {
        static ConcurrentDictionary<Type, EntityFields> dicEntity = new ConcurrentDictionary<Type, EntityFields>(64, 64);

        public static EntityFields GetFields<T>()
        {
            return GetFields(typeof(T));
        }

        public static EntityFields GetFields(Type type)
        {
            return dicEntity.GetOrAdd(type, CreateFields);
        }

        static EntityFields CreateFields(Type type)
        {
            var propertys = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var fields = new EntityFields(type);
            foreach (var property in propertys)
            {
                if (property.CanRead)
                {
                    var name = property.Name.ToLower();
                    var columnAttr = property.GetCustomAttribute<LinqToDB.Mapping.ColumnAttribute>();
                    if(columnAttr != null)
                    {
                        name = columnAttr.Name.ToLower();
                    }
                    fields.Add(name, new EntityField(property));
                }
            }
            return fields;
        }
    }

    public class EntityFields : Dictionary<string, EntityField>
    {
        Func<object> createInstance;

        public EntityFields(Type type)
        {
            Init(type);
        }

        public EntityFields(Type type, int capacity) : base(capacity)
        {
            Init(type);
        }

        private void Init(Type type)
        {
            //if (type.IsPublic || type.IsNestedPublic)
            this.createInstance = ReflectionHelper.CreateInstanceDelegate(type);
        }

        public T CreateInstance<T>()
        {
            return (T)this.createInstance();
        }

        public object CreateInstance()
        {
            return this.createInstance();
        }

        public void SetValue(string key, object instance, object value)
        {
            EntityField field;
            if (this.TryGetValue(key, out field))
            {
                field.SetValue(instance, value);
            }
        }

        public object GetValue(string key, object instance)
        {
            EntityField field;
            if (this.TryGetValue(key, out field))
            {
                return field.GetValue(instance);
            }
            return null;
        }
    }

    public class EntityField
    {
        public string Name { get; set; }
        public Type UnderlyingType { get; set; }
        public PropertyInfo Property { get; set; }

        internal Func<object, object> getValue;
        internal Action<object, object> setValue;
        public EntityField(PropertyInfo property)
        {
            this.Property = property;
            this.Name = property.Name;
            this.UnderlyingType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
            InitGetValue();
            InitSetValue();
        }

        void InitGetValue()
        {
            var instanceExp = Expression.Parameter(typeof(object), "instance");
            var instanceCast = Expression.Convert(instanceExp, this.Property.ReflectedType);
            this.getValue = Expression.Lambda<Func<object, object>>(Expression.Convert(Expression.Property(instanceCast, this.Property), typeof(object)), instanceExp).Compile();
        }

        void InitSetValue()
        {
            var instanceExp = Expression.Parameter(typeof(object), "instance");
            var valueExp = Expression.Parameter(typeof(object), "value");
            var instanceCast = Expression.Convert(instanceExp, this.Property.ReflectedType);
            var valueCast = Expression.Convert(valueExp, this.Property.PropertyType);
            this.setValue = Expression.Lambda<Action<object, object>>(ExpressionEx.Assign(
                Expression.Property(instanceCast, this.Property), valueCast), instanceExp, valueExp).Compile();
        }

        public void SetValue(object instance, object value)
        {
            this.setValue(instance, ConvertHelper.ChangeType(value, this.UnderlyingType));
        }

        public object GetValue(object instance)
        {
            return this.getValue(instance);
        }
    }

    static class ExpressionEx
    {
        public static Expression Assign(Expression left, Expression right)
        {
            return Expression.Assign(left, right);
            //return Expression.Call(null, assignMethod.MakeGenericMethod(left.Type), left, right);
        }

        //static MethodInfo assignMethod = typeof(ExpressionEx).GetMethod("AssignTo", BindingFlags.NonPublic | BindingFlags.Static);
        //private static void AssignTo<T>(ref T left, T right)
        //{
        //    left = right;
        //}
    }

    public static class ReflectionHelper
    {
        static ConcurrentDictionary<MethodInfo, Func<object, object[], object>> dicMethodInvoke = 
            new ConcurrentDictionary<MethodInfo, Func<object, object[], object>>(128, 128);
        static ConcurrentDictionary<ConstructorInfo, Func<object[], object>> dicInitInvoke = 
            new ConcurrentDictionary<ConstructorInfo, Func<object[], object>>(64, 64);
        static ConcurrentDictionary<Type, Func<object>> dicCreateInstance = 
            new ConcurrentDictionary<Type, Func<object>>(64, 64);

        public static T FastCreateInstance<T>(this Type type)
        {
            return FastCreateInstance(type).Parse<T>();
        }
        public static object FastCreateInstance(this Type type)
        {
            return dicCreateInstance.GetOrAdd(type, CreateInstanceDelegate);
        }

        public static object FastInvoke(this MethodInfo methodInfo, object instance, params object[] parameters)
        {
            var func = dicMethodInvoke.GetOrAdd(methodInfo, CreateMethodInvokeDelegate);
            return func(instance, parameters);
        }
        public static T FastInvoke<T>(this MethodInfo methodInfo, object instance, params object[] parameters)
        {
            return FastInvoke(methodInfo, instance, parameters).Parse<T>();
        }

        public static object FastInvoke(this ConstructorInfo constructorInfo, params object[] parameters)
        {
            var func = dicInitInvoke.GetOrAdd(constructorInfo, CreateInitializeInvoker);
            return func(parameters);
        }

        public static Func<object> CreateInstanceDelegate(Type type)
        {
            return Expression.Lambda<Func<object>>(Expression.New(type)).Compile();
        }

        public static Func<object, object[], object> CreateMethodInvokeDelegate(MethodInfo methodInfo)
        {
            var instanceParameter = Expression.Parameter(typeof(object), "instance");
            var parametersParameter = Expression.Parameter(typeof(object[]), "parameters");

            var parameterExpressions = new List<Expression>();
            var paramInfos = methodInfo.GetParameters();
            for (int i = 0; i < paramInfos.Length; i++)
            {
                BinaryExpression valueObj = Expression.ArrayIndex(
                    parametersParameter, Expression.Constant(i));
                UnaryExpression valueCast = Expression.Convert(
                    valueObj, paramInfos[i].ParameterType);

                parameterExpressions.Add(valueCast);
            }

            var instanceCast = methodInfo.IsStatic ? null :
                Expression.Convert(instanceParameter, methodInfo.ReflectedType);

            var methodCall = Expression.Call(instanceCast, methodInfo, parameterExpressions);

            if (methodCall.Type == typeof(void))
            {
                var lambda = Expression.Lambda<Action<object, object[]>>(
                        methodCall, instanceParameter, parametersParameter);

                Action<object, object[]> execute = lambda.Compile();
                return (instance, parameters) =>
                {
                    execute(instance, parameters);
                    return null;
                };
            }
            else
            {
                var castMethodCall = Expression.Convert(methodCall, typeof(object));
                var lambda = Expression.Lambda<Func<object, object[], object>>(
                    castMethodCall, instanceParameter, parametersParameter);

                return lambda.Compile();
            }
        }

        public static Func<object[], object> CreateInitializeInvoker(ConstructorInfo constructorInfo)
        {
            var parametersParameter = Expression.Parameter(typeof(object[]), "parameters");
            var parameterExpressions = new List<Expression>();
            var paramInfos = constructorInfo.GetParameters();
            for (int i = 0; i < paramInfos.Length; i++)
            {
                var valueObj = Expression.ArrayIndex(parametersParameter, Expression.Constant(i));
                var valueCast = Expression.Convert(valueObj, paramInfos[i].ParameterType);

                parameterExpressions.Add(valueCast);
            }
            var instanceCreate = Expression.New(constructorInfo, parameterExpressions);
            var instanceCreateCast = Expression.Convert(instanceCreate, typeof(object));
            var lambda = Expression.Lambda<Func<object[], object>>(instanceCreateCast, parametersParameter);
            return lambda.Compile();
        }
    }

    public sealed class DynamicDictionary
        : Dictionary<string, object>, IDynamicMetaObjectProvider
    {
        public DynamicDictionary() : base() { }
        public DynamicDictionary(int capacity) : base(capacity) { }

        public DynamicMetaObject GetMetaObject(Expression parameter)
        {
            return new DynamicDictionaryMetaObject(parameter, BindingRestrictions.Empty, this);
        }
    }

    sealed partial class DynamicDictionaryMetaObject : DynamicMetaObject
    {
        static readonly MethodInfo getValueMethod = typeof(IDictionary<string, object>).GetProperty("Item").GetGetMethod();
        static readonly MethodInfo setValueMethod = typeof(DynamicDictionary).GetMethod("SetValue", new Type[] { typeof(string), typeof(object) });

        public DynamicDictionaryMetaObject(Expression expression, BindingRestrictions restrictions) : base(expression, restrictions)
        {
        }

        public DynamicDictionaryMetaObject(Expression expression, BindingRestrictions restrictions, object value) : base(expression, restrictions, value)
        {
        }

        private DynamicMetaObject CallMethod(MethodInfo method, Expression[] parameters)
        {
            return new DynamicMetaObject(Expression.Call(Expression.Convert(Expression, LimitType), method, parameters), BindingRestrictions.GetTypeRestriction(Expression, LimitType));
        }

        public override DynamicMetaObject BindGetMember(GetMemberBinder binder)
        {
            return CallMethod(getValueMethod, new Expression[] { Expression.Constant(binder.Name) });
        }

        public override DynamicMetaObject BindInvokeMember(InvokeMemberBinder binder, DynamicMetaObject[] args)
        {
            return CallMethod(getValueMethod, new Expression[] { Expression.Constant(binder.Name) });
        }

        public override DynamicMetaObject BindSetMember(SetMemberBinder binder, DynamicMetaObject value)
        {
            return CallMethod(setValueMethod, new Expression[] { Expression.Constant(binder.Name), value.Expression });
        }
    }

    public static class Mapper
    {
        private static Dictionary<string, string[]> memberDic = new Dictionary<string, string[]>();
        public static void MemberMap<TSource, TTarget>(Dictionary<string, string> member)
            where TSource : class
            where TTarget : class
        {
            Type sourceType = typeof(TSource);
            Type targetType = typeof(TTarget);
            memberDic[sourceType.FullName + "|" + targetType.FullName] = member.Select(t => t.Key.ToLower() + "|" + t.Value.ToLower()).Distinct().ToArray();
            memberDic[targetType.FullName + "|" + sourceType.FullName] = member.Select(t => t.Value.ToLower() + "|" + t.Key.ToLower()).Distinct().ToArray();
        }

        public static TTarget MapTo<TSource, TTarget>(this TSource source, TTarget target = null)
            where TSource : class
            where TTarget : class
        {
            Type sourceType = typeof(TSource);
            Type targetType = typeof(TTarget);
            var sFields = EntityCache.GetFields(sourceType);
            var tFields = EntityCache.GetFields(targetType);
            TTarget tEntity = target ?? tFields.CreateInstance<TTarget>();
            string dicName = sourceType.FullName + "|" + targetType.FullName;
            string[] dic;
            if (memberDic.TryGetValue(dicName, out dic))
            {
                foreach (var item in sFields)
                {
                    string sFieldName = dic.FirstOrDefault(t => t.StartsWith(item.Key + "|", StringComparison.Ordinal));
                    string tFieldName = string.IsNullOrEmpty(sFieldName) ? item.Key : sFieldName.Substring(sFieldName.IndexOf('|') + 1);
                    tFields.SetValue(tFieldName, tEntity, item.Value.GetValue(source));
                }
            }
            else
            {
                foreach (var item in tFields)
                {
                    EntityField field;
                    if (sFields.TryGetValue(item.Key, out field))
                    {
                        tFields.SetValue(item.Key, tEntity, field.GetValue(source));
                    }
                }
            }

            return tEntity;
        }

        public static object Clone(this object obj, params Type[] igonreTypes)
        {
            if (obj == null) return null;
            var type = obj.GetType();
            if (type.IsPrimitiveType())
            {
                return obj;
            }
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Dictionary<,>))
            {
                var dictionary = (IDictionary)obj;
                var keyType = type.GetGenericArguments()[0];
                var valueType = type.GetGenericArguments()[1];
                var clonedDict = (IDictionary)Activator.CreateInstance(type, [obj]);
                return clonedDict;
            }
            else if (obj is ICollection)
            {
                return Activator.CreateInstance(type, [obj]);
            }
            else if(igonreTypes.Contains(type))
            {
                return obj;
            }
            else
            {
                var fields = EntityCache.GetFields(type);
                var entity = fields.CreateInstance();
                foreach (var item in fields)
                {
                    var field = item.Value;
                    field.SetValue(entity, Clone(field.GetValue(obj), igonreTypes));
                }
                return entity;
            }
        }

        public static IEnumerable<T> CloneArray<T>(this IEnumerable<T> arr, params Type[] igonreTypes)
        {
            IList<T> list = new List<T>();
            var fields = EntityCache.GetFields<T>();
            foreach (var item in arr)
            {
                T entity = (T)fields.CreateInstance();
                foreach (var fieldItem in fields)
                {
                    var field = fieldItem.Value;
                    field.SetValue(entity, Clone(field.GetValue(item), igonreTypes));
                }
                list.Add(entity);
            }
            return list;
        }
    }
}