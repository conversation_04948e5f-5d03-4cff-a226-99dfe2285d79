namespace GCP.Functions.Common.Attributes
{
    /// <summary>
    /// 函数使用示例属性，用于定义函数的使用示例
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public class FunctionExampleAttribute : Attribute
    {
        /// <summary>
        /// 示例标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 示例代码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 预期结果
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 示例描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="title">示例标题</param>
        /// <param name="code">示例代码</param>
        /// <param name="result">预期结果</param>
        /// <param name="description">示例描述</param>
        public FunctionExampleAttribute(string title, string code, string result = null, string description = null)
        {
            Title = title;
            Code = code;
            Result = result;
            Description = description;
        }
    }
}
