using GCP.DataAccess;

namespace GCP.Iot.Models
{
    public class EquipmentVariable : LcIotEquipmentVariable
    {
        public DataTypeEnum DataTypeEnum
        {
            get
            {
                return Enum.Parse<DataTypeEnum>(DataType);
            }
            set
            {
                DataType = value.ToString();
            }
        }
        public object CurrentValue { get; private set; }
        public DateTime LastUpdateTime { get; private set; }
        public DateTime LastArchiveTime { get; private set; }

        public void UpdateValue(object value)
        {
            CurrentValue = value;
            LastUpdateTime = DateTime.Now;
        }

        public void Archive()
        {
            LastArchiveTime = DateTime.Now;
        }
    }
}