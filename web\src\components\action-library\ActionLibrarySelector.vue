<template>
  <div class="action-library-selector">
    <t-select
      v-model="selectedActionLibraryId"
      :options="actionLibraryOptions"
      placeholder="请选择动作库"
      filterable
      :loading="loading"
      @change="onActionLibraryChange"
      @focus="loadActionLibraries"
    >
      <template #option="{ option }">
        <div class="action-library-option">
          <div class="option-header">
            <span class="name">{{ option.label }}</span>
            <t-tag :theme="getStatusTheme(option.data.status)" size="small">
              {{ getStatusText(option.data.status) }}
            </t-tag>
          </div>
          <div class="option-description">{{ option.data.description || '暂无描述' }}</div>
          <div class="option-stats">
            <span class="stat-item">分类: {{ option.data.category || '未分类' }}</span>
            <span class="stat-item">执行次数: {{ option.data.executionCount || 0 }}</span>
            <span class="stat-item">平均耗时: {{ option.data.averageExecutionTime || '-' }}ms</span>
          </div>
        </div>
      </template>
    </t-select>

    <!-- 动作库详情预览 -->
    <div v-if="selectedActionLibrary" class="action-library-preview">
      <t-card title="动作库详情" size="small">
        <t-descriptions :column="2" size="small">
          <t-descriptions-item label="名称">{{ selectedActionLibrary.name }}</t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag :theme="getStatusTheme(selectedActionLibrary.status)">
              {{ getStatusText(selectedActionLibrary.status) }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="分类">{{ selectedActionLibrary.category || '未分类' }}</t-descriptions-item>
          <t-descriptions-item label="版本">v{{ selectedActionLibrary.version }}</t-descriptions-item>
          <t-descriptions-item label="执行次数">{{ selectedActionLibrary.executionCount || 0 }}</t-descriptions-item>
          <t-descriptions-item label="平均耗时">{{ selectedActionLibrary.averageExecutionTime || '-' }}ms</t-descriptions-item>
        </t-descriptions>
        
        <div v-if="selectedActionLibrary.description" class="description">
          <h5>描述</h5>
          <p>{{ selectedActionLibrary.description }}</p>
        </div>

        <!-- 输入参数预览 -->
        <div v-if="inputParameters.length > 0" class="parameters-preview">
          <h5>输入参数</h5>
          <t-table
            :data="inputParameters"
            :columns="parameterColumns"
            size="small"
            :pagination="false"
            max-height="200"
          >
            <template #required="{ row }">
              <t-tag v-if="row.required" theme="danger" size="small">必填</t-tag>
              <t-tag v-else theme="default" size="small">可选</t-tag>
            </template>
          </t-table>
        </div>

        <!-- 输出参数预览 -->
        <div v-if="outputParameters.length > 0" class="parameters-preview">
          <h5>输出参数</h5>
          <t-table
            :data="outputParameters"
            :columns="parameterColumns"
            size="small"
            :pagination="false"
            max-height="200"
          >
            <template #required="{ row }">
              <t-tag v-if="row.required" theme="danger" size="small">必填</t-tag>
              <t-tag v-else theme="default" size="small">可选</t-tag>
            </template>
          </t-table>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <t-space>
            <t-button size="small" theme="default" @click="testActionLibrary" :disabled="!selectedActionLibrary.id">
              测试执行
            </t-button>
            <t-button size="small" theme="default" @click="viewLogs" :disabled="!selectedActionLibrary.id">
              查看日志
            </t-button>
            <t-button size="small" theme="default" @click="editActionLibrary" :disabled="!selectedActionLibrary.id">
              编辑配置
            </t-button>
          </t-space>
        </div>
      </t-card>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibrarySelector',
};
</script>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';

interface ActionLibrary {
  id: string;
  name: string;
  description?: string;
  category?: string;
  status: string;
  version: number;
  executionCount: number;
  averageExecutionTime?: number;
  inputSchemaJson?: string;
  outputSchemaJson?: string;
}

interface Parameter {
  name: string;
  type: string;
  required: boolean;
  description?: string;
}

const props = defineProps<{
  modelValue?: string;
  category?: string;
  status?: string;
  showPreview?: boolean;
}>();

const emits = defineEmits<{
  'update:modelValue': [value: string];
  'change': [actionLibrary: ActionLibrary | null];
  'test': [actionLibrary: ActionLibrary];
  'view-logs': [actionLibrary: ActionLibrary];
  'edit': [actionLibrary: ActionLibrary];
}>();

const selectedActionLibraryId = ref(props.modelValue || '');
const actionLibraries = ref<ActionLibrary[]>([]);
const loading = ref(false);

// 动作库选项
const actionLibraryOptions = computed(() => {
  return actionLibraries.value
    .filter(lib => {
      if (props.category && lib.category !== props.category) return false;
      if (props.status && lib.status !== props.status) return false;
      return true;
    })
    .map(lib => ({
      label: lib.name,
      value: lib.id,
      data: lib,
    }));
});

// 选中的动作库
const selectedActionLibrary = computed(() => {
  return actionLibraries.value.find(lib => lib.id === selectedActionLibraryId.value) || null;
});

// 输入参数
const inputParameters = computed(() => {
  if (!selectedActionLibrary.value?.inputSchemaJson) return [];
  try {
    const schema = JSON.parse(selectedActionLibrary.value.inputSchemaJson);
    return parseParameters(schema);
  } catch {
    return [];
  }
});

// 输出参数
const outputParameters = computed(() => {
  if (!selectedActionLibrary.value?.outputSchemaJson) return [];
  try {
    const schema = JSON.parse(selectedActionLibrary.value.outputSchemaJson);
    return parseParameters(schema);
  } catch {
    return [];
  }
});

// 参数表格列定义
const parameterColumns = [
  { colKey: 'name', title: '参数名', width: 120 },
  { colKey: 'type', title: '类型', width: 80 },
  { colKey: 'required', title: '必填', width: 60 },
  { colKey: 'description', title: '描述', ellipsis: true },
];

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedActionLibraryId.value = newValue || '';
  }
);

// 监听选中值变化
watch(
  () => selectedActionLibraryId.value,
  (newValue) => {
    emits('update:modelValue', newValue);
    emits('change', selectedActionLibrary.value);
  }
);

// 加载动作库列表
const loadActionLibraries = async () => {
  loading.value = true;
  try {
    const params = {
      status: 'active', // 只加载激活状态的动作库
      pageIndex: 1,
      pageSize: 1000, // 加载所有
    };
    
    const data = await api.run(Services.actionLibraryGetAll, params);
    actionLibraries.value = data.data || [];
  } catch (error) {
    MessagePlugin.error(`加载动作库列表失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 解析参数Schema
const parseParameters = (schema: any): Parameter[] => {
  if (!schema || !schema.properties) return [];
  
  const parameters: Parameter[] = [];
  const required = schema.required || [];
  
  for (const [name, prop] of Object.entries(schema.properties)) {
    const propDef = prop as any;
    parameters.push({
      name,
      type: propDef.type || 'string',
      required: required.includes(name),
      description: propDef.description || '',
    });
  }
  
  return parameters;
};

// 动作库变化处理
const onActionLibraryChange = (value: string) => {
  selectedActionLibraryId.value = value;
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  switch (status) {
    case 'active': return 'success';
    case 'inactive': return 'danger';
    case 'draft': return 'warning';
    default: return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '激活';
    case 'inactive': return '停用';
    case 'draft': return '草稿';
    default: return status;
  }
};

// 测试动作库
const testActionLibrary = () => {
  if (selectedActionLibrary.value) {
    emits('test', selectedActionLibrary.value);
  }
};

// 查看日志
const viewLogs = () => {
  if (selectedActionLibrary.value) {
    emits('view-logs', selectedActionLibrary.value);
  }
};

// 编辑动作库
const editActionLibrary = () => {
  if (selectedActionLibrary.value) {
    emits('edit', selectedActionLibrary.value);
  }
};

defineExpose({
  loadActionLibraries,
  selectedActionLibrary,
});
</script>

<style lang="less" scoped>
.action-library-selector {
  .action-library-option {
    padding: 8px 0;

    .option-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;

      .name {
        font-weight: 500;
      }
    }

    .option-description {
      font-size: 12px;
      color: var(--td-text-color-secondary);
      margin-bottom: 4px;
    }

    .option-stats {
      font-size: 11px;
      color: var(--td-text-color-placeholder);

      .stat-item {
        margin-right: 12px;
      }
    }
  }

  .action-library-preview {
    margin-top: 16px;

    .description {
      margin-top: 16px;

      h5 {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
      }

      p {
        margin: 0;
        font-size: 13px;
        color: var(--td-text-color-secondary);
        line-height: 1.5;
      }
    }

    .parameters-preview {
      margin-top: 16px;

      h5 {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .action-buttons {
      margin-top: 16px;
      text-align: right;
    }
  }
}
</style>
