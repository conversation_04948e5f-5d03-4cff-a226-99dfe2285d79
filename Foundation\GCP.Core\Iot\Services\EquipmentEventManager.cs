using System.Collections.Concurrent;
using GCP.Iot.Models;

namespace GCP.Iot.Services
{
    class EquipmentEventManager
    {
        private readonly ConcurrentDictionary<string, EventHandler<VariableValueChangedEventArgs>> _variableHandlers = new();
        private readonly ConcurrentDictionary<string, EventHandler<EquipmentDataChangedEventArgs>> _equipmentHandlers = new();
        private readonly ConcurrentDictionary<string, EventHandler<EquipmentDataChangedEventArgs>> _equipmentTypeHandlers = new();

        public void AddVariableHandler(string equipmentId, string variableName, EventHandler<VariableValueChangedEventArgs> handler)
        {
            var key = $"{equipmentId}:{variableName}";

            // 检查是否已存在相同的处理器，避免重复添加
            if (_variableHandlers.TryGetValue(key, out var existing))
            {
                // 检查处理器是否已经包含在委托链中
                var delegates = existing.GetInvocationList();
                if (delegates.Contains(handler))
                {
                    return; // 已存在，不重复添加
                }
            }

            _variableHandlers.AddOrUpdate(key, handler, (_, existingHandler) => existingHandler + handler);
        }

        public void RemoveVariableHandler(string equipmentId, string variableName, EventHandler<VariableValueChangedEventArgs> handler)
        {
            var key = $"{equipmentId}:{variableName}";
            if (_variableHandlers.TryGetValue(key, out var existing))
            {
                existing -= handler;
                if (existing == null)
                {
                    _variableHandlers.TryRemove(key, out _);
                }
                else
                {
                    _variableHandlers[key] = existing;
                }
            }
        }

        public void AddEquipmentHandler(string equipmentId, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            // 检查是否已存在相同的处理器，避免重复添加
            if (_equipmentHandlers.TryGetValue(equipmentId, out var existing))
            {
                // 检查处理器是否已经包含在委托链中
                var delegates = existing.GetInvocationList();
                if (delegates.Contains(handler))
                {
                    return; // 已存在，不重复添加
                }
            }

            _equipmentHandlers.AddOrUpdate(equipmentId, handler, (_, existingHandler) => existingHandler + handler);
        }

        public void RemoveEquipmentHandler(string equipmentId, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            if (_equipmentHandlers.TryGetValue(equipmentId, out var existing))
            {
                existing -= handler;
                if (existing == null)
                {
                    _equipmentHandlers.TryRemove(equipmentId, out _);
                }
                else
                {
                    _equipmentHandlers[equipmentId] = existing;
                }
            }
        }

        public void AddEquipmentTypeHandler(string equipmentType, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            // 检查是否已存在相同的处理器，避免重复添加
            if (_equipmentTypeHandlers.TryGetValue(equipmentType, out var existing))
            {
                // 检查处理器是否已经包含在委托链中
                var delegates = existing.GetInvocationList();
                if (delegates.Contains(handler))
                {
                    return; // 已存在，不重复添加
                }
            }

            _equipmentTypeHandlers.AddOrUpdate(equipmentType, handler, (_, existingHandler) => existingHandler + handler);
        }

        public void RemoveEquipmentTypeHandler(string equipmentType, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            if (_equipmentTypeHandlers.TryGetValue(equipmentType, out var existing))
            {
                existing -= handler;
                if (existing == null)
                {
                    _equipmentTypeHandlers.TryRemove(equipmentType, out _);
                }
                else
                {
                    _equipmentTypeHandlers[equipmentType] = existing;
                }
            }
        }

        internal void HandleVariableValueChanged(string equipmentId, string variableName, object oldValue, object newValue)
        {
            var key = $"{equipmentId}:{variableName}";
            if (_variableHandlers.TryGetValue(key, out var handler))
            {
                handler?.Invoke(this, new VariableValueChangedEventArgs(equipmentId, variableName, oldValue, newValue));
            }
        }

        internal void HandleEquipmentDataChanged(string equipmentId, string equipmentType, Dictionary<string, object> values)
        {
            var args = new EquipmentDataChangedEventArgs(equipmentId, equipmentType, values);

            // 触发设备级别事件
            if (_equipmentHandlers.TryGetValue(equipmentId, out var equipmentHandler))
            {
                equipmentHandler?.Invoke(this, args);
            }

            // 触发设备类型级别事件
            if (_equipmentTypeHandlers.TryGetValue(equipmentType, out var typeHandler))
            {
                typeHandler?.Invoke(this, args);
            }
        }

        /// <summary>
        /// 清理指定设备的所有事件处理器
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        public void ClearEquipmentHandlers(string equipmentId)
        {
            // 清理设备级别的处理器
            _equipmentHandlers.TryRemove(equipmentId, out _);

            // 清理变量级别的处理器
            var variableKeysToRemove = _variableHandlers.Keys
                .Where(key => key.StartsWith($"{equipmentId}:"))
                .ToList();

            foreach (var key in variableKeysToRemove)
            {
                _variableHandlers.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// 清理指定设备类型的所有事件处理器
        /// </summary>
        /// <param name="equipmentType">设备类型</param>
        public void ClearEquipmentTypeHandlers(string equipmentType)
        {
            _equipmentTypeHandlers.TryRemove(equipmentType, out _);
        }
    }
}