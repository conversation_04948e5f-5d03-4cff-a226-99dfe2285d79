﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20221108121800, "初始化解决方案、项目表")]
    public class AddSolutionTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_SOLUTION").WithDescription("解决方案")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")

               .WithColumn("SOLUTION_NAME").AsAnsiString(200).WithColumnDescription("解决方案名称")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("ICON").AsAnsiString(200).Nullable().WithColumnDescription("图标")
               .WithColumn("DATA_SOURCE_ID").AsAnsiString(36).Nullable().WithColumnDescription("默认数据源 ID")
               ;

            Create.Table("LC_PROJECT").WithDescription("项目")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")

               .WithColumn("PROJECT_NAME").AsAnsiString(200).WithColumnDescription("项目名称")
               .WithColumn("PROJECT_TYPE").AsAnsiString(50).Nullable().WithColumnDescription("项目类型")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("SEQ").AsInt16().WithColumnDescription("序号")
               .WithColumn("ICON").AsAnsiString(200).Nullable().WithColumnDescription("图标")
               .WithColumn("DATA_SOURCE_ID").AsAnsiString(36).Nullable().WithColumnDescription("默认数据源 ID")
               ;

            Create.Table("LC_DIR_TREE").WithDescription("目录树")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")

               .WithColumn("PARENT_ID").AsAnsiString(36).WithColumnDescription("父目录 ID ROOT:根目录")
               .WithColumn("DIR_CODE").AsAnsiString(50).Nullable().WithColumnDescription("目录编码")
               .WithColumn("DIR_TYPE").AsFixedLengthAnsiString(2).WithColumnDescription("目录类型 P:项目子目录 A:API分类目录 F:函数分类目录 D:字典分类目录")
               .WithColumn("DIR_NAME").AsAnsiString(200).WithColumnDescription("目录名称")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("SEQ").AsInt16().WithColumnDescription("序号")
               .WithColumn("ICON").AsAnsiString(200).Nullable().WithColumnDescription("图标")
               .WithColumn("IS_LEAF").AsAnsiString(1).Nullable().WithColumnDescription("是否叶子结点 Y:是")
               .WithColumn("LEAF_ID").AsAnsiString(36).Nullable().WithColumnDescription("叶子结点对应数据ID")
               ;

            Create.Index("TREE_GROUP_IDX")
             .OnTable("LC_DIR_TREE")
             .OnColumn("PARENT_ID").Ascending()
             .OnColumn("DIR_CODE").Ascending()
             .OnColumn("SOLUTION_ID").Ascending()
             .WithOptions().Unique();
        }

        public override void Down()
        {
            Delete.Table("LC_SOLUTION");
            Delete.Table("LC_PROJECT");
            Delete.Table("LC_DIR_TREE");
        }
    }
}
