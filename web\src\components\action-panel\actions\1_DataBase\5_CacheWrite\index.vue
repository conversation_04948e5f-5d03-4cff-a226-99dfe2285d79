<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="150px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="缓存键" prop="cacheKey">
              <value-input
                v-model:data-value="formData.cacheKey"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
                placeholder="请输入缓存键"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="缓存值" prop="cacheValue">
              <value-input
                v-model:data-value="formData.cacheValue"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
                placeholder="请输入缓存值"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="过期时间（秒）" prop="expirationSeconds">
              <value-input
                v-model:data-value="formData.expirationSeconds"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
                placeholder="请输入过期时间，为空则永不过期"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'CacheWriteActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { watch } from 'vue';

import ValueInput from '@/components/action-panel/ValueInput.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { useCacheWriteStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataStore = useCacheWriteStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataStore.updateState();
  },
  {
    immediate: true,
  },
);
const { args: formData } = storeToRefs(dataStore);

watch(
  () => formData.value,
  (newValue) => {
    dataStore.setArgs(newValue);
  },
  {
    deep: true,
  },
);
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}
</style>
