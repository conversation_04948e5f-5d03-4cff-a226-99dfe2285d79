﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20241204001000, "添加发布配置定义")]
    public class AddPublish : Migration
    {
        public override void Up()
        {
            Create.Table("LC_PUBLISH").WithDescription("系统发布配置")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("ENVIRONMENT_NAME").AsAnsiString(255).WithColumnDescription("环境名称")
               .WithColumn("ENVIRONMENT_TYPE").AsAnsiString(50).WithColumnDescription("类型（正式环境、其他）")
               .WithColumn("DESCRIPTION").AsString().Nullable().WithColumnDescription("描述")
               .WithColumn("TAG").AsAnsiString(255).Nullable().WithColumnDescription("标签")
               .WithColumn("TAG_COLOR").AsAnsiString(50).Nullable().WithColumnDescription("标签颜色")
               .WithColumn("SERVICE_ADDRESS").AsString().Nullable().WithColumnDescription("服务地址")
               .WithColumn("SERVICE_ID").AsAnsiString(255).Nullable().WithColumnDescription("服务ID")
               .WithColumn("SERVICE_SECRET").AsString().Nullable().WithColumnDescription("服务Secret")
               ;

            Create.Index("LC_PUBLISH_IDX")
                .OnTable("LC_PUBLISH")
                .OnColumn("ENVIRONMENT_NAME").Ascending()
                .WithOptions().Unique();
        }

        public override void Down()
        {
            Delete.Table("LC_PUBLISH");
        }
    }
}
