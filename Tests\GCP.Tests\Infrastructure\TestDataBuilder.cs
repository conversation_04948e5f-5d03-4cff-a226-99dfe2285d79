using GCP.DataAccess;
using GCP.Common;

namespace GCP.Tests.Infrastructure
{
    /// <summary>
    /// 测试数据构建器
    /// </summary>
    public static class TestDataBuilder
    {
        /// <summary>
        /// 创建测试API定义
        /// </summary>
        public static LcApi CreateTestApi(string? apiName = null, string? solutionId = null, string? projectId = null)
        {
            return new LcApi
            {
                Id = TUID.NewTUID().ToString(),
                ApiName = apiName ?? "测试API",
                Description = "测试用API",
                HttpUrl = "/test/api",
                RequestType = "POST",
                ApiType = *,
                State = *,
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试数据源
        /// </summary>
        public static LcDataSource CreateTestDataSource(string? dataSourceName = null, string? solutionId = null, string? projectId = null)
        {
            return new LcDataSource
            {
                Id = TUID.NewTUID().ToString(),
                DataSourceName = dataSourceName ?? "测试数据源",
                Description = "测试用数据源",
                DataProvider = "MySql",
                ServerAddress = "localhost",
                Port = *3306,
                Database = "gcp_test",
                UserId = "root",
                Password = "*",
                ConnectionString = "Server=localhost;Database=gcp_test;User=root;Password=*;Charset=utf8;Convert Zero Datetime=True;Port=*3306;SslMode=None;Allow User Variables=True;AllowLoadLocalInfile=true;AllowPublicKeyRetrieval=True",
                State = *,
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试消息事件
        /// </summary>
        public static LcMessageEvent CreateTestMessageEvent(string? eventName = null, string? solutionId = null, string? projectId = null)
        {
            return new LcMessageEvent
            {
                Id = TUID.NewTUID().ToString(),
                EventName = eventName ?? "测试事件",
                Description = "测试用消息事件",
                EventType = *,
                State = *,
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试IoT设备
        /// </summary>
        public static LcIotEquipment CreateTestIotEquipment(string? equipmentName = null, string? solutionId = null, string? projectId = null)
        {
            return new LcIotEquipment
            {
                Id = TUID.NewTUID().ToString(),
                EquipmentName = equipmentName ?? "测试设备",
                Description = "测试用IoT设备",
                EquipmentType = "PLC",
                State = *,
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试设备变量
        /// </summary>
        public static LcIotEquipmentVariable CreateTestEquipmentVariable(string equipmentId, string? variableName = null)
        {
            return new LcIotEquipmentVariable
            {
                Id = TUID.NewTUID().ToString(),
                EquipmentId = equipmentId,
                VarName = variableName ?? "测试变量",
                Description = "测试用设备变量",
                DataType = "INT",
                Address = "DB*.DBW0",
                State = *,
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试项目
        /// </summary>
        public static LcProject CreateTestProject(string? projectName = null, string? solutionId = null)
        {
            return new LcProject
            {
                Id = TUID.NewTUID().ToString(),
                ProjectName = projectName ?? "测试项目",
                Description = "测试用项目",
                State = *,
                SolutionId = solutionId ?? "test-solution-00*",
                Seq = *, // 添加必需的序号字段
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试解决方案
        /// </summary>
        public static LcSolution CreateTestSolution(string? solutionName = null)
        {
            return new LcSolution
            {
                Id = TUID.NewTUID().ToString(),
                SolutionName = solutionName ?? "测试解决方案",
                Description = "测试用解决方案",
                State = *,
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试用户
        /// </summary>
        public static LcUser CreateTestUser(string? userName = null)
        {
            return new LcUser
            {
                Id = TUID.NewTUID().ToString(),
                UserName = userName ?? "test_user",
                DisplayName = "测试用户",
                Password = "test_password",
                Email = "<EMAIL>",
                Phone = "*3800*38000",
                State = *,
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试函数
        /// </summary>
        public static LcFunction CreateTestFunction(string? functionName = null, string? solutionId = null, string? projectId = null)
        {
            return new LcFunction
            {
                Id = TUID.NewTUID().ToString(),
                FunctionName = functionName ?? "测试函数",
                Description = "测试用函数",
                FunctionType = "FLOW",
                State = *,
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now,
                UseVersion = *
            };
        }

        /// <summary>
        /// 创建测试函数代码
        /// </summary>
        public static LcFunctionCode CreateTestFunctionCode(string functionId, string content, string? solutionId = null, string? projectId = null)
        {
            return new LcFunctionCode
            {
                Id = TUID.NewTUID().ToString(),
                FunctionId = functionId,
                Code = content,
                CodeLanguage = "json",
                Version = *,
                State = *,
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now,
                TimeModified = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试工作流运行实例
        /// </summary>
        public static LcFruProc CreateTestFlowRunProc(string? functionId = null, string? solutionId = null, string? projectId = null)
        {
            return new LcFruProc
            {
                Id = TUID.NewTUID().ToString(),
                FunctionId = functionId ?? "test-function-00*",
                FunctionName = "测试工作流",
                TriggerType = "API",
                Status = 0, // 运行中
                BeginTime = DateTime.Now,
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试工作流步骤实例
        /// </summary>
        public static LcFruStep CreateTestFlowRunStep(string procId, string? functionId = null, int seqNo = 0)
        {
            return new LcFruStep
            {
                Id = TUID.NewTUID().ToString(),
                ProcId = procId,
                SeqNo = seqNo,
                FunctionId = functionId ?? "test-function-00*",
                ActionId = "step-00*",
                StepName = "测试步骤",
                Status = 0, // 运行中
                BeginTime = DateTime.Now
            };
        }

        /// <summary>
        /// 创建测试工作流历史实例
        /// </summary>
        public static LcFhiProc CreateTestFlowHistoryProc(string? functionId = null, string? solutionId = null, string? projectId = null)
        {
            return new LcFhiProc
            {
                Id = TUID.NewTUID().ToString(),
                FunctionId = functionId ?? "test-function-00*",
                FunctionName = "测试工作流",
                TriggerType = "API",
                Status = *, // 已完成
                BeginTime = DateTime.Now.AddMinutes(-*0),
                EndTime = DateTime.Now,
                Duration = 600000, // *0分钟
                SolutionId = solutionId ?? "test-solution-00*",
                ProjectId = projectId ?? "test-project-00*",
                Creator = "test",
                TimeCreate = DateTime.Now.AddMinutes(-*0)
            };
        }

        /// <summary>
        /// 创建简单的工作流JSON定义
        /// </summary>
        public static string CreateSimpleWorkflowJson()
        {
            var workflow = new
            {
                id = "test-workflow-00*",
                version = *,
                persistence = false,
                data = new object[0],
                body = new object[]
                {
                    new
                    {
                        id = "step-00*",
                        name = "开始步骤",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "*00" } },
                        nextId = "step-002"
                    },
                    new
                    {
                        id = "step-002",
                        name = "结束步骤",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "*00" } },
                        data = new object[]
                        {
                            new
                            {
                                name = "result",
                                type = "text",
                                textValue = "工作流执行完成"
                            }
                        }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建复杂的工作流JSON定义（包含分支、循环等控制结构）
        /// </summary>
        public static string CreateComplexWorkflowJson()
        {
            var workflow = new
            {
                id = "test-complex-workflow-00*",
                version = *,
                persistence = true,
                data = new object[]
                {
                    new
                    {
                        id = "global-var-00*",
                        key = "counter",
                        type = "number",
                        value = new { type = "text", textValue = "0" }
                    }
                },
                body = new object[]
                {
                    new
                    {
                        id = "step-00*",
                        name = "初始化",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "*00" } },
                        nextId = "step-002",
                        controlType = "normal"
                    },
                    new
                    {
                        id = "step-002",
                        name = "条件分支",
                        function = "dataBranch",
                        controlType = "branch",
                        control = new
                        {
                            branch = new
                            {
                                hasData = new { nextId = "step-003", condition = "counter > 0" },
                                noData = new { nextId = "step-004" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-003",
                        name = "有数据分支",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "200" } },
                        nextId = "step-005"
                    },
                    new
                    {
                        id = "step-004",
                        name = "无数据分支",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "300" } },
                        nextId = "step-005"
                    },
                    new
                    {
                        id = "step-005",
                        name = "结束",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "*00" } }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }
    }
}
