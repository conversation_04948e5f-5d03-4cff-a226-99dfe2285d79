﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20221109000000, "初始化数据源表")]
    public class AddDataSourceTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_DATA_SOURCE").WithDescription("数据源")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("DATA_SOURCE_NAME").AsAnsiString(50).WithColumnDescription("数据源名称")
               .WithColumn("DATA_PROVIDER").AsAnsiString(50).WithColumnDescription("数据提供者 Oracle、SqlServer、MySql、PostgreSQL、Doris")
               .WithColumn("CONNECT_BY").AsAnsiString(50).WithColumnDescription("根据特定方式连接 HOST:地址用户密码 URL:连接串")
               .WithColumn("CONNECTION_STRING").AsAnsiString(500).Nullable().WithColumnDescription("数据连接串")
               .WithColumn("SERVER_ADDRESS").AsAnsiString(50).Nullable().WithColumnDescription("服务器地址")
               .WithColumn("PORT").AsInt16().Nullable().WithColumnDescription("端口")
               .WithColumn("DATABASE").AsAnsiString(50).Nullable().WithColumnDescription("数据库")
               .WithColumn("USER_ID").AsAnsiString(50).Nullable().WithColumnDescription("用户名")
               .WithColumn("PASSWORD").AsAnsiString(255).Nullable().WithColumnDescription("密码")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("GROUP").AsAnsiString(50).Nullable().WithColumnDescription("分组")
            ;

            Create.Table("LC_DATA_SOURCE_ATTR").WithDescription("数据源属性")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")
               
               .WithColumn("DATA_SOURCE_ID").AsAnsiString(36).WithColumnDescription("数据源 ID")
               .WithColumn("ATTR_NAME").AsAnsiString(50).WithColumnDescription("属性名称")
               .WithColumn("ATTR_VALUE").AsAnsiString(200).WithColumnDescription("属性值")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               ;

            Create.Index("DATA_SOURCE_ATTR_ID_IDX")
                .OnTable("LC_DATA_SOURCE_ATTR")
                .OnColumn("DATA_SOURCE_ID").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();

            Create.Table("LC_DATA_DICT").WithDescription("数据字典")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("DIR_CODE").AsAnsiString(50).Nullable().WithColumnDescription("目录编码")
               .WithColumn("GROUP_CODE").AsAnsiString(50).Nullable().WithColumnDescription("分组编码")
               .WithColumn("SEQ").AsInt16().WithColumnDescription("字典排序")
               .WithColumn("DICT_CODE").AsAnsiString(50).WithColumnDescription("字典编码")
               .WithColumn("DICT_NAME").AsAnsiString(100).Nullable().WithColumnDescription("字典名称")
               .WithColumn("DICT_VALUE").AsAnsiString(2000).Nullable().WithColumnDescription("字典值")
               .WithColumn("DICT_TYPE").AsAnsiString(100).Nullable().WithColumnDescription("字典类型 TEXT、JSON")
               .WithColumn("IS_DEFAULT").AsFixedLengthAnsiString(1).Nullable().WithColumnDescription("是否默认 Y:是")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               ;

            Create.Index("DATA_DICT_DIR_IDX")
                .OnTable("LC_DATA_DICT")
                .OnColumn("DIR_CODE").Ascending()
                .OnColumn("GROUP_CODE").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();

            //Create.Index("DICT_GROUP_IDX")
            //    .OnTable("LC_DATA_DICT")
            //    .OnColumn("GROUP_CODE").Ascending()
            //    .OnColumn("SOLUTION_ID").Ascending()
            //    .OnColumn("PROJECT_ID").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_DATA_SOURCE");
            Delete.Table("LC_DATA_SOURCE_ATTR");
            Delete.Table("LC_DATA_DICT");
        }
    }
}
