﻿namespace GCP.Common
{
    public class CustomException : Exception
    {
        public int? Code { get; set; } = 501;

        public CustomException(string message) : base(message)
        {
        }

        public CustomException(string message, int code) : this(message)
        {
            Code = code;
        }

        public CustomException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
