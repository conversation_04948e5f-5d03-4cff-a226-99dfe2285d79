using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.FunctionPool.Flow.Services;
using GCP.Functions.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace GCP.Core.Tests.FunctionPool.Flow.Services
{
    public class ApiRequestV2Tests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly DbContext _dbContext;
        private readonly FunctionContext _functionContext;

        public ApiRequestV2Tests()
        {
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            
            _serviceProvider = services.BuildServiceProvider();
            
            // 创建测试数据库上下文
            _dbContext = new DbContext();
            _dbContext.SetDefaultConnection("Data Source=test_api_request_v2.db", "SQLite");
            
            // 创建测试数据
            CreateTestData();
            
            // 创建功能上下文
            _functionContext = new FunctionContext
            {
                db = _dbContext,
                globalData = new Dictionary<string, object>(),
                Current = new StepInfo { StepName = "ApiRequestV2Test" },
                SqlLog = new TestSqlLog()
            };
        }

        private void CreateTestData()
        {
            // 创建测试API配置
            var createApiTableSql = @"
                CREATE TABLE IF NOT EXISTS lc_api (
                    ID TEXT PRIMARY KEY,
                    TIME_CREATE DATETIME,
                    CREATOR TEXT,
                    TIME_MODIFIED DATETIME,
                    MODIFIER TEXT,
                    STATE INTEGER,
                    SOLUTION_ID TEXT,
                    PROJECT_ID TEXT,
                    REQUEST_TYPE TEXT,
                    HTTP_URL TEXT,
                    API_NAME TEXT,
                    DESCRIPTION TEXT,
                    FUNCTION_ID TEXT,
                    CLUSTER_ID TEXT,
                    HOSTS TEXT,
                    HEADERS TEXT,
                    QUERY_PARAMETERS TEXT,
                    API_TYPE INTEGER,
                    BODY TEXT,
                    BASE_URL TEXT,
                    RESPONSE TEXT,
                    DIR_CODE TEXT,
                    TIMEOUT_IN_SECONDS INTEGER
                )";
            
            _dbContext.ExecuteNonQuery(createApiTableSql);
            
            // 插入测试API数据
            var insertApiSql = @"
                INSERT OR REPLACE INTO lc_api (
                    ID, TIME_CREATE, CREATOR, STATE, SOLUTION_ID, PROJECT_ID,
                    REQUEST_TYPE, HTTP_URL, API_NAME, DESCRIPTION, API_TYPE,
                    HEADERS, QUERY_PARAMETERS, BODY, RESPONSE, BASE_URL
                ) VALUES (
                    'test-api-id', datetime('now'), 'test', 1, 'test-solution', 'test-project',
                    'GET', '/api/test', '测试API', '测试用API', 2,
                    '[{""id"":""content-type"",""key"":""Content-Type"",""description"":""内容类型"",""type"":""string"",""value"":{""type"":""text"",""textValue"":""application/json""}}]',
                    '[{""id"":""param1"",""key"":""param1"",""description"":""参数1"",""type"":""string""}]',
                    '[{""id"":""field1"",""key"":""field1"",""description"":""字段1"",""type"":""string""}]',
                    '[{""id"":""result"",""key"":""result"",""description"":""结果"",""type"":""object""}]',
                    'http://localhost:8080'
                )";
            
            _dbContext.ExecuteNonQuery(insertApiSql);
        }

        [Fact]
        public void TestApiRequestV2DataModel()
        {
            // 测试新版本数据模型的基本功能
            var request = new DataApiRequestV2
            {
                Name = "测试API请求V2",
                Description = "测试描述",
                ApiId = "test-api-id",
                Version = "v2",
                UseRoot = true,
                Headers = new List<FlowData>
                {
                    new FlowData
                    {
                        Id = "content-type",
                        Key = "Content-Type",
                        Description = "内容类型",
                        Type = "string",
                        Value = new DataValue
                        {
                            Type = "text",
                            TextValue = "application/json"
                        }
                    }
                },
                Params = new List<FlowData>
                {
                    new FlowData
                    {
                        Id = "param1",
                        Key = "param1",
                        Description = "参数1",
                        Type = "string",
                        Value = new DataValue
                        {
                            Type = "text",
                            TextValue = "test-value"
                        }
                    }
                }
            };

            Assert.Equal("v2", request.Version);
            Assert.Equal("测试API请求V2", request.Name);
            Assert.Single(request.Headers);
            Assert.Single(request.Params);
            Assert.Equal("Content-Type", request.Headers[0].Key);
            Assert.Equal("param1", request.Params[0].Key);
        }

        [Fact]
        public void TestApiRequestV2Service_BasicFunctionality()
        {
            // 测试API请求V2服务的基本功能
            var apiRequestService = new ApiRequest();
            apiRequestService.SetContext(_functionContext);

            var request = new DataApiRequestV2
            {
                Name = "测试API请求V2",
                ApiId = "test-api-id",
                Headers = new List<FlowData>
                {
                    new FlowData
                    {
                        Id = "content-type",
                        Key = "Content-Type",
                        Description = "内容类型",
                        Type = "string",
                        Value = new DataValue
                        {
                            Type = "text",
                            TextValue = "application/json"
                        }
                    }
                },
                Params = new List<FlowData>
                {
                    new FlowData
                    {
                        Id = "param1",
                        Key = "param1",
                        Description = "参数1",
                        Type = "string",
                        Value = new DataValue
                        {
                            Type = "text",
                            TextValue = "test-value"
                        }
                    }
                }
            };

            // 由于这是一个集成测试，需要真实的API端点，这里我们主要测试数据结构
            Assert.NotNull(request);
            Assert.Equal("v2", request.Version);
            Assert.NotEmpty(request.Headers);
            Assert.NotEmpty(request.Params);
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
            _serviceProvider?.Dispose();
            
            // 清理测试数据库文件
            try
            {
                if (File.Exists("test_api_request_v2.db"))
                {
                    File.Delete("test_api_request_v2.db");
                }
            }
            catch
            {
                // 忽略清理错误
            }
        }
    }

    // 测试用的SQL日志类
    public class TestSqlLog : ISqlLog
    {
        public void Log(string sql, object parameters = null)
        {
            // 测试环境下不需要实际记录日志
        }
    }
}
