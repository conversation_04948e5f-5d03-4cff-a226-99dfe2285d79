﻿using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    /// <summary>
    /// IDbConnection Extension（基础扩展）
    /// </summary>
    public static class DbConnectionExtension
    {
        internal static async Task PrepareBatchCommand(this DbBatchCommand cmd, string cmdText, IDataParameter[] parms)
        {
            cmd.CommandText = cmdText;
            if (parms == null || parms.Length == 0) return;
            PrepareParameters(parms, (param) => cmd.Parameters.Add(param));

            if (DbDebug.SqlBatchExecuteBeforeAction != null) await DbDebug.SqlBatchExecuteBeforeAction.Invoke(cmd);
        }

        internal static async Task PrepareCommand(this DbCommand cmd, string cmdText, IDataParameter[] parms)
        {
            cmd.CommandText = cmdText;
            if (parms == null || parms.Length == 0) return;
            PrepareParameters(parms, (param) => cmd.Parameters.Add(param));
            if (DbDebug.SqlExecuteBeforeAction != null) await DbDebug.SqlExecuteBeforeAction.Invoke(cmd);
        }

        private static void PrepareParameters(IDataParameter[] parms, Action<IDataParameter> action)
        {
            foreach (var parameter in parms)
            {
                if (parameter.Value == null || (parameter.Value is string && parameter.Value.ToString() == ""))
                {
                    parameter.Value = DBNull.Value;
                }

                action(parameter);
            }
        }

        private static T RunSql<T>(this DbConnection connection, Func<DbCommand, bool, T> function)
        {
            var wasClosed = connection.State == ConnectionState.Closed;
            DbCommand cmd = null;
            try
            {
                if (wasClosed) connection.Open();
                cmd = connection.CreateCommand();
                cmd.Connection = connection;

                T obj;
                if (DbDebug.IsOpen)
                {
                    obj = DbDebug.Run<T>(connection, cmd, wasClosed, function);
                }
                else
                {
                    obj = function(cmd, wasClosed);
                }

                if (obj is IDataReader)
                {
                    wasClosed = false;
                }
                return obj;
            }
            finally
            {
                if (cmd != null)
                {
                    cmd.Parameters.Clear();
                    cmd.Dispose();
                }
                if (wasClosed) connection.Close();
            }
        }

        /// <summary>
        /// 将cmd转换为可执行SQL
        /// </summary>
        public static string GetExecutableSql(this DbCommand cmd)
        {
            return SqlDebug.GetExecutableSql(cmd.Connection.GetDbProvider(), cmd.CommandText, cmd.Parameters);
        }

        /// <summary>
        /// 执行非查询SQL语句, 返回受影响行数
        /// </summary>
        public static int Execute(this DbConnection connection, DbTransaction tran, string sqlString, CommandType cmdType, int commandTimeout, params IDataParameter[] parms)
        {
            return connection.RunSql((cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = cmdType;
                cmd.CommandTimeout = commandTimeout;
                _ = PrepareCommand(cmd, sqlString, parms);
                return cmd.ExecuteNonQuery();
            });
        }
        public static int Execute(this DbConnection connection, string sqlString, CommandType cmdType, int commandTimeout, params IDataParameter[] parms)
        {
            return Execute(connection, null, sqlString, cmdType, commandTimeout, parms);
        }
        public static int Execute(this DbConnection connection, string sqlString, params IDataParameter[] parms)
        {
            return Execute(connection, null, sqlString, CommandType.Text, DbSettings.CommandTimeout, parms);
        }
        public static int Execute(this DbTransaction tran, string sqlString, CommandType cmdType, int commandTimeout, params IDataParameter[] parms)
        {
            return Execute(tran.Connection, tran, sqlString, cmdType, commandTimeout, parms);
        }
        public static int Execute(this DbTransaction tran, string sqlString, params IDataParameter[] parms)
        {
            return Execute(tran.Connection, tran, sqlString, CommandType.Text, DbSettings.CommandTimeout, parms);
        }


        /// <summary>
        /// 执行查询, 返回第一行的第一列数据
        /// </summary>
        public static object ExecuteScalar(this DbConnection connection, DbTransaction tran, string sqlString, int commandTimeout, params IDataParameter[] parms)
        {
            return connection.RunSql((cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = CommandType.Text;
                cmd.CommandTimeout = commandTimeout;
                _ = PrepareCommand(cmd, sqlString, parms);
                object result = cmd.ExecuteScalar();
                return Convert.IsDBNull(result) ? null : result;
            });
        }

        /// <summary>
        /// 执行查询语句, 返回DataReader（使用完DataReader必须Close掉）
        /// </summary>
        public static DbDataReader ExecuteReader(this DbConnection connection, DbTransaction tran, string sqlString, CommandType cmdType = CommandType.Text, CommandBehavior behavior = CommandBehavior.Default, int commandTimeout = 30, params IDataParameter[] parms)
        {
            return connection.RunSql((cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = cmdType;
                cmd.CommandTimeout = commandTimeout;
                _ = PrepareCommand(cmd, sqlString, parms);
                return cmd.ExecuteReader(wasClosed ? behavior | CommandBehavior.CloseConnection : behavior);
            });
        }
        public static DbDataReader ExecuteReader(this DbConnection connection, string sqlString, params IDataParameter[] parms)
        {
            return ExecuteReader(connection, null, sqlString, CommandType.Text, CommandBehavior.Default, DbSettings.CommandTimeout, parms);
        }

        /// <summary>
        /// 执行查询语句, 返回DataSet
        /// </summary>
        public static DataSet GetDataSet(this DbConnection connection, DbTransaction tran, string sqlString, int commandTimeout, params IDataParameter[] parms)
        {
            return connection.RunSql((cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = CommandType.Text;
                cmd.CommandTimeout = commandTimeout;
                _ = PrepareCommand(cmd, sqlString, parms);
                using (DbDataAdapter adapter = connection.GetFactory().CreateDataAdapter())
                {
                    DataSet ds = new DataSet();
                    adapter.SelectCommand = (DbCommand)cmd;
                    adapter.Fill(ds);
                    return ds;
                }
            });
        }
        public static DataSet GetDataSet(this DbConnection connection, string sqlString, params IDataParameter[] parms)
        {
            return GetDataSet(connection, null, sqlString, DbSettings.CommandTimeout, parms);
        }


        /// <summary>
        /// 执行查询语句, 返回DataTable
        /// </summary>
        public static DataTable GetDataTable(this DbConnection connection, DbTransaction tran, string sqlString, int commandTimeout, params IDataParameter[] parms)
        {
            return connection.RunSql((cmd, wasClosed) => {
                cmd.Transaction = tran;
                cmd.CommandType = CommandType.Text;
                cmd.CommandTimeout = commandTimeout;
                _ = PrepareCommand(cmd, sqlString, parms);
                using (DbDataAdapter adapter = connection.GetFactory().CreateDataAdapter())
                {
                    DataTable dt = new DataTable();
                    adapter.SelectCommand = (DbCommand)cmd;
                    adapter.Fill(dt);
                    return dt;
                }
            });
        }
        public static DataTable GetDataTable(this DbConnection connection, string sqlString, params IDataParameter[] parms)
        {
            return GetDataTable(connection, null, sqlString, DbSettings.CommandTimeout, parms);
        }
    }
}
