﻿using GCP.Common;

namespace GCP.FunctionPool
{

    internal class MiddlewareHandler
    {
        /// <summary>
        /// 组合函数中间件
        /// </summary>
        /// <param name="middlewares"></param>
        /// <returns></returns>
        public static FunctionDelegate Compose(params FunctionMiddlewareDelegate[] middlewares)
        {
            return async (ctx) =>
            {
                var index = -1;
                await Dispatch(0);
                async Task Dispatch(int num)
                {
                    if (num <= index) return;
                    index = num;
                    var val = middlewares.GetValue(index);
                    if (num >= middlewares.Length) val = null;
                    if (val == null) return;
                    var fn = (FunctionMiddlewareDelegate)val;
                    await fn(ctx, async () => await Dispatch(index + 1).ConfigureAwait(false)).ConfigureAwait(false);
                }
            };
        }
    }
}
