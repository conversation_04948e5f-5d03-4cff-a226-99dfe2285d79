<template>
  <CmpContainer full style="padding-top: 16px">
    <CmpCard ghost>
      <t-form ref="form" :data="filterCondition" :label-width="100" colon @reset="onReset" @submit="fetchData">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="触发类型" name="triggerType">
                  <t-select
                    v-model="filterCondition.triggerType"
                    class="form-item-content"
                    clearable
                    placeholder="请选择触发类型"
                  >
                    <t-option v-for="(value, key) in TRIGGER_TYPES" :key="key" :value="key" :label="value"></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="任务名称" name="functionId">
                  <t-select
                    v-model="filterCondition.functionId"
                    class="form-item-content"
                    :options="functionOptions"
                    filterable
                    clearable
                    placeholder="请选择任务名称"
                  >
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="步骤日志" name="keyword">
                  <t-input
                    v-model="searchValue"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入步骤日志"
                    clearable
                  >
                    <template #suffix-icon>
                      <search-icon size="16px" />
                    </template>
                  </t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="开始时间" name="beginTime">
                  <t-date-picker
                    v-model="filterCondition.beginTime"
                    class="form-item-content"
                    format="YYYY/MM/DD HH:mm:ss"
                    enable-time-picker
                    allow-input
                    clearable
                  ></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="结束时间" name="endTime">
                  <t-date-picker
                    v-model="filterCondition.endTime"
                    class="form-item-content"
                    format="YYYY/MM/DD HH:mm:ss"
                    enable-time-picker
                    allow-input
                    clearable
                  ></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="运行日志" name="runLog">
                  <t-input v-model="filterCondition.runLog" type="search" placeholder="请输入运行日志" clearable>
                    <template #suffix-icon>
                      <search-icon size="16px" />
                    </template>
                  </t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="包含日志" name="hasRunLog">
                  <t-switch v-model="filterCondition.hasRunLog" />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="最小耗时(ms)" name="duration">
                  <t-input-number
                    v-model="filterCondition.duration"
                    theme="column"
                    placeholder="请输入最小耗时(ms)"
                    style="width: 100%"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>
          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }"> 查询 </t-button>
            <t-button type="reset" variant="base" theme="default"> 重置 </t-button>
          </t-col>
        </t-row>
      </t-form>

      <div class="table-container">
        <t-table
          :data="data"
          :columns="COLUMNS"
          size="small"
          height="calc(-408px + 100vh)"
          row-key="id"
          :hover="true"
          :pagination="pagination"
          :loading="dataLoading"
          @page-change="onPageChange"
        >
          <template #triggerType="{ row }">
            {{ TRIGGER_TYPES[row.triggerType] }}
          </template>

          <template #status="{ row }">
            <log-status-tag :status="row.status" />
          </template>
          <template #runLog="{ row }">
            <div v-if="row.runLog" :title="row.runLog.substr(0, 320) + '...'">
              <t-link theme="primary" @click="onClickShowLog(row)"> 日志概览 </t-link>
            </div>
          </template>
          <template #duration="{ row }">
            {{ formatDuration(row.duration) }}
          </template>

          <template #operate="{ row }">
            <t-space>
              <t-link theme="primary" @click="onClickDetail(row)"> 详情 </t-link>
            </t-space>
          </template>
        </t-table>
      </div>
      <t-dialog v-model:visible="isShowLog" width="70%" header="日志概览" :footer="false">
        <code-preview :code="logContent" lang="log" style="height: 350px" />
      </t-dialog>
    </CmpCard>
  </CmpContainer>
</template>
<script lang="ts">
export default {
  name: 'HistoryLogs',
};
</script>
<script setup lang="ts">
import { SearchIcon } from 'tdesign-icons-vue-next';
import { PrimaryTableCol, TableProps, TableRowData } from 'tdesign-vue-next';
import { onActivated, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { api, Services } from '@/api/system';
import CodePreview from '@/components/code-preview/index.vue';
import { formatDuration } from '@/utils';

import { TRIGGER_TYPES } from './constants';
import LogStatusTag from './LogStatusTag.vue';

const route = useRoute();

onMounted(() => init());

onActivated(() => init());

const init = () => {
  if (route.query.keyword) {
    searchValue.value = route.query.keyword?.toString();
  }
  if (route.query.triggerType) {
    filterCondition.value.triggerType = route.query.triggerType?.toString();
  }
  if (route.query.functionId) {
    filterCondition.value.functionId = route.query.functionId?.toString();
  }
  fetchData();
  fetchFunctionOptions();
};
const searchValue = ref('');

// 过滤条件
const filterCondition = ref({
  triggerType: '',
  functionId: '',
  beginTime: null,
  endTime: null,
  keyword: '',
  hasRunLog: false,
  runLog: '',
  duration: '',
});

// 将searchValue绑定到filterCondition.keyword
watch(searchValue, (newVal) => {
  filterCondition.value.keyword = newVal;
});

// 任务名称选项
const functionOptions = ref([]);

// 监听触发类型变化
watch(
  () => filterCondition.value.triggerType,
  () => {
    fetchFunctionOptions();
  },
);

// 获取任务名称列表
const fetchFunctionOptions = async () => {
  try {
    const { triggerType } = filterCondition.value;
    let service = null;

    if (triggerType === 'JOB') {
      service = Services.jobGetFunctionNames;
    } else if (triggerType === 'API') {
      service = Services.apiGetFunctionNames;
    } else if (triggerType === 'EVENT') {
      service = Services.eventGetFunctionNames;
    } else {
      functionOptions.value = [];
      return;
    }

    functionOptions.value = await api.run(service);
  } catch (error) {
    console.error('获取任务名称列表失败:', error);
    functionOptions.value = [];
  }
};

const COLUMNS: PrimaryTableCol<TableRowData>[] = [
  {
    title: '任务名称',
    align: 'left',
    colKey: 'functionName',
    fixed: 'left',
    width: 180,
    ellipsis: true,
  },
  {
    title: '触发类型',
    colKey: 'triggerType',
    width: 100,
    align: 'center',
  },
  { title: '状态', colKey: 'status', width: 100 },
  {
    title: '开始时间',
    width: 180,
    colKey: 'beginTime',
  },
  {
    title: '结束时间',
    width: 180,
    colKey: 'endTime',
  },
  {
    title: '运行日志',
    width: 120,
    colKey: 'runLog',
    fixed: 'right',
  },
  {
    title: '运行时长',
    width: 120,
    colKey: 'duration',
    fixed: 'right',
  },
  {
    title: '操作',
    align: 'left',
    fixed: 'right',
    width: 80,
    colKey: 'operate',
  },
];
const data = ref([]);
const pagination = ref({
  'page-ellipsis-mode': 'both-ends',
  pageSize: 20,
  total: 99999,
  totalContent: false,
  current: 1,
  pageSizeOptions: [20, 200, 500, 1000],
});

const dataLoading = ref(false);
const fetchData = async () => {
  dataLoading.value = true;
  try {
    route.query.keyword = searchValue.value;
    const list = await api.run(Services.flowHistoryGetAll, {
      keyword: searchValue.value?.trim(),
      triggerType: filterCondition.value.triggerType || null,
      functionId: filterCondition.value.functionId || null,
      beginTime: filterCondition.value.beginTime || null,
      endTime: filterCondition.value.endTime || null,
      runLog: filterCondition.value.runLog || null,
      hasRunLog: filterCondition.value.hasRunLog || null,
      duration: filterCondition.value.duration || null,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });
    data.value = list;
    pagination.value = {
      ...pagination.value,
      total: list.length < pagination.value.pageSize ? list.length : 99999,
    };
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};

const onReset = () => {
  searchValue.value = '';
  filterCondition.value = {
    triggerType: '',
    functionId: '',
    beginTime: null,
    endTime: null,
    keyword: '',
    hasRunLog: false,
    runLog: '',
    duration: '',
  };
  fetchData();
};

const onPageChange: TableProps['onPageChange'] = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchData();
};

const emits = defineEmits(['showDetail']);
const onClickDetail = (row) => {
  emits('showDetail', {
    row,
    state: 'history',
  });
};

const isShowLog = ref(false);
const logContent = ref('');
const onClickShowLog = (row) => {
  logContent.value = row.runLog;
  isShowLog.value = true;
};
</script>
<style lang="less" scoped>
.left-operation-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-comp-margin-xxl);

  .selected-count {
    display: inline-block;
    margin-left: var(--td-comp-margin-l);
    color: var(--td-text-color-secondary);
  }
}

.form-item-content {
  width: 100%;
}

.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.table-container {
  margin-top: var(--td-comp-margin-xxl);
}

.search-input {
  width: 360px;
}
</style>
