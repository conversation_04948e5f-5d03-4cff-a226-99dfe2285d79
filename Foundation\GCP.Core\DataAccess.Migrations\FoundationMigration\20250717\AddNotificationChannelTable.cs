﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20250717002000, "添加通知通道表")]
    public class AddNotificationChannelTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_NOTIFICATION_CHANNEL").WithDescription("通知通道配置")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")
               
               .WithColumn("CHANNEL_NAME").AsAnsiString(100).WithColumnDescription("通道名称")
               .WithColumn("CHANNEL_TYPE").AsAnsiString(20).WithColumnDescription("通道类型：Email、Dingtalk、Feishu、Weixin")
               .WithColumn("DESCRIPTION").AsAnsiString(500).Nullable().WithColumnDescription("描述")
               .WithColumn("IS_ENABLED").AsBoolean().WithDefaultValue(true).WithColumnDescription("是否启用")
               .WithColumn("CONFIG_JSON").AsString(2000).WithColumnDescription("配置参数JSON")
               .WithColumn("INTERVAL_SECONDS").AsInt32().WithDefaultValue(10).WithColumnDescription("发送间隔秒数")
               ;
        }

        public override void Down()
        {
            Delete.Table("LC_NOTIFICATION_CHANNEL");
        }
    }
}
