﻿using Microsoft.AspNetCore.Http;

namespace GCP.Common
{
    public class EnableRequestBodyBufferingMiddleware(RequestDelegate next)
    {
        public async Task InvokeAsync(HttpContext context)
        {
            context.Request.EnableBuffering();
            await next(context);
        }
    }

    public static class HttpExtensions
    {
        /// <summary>
        /// 替换响应体
        /// </summary>
        /// <param name="response"></param>
        /// <param name="bytes"></param>
        /// <returns></returns>
        public static async Task ReplaceBodyAsync(this HttpResponse response, byte[] bytes)
        {
            var buffer = new MemoryStream();
            var bodyStream = response.Body;
            response.Headers.ContentLength = null;
            await response.Body.WriteAsync(bytes, 0, bytes.Length);
            response.Body = buffer;

            buffer.Position = 0;
            await buffer.CopyToAsync(bodyStream, bytes.Length);
        }

        /// <summary>
        /// 读取请求体
        /// </summary>
        /// <param name="req"></param>
        /// <param name="func"></param>
        /// <returns></returns>
        public static async Task ReadBodyAsync(this HttpRequest req, Func<Stream, Task> func)
        {
            var length = req.ContentLength;
            
            if(length is null or 0) return;

            await func(req.Body).ConfigureAwait(false);
            req.Body.Position = 0;

            //var memoryStream = new MemoryStream();
            //await req.Body.CopyToAsync(memoryStream);            
            //memoryStream.Position = 0;
            //await func(memoryStream).ConfigureAwait(false);
            //memoryStream.Position = 0;
            //req.Body = memoryStream;
        }
    }
}
