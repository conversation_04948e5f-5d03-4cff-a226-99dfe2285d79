/* eslint-disable max-classes-per-file */
// nodejs环境依赖
// import fetch from "node-fetch";
// import { Request, Response, RequestInit, BodyInit } from "node-fetch";

import { debounce, isObject, throttle } from 'lodash-es';
import SparkMD5 from 'spark-md5';

import { compute, isEmpty } from '../base';

// 通用请求头
export enum ContentTypeEnum {
  Json = 'application/json;charset=UTF-8',
  FormURLEncoded = 'application/x-www-form-urlencoded;charset=UTF-8',
  FormData = 'multipart/form-data;charset=UTF-8',
}

/**
 * HTTP中间件
 */
export type HttpMiddleware = (ctx: HttpContext, next: () => any) => any;
/**
 * HTTP URL 参数
 */
export type HttpUrlParams = string | { [key: string]: string | number | boolean } | (() => string);
/**
 * HTTP服务路径（前面自动加上`baseURL`，除非是绝对URL）
 */
export type HttpPath = string | (() => string);

/**
 * HTTP上下文
 */
export class HttpContext {
  key: string = '';

  fetchPromise: Promise<any> | undefined = undefined;

  /**
   * 服务基础路径
   */
  baseURL: string | (() => string) = '';

  /**
   * 请求路径
   */
  path: string = '';

  /**
   * 请求全路径
   */
  url: string = '';

  originUrl: string = '';

  /**
   * 请求结果
   */
  result: any = null;

  /**
   * 请求参数
   */
  requestOptions: RequestOptions = {};

  /**
   * 请求信息
   */
  request?: Request = undefined;

  /**
   * 请求体
   */
  requestBody?: BodyInit | null;

  /**
   * 请求返回信息
   */
  response?: Response = undefined;
}

interface RequestOptions extends RequestInit {
  /**
   * 指定请求超时的毫秒数（默认永不超时，Chrome中网络请求超时为300秒，Firefox中为90秒）
   */
  timeout?: number;

  /**
   * URL 参数
   */
  params?: HttpUrlParams;

  /**
   * 是否返回原生输出，不做转换和处理
   */
  returnNativeResponse?: boolean;

  /**
   * 接口级节流
   *
   * 单位: 毫秒
   */
  throttle?: number;

  /**
   * 接口级防抖
   *
   * 单位: 毫秒
   */
  debounce?: number;
}

/**
 * HTTP配置项
 */
export class HttpOptions {
  /**
   * 服务基础路径
   */
  baseURL?: string | (() => string);

  /**
   * 指定请求超时的毫秒数（默认永不超时，Chrome中网络请求超时为300秒，Firefox中为90秒）
   */
  timeout?: number;

  /**
   * 服务中间件
   */
  middlewares: HttpMiddleware[] | (() => HttpMiddleware[]);

  constructor(url?: string) {
    this.baseURL = url || '';
    this.middlewares = [];
  }

  normalizeUrl(url: string) {
    return url.toString().replace(/(^\/+|\/+$)/g, '');
  }

  normalizeUrlParams(url: string, urlParams: HttpUrlParams): string {
    if (isEmpty(urlParams)) return '';

    const divider = ~url.indexOf('?') ? '&' : '?';

    if (typeof urlParams === 'object') {
      return (
        divider +
        Object.entries(urlParams)
          .map(
            (t) => `${encodeURIComponent(t[0])}=${encodeURIComponent(t[1] === undefined || t[1] === null ? '' : t[1])}`,
          )
          .join('&')
      );
    }
    if (urlParams !== '') {
      return divider + compute(urlParams);
    }

    return '';
  }
}

/**
 * HTTP 基础请求库（基于原生Fetch封装，支持Nodejs）
 */
export class Http {
  middlewares: HttpMiddleware[];

  options: HttpOptions;

  pendingMap: Map<string, HttpContext>;

  private capture?: boolean;

  private onceCapture?: (item: { api: string; method?: string; data?: any; header?: any }) => any;

  history: { api: string; method?: string; data?: any; header?: any }[];

  constructor(options?: HttpOptions | string) {
    this.middlewares = [];
    this.pendingMap = new Map<string, HttpContext>();
    this.history = [];

    if (options instanceof HttpOptions) {
      const opts = new HttpOptions();
      opts.baseURL = options.baseURL;
      opts.middlewares = options.middlewares;
      opts.timeout = options.timeout;
      this.options = opts;
    } else {
      this.options = new HttpOptions(options);
    }
  }

  /**
   * 新增中间件
   * @param middleware 中间件
   */
  use(middleware: HttpMiddleware) {
    this.middlewares.push(middleware);
    return this;
  }

  private getUrl(baseURL: string, path: string, urlParams: HttpUrlParams = '') {
    let url = baseURL ? new URL(path, new URL(baseURL, window.location.href).toString()).toString() : path;
    if (urlParams) url += this.options.normalizeUrlParams(url, urlParams);
    return url;
  }

  private createContext(path: HttpPath, requestOptions: RequestOptions = {}): HttpContext {
    const opts = this.options;
    const baseURL: string = compute(opts.baseURL);

    let pathStr: string = compute(path);
    pathStr = opts.normalizeUrl(encodeURI(pathStr));

    let ctx = new HttpContext();
    ctx.baseURL = baseURL;
    ctx.path = pathStr;
    ctx.originUrl = this.getUrl(baseURL, pathStr);
    if (requestOptions.params) {
      ctx.url = ctx.originUrl + this.options.normalizeUrlParams(ctx.originUrl, requestOptions.params);
    } else {
      ctx.url = ctx.originUrl;
    }
    ctx.requestOptions = {
      headers: {},
      timeout: opts.timeout,
      ...requestOptions,
    };

    const hasBody = !isEmpty(requestOptions?.body);
    let bodyIsObject = false;

    const spark = new SparkMD5();
    spark.append(requestOptions.method || '');
    spark.append(ctx.url);
    if (hasBody) {
      bodyIsObject = isObject(requestOptions.body);
      ctx.requestBody = requestOptions.body;
      if (requestOptions.body instanceof URLSearchParams) {
        spark.append(requestOptions.body.toString());
      } else if (requestOptions.body instanceof FormData) {
        spark.append(
          // @ts-ignore
          Array.from(requestOptions.body)
            // @ts-ignore
            .map(([key, value]) => `${key.toString()}=${value.toString()}`)
            .join('&'),
        );
      } else if (bodyIsObject) {
        spark.append(JSON.stringify(requestOptions.body));
      }
    }
    ctx.key = spark.end();

    const lastCtx = this.pendingMap.get(ctx.key);
    if (lastCtx) {
      console.debug(
        `[WebCore Http]: The same request is being executed, and this request has been cancelled. url: ${ctx.url}`,
      );
      ctx = lastCtx;
    } else {
      this.pendingMap.set(ctx.key, ctx);

      // 如果json body包含二进制类型，则转换类型为FormData，使用流式传输
      if (hasBody && bodyIsObject) {
        let hasBlob = false;
        const formData = new FormData();
        for (const key in requestOptions.body as any) {
          if (Object.prototype.hasOwnProperty.call(requestOptions.body, key)) {
            const value = (requestOptions.body as any)[key];
            if (value instanceof Blob) {
              hasBlob = true;
            }
            formData.append(key, value);
          }
        }
        if (hasBlob) {
          ctx.requestBody = formData;
          ctx.requestOptions.body = formData;
        }
      }
    }
    return ctx;
  }

  private createRequest<T = any>(
    url: HttpPath,
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    const ctx = this.createContext(url, requestOptions);
    const opts = this.options;
    let middlewares: HttpMiddleware[] = [];

    if (typeof opts.middlewares === 'function') {
      // 每次请求的时候运算全局中间件函数（例如根据外部参数动态载入不同全局中间件）
      middlewares = middlewares.concat(compute(opts.middlewares));
    }

    middlewares = middlewares.concat(this.middlewares);
    if (middleware) {
      if (typeof middleware === 'function') {
        middlewares.push(middleware);
        ctx.fetchPromise = undefined;
      } else if (middleware instanceof Array && middleware.length > 0) {
        middlewares = middlewares.concat(middleware);
        ctx.fetchPromise = undefined;
      }
    }

    const dispatch = (i: number): Promise<any> => {
      const fn = middlewares[i];
      if (!fn) {
        // 未配置自定义中断配置器，并且有设置超时时间，才会使用默认的超时中断控制器
        if (ctx.requestOptions.timeout && !ctx.requestOptions.signal) {
          const timeoutController = new AbortController();
          ctx.requestOptions.signal = timeoutController.signal;
          setTimeout(() => timeoutController.abort(), ctx.requestOptions.timeout);
        }

        ctx.request = new Request(ctx.url, ctx.requestOptions);
        return fetch(ctx.request)
          .then((response: Response) => {
            ctx.response = response;
            return response;
          })
          .finally(() => {
            if (this.pendingMap.has(ctx.key)) {
              window.setTimeout(() => {
                this.pendingMap.delete(ctx.key);
              }, 600);
            }
          });
      }
      try {
        return Promise.resolve(fn(ctx, dispatch.bind(null, i + 1)));
      } catch (err) {
        return Promise.reject(err);
      }
    };

    if (!ctx.fetchPromise) {
      ctx.fetchPromise = dispatch(0).then((res) => {
        if (this.onceCapture || this.capture) {
          const hasBody = !isEmpty(ctx.requestOptions.body);
          const hasParams = !isEmpty(ctx.requestOptions.params);
          const item = {
            api: ctx.originUrl,
            method: ctx.requestOptions.method,
            headers: ctx.requestOptions.headers,
            // eslint-disable-next-line no-nested-ternary
            data: hasBody ? ctx.requestOptions.body : hasParams ? JSON.stringify(ctx.requestOptions.params) : null,
            hasParams,
          };

          this.history.push(item);
          if (this.onceCapture) {
            this.onceCapture(item);
            this.onceCapture = undefined;
          }
        }
        return res;
      });
    }

    return ctx.fetchPromise.then((res) => {
      if (middlewares.length > 0) {
        return ctx.result as unknown as Promise<T>;
      }
      return res;
    });
  }

  request<T = any>(
    url: HttpPath,
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    if (requestOptions.throttle !== undefined && requestOptions.debounce !== undefined) {
      throw new Error('throttle and debounce cannot be set at the same time');
    }

    if (requestOptions.throttle) {
      return new Promise((resolve) => {
        throttle(() => resolve(this.createRequest(url, requestOptions, middleware)), requestOptions.throttle);
      });
    }

    if (requestOptions.debounce) {
      return new Promise((resolve) => {
        debounce(() => resolve(this.createRequest(url, requestOptions, middleware)), requestOptions.debounce);
      });
    }

    return this.createRequest(url, requestOptions, middleware);
  }

  get<T = any>(
    url: HttpPath,
    params?: HttpUrlParams,
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    requestOptions.method = 'GET';
    requestOptions.params = params;
    return this.request(url, requestOptions, middleware);
  }

  post<T = any>(
    url: HttpPath,
    data: any,
    // data: BodyInit | { [key: string]: any },
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    requestOptions.method = 'POST';
    requestOptions.body = data;
    return this.request(url, requestOptions, middleware);
  }

  patch<T = any>(
    url: HttpPath,
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    requestOptions.method = 'PATCH';
    return this.request(url, requestOptions, middleware);
  }

  put<T = any>(
    url: HttpPath,
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    requestOptions.method = 'PUT';
    return this.request(url, requestOptions, middleware);
  }

  delete<T = any>(
    url: HttpPath,
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    requestOptions.method = 'DELETE';
    return this.request(url, requestOptions, middleware);
  }

  upload<T = any>(
    url: HttpPath,
    data: FormData,
    requestOptions: RequestOptions = {},
    middleware: HttpMiddleware | HttpMiddleware[] = [],
  ): Promise<T> {
    requestOptions.method = 'POST';
    requestOptions.body = data;
    // requestOptions.headers = {
    //   'Content-Type': ContentTypeEnum.FormData,
    // };
    return this.request(url, requestOptions, middleware);
  }

  openCapture() {
    this.capture = true;
    this.history = [];
  }

  openOnceCapture(func: (item: { api: string; method?: string; data?: any; header?: any }) => any) {
    this.capture = true;
    this.onceCapture = func;
    this.history = [];
  }

  closeCapture() {
    this.capture = false;
  }
}
