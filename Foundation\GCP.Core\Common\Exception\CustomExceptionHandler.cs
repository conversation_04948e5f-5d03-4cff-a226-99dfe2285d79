﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace GCP.Common
{
    public static class CustomExceptionHandlerExtensions
    {
        public static IApplicationBuilder UseCustomExceptionMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<CustomExceptionHandler>();
        }
    }

    public class CustomExceptionHandler
    {
        private readonly RequestDelegate next;

        public CustomExceptionHandler(RequestDelegate next)
        {
            this.next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                await next.Invoke(context).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                await HandleException(context, ex).ConfigureAwait(false);
            }
        }

        private async Task HandleException(HttpContext context, Exception ex)
        {
            if (context.Response.HasStarted) return;
            context.Response.StatusCode = 200;
            context.Response.ContentType = "application/json";
            string error = JsonHelper.Serialize(new ResponseBase
            {
                Code = 500,
                Message = ex.Message
            });
            await context.Response.WriteAsync(error);
        }
    }
}
