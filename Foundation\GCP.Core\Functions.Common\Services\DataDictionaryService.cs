﻿using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using LinqToDB;
using Serilog;

namespace GCP.Functions.Common.Services
{
    [Function("dataDictionary", "数据字典服务")]
    class DataDictionaryService : BaseService
    {
        public DataDictionaryService() : base()
        {

        }

        public DataDictionaryService(BaseService service) : base(service)
        {
        }

        public DataDictionaryService(FunctionContext context, string solutionId, string projectId) : base(context, solutionId, projectId)
        {
        }

        [Function("getAll", "获取函数据字典清单")]
        public List<LcDataDict> GetAll(string id = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDataDicts
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(id) || a.Id == id)
                        select a).ToList();
            return data;
        }

        [Function("get", "获取数据字典信息")]
        public LcDataDict Get(string id)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDataDicts
                        where a.Id == id &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        select a).FirstOrDefault();
            return data;
        }

        [Function("getByCode", "获取数据字典信息")]
        public LcDataDict GetByCode(string groupCode, string dictCode)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDataDicts
                        where
                        a.DictCode == dictCode &&
                        a.GroupCode == groupCode &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        orderby a.Seq
                        select a).FirstOrDefault();
            return data;
        }

        [Function("getByGroupCode", "获取数据字典信息")]
        public List<LcDataDict> GetByGroupCode(string groupCode)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDataDicts
                        where
                        a.GroupCode == groupCode &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        orderby a.Seq
                        select a).ToList();
            return data;
        }

        [Function("getSysParamDict", "获取计算后的系统参数字典")]
        public async Task<Dictionary<string, object>> GetSysParamDict()
        {
            var cacheKey = $"SystemParamDict_{this.ProjectId}";
            var cacheData = await this.Cache.GetAsync<string>(cacheKey);

            if (!string.IsNullOrEmpty(cacheData.Value))
            {
                try
                {
                    return JsonHelper.Deserialize<Dictionary<string, object>>(cacheData.Value);
                }
                catch (Exception ex)
                {
                    Log.Error("解析缓存数据失败: {ExMessage}, 将从数据库重新加载", ex.Message);
                    // 如果解析失败，移除错误的缓存
                    await this.Cache.RemoveAsync(cacheKey);
                }
            }
            //else
            //{
            //    Log.Information("缓存中未找到系统参数: {CacheKey}, 将从数据库加载", cacheKey);
            //}

            // 缓存中没有数据或解析失败，从数据库加载
            return await CacheCurrentProjectSystemParamDict();
        }

        private Dictionary<string, object> GetCurrentSysParamDict(string solutionId, string projectId)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDataDicts
                        where
                        a.GroupCode == "SYS_PARAM" &&
                        a.State == 1 &&
                        a.SolutionId == solutionId &&
                        a.ProjectId == projectId
                        orderby a.Seq
                        select a).ToList();
            var result = new Dictionary<string, object>();
            foreach (var item in data)
            {
                object value = item.DictValue;

                if (item.DictType == "JSON")
                {
                    try
                    {
                        value = JsonHelper.Deserialize<dynamic>(item.DictValue);
                    }
                    catch
                    {
                        throw new CustomException($"数据字典[{item.DictCode}]格式错误，请检查是否为JSON格式。");
                    }
                }
                result.Add(item.DictCode, value);
            }
            return result;
        }

        [Function("cacheSystemParamDict", "缓存系统参数字典", startupRun: true)]
        [Job(JobId = "CacheSystemParamDict", JobCron = "0 0/5 * * * ?", JobName = "缓存系统参数字典")]
        public async Task CacheSystemParamDict()
        {
            await using var db = this.GetDb();
            var data = from a in db.LcProjects
                       where a.State == 1
                       select a;

            foreach (var item in data)
            {
                await CacheCurrentProjectSystemParamDict(item.SolutionId, item.Id);
            }
        }

        /// <summary>
        /// 更新当前项目的系统参数字典缓存
        /// </summary>
        /// <param name="solutionId">解决方案ID，默认使用当前上下文的</param>
        /// <param name="projectId">项目ID，默认使用当前上下文的</param>
        /// <returns>缓存是否更新成功</returns>
        private async Task<Dictionary<string, object>> CacheCurrentProjectSystemParamDict(string solutionId = null, string projectId = null)
        {
            solutionId ??= this.SolutionId;
            projectId ??= this.ProjectId;

            var cacheKey = $"SystemParamDict_{projectId}";
            var dict = GetCurrentSysParamDict(solutionId, projectId);
            var jsonValue = JsonHelper.Serialize(dict);

            await this.Cache.RemoveAsync(cacheKey);
            await this.Cache.SetAsync(cacheKey, jsonValue, TimeSpan.FromMinutes(10));

            return dict;
        }

        [Function("getOptionsByGroupCode", "根据组获取选项列表")]
        public List<OptionVO> GetOptionsByGroupCode(string groupCode)
        {
            var list = GetByGroupCode(groupCode);
            return list.Select(t => new OptionVO { Id = t.Id, Label = t.DictName, Value = t.DictCode, Description = t.DictValue }).ToList();
        }

        [Function("saveListByGroup", "保存数据字典信息")]
        public void SaveListByGroup(string groupCode, List<LcDataDict> data)
        {
            using var db = this.GetDb();
            var oldData = db.LcDataDicts.Where(t => t.GroupCode == groupCode && t.SolutionId == this.SolutionId && t.ProjectId == this.ProjectId).ToList();

            db.BeginTransaction();
            short seq = 1;
            foreach (var item in data)
            {
                if (string.IsNullOrEmpty(item.DictCode) || string.IsNullOrEmpty(item.DictValue))
                {
                    throw new Exception("字典代码或字典值不能为空");
                }

                LcDataDict oldItem = null;
                if (string.IsNullOrEmpty(item.Id))
                {
                    oldItem = oldData.FirstOrDefault(t => t.DictCode == item.DictCode);
                    if (oldItem == null)
                    {
                        item.SolutionId = this.SolutionId;
                        item.ProjectId = this.ProjectId;
                        item.GroupCode = groupCode;
                        item.Seq = seq;
                        this.InsertData(item, db);
                        continue;
                    }
                }

                if (item.GroupCode != groupCode)
                {
                    throw new Exception("请勿添加其他数据字典分组数据");
                }
                oldItem ??= oldData.FirstOrDefault(t => t.Id == item.Id) ?? throw new Exception("数据字典不存在 ID: " + item.Id);
                oldItem.Seq = seq;
                oldItem.DictCode = item.DictCode;
                oldItem.DictName = item.DictName;
                oldItem.DictValue = item.DictValue;
                this.UpdateData(item, db);

                seq++;
            }

            db.CommitTransaction();
        }

        [Function("getVariablesByDirCode", "根据目录编码获取变量列表")]
        public List<LcDataDict> GetVariablesByDirCode(string dirId)
        {
            using var db = this.GetDb();

            // 先获取目录信息
            var dir = db.LcDirTrees.FirstOrDefault(d =>
                d.Id == dirId &&
                d.State == 1 &&
                d.SolutionId == this.SolutionId &&
                d.DirType == "D");

            if (dir == null)
            {
                throw new Exception("目录不存在");
            }

            // 获取该目录下的所有变量
            var variables = (from a in db.LcDataDicts
                             where a.State == 1 &&
                             a.SolutionId == this.SolutionId &&
                             a.ProjectId == this.ProjectId &&
                             a.DirCode == dirId
                             orderby a.Seq
                             select a).ToList();

            return variables;
        }

        [Function("save", "保存单条数据字典")]
        public async Task<string> Save(LcDataDict data)
        {
            await using var db = this.GetDb();

            await db.BeginTransactionAsync();
            if (!string.IsNullOrEmpty(data.Id))
            {
                var exists = db.LcDataDicts.FirstOrDefault(x =>
                    x.Id == data.Id &&
                    x.SolutionId == this.SolutionId &&
                    x.ProjectId == this.ProjectId);

                if (exists != null)
                {
                    // 更新
                    exists.State = 1;
                    exists.DictCode = data.DictCode;
                    exists.DictName = data.DictName;
                    exists.DictValue = data.DictValue;
                    exists.DictType = data.DictType;
                    exists.Description = data.Description;
                    exists.GroupCode = data.GroupCode;
                    this.UpdateData(exists, db);
                }
                else
                {
                    throw new Exception("数据字典id不存在");
                }
            }
            else
            {
                await db.LcDataDicts.DeleteAsync(t => t.SolutionId == this.SolutionId && t.ProjectId == this.ProjectId && t.GroupCode == data.GroupCode && t.DirCode == data.DirCode && t.State == 0);

                data.SolutionId = this.SolutionId;
                data.ProjectId = this.ProjectId;
                // 新增
                this.InsertData(data, db);
            }
            await db.CommitTransactionAsync();

            // 只更新当前项目的缓存
            if (data.GroupCode == "SYS_PARAM")
            {
                await CacheCurrentProjectSystemParamDict(this.SolutionId, this.ProjectId);
            }
            return data.Id;
        }

        [Function("remove", "删除单条数据字典")]
        public async Task<bool> Remove(string id)
        {
            await using var db = this.GetDb();

            var exists = db.LcDataDicts.FirstOrDefault(x =>
                x.Id == id &&
                x.State == 1 &&
                x.SolutionId == this.SolutionId);

            if (exists != null)
            {
                await db.BeginTransactionAsync();
                if (!string.IsNullOrEmpty(exists.DirCode))
                {
                    await db.LcDirTrees.DeleteAsync(t => t.LeafId == exists.Id && t.IsLeaf == 'Y' && t.SolutionId == this.SolutionId);
                }

                exists.State = 0;
                this.UpdateData(exists, db);
                await db.CommitTransactionAsync();

                // 只在删除系统参数时更新缓存
                if (exists.GroupCode == "SYS_PARAM")
                {
                    await CacheCurrentProjectSystemParamDict(this.SolutionId, this.ProjectId);
                }
                return true;
            }

            return false;
        }
    }
}
