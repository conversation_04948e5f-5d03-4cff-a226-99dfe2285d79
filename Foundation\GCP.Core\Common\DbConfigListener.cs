﻿using GCP.DataAccess;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Serilog;

//using Nacos.V2;
using System.Text.RegularExpressions;

namespace GCP.Common
{
    //class DbConfigListener : IListener
    class DbConfigListener
    {
        Debouncer _debouncer;
        IConfiguration _configuration;

        public DbConfigListener(IConfiguration configuration)
        {
            _configuration = configuration;
            _debouncer = new Debouncer(TimeSpan.FromSeconds(1));
            WatchConfigChanges();
        }

        public void WatchConfigChanges()
        {
            ChangeToken.OnChange(
                _configuration.GetReloadToken,
                () =>
                {
                    _debouncer.DebounceAsync(() =>
                    {
                        UpdateDbSettings();
                        return Task.CompletedTask;
                    });
                }
            );
        }

        public void UpdateDbSettings()
        {
            var jdbcUrl = _configuration["dbSettings:DataSource:Default:JdbcUrl"]?.ToString();
            if (jdbcUrl != null)
            {
                var jdbcUrlStr = jdbcUrl.ToString();
                var pwd = _configuration["dbSettings:DataSource:Default:Username"].ToString();
                var username = _configuration["dbSettings:DataSource:Default:Password"].ToString();
                ResolveJdbcUrl(jdbcUrlStr, username, pwd);
            }
            else
            {
                DbSettings.DefaultConnectionString = _configuration["dbSettings:DataSource:Default:ConnectionString"].ToString();
                DbSettings.DefaultDbProvider = _configuration["dbSettings:DataSource:Default:ProviderName"].ToString();
            }

            SqlLogSettings.Load(_configuration);

            var db = new DbContext();
            using (var conn = db.CreateConnection())
            {
                Log.Information("数据库配置已载入, 数据库类型：{DbProvider}, 服务器地址：{DataSource}, 数据库：{Database}", DbSettings.DefaultDbProvider, conn.DataSource, conn.Database);
                try
                {
                    conn.Open();
                    DbSettings.DbVersion = conn.ServerVersion;
                    Log.Information("数据库已连接");

                }
                catch (Exception ex)
                {
                    Log.Error("数据库连接失败", ex);
                }
            }
        }

        private void ResolveJdbcUrl(string jdbcUrl, string username, string pwd)
        {
            if (!jdbcUrl.StartsWith("jdbc:")) return;
            jdbcUrl = jdbcUrl.Substring(5);
            var dbType = jdbcUrl.Substring(0, jdbcUrl.IndexOf(":")).ToLower();

            var ip = "";
            var database = "";
            Match matchs;
            switch (dbType)
            {
                case "mysql":
                    matchs = Regex.Match(jdbcUrl, @"//(.+)/(.+)\?", RegexOptions.IgnoreCase);
                    ip = matchs.Groups[1].Value;
                    database = matchs.Groups[2].Value;
                    DbSettings.DefaultConnectionString = $"Server={ip};Database={database};User={username};Password=*****;Charset=utf8;Convert Zero Datetime=True;Port=3306;SslMode=None;AllowLoadLocalInfile=true;";
                    DbSettings.DbProviderType = DbProviderType.MySql;
                    break;
                case "oracle":
                    matchs = Regex.Match(jdbcUrl, "//(.+)/(.+)", RegexOptions.IgnoreCase);
                    ip = matchs.Groups[1].Value;
                    database = matchs.Groups[2].Value;
                    DbSettings.DefaultConnectionString = $"user id={username};password=*****;data source={ip}/{database}";
                    DbSettings.DbProviderType = DbProviderType.Oracle;
                    break;
                case "sqlserver":
                    matchs = Regex.Match(jdbcUrl, "//(.+);.*databaseName=(.+)", RegexOptions.IgnoreCase);
                    ip = matchs.Groups[1].Value;
                    database = matchs.Groups[2].Value;
                    DbSettings.DefaultConnectionString = $"server={ip};database={database};uid={username};pwd=*****;";
                    DbSettings.DbProviderType = DbProviderType.SqlServer;
                    break;
                default:
                    return;
            }
        }
    }
}
