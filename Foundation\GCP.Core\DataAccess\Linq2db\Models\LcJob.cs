// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	[Table("lc_job")]
	public class LcJob : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"                      , CanBeNull = false, IsPrimaryKey = true)] public string    Id                     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                                     )] public DateTime  TimeCreate             { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"                 , CanBeNull = false                     )] public string    Creator                { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                                   )] public DateTime? TimeModified           { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                                        )] public string?   Modifier               { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                           )] public short     State                  { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"             , CanBeNull = false                     )] public string    SolutionId             { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"              , CanBeNull = false                     )] public string    ProjectId              { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:任务名称
		/// </summary>
		[Column("JOB_NAME"                , CanBeNull = false                     )] public string    JobName                { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:任务组
		/// </summary>
		[Column("JOB_GROUP"               , CanBeNull = false                     )] public string    JobGroup               { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                                     )] public string?   Description            { get; set; } // varchar(200)
		/// <summary>
		/// Description:触发器表达式
		/// </summary>
		[Column("CRON_EXPRESSION"                                                 )] public string?   CronExpression         { get; set; } // varchar(200)
		/// <summary>
		/// Description:状态 0：暂停，1：运行
		/// </summary>
		[Column("STATUS"                                                          )] public short?    Status                 { get; set; } // smallint
		/// <summary>
		/// Description:开始时间
		/// </summary>
		[Column("JOB_BEGIN_TIME"                                                  )] public DateTime? JobBeginTime           { get; set; } // datetime
		/// <summary>
		/// Description:结束时间
		/// </summary>
		[Column("JOB_END_TIME"                                                    )] public DateTime? JobEndTime             { get; set; } // datetime
		/// <summary>
		/// Description:日志保留天数
		/// </summary>
		[Column("LOG_RETENTION_DAYS"                                              )] public short?    LogRetentionDays       { get; set; } // smallint
		/// <summary>
		/// Description:是否允许并（1:允许）
		/// </summary>
		[Column("ALLOW_PARALLELIZATION"                                           )] public short?    AllowParallelization   { get; set; } // smallint
		/// <summary>
		/// Description:允许重试（1:允许）
		/// </summary>
		[Column("ALLOW_RETRY"                                                     )] public short?    AllowRetry             { get; set; } // smallint
		/// <summary>
		/// Description:重试次数
		/// </summary>
		[Column("RETRY_COUNT"                                                     )] public short?    RetryCount             { get; set; } // smallint
		/// <summary>
		/// Description:重试间时间（秒）
		/// </summary>
		[Column("RETRY_DELAYS_IN_SECONDS"                                         )] public short?    RetryDelaysInSeconds   { get; set; } // smallint
		/// <summary>
		/// Description:允许错邮件通知（1:允许）
		/// </summary>
		[Column("ALLOW_ERROR_NOTIFICATION"                                        )] public short?    AllowErrorNotification { get; set; } // smallint
		/// <summary>
		/// Description:通知邮件地址
		/// </summary>
		[Column("NOTIFICATION_EMAIL"                                              )] public string?   NotificationEmail      { get; set; } // varchar(500)
		/// <summary>
		/// Description:函数ID
		/// </summary>
		[Column("FUNCTION_ID"                                                     )] public string?   FunctionId             { get; set; } // varchar(36)
		/// <summary>
		/// 异常停止（1:下次运行开始时间不变）
		/// </summary>
		[Column("EXCEPTION_STOP"                                                  )] public short?    ExceptionStop          { get; set; } // smallint
		/// <summary>
		/// 下次开始时间
		/// </summary>
		[Column("NEXT_BEGIN_TIME"                                                 )] public DateTime? NextBeginTime          { get; set; } // datetime
		/// <summary>
		/// 超时时间（秒）
		/// </summary>
		[Column("TIMEOUT_IN_SECONDS"                                              )] public short?    TimeoutInSeconds       { get; set; } // smallint
	}
}
