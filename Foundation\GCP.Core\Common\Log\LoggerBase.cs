﻿using System.Threading.Channels;
using Serilog;

namespace GCP.Common
{
    public abstract class LoggerBase<T> : IDisposable
    {
        protected Channel<T> channel { get; set; }
        private CancellationTokenSource tokenSource { get; set; }
        private CancellationToken cancellationToken { get; set; }

        protected int BufferSize { get; set; } = 340;

        public LoggerBase()
        {
        }

        public LoggerBase(int capacity)
        {
            Initialize(capacity);
        }

        protected void Initialize(int capacity = 340)
        {
            if (capacity <= 0)
            {
                channel = Channel.CreateUnbounded<T>(new UnboundedChannelOptions()
                {
                    SingleWriter = false,    // 支持多生产者
                    SingleReader = true      // 单消费者降低竞争
                });
            }
            else
            {
                channel = Channel.CreateBounded<T>(new BoundedChannelOptions(capacity)
                {
                    // 超过数量 移除老数据
                    FullMode = BoundedChannelFullMode.DropOldest
                });
            }
            tokenSource = new CancellationTokenSource();
            cancellationToken = tokenSource.Token;
        }

        public virtual async Task Write(T message)
        {
            await channel.Writer.WriteAsync(message, cancellationToken);
        }

        public virtual async Task Write(List<T> list)
        {
            foreach (var item in list)
            {
                await Write(item);
            }
        }

        public IAsyncEnumerable<T> ReadAllAsync(CancellationToken token = default)
        {
            return channel.Reader.ReadAllAsync(token == CancellationToken.None ? cancellationToken : token);
            //while (await channel.Reader.WaitToReadAsync(cancellationToken))
            //{
            //    while (channel.Reader.TryRead(out var item))
            //    {
            //        yield return item;
            //    }
            //}
        }

        protected async Task StartConsumer(Func<List<T>, Task> batchAction)
        {
            // 不再需要Timer和SemaphoreSlim，逻辑更清晰
            var localBuffer = new List<T>(BufferSize);

            // 只要通道没有被关闭且没有收到取消请求，就持续运行
            while (!cancellationToken.IsCancellationRequested && await channel.Reader.WaitToReadAsync(cancellationToken))
            {
                try
                {
                    // 开始一个新的批处理，首先尝试读取一个元素
                    if (channel.Reader.TryRead(out T firstItem))
                    {
                        localBuffer.Add(firstItem);

                        // “贪婪地”拉取通道中所有可用的日志，直到缓冲区满
                        // 这是为了形成更大的批次，提高处理效率
                        while (localBuffer.Count < BufferSize && channel.Reader.TryRead(out T nextItem))
                        {
                            localBuffer.Add(nextItem);
                        }
                    }

                    // 如果缓冲区中有任何数据，则执行批量操作
                    if (localBuffer.Count > 0)
                    {
                        await batchAction(localBuffer).ConfigureAwait(false);
                        localBuffer.Clear(); // 为下一个批次清空缓冲区
                    }
                }
                catch (OperationCanceledException)
                {
                    Log.Debug("当CancellationToken被触发时，捕获异常并退出循环");
                    // 当CancellationToken被触发时，捕获异常并退出循环
                    break;
                }
                catch (Exception ex)
                {
                    // 记录批处理操作中可能发生的任何错误，但保持消费者继续运行
                    Log.Error(ex, "日志批处理时发生错误，但消费者将继续运行。");
                    // 在继续之前可以增加一个小的延迟，以防止在持续失败的情况下快速循环
                    await Task.Delay(1000, cancellationToken);
                }
            }

            // 循环结束后，检查缓冲区中是否还有剩余的日志需要处理
            if (localBuffer.Count > 0)
            {
                Log.Debug("应用程序关闭中，正在处理最后一批 {count} 条日志...", localBuffer.Count);
                try
                {
                    await batchAction(localBuffer).ConfigureAwait(false);
                    localBuffer.Clear();
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "处理最后一批日志时失败。");
                }
            }
        }

        public virtual void Dispose()
        {
            try
            {
                // 处理通道
                if (channel != null)
                {
                    channel.Writer.Complete();
                    // 等待所有数据读取完毕
                    channel.Reader.Completion.GetAwaiter().GetResult();
                }

                tokenSource?.Cancel();
                tokenSource?.Dispose();
                tokenSource = null;

                channel = null;
            }
            catch (Exception ex)
            {
                // 确保异常不会阻止资源释放
                Log.Error(ex, "释放SqlLogger资源时发生错误");
            }
        }
    }
}
