﻿using LinqToDB;

namespace GCP.DataAccess
{
    public partial class GcpDb : DbBase
    {
        public GcpDb() : base()
        {
        }

        public GcpDb(string providerName, string connectionString) : base(providerName, connectionString)
        {
        }

        public DateTime GetDbTime()
        {
            return this.Select(() => Sql.CurrentTimestamp);
        }
    }

    public partial class GcpFlowDb : DbBase
    {
        public GcpFlowDb() : base()
        {
        }

        public GcpFlowDb(string providerName, string connectionString) : base(providerName, connectionString)
        {
        }

        public DateTime GetDbTime()
        {
            return this.Select(() => Sql.CurrentTimestamp);
        }
    }

    public static class DbContextExtension
    {
        public static GcpDb CreateGcpDb(this IDbContext db)
        {
            return CreateGcpDb(db, db.DbProvider.ProviderName, db.ConnectionString);
        }

        public static GcpDb CreateGcpDb(this IDbContext db, string connectionString)
        {
            return CreateGcpDb(db, db.DbProvider.ProviderName, connectionString);
        }

        public static GcpDb CreateGcpDb(this IDbContext db, string providerName, string connectionString)
        {
            return new GcpDb(providerName, connectionString);
        }

        public static GcpFlowDb CreateGcpFlowDb(this IDbContext db)
        {
            return CreateGcpFlowDb(db, db.DbProvider.ProviderName, db.ConnectionString);
        }

        public static GcpFlowDb CreateGcpFlowDb(this IDbContext db, string connectionString)
        {
            return CreateGcpFlowDb(db, db.DbProvider.ProviderName, connectionString);
        }

        public static GcpFlowDb CreateGcpFlowDb(this IDbContext db, string providerName, string connectionString)
        {
            return new GcpFlowDb(providerName, connectionString);
        }
    }
}
