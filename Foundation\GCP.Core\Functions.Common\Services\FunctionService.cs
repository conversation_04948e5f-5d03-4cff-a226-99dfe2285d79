﻿using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Builder;
using GCP.Functions.Common.Models;

namespace GCP.Functions.Common.Services
{
    [Function("function", "函数服务")]
    internal class FunctionService : BaseService
    {
        [Function("getAll", "获取函数清单")]
        public List<LcFunction> GetAll(string id = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcFunctions
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(id) || a.Id == id)
                        select a).ToList();
            return data;
        }

        [Function("get", "获取函数信息")]
        public LcFunction Get(string id)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcFunctions
                        where a.Id == id &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        select a).FirstOrDefault();
            return data;
        }

        [Function("getByIds", "获取函数信息")]
        public List<OptionVO> GetByIds(string[] ids)
        {
            return FunctionCompiler.DicFunction.Where(t => ids.Contains(t.Key))
                .Select(t => new OptionVO { Value = t.Key, Label = t.Value.FunctionName }).ToList();
        }

        [Function("getVersion", "获取函数版本")]
        public long? GetVersion(string id)
        {
            return Get(id)?.UseVersion;
        }

    }
}
