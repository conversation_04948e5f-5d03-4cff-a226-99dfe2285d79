using GCP.Common;
using GCP.Functions.Common.ScriptExtensions;
using System.Linq.Expressions;
using System.Reflection;

namespace GCP.Functions.Common.VisualFunction
{
    /// <summary>
    /// 可视化函数表达式编译引擎
    /// 专门用于将函数链编译为高性能的表达式树
    /// </summary>
    public class VisualFunctionExpressionEngine
    {
        private readonly JavascriptUtils _utils;
        private static readonly Dictionary<string, Func<Dictionary<string, object>, JavascriptUtils, object>> _compiledCache 
            = new Dictionary<string, Func<Dictionary<string, object>, JavascriptUtils, object>>();

        public VisualFunctionExpressionEngine(JavascriptUtils utils)
        {
            _utils = utils;
        }

        /// <summary>
        /// 执行编译后的表达式
        /// </summary>
        public object Execute(List<VisualFunctionStep> steps, Dictionary<string, object> inputData)
        {
            var cacheKey = GenerateCacheKey(steps);
            
            if (!_compiledCache.TryGetValue(cacheKey, out var compiledFunc))
            {
                compiledFunc = CompileToExpression(steps);
                _compiledCache[cacheKey] = compiledFunc;
            }

            return compiledFunc(inputData, _utils);
        }

        /// <summary>
        /// 生成缓存键
        /// </summary>
        private string GenerateCacheKey(List<VisualFunctionStep> steps)
        {
            var keyParts = steps.OrderBy(s => s.Order).Select(step => 
                $"{step.FunctionName}({string.Join(",", step.Parameters.Select(p => $"{p.Type}:{p.Value}"))})");
            return string.Join("|", keyParts);
        }

        /// <summary>
        /// 将函数步骤编译为表达式
        /// </summary>
        private Func<Dictionary<string, object>, JavascriptUtils, object> CompileToExpression(List<VisualFunctionStep> steps)
        {
            if (steps == null || steps.Count == 0)
            {
                return (inputData, utils) => inputData;
            }

            // 验证所有步骤都是内置函数
            if (steps.Any(s => s.FunctionType != VisualFunctionType.Builtin))
            {
                throw new NotSupportedException("表达式编译目前只支持内置函数");
            }

            // 参数：inputData, utils
            var inputDataParam = Expression.Parameter(typeof(Dictionary<string, object>), "inputData");
            var utilsParam = Expression.Parameter(typeof(JavascriptUtils), "utils");
            
            // 变量字典，用于存储中间结果
            var variables = new Dictionary<string, ParameterExpression>();
            var expressions = new List<Expression>();

            // 为每个步骤生成表达式
            foreach (var step in steps.OrderBy(s => s.Order))
            {
                var stepExpression = BuildStepExpression(step, inputDataParam, utilsParam, variables);
                
                // 创建变量来存储步骤结果
                var resultVar = !string.IsNullOrEmpty(step.OutputVariable) 
                    ? step.OutputVariable 
                    : $"step_{step.Order}_result";
                
                if (!variables.ContainsKey(resultVar))
                {
                    var varExpression = Expression.Variable(typeof(object), resultVar);
                    variables[resultVar] = varExpression;
                }
                
                // 赋值表达式
                var assignExpression = Expression.Assign(variables[resultVar], stepExpression);
                expressions.Add(assignExpression);
            }

            // 返回最后一个变量的值
            Expression returnExpression;
            if (variables.Count > 0)
            {
                var lastStep = steps.OrderBy(s => s.Order).Last();
                var lastResultVar = !string.IsNullOrEmpty(lastStep.OutputVariable) 
                    ? lastStep.OutputVariable 
                    : $"step_{lastStep.Order}_result";
                returnExpression = variables[lastResultVar];
            }
            else
            {
                returnExpression = inputDataParam;
            }

            // 创建块表达式
            var blockExpression = Expression.Block(
                variables.Values,
                expressions.Concat(new[] { returnExpression })
            );

            // 编译为委托
            var lambda = Expression.Lambda<Func<Dictionary<string, object>, JavascriptUtils, object>>(
                blockExpression, inputDataParam, utilsParam);
            
            return lambda.Compile();
        }

        /// <summary>
        /// 为单个步骤构建表达式
        /// </summary>
        private Expression BuildStepExpression(VisualFunctionStep step, ParameterExpression inputDataParam, 
            ParameterExpression utilsParam, Dictionary<string, ParameterExpression> variables)
        {
            // 获取方法信息
            var method = typeof(JavascriptUtils).GetMethod(step.FunctionName);
            if (method == null)
                throw new CustomException($"未找到内置函数: {step.FunctionName}");

            // 构建参数表达式
            var parameterExpressions = new List<Expression>();
            var methodParams = method.GetParameters();

            for (int i = 0; i < step.Parameters.Count && i < methodParams.Length; i++)
            {
                var param = step.Parameters[i];
                var methodParam = methodParams[i];
                var paramExpression = BuildParameterExpression(param, inputDataParam, variables);
                
                // 类型转换
                if (paramExpression.Type != methodParam.ParameterType)
                {
                    paramExpression = Expression.Convert(paramExpression, methodParam.ParameterType);
                }
                
                parameterExpressions.Add(paramExpression);
            }

            // 处理可变参数
            if (method.GetParameters().LastOrDefault()?.GetCustomAttribute<ParamArrayAttribute>() != null)
            {
                // 如果是params参数，需要特殊处理
                var paramsType = methodParams.Last().ParameterType.GetElementType();
                var remainingParams = step.Parameters.Skip(methodParams.Length - 1)
                    .Select(p => BuildParameterExpression(p, inputDataParam, variables))
                    .Select(e => Expression.Convert(e, paramsType))
                    .ToArray();
                
                if (remainingParams.Length > 0)
                {
                    var arrayExpression = Expression.NewArrayInit(paramsType, remainingParams);
                    parameterExpressions[parameterExpressions.Count - 1] = arrayExpression;
                }
            }

            // 调用方法表达式
            var methodCallExpression = Expression.Call(utilsParam, method, parameterExpressions);
            
            return methodCallExpression;
        }

        /// <summary>
        /// 构建参数表达式
        /// </summary>
        private Expression BuildParameterExpression(VisualFunctionParameter param, 
            ParameterExpression inputDataParam, Dictionary<string, ParameterExpression> variables)
        {
            return param.Type switch
            {
                VisualParameterType.Text => Expression.Constant(param.Value),
                VisualParameterType.Variable => BuildVariableExpression(param.Value?.ToString(), inputDataParam),
                VisualParameterType.PreviousResult => BuildPreviousResultExpression(param.Value?.ToString(), variables),
                _ => Expression.Constant(param.Value)
            };
        }

        /// <summary>
        /// 构建变量访问表达式
        /// </summary>
        private Expression BuildVariableExpression(string variablePath, ParameterExpression inputDataParam)
        {
            if (string.IsNullOrEmpty(variablePath))
                return Expression.Constant(null);

            // 处理 _data.xxx.xxx 格式的变量路径
            if (variablePath.StartsWith("_data."))
            {
                var path = variablePath[6..]; // 移除 "_data." 前缀
                return BuildPathAccessExpression(inputDataParam, path);
            }

            // 处理直接变量名 - 从inputData字典中获取
            return BuildDictionaryAccessExpression(inputDataParam, variablePath);
        }

        /// <summary>
        /// 构建上一步结果访问表达式
        /// </summary>
        private Expression BuildPreviousResultExpression(string resultKey, Dictionary<string, ParameterExpression> variables)
        {
            if (string.IsNullOrEmpty(resultKey) || !variables.TryGetValue(resultKey, out var variable))
                return Expression.Constant(null);

            return variable;
        }

        /// <summary>
        /// 构建字典访问表达式
        /// </summary>
        private Expression BuildDictionaryAccessExpression(Expression dictExpression, string key)
        {
            var tryGetValueMethod = typeof(Dictionary<string, object>).GetMethod("TryGetValue");
            var valueVar = Expression.Variable(typeof(object), "dictValue");
            var keyExpression = Expression.Constant(key);
            
            var tryGetCall = Expression.Call(dictExpression, tryGetValueMethod, keyExpression, valueVar);
            var conditionalExpression = Expression.Condition(
                tryGetCall,
                valueVar,
                Expression.Constant(null, typeof(object))
            );

            return Expression.Block(new[] { valueVar }, conditionalExpression);
        }

        /// <summary>
        /// 构建路径访问表达式（用于 _data.xxx.xxx 格式）
        /// </summary>
        private Expression BuildPathAccessExpression(Expression dataExpression, string path)
        {
            if (string.IsNullOrEmpty(path))
                return dataExpression;

            var parts = path.Split('.');
            var current = dataExpression;

            foreach (var part in parts)
            {
                current = BuildDictionaryAccessExpression(current, part);
            }

            return current;
        }

        /// <summary>
        /// 清除编译缓存
        /// </summary>
        public static void ClearCache()
        {
            _compiledCache.Clear();
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        public static Dictionary<string, object> GetCacheStats()
        {
            return new Dictionary<string, object>
            {
                ["CacheCount"] = _compiledCache.Count,
                ["CacheKeys"] = _compiledCache.Keys.ToList()
            };
        }
    }
}
