<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="150px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="数据源" prop="dataSource">
              <data-source-select v-model="formData.dataSource" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="是否分页" prop="isPaging">
              <t-switch v-model="formData.isPaging" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="查询总数" prop="hasTotal">
              <t-switch v-model="formData.hasTotal" />
            </t-form-item>
          </t-col>
          <t-col v-if="formData.isPaging" :span="6">
            <t-form-item label="当前页码" prop="pageIndex">
              <value-input
                v-model:data-value="formData.pageIndex"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col v-if="formData.isPaging" :span="6">
            <t-form-item label="每页条数" prop="pageSize">
              <value-input
                v-model:data-value="formData.pageSize"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <t-tabs v-model="formData.operateType">
          <t-tab-panel value="configure" label="配置">
            <t-collapse :default-value="[0]" class="collapse-container">
              <t-collapse-panel
                v-for="(item, index) in formData.configureInfo"
                :key="item.id"
                :header="
                  index == 0
                    ? '主表 ' + (item.tableData?.tableName || '')
                    : '关联表 ' + (item.tableData?.tableName || '')
                "
              >
                <data-source-table
                  v-model:data="item.tableData"
                  operation-type="Query"
                  :data-source-id="formData.dataSource"
                ></data-source-table>
              </t-collapse-panel>
            </t-collapse>
          </t-tab-panel>
          <t-tab-panel value="sql" label="SQL查询">
            <action-form-title title="SQL语句">
              <!-- <t-button theme="default" variant="outline" @click="onClickFormatSql">格式化SQL</t-button> -->
              <!-- <t-button theme="default" variant="outline" @click="onClickTestSql">测试SQL</t-button> -->
            </action-form-title>
            <editor
              ref="sqlEditorRef"
              v-model:value="formData.sqlInfo.sql"
              class="sql-editor"
              language="sql"
              style="height: 200px"
            ></editor>
            <action-form-title title="输入参数" tip="自动根据SQL语句【:参数名 | @参数名】生成参数列表">
            </action-form-title>
            <t-table
              class="small-table"
              size="small"
              row-key="paramName"
              :columns="sqlInputTableColumns"
              :data="formData.sqlInfo.parameters"
            >
              <template #paramName="{ row }">
                <t-input v-model="row.paramName" size="small" borderless disabled></t-input>
              </template>
              <template #paramValue="{ row }">
                <value-input v-model:data-value="row.paramValue"></value-input>
              </template>
            </t-table>
          </t-tab-panel>
        </t-tabs>

        <!-- 输出配置 -->
        <action-form-title title="输出配置"> </action-form-title>
        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="输出方式" prop="outputConfig.type">
              <t-select v-model="formData.outputConfig.type" placeholder="请选择输出方式" :disabled="formData.hasTotal">
                <t-option value="list" label="列表（默认）" />
                <t-option value="dictionary" label="字典" />
                <t-option value="single" label="单条记录" />
                <t-option value="count" label="记录数量" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col v-if="formData.hasTotal" :span="6">
            <t-alert theme="info" message="查询总数开启时，输出方式固定为列表格式：{list: [], total: number}" />
          </t-col>
        </t-row>

        <!-- 字典配置 -->
        <div v-if="formData.outputConfig.type === 'dictionary' && !formData.hasTotal" class="dictionary-config">
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="键字段" prop="outputConfig.dictionaryConfig.keyColumns">
                <t-select
                  v-model="formData.outputConfig.dictionaryConfig.keyColumns"
                  :options="columnOptions"
                  placeholder="请选择键字段"
                  multiple
                  clearable
                />
              </t-form-item>
            </t-col>
            <t-col :span="3">
              <t-form-item label="键分隔符" prop="outputConfig.dictionaryConfig.keySeparator">
                <t-input
                  v-model="formData.outputConfig.dictionaryConfig.keySeparator"
                  placeholder="多列组合时的分隔符"
                />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="值字段" prop="outputConfig.dictionaryConfig.valueColumn">
                <t-select
                  v-model="formData.outputConfig.dictionaryConfig.valueColumn"
                  :options="columnOptions"
                  placeholder="请选择值字段"
                  clearable
                  :disabled="formData.outputConfig.dictionaryConfig.useFullObjectAsValue"
                />
              </t-form-item>
            </t-col>
            <t-col :span="9">
              <t-form-item label="使用完整对象作为值" prop="outputConfig.dictionaryConfig.useFullObjectAsValue">
                <t-switch v-model="formData.outputConfig.dictionaryConfig.useFullObjectAsValue" />
                <span class="form-tip">启用后将忽略值字段配置，使用完整记录作为字典的值</span>
              </t-form-item>
            </t-col>
          </t-row>

          <!-- 配置示例 -->
          <div class="config-example">
            <t-alert theme="info" title="配置示例">
              <div>
                <strong>多列组合键示例：</strong><br />
                键字段：['eid', 'oid', 'mo_code']，分隔符：'|'<br />
                生成的键：'E001|O002|MO003'
              </div>
              <div style="margin-top: 8px">
                <strong>当前配置预览：</strong><br />
                <code v-if="dictionaryConfigPreview">{{ dictionaryConfigPreview }}</code>
                <span v-else class="text-placeholder">请完成配置</span>
              </div>
            </t-alert>
          </div>
        </div>

        <action-form-title title="输出参数"> </action-form-title>
        <variable-list
          :key="`output-${formData.outputConfig?.type || 'list'}`"
          v-model:data="currentStep.result"
          :show-root-node="formData.useRoot ?? false"
          :output-type="formData.outputConfig?.type || 'list'"
        >
        </variable-list>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'DataCustomQueryActions',
};
</script>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref, watch } from 'vue';

import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import DataSourceSelect from '@/components/action-panel/DataSourceSelect.vue';
import DataSourceTable from '@/components/action-panel/DataSourceTable.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';
import Editor from '@/components/editor/index.vue';

import { useDataQueryStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataQueryStore = useDataQueryStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataQueryStore.updateState();
  },
  {
    immediate: true,
  },
);

const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData, forEachData } = storeToRefs(dataQueryStore);

const sqlInputTableColumns = ref([
  {
    colKey: 'paramName',
    title: '参数名',
    width: 150,
  },
  {
    colKey: 'paramValue',
    title: '参数值',
    width: 300,
  },
]);

// 列选项（用于字典配置）
const columnOptions = computed(() => {
  const columns = formData.value.configureInfo?.[0]?.tableData?.columns || [];
  return columns.map((column) => ({
    label: `${column.columnName}${column.description ? ` (${column.description})` : ''}`,
    value: column.columnName,
  }));
});

// 字典配置预览
const dictionaryConfigPreview = computed(() => {
  const config = formData.value.outputConfig?.dictionaryConfig;
  if (!config || !config.keyColumns?.length) {
    return '';
  }

  const keyExample = config.keyColumns.map((col) => `item.${col}`).join(` + '${config.keySeparator}' + `);
  const valueExample = config.useFullObjectAsValue
    ? 'item (完整对象)'
    : config.valueColumn
      ? `item.${config.valueColumn}`
      : '未配置';

  return `dic[${keyExample}] = ${valueExample}`;
});

watch(
  () => formData.value.sqlInfo?.sql,
  debounce((newValue) => {
    if (formData.value.operateType === 'sql') dataQueryStore.setSqlParameters(newValue);
  }, 300),
);

watch(
  () => formData.value,
  (newValue) => {
    dataQueryStore.setArgs(newValue);
  },
  {
    deep: true,
  },
);

// 监听输出配置变化，立即更新输出参数结构
watch(
  () => formData.value.outputConfig?.type,
  (newType, oldType) => {
    if (newType !== oldType) {
      dataQueryStore.setArgs(formData.value);
    }
  },
  { immediate: false },
);

// 监听查询总数变化，自动设置输出方式
watch(
  () => formData.value.hasTotal,
  (hasTotal) => {
    if (hasTotal) {
      // 当开启查询总数时，强制设置为列表输出
      formData.value.outputConfig.type = 'list';
      dataQueryStore.setArgs(formData.value);
    }
  },
  { immediate: false },
);

// 组件挂载时确保输出参数正确初始化
onMounted(() => {
  dataQueryStore.setArgs(formData.value);
});

const sqlEditorRef = ref(null);
// const onClickFormatSql = () => {
//   sqlEditorRef.value?.formatCode();
// };
// const onClickTestSql = () => {
// };
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}

.sql-editor {
  margin-top: 10px;
}

.collapse-container {
  margin-top: 16px;

  :deep(.t-collapse-panel__content) {
    padding-left: var(--td-comp-paddingLR-l) !important;
  }
}

.dictionary-config {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin: 16px 0;
}

.config-example {
  margin-top: 16px;
}

.form-tip {
  color: #666;
  font-size: 12px;
  margin-left: 8px;
}

.text-placeholder {
  color: #999;
  font-style: italic;
}
</style>
