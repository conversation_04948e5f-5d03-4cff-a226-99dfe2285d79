<template>
  <cmp-container full>
    <cmp-card header="许可证管理">
      <t-form label-align="top" label-width="100px" class="form-container form-container-center">
        <div class="form-item" style="padding: 24px">
          <!-- <div class="form-container-title"></div> -->
          <t-row :gutter="[32, 24]">
            <t-col :span="12">
              <t-alert :theme="isActivated ? 'success' : 'warning'">
                <template #message>
                  <div v-if="isActivated">
                    当前已激活，有效期至 <t-tag size="small" theme="warning">{{ expireDate }}</t-tag>
                  </div>
                  <div v-else>当前系统未激活，请激活系统</div>
                </template>
              </t-alert>
            </t-col>
            <t-col :span="12">
              <t-form-item label="注册码">
                <t-textarea
                  :value="registerCode"
                  readonly
                  :autosize="{ minRows: 5 }"
                  @click="(e) => e.target?.select()"
                />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-space direction="vertical" style="width: 100%">
                <t-form-item label="激活码">
                  <t-textarea v-model="activatedCode" :autosize="{ minRows: 5 }" />
                </t-form-item>
                <t-button block theme="primary" @click="onClickActivateLicense">激活</t-button>
              </t-space>
            </t-col>
          </t-row>
        </div>
      </t-form>
    </cmp-card>
  </cmp-container>
</template>
<script lang="ts">
export default {
  name: 'LicenseManager',
};
</script>
<script setup lang="ts">
import { onActivated, onMounted, ref } from 'vue';

import { api, Services } from '@/api/system';

const registerCode = ref('');
const isActivated = ref(false);
const expireDate = ref('');
const activatedCode = ref('');
onActivated(() => {
  fetchData();
});

onMounted(() => {
  fetchData();
});

const fetchData = async () => {
  await api.run(Services.license).then((res) => {
    registerCode.value = res.registrationCode;
    isActivated.value = res.isActivated;
    expireDate.value = res.expireDate;
  });
};

const onClickActivateLicense = async () => {
  await api
    .run(Services.licenseActivate, {
      registrationCode: activatedCode.value,
    })
    .then(() => {
      activatedCode.value = '';
      fetchData();
    });
};
</script>
<style lang="less" scoped>
@import '@/style/form.less';

.form-container {
  background-color: var(--td-bg-color-container);
}

.form-item {
  width: 676px;
}
</style>
