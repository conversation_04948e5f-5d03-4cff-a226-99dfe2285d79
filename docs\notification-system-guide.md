# 通知系统使用指南

## 概述

本系统集成了EasyNotice通知组件，支持多种通知方式，包括邮件、钉钉、飞书、企业微信等。通过数据库表维护消息通道配置，在动作流程中可以选择对应的消息通道发送通知信息。

## 功能特性

- **多种通知方式**：支持邮件、钉钉、飞书、企业微信四种通知方式
- **数据库配置**：通知通道配置存储在数据库中，支持动态管理
- **发送间隔控制**：支持配置发送间隔，避免频繁发送相同通知
- **工作流集成**：可在动作面板中使用通知动作
- **测试功能**：支持通知通道测试功能

## 数据库表结构

### LC_NOTIFICATION_CHANNEL 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| ID | varchar(36) | 主键ID |
| CHANNEL_NAME | varchar(100) | 通道名称 |
| CHANNEL_TYPE | varchar(20) | 通道类型（Email/Dingtalk/Feishu/Weixin） |
| DESCRIPTION | varchar(500) | 描述 |
| IS_ENABLED | boolean | 是否启用 |
| CONFIG_JSON | varchar(2000) | 配置参数JSON |
| INTERVAL_SECONDS | int | 发送间隔秒数 |
| SOLUTION_ID | varchar(36) | 解决方案ID |
| PROJECT_ID | varchar(36) | 项目ID |

## 通知通道配置

### 1. 邮件通知配置

```json
{
  "host": "smtp.qq.com",
  "port": 465,
  "fromName": "系统通知",
  "fromAddress": "<EMAIL>",
  "password": "授权码",
  "toAddress": ["<EMAIL>", "<EMAIL>"],
  "enableSsl": true
}
```

### 2. 钉钉通知配置

```json
{
  "webHook": "https://oapi.dingtalk.com/robot/send?access_token=xxx",
  "secret": "签名密钥",
  "isAtAll": false,
  "atMobiles": ["13800138000"]
}
```

### 3. 飞书通知配置

```json
{
  "webHook": "https://open.feishu.cn/open-apis/bot/v2/hook/xxx",
  "secret": "签名密钥"
}
```

### 4. 企业微信通知配置

```json
{
  "webHook": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx",
  "mentionedList": ["user1", "user2"],
  "mentionedMobileList": ["13800138000"]
}
```

## API接口

### 通知通道管理

- `POST /api/notificationChannel/getAll` - 获取通知通道列表
- `POST /api/notificationChannel/get` - 获取通知通道详情
- `POST /api/notificationChannel/save` - 保存通知通道
- `POST /api/notificationChannel/delete` - 删除通知通道
- `POST /api/notificationChannel/getChannelTypes` - 获取支持的通道类型
- `POST /api/notificationChannel/testChannel` - 测试通知通道

### 通知动作

- `POST /api/notificationAction/sendNotification` - 发送通知
- `POST /api/notificationAction/sendNotificationWithException` - 发送异常通知
- `POST /api/notificationAction/getAvailableChannels` - 获取可用通知通道

## 前端页面

### 通知通道管理页面

路径：`/service/notification-channels`

功能：
- 查看通知通道列表
- 新增/编辑通知通道
- 测试通知通道
- 删除通知通道

访问方式：在左侧菜单中选择"服务" -> "通知通道"

### 动作面板集成

在动作面板的"日志"分类下新增了两个通知动作：
- **发送通知**：发送普通通知消息
- **发送异常通知**：发送包含异常信息的通知

## 使用步骤

### 1. 配置通知通道

1. 访问通知通道管理页面
2. 点击"新增通道"按钮
3. 填写通道基本信息（名称、类型、描述等）
4. 根据选择的通道类型配置相应参数
5. 保存并测试通道

### 2. 在工作流中使用

1. 在动作面板中选择"日志" -> "发送通知"或"发送异常通知"
2. 选择要使用的通知通道
3. 配置通知标题和内容（支持变量）
4. 保存动作配置

### 3. 测试验证

1. 运行包含通知动作的工作流
2. 检查通知是否正确发送
3. 查看日志确认发送状态

## 注意事项

1. **发送间隔**：相同标题的通知会受到间隔限制，避免频繁发送
2. **通道状态**：只有启用状态的通道才能发送通知
3. **配置验证**：保存通道时会验证配置JSON格式的正确性
4. **权限控制**：通知通道按解决方案和项目隔离
5. **错误处理**：发送失败时会记录错误日志，不会中断工作流执行

## 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP服务器配置
   - 确认邮箱密码或授权码正确
   - 验证网络连接

2. **钉钉/飞书/企业微信发送失败**
   - 检查Webhook地址是否正确
   - 确认机器人是否正常工作
   - 验证签名密钥配置

3. **通知未发送**
   - 检查通道是否启用
   - 确认发送间隔设置
   - 查看系统日志

### 日志查看

系统会记录通知发送的详细日志，包括：
- 发送成功/失败状态
- 错误信息
- 发送时间
- 通道信息

可通过系统日志功能查看相关信息进行故障排除。
