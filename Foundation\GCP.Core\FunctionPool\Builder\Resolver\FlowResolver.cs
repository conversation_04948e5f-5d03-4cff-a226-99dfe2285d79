﻿namespace GCP.FunctionPool.Builder
{
    class FlowResolver
    {
        /// <summary>
        /// 函数流处理器
        /// </summary>
        internal static void Handler(string path, string name, string sourceCode, FunctionInfo funcInfo, FunctionCodeLanguage codeLanguage, Action<FunctionInfo> initFunc = null)
        {
            if (funcInfo == null)
                funcInfo = new FunctionInfo(path, name);

            if (funcInfo.LoadFlow(sourceCode, codeLanguage))
            {
                initFunc?.Invoke(funcInfo);
                FunctionCompiler.DicFunction.AddOrUpdate(path, funcInfo, (key, t) => funcInfo);
            }
        }
    }
}
