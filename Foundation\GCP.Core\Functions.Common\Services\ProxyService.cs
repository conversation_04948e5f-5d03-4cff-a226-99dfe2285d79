﻿using GCP.Common;
using LinqToDB;
using Microsoft.Extensions.DependencyInjection;
using Yarp.ReverseProxy.Configuration;

namespace GCP.Functions.Common.Services
{
    [Function("proxy", "代理服务")]
    internal class ProxyService : BaseService
    {
        public ProxyService() : base()
        {
        }

        [Function(Path = "refresh", Description = "刷新代理", StartupRun = true)]
        public void RefreshProxy()
        {
            var configProvider = this.Context.scope.ServiceProvider.GetRequiredService<InMemoryConfigProvider>();

            var routes = new List<RouteConfig>();
            var clusters = new List<ClusterConfig>();

            using var db = this.GetDb();

            var serverDic = db.LcServers.Where(t => t.State == 1).ToDictionary(t => t.Id, t => t.Address);

            var data = (from a in db.LcApis
                        from b in db.LcApiClusters.InnerJoin(t => t.Id == a.ClusterId)
                        where a.State == 1 && a.State == b.State
                        select new
                        {
                            routeID = a.Id,
                            routePath = a.HttpUrl,
                            routeHosts = a.Hosts,
                            routeHeaders = a.Headers,
                            routeQueryParameters = a.QueryParameters,
                            cluster = b,
                        }
             ).ToList();


            foreach (var item in data)
            {
                if (!routes.Exists(t => t.RouteId == item.routeID))
                {
                    routes.Add(new RouteConfig
                    {
                        RouteId = item.routeID,
                        ClusterId = item.cluster.Id,
                        Match = new RouteMatch
                        {
                            Path = item.routePath,
                            Hosts = item.routeHosts?.Split(','),
                            Headers = JsonHelper.Deserialize<List<RouteHeader>>(item.routeHeaders),
                            QueryParameters = JsonHelper.Deserialize<List<RouteQueryParameter>>(item.routeQueryParameters),
                        }
                    });
                }

                var destDic = new Dictionary<string, DestinationConfig>(StringComparer.OrdinalIgnoreCase);
                foreach (var serverId in item.cluster.Servers?.Split(','))
                {
                    if (serverDic.TryGetValue(serverId, out string address))
                    {
                        destDic.Add(serverId, new DestinationConfig
                        {
                            Address = address
                        });
                    }
                }
                clusters.Add(new ClusterConfig()
                {
                    ClusterId = item.cluster.Id,
                    LoadBalancingPolicy = item.cluster.LoadBalancingPolicy,
                    Destinations = destDic
                });
            }


            if (routes.Count > 0 && clusters.Count > 0)
                configProvider.Update(routes, clusters);
        }

        [Function(Path = "add", Description = "新增代理")]
        public void AddProxy()
        {

        }
    }
}
