﻿using EasyCaching.Core;
using GCP.Common;
using GCP.DataAccess;
using LinqToDB;
using LinqToDB.Linq;
using System.Net.Http.Headers;

namespace GCP.Functions.Common.Services
{
    [Function("gcp", "基础服务", AutoGenerateApi = true, SpaceCharacters = "/")]
    public abstract class BaseService : IFunctionService
    {
        public FunctionContext Context { get; set; }
        public IEasyCachingProvider Cache { get; set; }
        public IDbContext DbContext { get; set; }

        protected BaseService()
        {
            Init();
        }

        protected BaseService(BaseService service)
        {
            Context = service.Context;
            Cache = service.Cache;
            DbContext = service.DbContext;
            _solutionId = service._solutionId;
            _projectId = service._projectId;
        }

        protected BaseService(FunctionContext context, string solutionId, string projectId)
        {
            Context = context;
            DbContext = context.db;
            _solutionId = solutionId;
            _projectId = projectId;
        }

        protected void Init()
        {
            if (Context == null)
            {
                Context = new FunctionContext();
                DbContext = ServiceLocator.Current.GetService(typeof(IDbContext)) as IDbContext;
            }
        }

        protected string userName;
        /// <summary>
        /// 获取当前登录用户名称
        /// </summary>
        protected string UserName
        {
            get
            {
                if (userName == null)
                {
                    LoadUserNameByCache();
                }

                return userName;
            }
        }

        private void LoadUserNameByCache()
        {
            var req = Context.GetHttpRequest();
            if (req == null)
            {
                userName = "sys";
                return;
            }

            try
            {
                var auth = req.Headers.Authorization;
                if (auth.Count > 0)
                {
                    var authValue = AuthenticationHeaderValue.Parse(auth[0] ?? string.Empty);
                    if (authValue is { Scheme: "Bearer" })
                    {
                        var cacheKey = "token_" + authValue.Parameter;
                        if (Cache.Exists(cacheKey))
                        {
                            userName = Cache.Get<string>(cacheKey).Value;
                            return;
                        }
                    }
                }
            }
            catch
            {
                userName = "sys";
                return;
            }

            throw new CustomException("无权限访问", 401);
        }

        protected void SetSolutionIdAndProjectId(string solutionId, string projectId)
        {
            _solutionId = solutionId;
            _projectId = projectId;
        }

        private string _solutionId;
        /// <summary>
        /// 获取当前解决方案ID
        /// </summary>
        protected string SolutionId
        {
            get
            {
                if (_solutionId != null) return _solutionId;
                _solutionId = Context.SolutionId;

                return _solutionId;
            }
        }

        private string _projectId;
        /// <summary>
        /// 获取当前项目ID
        /// </summary>
        protected string ProjectId
        {
            get
            {
                if (_projectId != null) return _projectId;
                _projectId = Context.ProjectId;

                return _projectId;
            }
        }

        protected GcpDb GetDb()
        {
            return DbContext == null ? new GcpDb() : DbContext.CreateGcpDb();
        }

        protected GcpFlowDb GetFlowDb()
        {
            return DbContext == null ? new GcpFlowDb() : DbContext.CreateGcpFlowDb();
        }

        protected string InsertData<T>(T entity, GcpDb db = null) where T : IBaseEntity
        {
            var model = (IBaseEntity)entity;
            if (string.IsNullOrWhiteSpace(model.Id))
            {
                model.Id = TUID.NewTUID().ToString();
            }
            model.Creator = UserName;
            model.Modifier = model.Creator;
            model.State = 1;
            var result = 0;
            if (db == null)
            {
                using var db2 = GetDb();
                model.TimeCreate = db2.GetDbTime();
                model.TimeModified = model.TimeCreate;
                result = db2.Insert((T)model);
            }
            else
            {
                model.TimeCreate = db.GetDbTime();
                model.TimeModified = model.TimeCreate;
                result = db.Insert((T)model);
            }
            return result > 0 ? model.Id : null;
        }

        protected int UpdateData<T>(T entity, GcpDb db = null) where T : IBaseEntity
        {
            var model = (IBaseEntity)entity;
            if (string.IsNullOrWhiteSpace(model?.Id))
            {
                throw new CustomException("Id不能为空");
            }
            model.Modifier = UserName;

            if (db == null)
            {
                using var db2 = GetDb();
                model.TimeModified = db2.GetDbTime();
                return db2.Update((T)model);
            }
            model.TimeModified = db.GetDbTime();
            return db.Update((T)model);
        }

        protected int UpdateData<T>(IUpdatable<T> source) where T : IBaseEntity
        {
            return source
                .Set(t => t.Modifier, UserName)
                .Set(t => t.TimeModified, () => Sql.CurrentTimestamp)
                .Update();
        }
    }
}
