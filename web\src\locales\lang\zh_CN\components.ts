export default {
  isSetup: {
    on: '已启用',
    off: '已停用',
  },
  manage: '管理',
  delete: '删除',
  commonTable: {
    contractName: '合同名称',
    contractNamePlaceholder: '请输入合同名称',
    contractStatus: '合同状态',
    contractStatusPlaceholder: '请输入合同状态',
    contractNum: '合同编号',
    contractNumPlaceholder: '请输入合同编号',
    contractType: '合同类型',
    contractTypePlaceholder: '请选择合同类型',
    contractPayType: '合同支付类型',
    contractAmount: '合同金额',
    operation: '操作',
    detail: '详情',
    delete: '删除',
    placeholder: '请输入内容搜索',
    contractStatusEnum: {
      fail: '审核失败',
      audit: '待审核',
      executing: '履行中',
      pending: '待履行',
      finish: '已完成',
    },
    contractTypeEnum: {
      main: '主合同',
      sub: '子合同',
      supplement: '补充合同',
    },
    reset: '重置',
    query: '查询',
  },
};
