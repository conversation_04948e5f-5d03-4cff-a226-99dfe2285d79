<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="130px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="8">
            <t-form-item label="名称" prop="name">
              <t-input v-model="args.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>

          <t-col :span="12">
            <t-form-item label="允许重试" prop="allowRetry">
              <t-switch v-model="formData.allowRetry" />
            </t-form-item>
          </t-col>

          <t-col :span="12">
            <t-form-item label="重试次数" prop="retryCount">
              <t-input-number v-model="formData.retryCount" :disabled="!formData.allowRetry" />
            </t-form-item>
          </t-col>

          <t-col :span="12">
            <t-form-item label="重试间隔时间(秒)" name="retryDelaysInSeconds">
              <t-input-number v-model="formData.retryDelaysInSeconds" :disabled="!formData.allowRetry" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'TryCatchActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { watch } from 'vue';

import { useActionFlowStore } from '@/components/action-panel/store/index';

import { useTryCatchStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataStore = useTryCatchStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataStore.updateState();
  },
  {
    immediate: true,
  },
);
const { args, tryCatchData: formData } = storeToRefs(dataStore);

watch(
  () => args.value.name,
  (newValue) => {
    dataStore.setArgs();
  },
  {
    deep: true,
  },
);

watch(
  () => formData.value,
  (newValue) => {
    dataStore.setArgs();
  },
  {
    deep: true,
  },
);
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}
</style>
