<template>
  <t-dialog
    v-model:visible="visibleDialog"
    header="项目概览"
    width="800px"
    :footer="false"
    :close-btn="allowClose"
    :close-on-overlay-click="allowClose"
    :close-on-esc-keydown="allowClose"
  >
    <t-row>
      <t-col :span="11">
        <t-input v-model="searchProjectKeyword" placeholder="搜索项目 / 分组名称添加" clearable>
          <template #suffixIcon>
            <search-icon />
          </template>
        </t-input>
      </t-col>
      <t-col :span="1" style="padding-left: 10px">
        <t-button shape="square" variant="dashed" @click="onShowAddDialog">
          <add-icon />
        </t-button>
      </t-col>
    </t-row>
    <t-tabs v-model="currentSolutionId">
      <t-tab-panel
        v-for="item in searchData"
        :key="item.id"
        :label="item.solutionName"
        :value="item.id"
        @remove="onRemoveSolution(item.id)"
      >
        <t-list>
          <t-list-item
            v-for="projectItem in item.projectList"
            :key="projectItem.id"
            class="project-card-item"
            @click="onClickProject(item.id, projectItem)"
          >
            <t-list-item-meta>
              <template #title>
                <div>
                  <span>{{ projectItem.projectName }}</span>
                  <span class="project-card-time">{{ projectItem.timeCreate }}</span>
                </div>
              </template>
              <template #description>
                <div>
                  <span>{{ projectItem.description }}</span>
                  <span class="project-card-active">
                    <check-circle-filled-icon
                      v-if="projectItem.id == userStore.projectId"
                      style="color: var(--td-success-color)"
                    />
                  </span>
                </div>
              </template>
            </t-list-item-meta>
          </t-list-item>
        </t-list>
        <t-button block variant="dashed" class="add-project-btn" @click="onShowAddProjectDialog">添 加 项 目</t-button>
      </t-tab-panel>
    </t-tabs>
  </t-dialog>
  <t-dialog v-model:visible="addSolutionDialog" header="新增分组" @confirm="onAddSolutionConfirm">
    确定新增分类【{{ searchProjectKeyword }}】吗？
  </t-dialog>
  <t-dialog v-model:visible="addProjectDialog" header="新增项目" :footer="false">
    <t-form v-if="projectFormData" :rules="projectFormRules" :data="projectFormData" @submit="onAddProjectConfirm">
      <t-form-item label="分组">
        {{ currentSolution?.solutionName }}
      </t-form-item>
      <t-form-item label="项目名称" name="projectName">
        <t-input v-model="projectFormData.projectName" placeholder="请输入项目名称" />
      </t-form-item>
      <t-form-item label="项目描述" name="description">
        <t-textarea v-model="projectFormData.description" placeholder="请输入项目描述" />
      </t-form-item>
      <t-form-item label="排序" name="seq">
        <t-input-number v-model="projectFormData.seq" theme="column"></t-input-number>
      </t-form-item>
      <t-form-item>
        <t-space size="small">
          <t-button theme="primary" type="submit">提交</t-button>
          <t-button theme="default" variant="base" type="reset">重置</t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'ProjectCardDialog',
};
</script>
<script setup lang="ts">
import { cloneDeep, isEmpty } from 'lodash-es';
import { AddIcon, CheckCircleFilledIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { api, Services } from '@/api/system';
import { useUserStore } from '@/store';

const userStore = useUserStore();
const allowClose = computed(() => {
  return !!(userStore.projectId && userStore.solutionId);
});

const props = defineProps<{
  visible: boolean;
}>();

const emits = defineEmits(['update:visible']);

const visibleDialog = computed({
  get() {
    return props.visible;
  },
  set(val) {
    emits('update:visible', val);
  },
});

watch(visibleDialog, (val) => {
  if (val) {
    fetchSolutionData();
  }
});

onMounted(() => {
  fetchSolutionData();
});

const currentSolutionId = ref('');
const currentSolution = computed(() => {
  return solutionData.value.find((item) => item.id === currentSolutionId.value);
});
watch(currentSolutionId, (val) => {
  if (val) {
    fetchProjectData([val]);
  }
});

const searchProjectKeyword = ref('');
const projectFormData = ref<{
  id: string;
  solutionId: string;
  projectName: string;
  description: string;
  seq: number;
}>(null);
const projectFormRules = {
  projectName: [{ required: true, message: '项目名称不能为空' }],
};

const addSolutionDialog = ref(false);
const addProjectDialog = ref(false);

const onShowAddDialog = () => {
  if (isEmpty(searchProjectKeyword.value)) {
    MessagePlugin.warning('请输入分组名称后再点击【+】按钮');
    return;
  }
  addSolutionDialog.value = true;
};

const onShowAddProjectDialog = () => {
  if (isEmpty(currentSolutionId.value)) {
    MessagePlugin.warning('请先选择分组后再点击【添 加 项 目】按钮');
    return;
  }
  addProjectDialog.value = true;
  const seq = (currentSolution.value.projectList?.length || 0) + 1;
  projectFormData.value = { id: '', solutionId: currentSolutionId.value, projectName: '', description: '', seq };
};

const solutionData = ref<
  {
    id: string;
    solutionName: string;
    projectList: { id: string; projectName: string; description: string; timeCreate: string }[];
  }[]
>([]);

const searchData = computed(() => {
  const keyword = searchProjectKeyword.value.trim();
  if (isEmpty(keyword)) {
    return solutionData.value;
  }
  return cloneDeep(solutionData.value).filter((item) => {
    item.projectList = item.projectList?.filter((project) => project.projectName.includes(keyword));
    return item.projectList?.length > 0;
  });
});

const fetchSolutionData = async () => {
  const list = await api.run(Services.solutionGetAll);
  if (!list || list.length === 0) {
    MessagePlugin.warning('获取项目列表失败，请点击添加按钮增加');
    solutionData.value = [];
    visibleDialog.value = true;
    return;
  }
  if (isEmpty(currentSolutionId.value)) {
    currentSolutionId.value = list[0].id;
  }

  solutionData.value = list;

  if (!solutionData.value.find((t) => t.id === userStore.solutionId)) {
    userStore.solutionId = '';
    userStore.projectId = '';
    userStore.projectName = '';
    visibleDialog.value = true;
  }

  fetchProjectData(solutionData.value.map((item) => item.id));
};

const fetchProjectData = async (solutionIds) => {
  const list = await api.run(Services.projectGetAll, { solutionIds });
  if (list && list.length > 0) {
    for (const solutionItem of solutionData.value) {
      solutionItem.projectList = list.filter((item) => item.solutionId === solutionItem.id);
    }
  }
};

const onRemoveSolution = (id) => {
  const confirmDia = DialogPlugin.confirm({
    header: '警告',
    body: '确认删除该分组吗？',
    onConfirm: async () => {
      await api.run(Services.solutionDelete, { id });
      MessagePlugin.success('删除分组成功');
      fetchSolutionData();
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const onAddSolutionConfirm = async () => {
  await api.run(Services.solutionAdd, { solutionName: searchProjectKeyword.value });
  MessagePlugin.success('新增分组成功');
  searchProjectKeyword.value = '';
  addSolutionDialog.value = false;
  fetchSolutionData();
};

const onAddProjectConfirm = async ({ validateResult, firstError, e }) => {
  e.preventDefault();
  if (validateResult === true) {
    await api.run(Services.projectAdd, projectFormData.value);
    MessagePlugin.success('新增项目成功');
    addProjectDialog.value = false;
    fetchProjectData([currentSolutionId.value]);
  } else {
    console.log('Validate Errors: ', firstError, validateResult);
    MessagePlugin.warning(firstError);
  }
};

const onClickProject = (solutionId, projectItem) => {
  if (userStore.projectId === projectItem.id && userStore.solutionId === solutionId) {
    userStore.projectName = projectItem.projectName;
    visibleDialog.value = false;
    return;
  }

  const confirmDia = DialogPlugin.confirm({
    header: '警告',
    body: `确认切换到到【${projectItem.projectName}】吗？切换后将重新加载页面`,
    onConfirm: async () => {
      userStore.setProjectInfo(solutionId, projectItem.id, projectItem.projectName);
      visibleDialog.value = false;
      confirmDia.destroy();
      nextTick(() => {
        window.location.reload();
      });
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
</script>
<style lang="less" scoped>
.project-card-item {
  &:hover {
    background-color: var(--td-bg-color-container-hover);
    transition: background-color 0.2s linear;
  }

  cursor: pointer;
  border-radius: var(--td-radius-default);
  margin-top: var(--td-comp-margin-s);

  :deep(.t-list-item__meta) {
    width: 100%;

    .t-list-item__meta-content {
      width: 100%;
    }
  }
}

.project-card-time {
  font: var(--td-font-body-small);
  padding-left: var(--td-comp-paddingLR-s);
  color: var(--td-text-color-placeholder);
  float: right;
}

.project-card-active {
  float: right;
  font-size: 16px;
}

.add-project-btn {
  margin-top: var(--td-comp-margin-s);
}
</style>
