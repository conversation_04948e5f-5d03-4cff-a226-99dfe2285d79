﻿using System.Data.Common;

namespace GCP.DataAccess
{
    public abstract class BaseDbProvider : IDbProvider
    {
        public abstract bool NotUseParameter { get; }
        public abstract string ProviderName { get; }
        public abstract DbProviderType ProviderType { get; }
        public abstract DbProviderFactory Factory { get; }

        public abstract string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "");
        public abstract string GetParameterName(string parameterName);

        public virtual string GetTableSizeSql(string tableName, string schemaName = null)
        {
            throw new NotImplementedException();    
        }

        public abstract string GetTimeFormatStr(DateTime time);
        public abstract string GetTimeSql();
    }
}
