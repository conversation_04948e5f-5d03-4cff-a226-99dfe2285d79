namespace GCP.Common
{
    public enum MessageBusType
    {
        RabbitMQ,
        Redis,
        Kafka,
        Mqtt,
        Tcp,
        InMemory
    }

    public class MessageBusOptions
    {
        public string Name { get; set; } = string.Empty;
        public MessageBusType Type { get; set; }
        public bool IsEnabled { get; set; }
        public Dictionary<string, string> Settings { get; set; } = new();
    }

    public class ConsumerOptions
    {
        public string Name { get; set; } = string.Empty;
        public string Topic { get; set; } = string.Empty;
        public string ConsumerId { get; set; } = string.Empty;
        public Func<object, CancellationToken, Task> ConsumerFunc { get; set; }
        public string BusName { get; set; } = string.Empty;
        public int ConcurrentCount { get; set; } = 1;
        public bool IsEnabled { get; set; }
        public bool IsFlow { get; set; }
        public bool IsBatch { get; set; }
        public int? MaxBatchSize { get; }
        public RetryOptions RetryOptions { get; set; }
        public Dictionary<string, string> Settings { get; set; } = new();
    }

    public class RetryOptions
    {
        public int MaxRetries { get; set; } = 3;
        public int DelayMilliseconds { get; set; } = 1000;
        public bool ExponentialBackoff { get; set; }
    }

    public class MessageEnvelope
    {
        public string MessageId { get; set; } = TUID.NewTUID().ToString();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public IDictionary<string, object> Headers { get; set; } = new Dictionary<string, object>();
        public object Payload { get; set; } = default!;

        public MessageEnvelope() { }

        public MessageEnvelope(object payload) 
        {
            Payload = payload;
        }

        public MessageEnvelope(object payload, IDictionary<string, object> headers)
        {
            Payload = payload;
            if (headers != null)
            {
                Headers = headers;
            }
        }
    }

    public class MessageBusException : Exception
    {
        public string BusName { get; }
        public MessageBusType BusType { get; }

        public MessageBusException(string busName, MessageBusType busType, string message, Exception innerException = null)
            : base(message, innerException)
        {
            BusName = busName;
            BusType = busType;
        }
    }
}