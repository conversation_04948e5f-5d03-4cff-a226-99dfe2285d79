<template>
  <CmpContainer full style="padding-top: 16px">
    <CmpCard ghost>
      <t-form ref="form" :data="filterCondition" :label-width="80" colon @reset="onReset" @submit="fetchData">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="触发类型" name="triggerType">
                  <t-select
                    v-model="filterCondition.triggerType"
                    class="form-item-content"
                    clearable
                    placeholder="请选择触发类型"
                  >
                    <t-option v-for="(value, key) in TRIGGER_TYPES" :key="key" :value="key" :label="value"></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="任务名称" name="functionId">
                  <t-select
                    v-model="filterCondition.functionId"
                    class="form-item-content"
                    :options="functionOptions"
                    filterable
                    clearable
                    placeholder="请选择任务名称"
                  >
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="步骤日志" name="keyword">
                  <t-input
                    v-model="searchValue"
                    class="form-item-content"
                    type="search"
                    :placeholder="t('pages.listBase.placeholder')"
                    clearable
                  >
                    <template #suffix-icon>
                      <search-icon size="16px" />
                    </template>
                  </t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="开始时间" name="beginTime">
                  <t-date-picker
                    v-model="filterCondition.beginTime"
                    class="form-item-content"
                    format="YYYY/MM/DD HH:mm:ss"
                    enable-time-picker
                    allow-input
                    clearable
                  ></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="结束时间" name="endTime">
                  <t-date-picker
                    v-model="filterCondition.endTime"
                    class="form-item-content"
                    format="YYYY/MM/DD HH:mm:ss"
                    enable-time-picker
                    allow-input
                    clearable
                  ></t-date-picker>
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>
          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }"> 查询 </t-button>
            <t-button type="reset" variant="base" theme="default"> 重置 </t-button>
          </t-col>
        </t-row>
      </t-form>

      <div class="table-container">
        <t-row justify="space-between">
          <div class="left-operation-container">
            <t-popconfirm content="确认删除失败日志吗" @confirm="onClickRemoveError">
              <t-button variant="base" theme="default" :disabled="!selectedRowKeys.length"> 删除失败日志 </t-button>
            </t-popconfirm>
          </div>
        </t-row>
        <t-table
          :data="data"
          :columns="COLUMNS"
          size="small"
          height="calc(-400px + 100vh)"
          row-key="id"
          :hover="true"
          :pagination="pagination"
          :selected-row-keys="selectedRowKeys"
          :loading="dataLoading"
          @page-change="onPageChange"
          @select-change="onSelectChange"
        >
          <template #triggerType="{ row }">
            {{ TRIGGER_TYPES[row.triggerType] }}
          </template>

          <template #status="{ row }">
            <log-status-tag :status="row.status" />
          </template>

          <template #duration="{ row }">
            {{ formatDuration(row.duration) }}
          </template>

          <template #operate="{ row }">
            <t-space size="small">
              <t-link theme="primary" @click="onClickDetail(row)"> 详情 </t-link>
              <t-link v-if="row.status !== 'success'" theme="primary" @click="onClickRetry(row)"> 重试 </t-link>
              <!-- <t-link v-if="row.status !== 'success'" theme="primary" @click="onClickContinue(row)"> 继续 </t-link> -->
            </t-space>
          </template>
        </t-table>
      </div>
    </CmpCard>
  </CmpContainer>
</template>
<script lang="ts">
export default {
  name: 'RunLogs',
};
</script>
<script setup lang="ts">
import { SearchIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin, PrimaryTableCol, TableProps, TableRowData } from 'tdesign-vue-next';
import { onActivated, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { api, Services } from '@/api/system';
import { t } from '@/locales';
import { formatDuration } from '@/utils';

import { TRIGGER_TYPES } from './constants';
import LogStatusTag from './LogStatusTag.vue';

const route = useRoute();

onMounted(() => init());

onActivated(() => init());

const init = () => {
  if (route.query.keyword) {
    searchValue.value = route.query.keyword?.toString();
  }
  if (route.query.triggerType) {
    filterCondition.value.triggerType = route.query.triggerType?.toString();
  }
  if (route.query.functionId) {
    filterCondition.value.functionId = route.query.functionId?.toString();
  }
  fetchData();
  fetchFunctionOptions();
};

const selectedRowKeys = ref<Array<string | number>>([]);
const searchValue = ref('');

// 过滤条件
const filterCondition = ref({
  triggerType: '',
  functionId: '',
  beginTime: null,
  endTime: null,
  keyword: '',
});

// 将searchValue绑定到filterCondition.keyword
watch(searchValue, (newVal) => {
  filterCondition.value.keyword = newVal;
});

// 任务名称选项
const functionOptions = ref([]);

// 监听触发类型变化
watch(
  () => filterCondition.value.triggerType,
  () => {
    fetchFunctionOptions();
  },
);

// 获取任务名称列表
const fetchFunctionOptions = async () => {
  try {
    const { triggerType } = filterCondition.value;
    let service = null;

    if (triggerType === 'JOB') {
      service = Services.jobGetFunctionNames;
    } else if (triggerType === 'API') {
      service = Services.apiGetFunctionNames;
    } else if (triggerType === 'EVENT') {
      service = Services.eventGetFunctionNames;
    } else {
      functionOptions.value = [];
      return;
    }

    functionOptions.value = await api.run(service);
  } catch (error) {
    console.error('获取任务名称列表失败:', error);
    functionOptions.value = [];
  }
};

const COLUMNS: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'row-select', type: 'multiple', width: 64, fixed: 'left' },
  {
    title: '任务名称',
    align: 'left',
    colKey: 'functionName',
    fixed: 'left',
    width: 180,
    ellipsis: true,
  },
  {
    title: '触发类型',
    colKey: 'triggerType',
    width: 100,
    align: 'center',
  },
  { title: '状态', colKey: 'status', width: 100 },
  {
    title: '开始时间',
    width: 180,
    colKey: 'beginTime',
  },
  {
    title: '结束时间',
    width: 180,
    colKey: 'endTime',
  },
  {
    title: '运行时长',
    width: 100,
    colKey: 'duration',
    fixed: 'right',
  },
  {
    title: '操作',
    align: 'left',
    fixed: 'right',
    width: 120,
    colKey: 'operate',
  },
];
const data = ref([]);
const pagination = ref({
  pageSize: 20,
  total: 0,
  current: 1,
  pageSizeOptions: [20, 200, 500, 1000],
});

const dataLoading = ref(false);
const fetchData = async () => {
  dataLoading.value = true;
  try {
    route.query.keyword = searchValue.value;
    const { list, total } = await api.run(Services.flowRunGetAll, {
      keyword: searchValue.value?.trim(),
      triggerType: filterCondition.value.triggerType || null,
      functionId: filterCondition.value.functionId || null,
      beginTime: filterCondition.value.beginTime || null,
      endTime: filterCondition.value.endTime || null,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });
    data.value = list;
    pagination.value = {
      ...pagination.value,
      total,
    };
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};

const onReset = () => {
  searchValue.value = '';
  filterCondition.value = {
    triggerType: '',
    functionId: '',
    beginTime: null,
    endTime: null,
    keyword: '',
  };
  fetchData();
};

const onSelectChange: TableProps['onSelectChange'] = (value, params) => {
  selectedRowKeys.value = value;
};
const onPageChange: TableProps['onPageChange'] = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchData();
};

const emits = defineEmits(['showDetail']);
const onClickDetail = (row) => {
  emits('showDetail', {
    row,
    state: 'error',
  });
};

const onClickRemoveError = async () => {
  if (!selectedRowKeys.value.length) {
    return;
  }

  await api.run(Services.flowRunDeleteErrorLogs, { ids: selectedRowKeys.value }).then(() => {
    selectedRowKeys.value = [];
    MessagePlugin.success('失败日志删除成功');
    fetchData();
  });
};

const onClickRetry = async (row) => {
  dataLoading.value = true;
  try {
    await api.run(Services.flowRunRetry, {
      id: row.id,
    });
    MessagePlugin.success('重试成功');
    fetchData();
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};

// 定义但未使用，添加下划线前缀避免lint报错
// const _onClickContinue = async (row) => {};
</script>
<style lang="less" scoped>
.left-operation-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-comp-margin-l);

  .selected-count {
    display: inline-block;
    margin-left: var(--td-comp-margin-l);
    color: var(--td-text-color-secondary);
  }
}

.form-item-content {
  width: 100%;
}

.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.table-container {
  margin-top: var(--td-comp-margin-xxl);
}

.search-input {
  width: 360px;
}
</style>
