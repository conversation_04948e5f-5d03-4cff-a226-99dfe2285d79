using GCP.Common;
using GCP.DataAccess;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("messageSubscription", "消息订阅服务")]
    class MessageSubscriptionService : BaseService
    {
        [Function("getAllEvents", "获取所有事件")]
        public PagingData<LcMessageEvent> GetAllEvents(string keyword = null, short? eventType = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcMessageEvents
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(keyword) || a.EventName.Contains(keyword) || a.Description.Contains(keyword)) &&
                        (eventType == null || a.EventType == eventType)
                        orderby a.TimeCreate descending
                        select a).ToPagingData(pageIndex, pageSize);
            return data;
        }

        [Function("getEventById", "获取消息事件详情")]
        public LcMessageEvent GetEventById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcMessageEvents.FirstOrDefault(t => t.Id == id && t.State == 1 && 
                        t.SolutionId == this.SolutionId && t.ProjectId == this.ProjectId);
            if (data == null)
            {
                throw new CustomException("未找到事件");
            }
            return data;
        }

        [Function("saveEvent", "保存消息事件")]
        public string SaveEvent(LcMessageEvent evt)
        {
            evt.SolutionId = this.SolutionId;
            evt.ProjectId = this.ProjectId;

            using var db = this.GetDb();
            db.BeginTransaction();
            
            try 
            {
                if (string.IsNullOrEmpty(evt.Id))
                {
                    // 检查事件名称是否重复
                    var existingEvent = db.LcMessageEvents
                        .FirstOrDefault(e => e.SolutionId == evt.SolutionId && 
                                        e.ProjectId == evt.ProjectId && 
                                        e.EventName == evt.EventName && 
                                        e.State == 1);
                    
                    if (existingEvent != null)
                    {
                        throw new CustomException($"事件名称 '{evt.EventName}' 已存在");
                    }

                    // 如果是新事件且没有指定FunctionId，则创建一个新函数
                    if (string.IsNullOrEmpty(evt.FunctionId))
                    {
                        evt.FunctionId = AddFunction(evt.EventName, evt.Description ?? $"事件处理: {evt.EventName}", db);
                    }
                    
                    this.InsertData(evt, db);
                }
                else
                {
                    var existingEvent = db.LcMessageEvents.FirstOrDefault(e => e.Id == evt.Id);
                    if (existingEvent == null)
                    {
                        throw new CustomException("事件不存在");
                    }

                    // 检查事件名称是否重复（排除自身）
                    var dupEvent = db.LcMessageEvents
                        .FirstOrDefault(e => e.Id != evt.Id && 
                                        e.SolutionId == evt.SolutionId && 
                                        e.ProjectId == evt.ProjectId && 
                                        e.EventName == evt.EventName && 
                                        e.State == 1);
                    
                    if (dupEvent != null)
                    {
                        throw new CustomException($"事件名称 '{evt.EventName}' 已存在");
                    }

                    existingEvent.EventName = evt.EventName;
                    existingEvent.EventType = evt.EventType;
                    existingEvent.SourceType = evt.SourceType;
                    existingEvent.IsEnabled = evt.IsEnabled;
                    existingEvent.Description = evt.Description;
                    
                    // 更新对应的函数名称和描述
                    if (!string.IsNullOrEmpty(existingEvent.FunctionId))
                    {
                        var func = db.LcFunctions.FirstOrDefault(f => f.Id == existingEvent.FunctionId);
                        if (func != null)
                        {
                            func.FunctionName = evt.EventName;
                            func.Description = evt.Description ?? $"事件处理: {evt.EventName}";
                            this.UpdateData(func, db);
                        }
                    }
                    
                    this.UpdateData(existingEvent, db);
                }
                
                db.CommitTransaction();
                return evt.Id;
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }
        
        private string AddFunction(string functionName, string description, GcpDb db = null)
        {
            LcFunction function = new()
            {
                SolutionId = this.SolutionId,
                ProjectId = this.ProjectId,
                FunctionName = functionName,
                FunctionType = "FLOW",
                Description = description,
                UseVersion = 1
            };
            return this.InsertData(function, db);
        }

        [Function("removeEvent", "删除消息事件")]
        public bool RemoveEvent(string id)
        {
            using var db = this.GetDb();
            var evt = db.LcMessageEvents.FirstOrDefault(e => e.Id == id && 
                       e.SolutionId == this.SolutionId && e.ProjectId == this.ProjectId);
            
            if (evt == null)
            {
                return false;
            }

            db.BeginTransaction();
            try
            {
                // 删除所有映射关系
                db.LcMessageEventMappings
                    .Where(m => m.EventId == id)
                    .Delete();
                
                // 标记事件为删除状态
                evt.State = 0;
                this.UpdateData(evt, db);
                
                db.CommitTransaction();
                return true;
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("updateEventStatus", "更新消息事件状态")]
        public async Task<bool> UpdateEventStatus(string id, short isEnabled)
        {
            await using var db = this.GetDb();
            var evt = db.LcMessageEvents.FirstOrDefault(e => e.Id == id && 
                      e.SolutionId == this.SolutionId && e.ProjectId == this.ProjectId);
            
            if (evt == null)
            {
                return false;
            }
            
            evt.IsEnabled = isEnabled;
            this.UpdateData(evt);
            
            // 根据启用状态初始化或停止事件
            if (isEnabled == 1)
            {
                await EventBusHelper.InitializeEvent?.Invoke(id);
            }
            else
            {
                await EventBusHelper.StopEvent?.Invoke(id);
            }
            
            return true;
        }

        [Function("getMappingsByEventId", "获取事件映射关系")]
        public List<LcMessageEventMapping> GetMappingsByEventId(string eventId)
        {
            using var db = this.GetDb();
            var mappings = db.LcMessageEventMappings
                .Where(m => m.EventId == eventId && m.State == 1)
                .ToList();
            
            return mappings;
        }

        [Function("saveMapping", "保存事件映射关系")]
        public async Task<string> SaveMappingAsync(LcMessageEventMapping mapping)
        {
            mapping.SolutionId = this.SolutionId;
            mapping.ProjectId = this.ProjectId;

            await using var db = this.GetDb();
            
            // 检查事件是否存在
            var evt = db.LcMessageEvents.FirstOrDefault(e => e.Id == mapping.EventId && 
                      e.SolutionId == this.SolutionId && e.ProjectId == this.ProjectId && e.State == 1);
            
            if (evt == null)
            {
                throw new CustomException("事件不存在");
            }
            
            if (string.IsNullOrEmpty(mapping.Id))
            {
                // 检查相同的源是否已存在
                var existingMapping = db.LcMessageEventMappings
                    .FirstOrDefault(m => m.EventId == mapping.EventId && 
                                     m.SourceId == mapping.SourceId && 
                                     m.State == 1);
                
                if (existingMapping != null)
                {
                    throw new CustomException($"源ID '{mapping.SourceId}' 已存在映射关系");
                }
                
                this.InsertData(mapping);
            }
            else
            {
                var existingMapping = db.LcMessageEventMappings.FirstOrDefault(m => m.Id == mapping.Id);
                if (existingMapping == null)
                {
                    throw new CustomException("映射关系不存在");
                }
                
                // 检查相同的源是否已存在（排除自身）
                var dupMapping = db.LcMessageEventMappings
                    .FirstOrDefault(m => m.Id != mapping.Id && 
                                     m.EventId == mapping.EventId && 
                                     m.SourceId == mapping.SourceId && 
                                     m.State == 1);
                
                if (dupMapping != null)
                {
                    throw new CustomException($"源ID '{mapping.SourceId}' 已存在映射关系");
                }
                
                existingMapping.SourceId = mapping.SourceId;
                existingMapping.SourceCode = mapping.SourceCode;
                existingMapping.FunctionId = mapping.FunctionId;

                this.UpdateData(existingMapping);
            }
            
            // 如果事件已启用，重新初始化
            if (evt.IsEnabled == 1)
            {
                await EventBusHelper.InitializeEvent?.Invoke(mapping.EventId);
            }
            
            return mapping.Id;
        }

        [Function("removeMapping", "删除事件映射关系")]
        public async Task<bool> RemoveMappingAsync(string id)
        {
            await using var db = this.GetDb();
            var mapping = db.LcMessageEventMappings.FirstOrDefault(m => m.Id == id);
            
            if (mapping == null)
            {
                return false;
            }
            
            // 检查事件是否存在并获取启用状态
            var evt = db.LcMessageEvents.FirstOrDefault(e => e.Id == mapping.EventId);
            bool needReInitialize = evt != null && evt.IsEnabled == 1;
            
            mapping.State = 0;
            this.UpdateData(mapping);
            
            // 如果事件已启用，重新初始化
            if (needReInitialize)
            {
                await EventBusHelper.InitializeEvent?.Invoke(mapping.EventId);
            }
            
            return true;
        }
        
        [Function("getEquipments", "获取设备列表")]
        public List<LcIotEquipment> GetEquipments(string keyword = null, string equipmentType = null)
        {
            using var db = this.GetDb();
            var equipments = db.LcIotEquipment
                .Where(e => e.State == 1 &&
                       e.SolutionId == this.SolutionId &&
                       e.ProjectId == this.ProjectId &&
                       (string.IsNullOrEmpty(keyword) || (e.EquipmentName.Contains(keyword) || e.EquipmentCode.Contains(keyword))) &&
                       (string.IsNullOrEmpty(equipmentType) || e.EquipmentType == equipmentType))
                .OrderBy(e => e.EquipmentCode)
                .ToList();
                
            return equipments;
        }
        
        [Function("getEquipmentTypes", "获取设备类型列表")]
        public List<string> GetEquipmentTypes()
        {
            using var db = this.GetDb();
            var types = db.LcIotEquipment
                .Where(e => e.State == 1 &&
                       e.SolutionId == this.SolutionId &&
                       e.ProjectId == this.ProjectId &&
                       e.EquipmentType != null)
                .Select(e => e.EquipmentType)
                .Distinct()
                .ToList();
                
            return types;
        }
        
        [Function("getEquipmentVariables", "获取设备变量列表")]
        public List<LcIotEquipmentVariable> GetEquipmentVariables(string equipmentId)
        {
            using var db = this.GetDb();
            var variables = db.LcIotEquipmentVariables
                .Where(v => v.State == 1 &&
                       v.EquipmentId == equipmentId)
                .OrderBy(v => v.VarName)
                .ToList();
                
            return variables;
        }
    }
} 