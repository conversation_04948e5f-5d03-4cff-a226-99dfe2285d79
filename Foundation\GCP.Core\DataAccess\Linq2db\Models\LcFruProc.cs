// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 流程运行实例
	/// </summary>
	[Table("lc_fru_proc")]
	public class LcFruProc
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"           , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                          )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"      , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"  , CanBeNull = false                     )] public string    SolutionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"   , CanBeNull = false                     )] public string    ProjectId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:函数ID
		/// </summary>
		[Column("FUNCTION_ID"  , CanBeNull = false                     )] public string    FunctionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:函数版本
		/// </summary>
		[Column("VERSION"                                              )] public long      Version      { get; set; } // bigint
		/// <summary>
		/// Description:函数名称
		/// </summary>
		[Column("FUNCTION_NAME", CanBeNull = false                     )] public string    FunctionName { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:触发流程类型 API/JOB/MQ
		/// </summary>
		[Column("TRIGGER_TYPE"                                         )] public string?   TriggerType  { get; set; } // varchar(36)
		/// <summary>
		/// Description:状态 -1：失败, 0：运行中, 1：已运行
		/// </summary>
		[Column("STATUS"                                               )] public short     Status       { get; set; } // smallint
		/// <summary>
		/// Description:开始时间
		/// </summary>
		[Column("BEGIN_TIME"                                           )] public DateTime  BeginTime    { get; set; } // datetime
		/// <summary>
		/// Description:结束时间
		/// </summary>
		[Column("END_TIME"                                             )] public DateTime? EndTime      { get; set; } // datetime
		/// <summary>
		/// Description:运行时长
		/// </summary>
		[Column("DURATION"                                             )] public int?      Duration     { get; set; } // int
		/// <summary>
		/// 总流量(KB)
		/// </summary>
		[Column("TOTAL_TRAFFIC"                                        )] public decimal?  TotalTraffic { get; set; } // decimal(19,5)
	}
}
