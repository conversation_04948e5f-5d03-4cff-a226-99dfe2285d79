﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace GCP.Common
{
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        private string DateFormat { get; set; }
        public DateTimeConverter(string dateFormat = "yyyy/MM/dd HH:mm:ss")
        {
            DateFormat = dateFormat;
        }

        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (!reader.TryGetDateTime(out DateTime value))
            {
                var text = reader.GetString();
                if (!DateTime.TryParse(text, out value))
                {
                    throw new JsonException($"Cannot convert '{text}' to {nameof(DateTime)}");
                }
            }

            return value;
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString(DateFormat));
        }
    }
}
