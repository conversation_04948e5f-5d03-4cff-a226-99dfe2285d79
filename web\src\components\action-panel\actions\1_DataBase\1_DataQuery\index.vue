<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="数据源" prop="dataSource">
              <data-source-select v-model="formData.dataSource" />
            </t-form-item>
          </t-col>
          <t-col v-if="formData.autoPaged" :span="6">
            <t-form-item label="每次获取条数" prop="pageSize">
              <value-input
                v-model:data-value="formData.pageSize"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <!-- <t-col :span="6">
            <t-form-item label="自动分页" prop="autoPaged">
              <t-switch v-model="formData.autoPaged" />
            </t-form-item>
          </t-col>
          <t-col v-if="!formData.autoPaged" :span="6">
            <t-form-item label="是否分页" prop="isPaging">
              <t-switch v-model="formData.isPaging" />
            </t-form-item>
          </t-col>
          <t-col v-if="!formData.autoPaged" :span="6">
            <t-form-item label="查询总数" prop="hasTotal">
              <t-switch v-model="formData.hasTotal" />
            </t-form-item>
          </t-col> -->
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <t-tabs v-model="formData.operateType">
          <t-tab-panel value="configure" label="配置">
            <t-collapse :default-value="[0]" class="collapse-container">
              <t-collapse-panel
                v-for="(item, index) in formData.configureInfo"
                :key="item.id"
                :header="
                  index == 0
                    ? '主表 ' + (item.tableData?.tableName || '')
                    : '关联表 ' + (item.tableData?.tableName || '')
                "
              >
                <data-source-table
                  v-model:data="item.tableData"
                  operation-type="Query"
                  :data-source-id="formData.dataSource"
                ></data-source-table>
              </t-collapse-panel>
            </t-collapse>
          </t-tab-panel>
          <t-tab-panel value="sql" label="SQL查询">
            <action-form-title title="SQL语句">
              <!-- <t-button theme="default" variant="outline" @click="onClickFormatSql">格式化SQL</t-button> -->
              <!-- <t-button theme="default" variant="outline" @click="onClickTestSql">测试SQL</t-button> -->
            </action-form-title>
            <editor
              ref="sqlEditorRef"
              v-model:value="formData.sqlInfo.sql"
              class="sql-editor"
              language="sql"
              style="height: 200px"
            ></editor>
            <action-form-title title="输入参数" tip="自动根据SQL语句【:参数名 | @参数名】生成参数列表">
            </action-form-title>
            <t-table
              class="small-table"
              size="small"
              row-key="paramName"
              :columns="sqlInputTableColumns"
              :data="formData.sqlInfo.parameters"
            >
              <template #paramName="{ row }">
                <t-input v-model="row.paramName" size="small" borderless disabled></t-input>
              </template>
              <template #paramValue="{ row }">
                <value-input v-model:data-value="row.paramValue"></value-input>
              </template>
            </t-table>
          </t-tab-panel>
        </t-tabs>
        <action-form-title title="输出参数"> </action-form-title>
        <variable-list
          v-if="formData.autoPaged"
          :data="forEachData.item"
          :show-root-node="formData.useRoot ?? false"
          @on-change="onVariableChange"
        ></variable-list>
        <variable-list
          v-else
          v-model:data="currentStep.result"
          :show-root-node="formData.useRoot ?? false"
        ></variable-list>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'DataQueryActions',
};
</script>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';

import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import DataSourceSelect from '@/components/action-panel/DataSourceSelect.vue';
import DataSourceTable from '@/components/action-panel/DataSourceTable.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';
import Editor from '@/components/editor/index.vue';

import { useDataAutoQueryStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataQueryStore = useDataAutoQueryStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataQueryStore.updateState();
  },
  {
    immediate: true,
  },
);

const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData, forEachData } = storeToRefs(dataQueryStore);

const sqlInputTableColumns = ref([
  {
    colKey: 'paramName',
    title: '参数名',
    width: 150,
  },
  {
    colKey: 'paramValue',
    title: '参数值',
    width: 300,
  },
]);

watch(
  () => formData.value.sqlInfo?.sql,
  debounce((newValue) => {
    if (formData.value.operateType === 'sql') dataQueryStore.setSqlParameters(newValue);
  }, 300),
);

watch(
  () => formData.value,
  (newValue) => {
    dataQueryStore.setArgs(newValue);
    if (newValue.autoPaged) {
      dataQueryStore.setAutoPagedData();
    } else {
      dataQueryStore.cleanAutoPagedData();
    }
  },
  {
    deep: true,
  },
);

const onVariableChange = () => {
  if (formData.value.autoPaged) {
    dataQueryStore.setAutoPagedData();
  } else {
    dataQueryStore.cleanAutoPagedData();
  }
};
const sqlEditorRef = ref(null);
// const onClickFormatSql = () => {
//   sqlEditorRef.value?.formatCode();
// };
// const onClickTestSql = () => {
// };
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}

.sql-editor {
  margin-top: 10px;
}

.collapse-container {
  margin-top: 16px;

  :deep(.t-collapse-panel__content) {
    padding-left: var(--td-comp-paddingLR-l) !important;
  }
}
</style>
