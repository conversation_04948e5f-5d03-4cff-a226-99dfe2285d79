using System.Text.Json;
using EasyNotice;
using EasyNotice.Core;
using EasyNotice.Email;
using EasyNotice.Dingtalk;
using EasyNotice.Feishu;
using EasyNotice.Weixin;
using GCP.DataAccess;
using GCP.Models;
using LinqToDB;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace GCP.Common
{
    /// <summary>
    /// 通知服务实现
    /// </summary>
    public class NotificationService : INotificationService
    {
        private readonly ILogger<NotificationService> _logger;
        private readonly Dictionary<string, DateTime> _lastSendTimes = new();
        private readonly object _lockObject = new();

        public NotificationService(ILogger<NotificationService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 发送通知
        /// </summary>
        public async Task<bool> SendNotificationAsync(string channelId, NotificationRequest request)
        {
            try
            {
                // 获取通知通道配置
                var channel = await GetNotificationChannelAsync(channelId);
                if (channel == null || !channel.IsEnabled)
                {
                    _logger.LogWarning("通知通道不存在或已禁用: {ChannelId}", channelId);
                    return false;
                }

                // 检查发送间隔
                if (!CheckSendInterval(channelId, request.Title, channel.IntervalSeconds))
                {
                    _logger.LogDebug("通知发送间隔限制: {ChannelId}, {Title}", channelId, request.Title);
                    return false;
                }

                // 根据通道类型发送通知
                var success = await SendNotificationByTypeAsync(channel, request);
                
                if (success)
                {
                    UpdateLastSendTime(channelId, request.Title);
                    _logger.LogInformation("通知发送成功: {ChannelId}, {Title}", channelId, request.Title);
                }
                else
                {
                    _logger.LogError("通知发送失败: {ChannelId}, {Title}", channelId, request.Title);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送通知时发生异常: {ChannelId}, {Title}", channelId, request.Title);
                return false;
            }
        }

        /// <summary>
        /// 测试通知通道
        /// </summary>
        public async Task<bool> TestNotificationChannelAsync(string channelId)
        {
            var testRequest = new NotificationRequest
            {
                Title = "测试通知",
                Content = $"这是一条测试通知消息，发送时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}"
            };

            return await SendNotificationAsync(channelId, testRequest);
        }

        /// <summary>
        /// 获取支持的通知通道类型
        /// </summary>
        public List<NotificationChannelType> GetSupportedChannelTypes()
        {
            return Enum.GetValues<NotificationChannelType>().ToList();
        }

        /// <summary>
        /// 获取通知通道配置
        /// </summary>
        private async Task<LcNotificationChannel?> GetNotificationChannelAsync(string channelId)
        {
            using var db = new GcpDb();
            return await db.LcNotificationChannels
                .Where(c => c.Id == channelId && c.State == 1)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 检查发送间隔
        /// </summary>
        private bool CheckSendInterval(string channelId, string title, int intervalSeconds)
        {
            lock (_lockObject)
            {
                var key = $"{channelId}:{title}";
                if (_lastSendTimes.TryGetValue(key, out var lastSendTime))
                {
                    var elapsed = DateTime.Now - lastSendTime;
                    return elapsed.TotalSeconds >= intervalSeconds;
                }
                return true;
            }
        }

        /// <summary>
        /// 更新最后发送时间
        /// </summary>
        private void UpdateLastSendTime(string channelId, string title)
        {
            lock (_lockObject)
            {
                var key = $"{channelId}:{title}";
                _lastSendTimes[key] = DateTime.Now;
            }
        }

        /// <summary>
        /// 根据通道类型发送通知
        /// </summary>
        private async Task<bool> SendNotificationByTypeAsync(LcNotificationChannel channel, NotificationRequest request)
        {
            return channel.ChannelType.ToLower() switch
            {
                "email" => await SendEmailNotificationAsync(channel, request),
                "dingtalk" => await SendDingtalkNotificationAsync(channel, request),
                "feishu" => await SendFeishuNotificationAsync(channel, request),
                "weixin" => await SendWeixinNotificationAsync(channel, request),
                _ => throw new NotSupportedException($"不支持的通知类型: {channel.ChannelType}")
            };
        }

        /// <summary>
        /// 发送邮件通知
        /// </summary>
        private async Task<bool> SendEmailNotificationAsync(LcNotificationChannel channel, NotificationRequest request)
        {
            try
            {
                var config = JsonSerializer.Deserialize<EmailNotificationConfig>(channel.ConfigJson);
                if (config == null) return false;

                var emailProvider = CreateEmailProvider(config);

                // 创建@用户信息（邮件使用收件人列表）
                var atUser = new EasyNoticeAtUser
                {
                    UserId = config.ToAddress?.ToArray() ?? new string[0]
                };

                await emailProvider.SendAsync(request.Title, request.Exception ?? new Exception(request.Content), atUser);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送邮件通知失败");
                return false;
            }
        }

        /// <summary>
        /// 发送钉钉通知
        /// </summary>
        private async Task<bool> SendDingtalkNotificationAsync(LcNotificationChannel channel, NotificationRequest request)
        {
            try
            {
                var config = JsonSerializer.Deserialize<DingtalkNotificationConfig>(channel.ConfigJson);
                if (config == null) return false;

                var dingtalkProvider = CreateDingtalkProvider(config);

                // 创建@用户信息
                var atUser = new EasyNoticeAtUser
                {
                    IsAtAll = config.IsAtAll,
                    Mobile = config.AtMobiles?.ToArray() ?? new string[0]
                };

                await dingtalkProvider.SendAsync(request.Title, request.Exception ?? new Exception(request.Content), atUser);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送钉钉通知失败");
                return false;
            }
        }

        /// <summary>
        /// 发送飞书通知
        /// </summary>
        private async Task<bool> SendFeishuNotificationAsync(LcNotificationChannel channel, NotificationRequest request)
        {
            try
            {
                var config = JsonSerializer.Deserialize<FeishuNotificationConfig>(channel.ConfigJson);
                if (config == null) return false;

                var feishuProvider = CreateFeishuProvider(config);
                await feishuProvider.SendAsync(request.Title, request.Exception ?? new Exception(request.Content));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送飞书通知失败");
                return false;
            }
        }

        /// <summary>
        /// 发送企业微信通知
        /// </summary>
        private async Task<bool> SendWeixinNotificationAsync(LcNotificationChannel channel, NotificationRequest request)
        {
            try
            {
                var config = JsonSerializer.Deserialize<WeixinNotificationConfig>(channel.ConfigJson);
                if (config == null) return false;

                var weixinProvider = CreateWeixinProvider(config);

                // 创建@用户信息
                var atUser = new EasyNoticeAtUser
                {
                    UserId = config.MentionedList?.ToArray() ?? new string[0],
                    Mobile = config.MentionedMobileList?.ToArray() ?? new string[0]
                };

                await weixinProvider.SendAsync(request.Title, request.Exception ?? new Exception(request.Content), atUser);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送企业微信通知失败");
                return false;
            }
        }

        /// <summary>
        /// 创建邮件提供者
        /// </summary>
        private IEmailProvider CreateEmailProvider(EmailNotificationConfig config)
        {
            var emailOptions = Options.Create(new EmailOptions
            {
                Host = config.Host,
                Port = config.Port,
                FromName = config.FromName,
                FromAddress = config.FromAddress,
                Password = config.Password,
                ToAddress = config.ToAddress
            });

            var noticeOptions = Options.Create(new NoticeOptions
            {
                IntervalSeconds = 0 // 我们自己控制间隔
            });

            return new EmailProvider(emailOptions, noticeOptions);
        }

        /// <summary>
        /// 创建钉钉提供者
        /// </summary>
        private IDingtalkProvider CreateDingtalkProvider(DingtalkNotificationConfig config)
        {
            var dingtalkOptions = Options.Create(new DingtalkOptions
            {
                WebHook = config.WebHook,
                Secret = config.Secret
            });

            var noticeOptions = Options.Create(new NoticeOptions
            {
                IntervalSeconds = 0 // 我们自己控制间隔
            });

            return new DingtalkProvider(dingtalkOptions, noticeOptions);
        }

        /// <summary>
        /// 创建飞书提供者
        /// </summary>
        private IFeishuProvider CreateFeishuProvider(FeishuNotificationConfig config)
        {
            var feishuOptions = Options.Create(new FeishuOptions
            {
                WebHook = config.WebHook,
                Secret = config.Secret
            });

            var noticeOptions = Options.Create(new NoticeOptions
            {
                IntervalSeconds = 0 // 我们自己控制间隔
            });

            return new FeishuProvider(feishuOptions, noticeOptions);
        }

        /// <summary>
        /// 创建企业微信提供者
        /// </summary>
        private IWeixinProvider CreateWeixinProvider(WeixinNotificationConfig config)
        {
            var weixinOptions = Options.Create(new WeixinOptions
            {
                WebHook = config.WebHook
            });

            var noticeOptions = Options.Create(new NoticeOptions
            {
                IntervalSeconds = 0 // 我们自己控制间隔
            });

            return new WeixinProvider(weixinOptions, noticeOptions);
        }
    }
}
