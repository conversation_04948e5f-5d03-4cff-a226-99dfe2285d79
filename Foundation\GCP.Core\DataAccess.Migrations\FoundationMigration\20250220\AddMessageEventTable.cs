using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20250225001000, "添加通用消息事件订阅相关表")]
    public class AddMessageEventTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_MESSAGE_EVENT").WithDescription("消息事件定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("DIR_CODE").AsAnsiString(50).Nullable().WithColumnDescription("目录编码")
               .WithColumn("EVENT_NAME").AsAnsiString(80).WithColumnDescription("事件名称")
               .WithColumn("EVENT_TYPE").AsInt16().Nullable().WithColumnDescription("事件类型：设备变量变化 1/设备状态变化 2/设备类型事件 3")
               .WithColumn("SOURCE_TYPE").AsInt16().WithColumnDescription("事件源类型：设备 1/MQTT 2/RabbitMQ 3")
               .WithColumn("FUNCTION_ID").AsAnsiString(36).Nullable().WithColumnDescription("函数ID")
               .WithColumn("IS_ENABLED").AsInt16().WithDefaultValue(0).WithColumnDescription("是否启用")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("SETTINGS").AsAnsiString(500).Nullable().WithColumnDescription("配置项")
               ;

            Create.Table("LC_MESSAGE_EVENT_MAPPING").WithDescription("消息事件映射关系")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("EVENT_ID").AsAnsiString(36).WithColumnDescription("事件ID")
               .WithColumn("SOURCE_ID").AsAnsiString(50).WithColumnDescription("消息源ID")
               .WithColumn("SOURCE_CODE").AsAnsiString(50).Nullable().WithColumnDescription("消息源编码")
               .WithColumn("FUNCTION_ID").AsAnsiString(36).Nullable().WithColumnDescription("函数ID")
               ;

            Create.Index("LC_MESSAGE_EVENT_IDX")
                .OnTable("LC_MESSAGE_EVENT")
                .OnColumn("EVENT_NAME").Ascending()
                .WithOptions().Unique();

            Create.Index("LC_MESSAGE_EVENT_MAPPING_IDX")
                .OnTable("LC_MESSAGE_EVENT_MAPPING")
                .OnColumn("EVENT_ID").Ascending()
                .OnColumn("SOURCE_ID").Ascending()
                .WithOptions().Unique();
        }

        public override void Down()
        {
            Delete.Table("LC_MESSAGE_EVENT_MAPPING");
            Delete.Table("LC_MESSAGE_EVENT");
        }
    }
}