﻿using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;

namespace GCP.FunctionPool.Flow.Services
{
    class DataDelete : DataBaseService
    {

        [Function("dataDelete", "数据库删除")]
        public async Task<int> DeleteData(DataDeleteData data)
        {
            var db = this.GetDataContext(data.DataSource);
            var sb = new SqlBuilder<object>(db);

            if (data.ConfigureInfo == null || data.ConfigureInfo.Conditions == null)
            {
                throw new CustomException("请设置删除条件");
            }

            sb.Append("DELETE FROM " + data.ConfigureInfo.TableName + " WHERE 1=1");
            FlowUtils.SetCondition(sb, data.ConfigureInfo.Conditions);

            if (this.Context.Persistence)
                using (var conn = db.CreateConnection())
                {
                    this.Context.Current.ArgsBuilder = new System.Text.StringBuilder();
                    this.Context.Current.ArgsBuilder.AppendLine($"DbProvider: {db.DbProvider.ProviderType.ToString()}");
                    this.Context.Current.ArgsBuilder.AppendLine($"DataSource: {conn.DataSource}");
                    this.Context.Current.ArgsBuilder.AppendLine($"Database: {conn.Database}");
                    this.Context.Current.ArgsBuilder.AppendLine();
                    this.Context.Current.ArgsBuilder.AppendLine(sb.ExecutableSql);
                }

            var result = await sb.ExecuteAsync();
            await this.Context.SqlLog.Info($"删除成功 {data.Name}, 删除影响行数 {result}", true);
            return result;
        }
    }
}
