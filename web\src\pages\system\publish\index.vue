<template>
  <div>
    <t-row justify="space-between" class="top-bar">
      <div>
        <t-button @click="addEnviron"> 新建发布环境 </t-button>
        <t-button @click="onUpload"> 导入当前环境 </t-button>
      </div>

      <div class="search-input">
        <t-input v-model="searchValue" :placeholder="t('pages.listBase.placeholder')" clearable @enter="fetchData">
          <template #suffix-icon>
            <search-icon size="16px" />
          </template>
        </t-input>
      </div>
    </t-row>
    <div class="card-container">
      <t-card
        v-for="(item, index) in cardList"
        :key="index"
        theme="poster2"
        :style="{ width: '24%', marginBottom: '16px', marginRight: '11px' }"
      >
        <template #header>
          <span class="card-title">{{ item.environmentName }}</span>
          <t-button :theme="item.state == 1 ? 'success' : 'default'" size="small">
            {{ item.state === 1 ? '已启用' : '已停用' }}
          </t-button>
        </template>
        {{ item.description }}
        <template #footer>
          <t-avatar-group cascading="left-up" :max="2">
            <t-avatar>{{ item.tag }}</t-avatar>
            <!-- <t-avatar>C</t-avatar>
            <t-avatar>G</t-avatar> -->
          </t-avatar-group>
        </template>
        <template #actions>
          <t-dropdown :options="options" :min-column-width="60" @click="clickHandler($event, item)">
            <t-button variant="text" shape="square">
              <more-icon />
            </t-button>
          </t-dropdown>
        </template>
      </t-card>
    </div>
    <AddModal v-model:visible="showAddModal" :title="title" :copy-data="copyData" @updata-list="fetchData" />
    <UploadModal v-model:visible="showUploadModal" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'PublishConfig',
};
</script>
<script setup lang="ts">
import { MoreIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { DialogPlugin, DropdownProps, MessagePlugin } from 'tdesign-vue-next';
import { onActivated, onMounted, ref } from 'vue';

import { api, Services } from '@/api/system';
import { t } from '@/locales';

import AddModal from './addModal.vue';
import UploadModal from './uploadModal.vue';

const cardList = ref([]);
const showAddModal = ref(false);
const showUploadModal = ref(false);
const title = ref('新建发布环境');
const copyData = ref({
  environmentName: '',
  environmentType: '',
  serviceAddress: '',
  state: 1,
  serviceSecret: '',
  serviceId: '',
  tag: '',
  description: '',
});

const searchValue = ref('');
const options: DropdownProps['options'] = [
  {
    content: '复制',
    value: 1,
  },
  {
    content: '编辑',
    value: 2,
  },
  {
    content: '备份',
    value: 3,
  },
  {
    content: '删除',
    value: 4,
  },
];
const clickHandler: DropdownProps['onClick'] = (e, row) => {
  if (e.value === 4) {
    publishDelete(row.id);
  } else if (e.value === 1) {
    showAddModal.value = true;
    title.value = '复制发布环境';
    copyData.value = { ...row };
  } else if (e.value === 2) {
    showAddModal.value = true;
    title.value = '编辑发布环境';
    copyData.value = { ...row };
  } else {
    MessagePlugin.info('备份');
  }
};
onActivated(() => {
  fetchData();
});

onMounted(() => {
  fetchData();
});

const addEnviron = () => {
  showAddModal.value = true;
  copyData.value = {
    environmentName: '',
    environmentType: '',
    serviceAddress: '',
    state: 1,
    serviceSecret: '',
    serviceId: '',
    tag: '',
    description: '',
  };
  title.value = '新建发布环境';
};
const onUpload = () => {
  showUploadModal.value = true;
};
const publishDelete = (id: string) => {
  const confirmDia = DialogPlugin({
    header: '确认删除该环境吗？',
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: () => {
      MessagePlugin.success('删除成功');
      confirmDia.hide();
      api.run(Services.publishDelete, { id }).then(() => {
        fetchData();
      });
    },
    onClose: () => {
      confirmDia.destroy();
    },
  });
};

const fetchData = async () => {
  await api.run(Services.publishGetAll).then((res) => {
    cardList.value = res;
  });
};
</script>

<style lang="less" scoped>
@import '@/style/form.less';
:deep(.t-form__controls-content) {
  width: 200px;
}
.top-bar {
  padding: 16px;
}
.card-container {
  padding: 0 16px 16px;
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  justify-content: left;
  .card-title {
    font-weight: bold;
  }
}
:deep(.t-card__body) {
  min-height: 54px;
}

.form-container {
  background-color: var(--td-bg-color-container);
}

.form-item {
  width: 676px;
}

.search-input {
  width: 360px;
}
</style>