﻿using EasyCaching.Core;
using FluentEmail.Core;
using GCP.Common;
using GCP.Common.Json;
using GCP.DataAccess;
using GCP.Functions.Common;
using Jint;
using Jint.Native;
using Jint.Native.Function;
using Jint.Native.Object;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System.Collections.Concurrent;
using System.Reflection;
using System.Text.Json;

namespace GCP.FunctionPool.Builder
{
    internal class FunctionInfo
    {
        internal static ConcurrentDictionary<Assembly, List<string>> dicAssemlyFunc { get; set; } = new ConcurrentDictionary<Assembly, List<string>>();

        internal string SolutionId { get; set; }
        internal string ProjectId { get; set; }

        public string Path { get; set; }
        public string FunctionName { get; set; }
        public string JobId { get; set; }
        public string JobName { get; set; }
        public bool AllowUnload { get; internal set; }
        public Func<FunctionContext, Task> FunctionProxy { get; set; }
        public FunctionMiddlewareDelegate MiddlewareProxy { get; set; }
        public MethodInfo FunctionMethodInfo { get; set; }
        public Assembly FunctionAssembly { get; set; }
        public FunctionLoadContext LoadContext { get; set; }

        public FunctionInfo()
        {
        }

        public FunctionInfo(string path, string name)
        {
            Path = path;
            FunctionName = name;
        }

        public void UnloadFunction()
        {
            if (FunctionAssembly == null) return;
            if (!AllowUnload) return;

            List<string> funcPaths;
            if (dicAssemlyFunc.TryGetValue(FunctionAssembly, out funcPaths))
            {
                funcPaths.Remove(Path);
                if (JobId != null)
                {
                    var job = (IJob)ServiceLocator.Current.GetService(typeof(IJob));
                    job.RemoveIfExists(JobId);
                }
                if (funcPaths.Count == 0)
                {
                    dicAssemlyFunc.TryRemove(FunctionAssembly, out _);
                    LoadContext?.Unload();

                    FunctionAssembly = null;
                    FunctionProxy = null;
                    FunctionMethodInfo = null;
                    MiddlewareProxy = null;

                }
            }
        }


        public bool LoadFlow(string flowConfig, FunctionCodeLanguage codeLanguage)
        {
            if (codeLanguage == FunctionCodeLanguage.Json)
            {
                var executor = new FlowExecutor(flowConfig);

                FunctionProxy = async (ctx) =>
                {
                    await ResiliencePipelineManager.ExecuteAsync(FunctionRunner.CommonPipelineKey, async (token) =>
                    {
                        await executor.Run(ctx).ConfigureAwait(false);
                    }, cancellationToken: ctx.CancellationToken).ConfigureAwait(false);
                };
                return true;
            }
            return false;
        }

        public bool LoadScript(string path, Prepared<Acornima.Ast.Module> preparedModule, FunctionResolveType resolverType, string basePath = "", string key = "")
        {
            if (resolverType == FunctionResolveType.PURE)
            {
                var lazyEngine = new Lazy<Engine>(() => JavascriptEngine.CreateEngine(basePath));
                FunctionProxy = async (ctx) =>
                {
                    var engine = lazyEngine.Value;
                    Function mainFunc;
                    ObjectInstance ns;
                    if (string.IsNullOrWhiteSpace(basePath))
                    {
                        engine.Modules.Add(path, t => t.AddModule(preparedModule));
                        ns = engine.Modules.Import(path);
                        mainFunc = ns.Get(JavaScriptResolver.MainSpecifier).AsFunctionInstance();
                    }
                    else
                    {
                        ns = engine.Modules.Import($"./{JavaScriptResolver.MainSpecifier}.js");
                        var prop = ns.Get(key);
                        if (prop is ScriptFunction)
                        {
                            mainFunc = prop.AsFunctionInstance();
                        }
                        else
                        {
                            ctx.Current.Result = prop.ToObject();
                            return;
                        }
                    }

                    var funParameters = JavaScriptResolver.GetFunctionParameters(mainFunc.FunctionDeclaration, out var parametersLength);


                    var argList = new List<JsValue>();
                    for (int i = 0; i < funParameters.Length; i++)
                    {
                        var paramName = funParameters[i];

                        if (ctx.Current.Args != null && ctx.Current.Args.TryGetValue(paramName, out var inputVal))
                        {
                            if (inputVal is JsonElement element)
                            {
                                inputVal = element.ParseObject();
                            }
                            //转换类型 
                            argList.Add(JsValue.FromObjectWithType(engine, inputVal, inputVal.GetType()));
                        }
                        else
                        {
                            ctx.globalData.TryGetValue(paramName, out var value);
                            if (value == null)
                            {
                                // 如果参数没有值, 则开始结束循环, 会自动使用js自带默认值
                                break;
                            }
                            else
                            {
                                argList.Add(JsValue.FromObject(engine, value));
                            }
                        }
                    }

                    if (funParameters.Length == 1 && argList.Count > 0 && argList[0] == null)
                    {
                        argList[0] = JsonHelper.Serialize(ctx.Current.Args);
                    }
                    else if (parametersLength > argList.Count)
                    {
                        throw new CustomException("参数数量不匹配, 请检查");
                    }

                    engine.SetValue("useFunctionLoad", (string id) =>
                    {
                        FunctionRunner.LoadDbFunction(id);
                        FunctionHttpHandler.LoadDbApi(id);
                    });
                    engine.JavascriptFuncBeforeExec(ctx);

                    var args = argList.ToArray();
                    await ResiliencePipelineManager.ExecuteAsync(FunctionRunner.CommonPipelineKey, async (token) =>
                    {
                        var result = mainFunc.Call(args).UnwrapIfPromise();
                        ctx.Current.Result = result.ToObject();
                        await Task.CompletedTask;
                    }, cancellationToken: ctx.CancellationToken).ConfigureAwait(false);
                };
                return true;
            }
            else if (resolverType == FunctionResolveType.MIDDLEWARE)
            {
                var lazyEngine = new Lazy<Engine>(() => JavascriptEngine.CreateEngine(basePath));
                MiddlewareProxy = async (ctx, next) =>
                {
                    var engine = lazyEngine.Value;
                    ObjectInstance ns;
                    if (string.IsNullOrWhiteSpace(basePath))
                    {
                        engine.Modules.Add(path, t => t.AddModule(preparedModule));
                        ns = engine.Modules.Import(path);
                    }
                    else
                    {
                        ns = engine.Modules.Import($"./{JavaScriptResolver.MainSpecifier}.js");
                    }
                    var handlerFunc = ns.Get(JavaScriptResolver.HandlerSpecifier).AsFunctionInstance();

                    var ctxVal = JsValue.FromObjectWithType(engine, ctx, typeof(FunctionContext));
                    var nextVal = JsValue.FromObjectWithType(engine, next, typeof(FunctionMiddlewareDelegate));
                    var result = handlerFunc.Call(ctxVal, nextVal).UnwrapIfPromise();
                    await Task.CompletedTask;
                };
                return true;
            }
            return false;
        }

        readonly Type taskType = typeof(Task);
        readonly string taskReturnName = typeof(Task<>).Name;

        public bool LoadFunction(Assembly assembly, MethodInfo methodInfo, FunctionResolveType resolverType)
        {
            FunctionAssembly = assembly;
            FunctionMethodInfo = methodInfo;

            var lazyFunc = new Lazy<Func<object, object[], object>>(() => ReflectionHelper.CreateMethodInvokeDelegate(methodInfo));

            if (resolverType == FunctionResolveType.MIDDLEWARE)
            {
                MiddlewareProxy = async (ctx, next) =>
                {
                    bool isAsync = methodInfo.ReturnType == taskType;
                    dynamic result = lazyFunc.Value.Invoke(null, [ctx, next]);
                    if (isAsync)
                    {
                        var task = (Task)result;
                        await task.ConfigureAwait(false);
                    }
                };
                return true;
            }

            var lazyParamInfos = new Lazy<ParameterInfo[]>(methodInfo.GetParameters);
            bool isAsync = methodInfo.ReturnType == taskType;
            bool isAsyncHasReturn = methodInfo.ReturnType.Name == taskReturnName;

            //var instance = ReflectionHelper.CreateInitializeInvoker(methodInfo.ReflectedType.GetConstructor(Type.EmptyTypes));
            //var lazyInstance = new Lazy<Func<object>>(() => ReflectionHelper.CreateInstanceDelegate(methodInfo.DeclaringType));

            FunctionProxy =
                async (ctx) =>
                {
                    var paramInfos = lazyParamInfos.Value;
                    object[] args = new object[paramInfos.Length];

                    if (paramInfos.Length == 1)
                    {
                        object data = ctx.Current.Args ?? throw new CustomException("参数不能为空");
                        try
                        {
                            var param = paramInfos[0];
                            var firstType = param.ParameterType;
                            var isPrimitiveType = firstType.IsPrimitiveType();
                            if (ctx.Current.Args.Count == 1)
                            {
                                var first = ctx.Current.Args.First();
                                if (param.Name == null || first.Key == param.Name)
                                {
                                    data = first.Value;
                                }
                            }
                            else if (ctx.Current.Args.Count == 0)
                            {
                                data = null;
                            }
                            else if (isPrimitiveType)
                            {
                                if(ctx.Current.Args.TryGetValue(param.Name, out var inputVal))
                                {
                                    data = inputVal;
                                }
                                else
                                {
                                    throw new CustomException("参数数量不匹配, 请检查");
                                }
                            }

                            if (isPrimitiveType)
                            {
                                args[0] = ConvertHelper.ChangeType(data, firstType);
                            }
                            else
                            {
                                args[0] = JsonHelper.Deserialize(JsonHelper.Serialize(data), firstType);
                            }
                        }
                        catch
                        {
                        }
                    }
                    else
                    {
                        for (int i = 0; i < paramInfos.Length; i++)
                        {
                            var param = paramInfos[i];

                            args[i] = GetParamValue(ctx, param.Name, param.ParameterType, param.HasDefaultValue, param.DefaultValue);
                        }
                    }

                    var func = lazyFunc.Value;

                    await ResiliencePipelineManager.ExecuteAsync(FunctionRunner.CommonPipelineKey, async (token) =>
                    {
                        dynamic result = func.Invoke(methodInfo.IsStatic ? null : CreateInstance(methodInfo.DeclaringType, ctx), args);
                        if (isAsync || isAsyncHasReturn)
                        {
                            var task = (Task)result;
                            // 使用传入的 token 来支持取消操作
                            await task.WaitAsync(token).ConfigureAwait(false);
                            ctx.Current.Result = isAsyncHasReturn ? result.Result : null;
                        }
                        else
                        {
                            ctx.Current.Result = result;
                        }
                    }, cancellationToken: ctx.CancellationToken).ConfigureAwait(false);
                };

            return true;
        }

        private object CreateInstance(Type type, FunctionContext ctx)
        {
            var obj = ReflectionHelper.CreateInstanceDelegate(type).Invoke();

            if (obj != null)
            {
                var properties = type.GetProperties(BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Instance);

                foreach (PropertyInfo property in properties)
                {
                    if (!property.CanWrite) continue;
                    var propertyType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
                    if (propertyType.IsPrimitiveType()) continue;
                    var val = GetParamValueByType(propertyType, ctx);
                    if (val == null) continue;
                    property.SetValue(obj, val);
                }
            }

            return obj;
        }

        private object GetParamValueByType(Type type, FunctionContext ctx = null)
        {
            try
            {
                if (ctx != null)
                {
                    if (type == typeof(FunctionContext))
                    {
                        return ctx;
                    }
                    else if (type == typeof(IDbContext))
                    {
                        //return ServiceLocator.GetScopedService<IDbContext>();
                        return ctx.scope.ServiceProvider.GetRequiredService<IDbContext>();
                    }
                    //else if (type == typeof(INacosNamingService))
                    //{
                    //    return ServiceLocator.Current.GetService(typeof(INacosNamingService));
                    //}
                    else if (type == typeof(IFluentEmailFactory))
                    {
                        return ctx.scope.ServiceProvider.GetRequiredService<IFluentEmailFactory>();
                        //return ServiceLocator.GetScopedService<IFluentEmailFactory>();
                    }
                    else if (type == typeof(IEasyCachingProvider))
                    {
                        return ctx.scope.ServiceProvider.GetRequiredService<IEasyCachingProvider>();
                        //return ServiceLocator.Current.GetService<IEasyCachingProvider>();
                    }
                }

                if (type == typeof(IJob))
                {
                    return ServiceLocator.Current.GetService(typeof(IJob));
                }
                else
                {
                    return ServiceLocator.Current.GetService(type);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "函数 {path} 执行错误, 未找到参数相关类型注入 {paramTypeName} ", Path, type.FullName);
            }
            return null;
        }

        private object GetParamValue(FunctionContext ctx, string name, Type type, bool hasDefaultValue = false, object defaultValue = null)
        {
            bool paramIsPrimitiveType = type.IsPrimitiveType();
            if (ctx.Current.Args != null && ctx.Current.Args.TryGetValue(name, out var inputVal))
            {
                if (inputVal is JsonElement element)
                {
                    return element.Deserialize(type, JsonHelper.SerializerOptions);
                }
                else if (paramIsPrimitiveType)
                {
                    return ConvertHelper.ChangeType(inputVal, type);
                }
                else
                {
                    //if (inputVal is JObject pairs)
                    //{
                    //    //return ((JObject)inputParam.Value).ToObject<Dictionary<string, object>>();
                    //    return pairs.ToObject(paramInfo.ParameterType);
                    //}

                    return null;
                }
            }

            if (!paramIsPrimitiveType)
            {
                var val = GetParamValueByType(type, ctx);
                if (val != null) return val;
            }

            if (!ctx.globalData.TryGetValue(name, out var value))
            {
                if (hasDefaultValue)
                {
                    value = paramIsPrimitiveType ? defaultValue : Activator.CreateInstance(type);
                }
            }

            return value;
        }
    }
}
