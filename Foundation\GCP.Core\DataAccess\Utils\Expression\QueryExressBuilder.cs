﻿using System.Linq.Expressions;
using System.Collections.ObjectModel;
using GCP.Common;

namespace GCP.DataAccess
{
    internal static class EvaluatorEx
    {
        internal static Type DbFuncType = typeof(DbFunc);
        internal static Type SqlBuilderType = typeof(ISqlBuilder);

        internal static ConstantExpression GetConstant(this Expression m)
        {
            return Expression.Constant(Expression.Lambda(m).Compile().DynamicInvoke(null), m.Type);
        }

        internal static string GetColumnFormat(string methodName, string columnName)
        {
            string tmp = "";
            switch (methodName)
            {
                case "Upper":
                    tmp = "UPPER({0})";
                    break;
                case "Lower":
                    tmp = "LOWER({0})";
                    break;
                case "Sum":
                    tmp = "SUM({0})";
                    break;
                case "Max":
                    tmp = "MAX({0})";
                    break;
                case "Min":
                    tmp = "MIN({0})";
                    break;
                case "Avg":
                    tmp = "AVG({0})";
                    break;
                case "Count":
                    tmp = "COUNT({0})";
                    break;
            }
            return tmp == "" ? "" : string.Format(tmp, columnName);
        }
    }

    /// <summary>
    /// 根据列名表达式计算
    /// </summary>
    internal class ColumnEvaluator : ExpressionVisitor
    {
        /// <summary>
        /// 列名
        /// </summary>
        internal List<string> _columnNames { get; set; }
        /// <summary>
        /// 表别名.列名
        /// </summary>
        private Stack<string> _columnNameWithTableAlias { get; set; }
        /// <summary>
        /// 列别名
        /// </summary>
        private List<string> _columnAlias { get; set; }

        /// <summary>
        /// 表别名.列名
        /// </summary>
        internal List<string> ColumnNames
        {
            get
            {
                return this._columnNameWithTableAlias.Reverse().ToList();
            }
        }

        private List<string> _columnNameWithAlias;
        /// <summary>
        /// 表别名.列名 AS 列别名
        /// </summary>
        internal List<string> ColumnNameWithAlias
        {
            get
            {
                if (_columnNameWithAlias == null)
                {
                    var colNames = this._columnNameWithTableAlias.Reverse().ToList();
                    if (_columnAlias.Count != 0)
                    {
                        for (int i = 0; i < colNames.Count; i++)
                        {
                            var alias = _columnAlias[i];
                            if (alias != null && _columnNames[i] != alias)
                            {
                                colNames[i] += " AS " + alias;
                            }
                        }
                    }

                    _columnNameWithAlias = colNames.Distinct().ToList();
                }

                return _columnNameWithAlias;
            }
        }

        private Dictionary<string, Type> _tableTypes { get; set; }

        public ColumnEvaluator(LambdaExpression exp, Dictionary<string, Type> tableTypes = null)
        {
            this._columnNames = new List<string>();
            this._columnNameWithTableAlias = new Stack<string>();
            this._columnAlias = new List<string>();
            this._tableTypes = tableTypes;
            this.Visit(exp.Body);
        }

        protected override Expression VisitMemberAccess(MemberExpression m)
        {
            this._columnNames.Add(m.Member.Name);
            this._columnNameWithTableAlias.Push(string.Format("{0}.{1}", m.Expression, m.Member.Name));
            return m;
        }

        protected override ReadOnlyCollection<Expression> VisitExpressionList(ReadOnlyCollection<Expression> original)
        {
            foreach (var item in original)
                this.Visit(item);
            return original;
        }

        protected override NewExpression VisitNew(NewExpression nex)
        {
            if (nex.Members != null)
            {
                for (int i = 0; i < nex.Members.Count; i++)
                {
                    var member = nex.Members[i];
                    var argument = nex.Arguments[i];

                    if (this._tableTypes != null)
                    {
                        Type type;
                        if (this._tableTypes.TryGetValue(member.Name, out type))
                        {
                            var entity = EntityCache.GetFields(type);
                            foreach (var field in entity)
                            {
                                this._columnAlias.Add(null);

                                this._columnNames.Add(field.Value.Name);
                                this._columnNameWithTableAlias.Push(string.Format("{0}.{1}", member.Name, field.Value.Name));
                            }

                            continue;
                        }
                    }

                    this._columnAlias.Add(member.Name);
                    this.Visit(argument);
                }
            }
            else
            {
                throw new Exception("Column type must be an Object type[new {a=x,b=x}]");
            }

            return nex;
        }

        protected override Expression VisitMethodCall(MethodCallExpression m)
        {
            IEnumerable<Expression> args = this.VisitExpressionList(m.Arguments);
            if (m.Method.DeclaringType == EvaluatorEx.DbFuncType)
            {
                string colName = this._columnNameWithTableAlias.Pop();
                string result = EvaluatorEx.GetColumnFormat(m.Method.Name, colName);
                if (string.IsNullOrEmpty(result)) result = colName;
                this._columnNameWithTableAlias.Push(result);
                this._columnNames[this._columnNames.Count - 1] = result;
            }
            return m;
        }
    }

    /// <summary>
    /// 根据子查询计算表达式
    /// </summary>
    internal class TableEvaluator : ExpressionVisitor
    {
        private Dictionary<string, ISqlBuilder> _tableSql { get; set; }
        private Dictionary<string, Type> _tableTypes { get; set; }

        public TableEvaluator(LambdaExpression exp, Dictionary<string, Type> tableTypes, Dictionary<string, ISqlBuilder> tableSql)
        {
            this._tableTypes = tableTypes;
            this._tableSql = tableSql;
            this.Visit(exp.Body);
        }
        protected override NewExpression VisitNew(NewExpression nex)
        {
            for (int i = 0; i < nex.Members.Count; i++)
            {
                var arg = nex.Arguments[i];
                if (EvaluatorEx.SqlBuilderType.IsAssignableFrom(arg.Type))
                {
                    var memberName = nex.Members[i].Name;
                    var sql = arg.GetConstant().Value as ISqlBuilder;
                    if (sql != null)
                    {
                        this._tableSql[memberName] = sql;
                    }
                }
            }

            return nex;
        }
    }


    internal class TableJoin
    {
        public string LeftTableAlias { get; set; }
        public string RightTableAlias { get; set; }
        public string JoinSql { get; set; }
        internal List<object> Arguments { get; set; }
        public DbJoinType JoinType { get; set; }

        public TableJoin()
        {
            Arguments = new List<object>();
        }
    }

    /// <summary>
    /// 根据表连接计算表达式
    /// </summary>
    internal class TableJoinEvaluator : ExpressionVisitor
    {
        private Dictionary<string, Type> _tableTypes { get; set; }
        private Dictionary<string, TableJoin> _tableJoin { get; set; }
        private DbJoinType _joinType { get; set; }
        private LambdaExpression _exp;

        public TableJoinEvaluator(LambdaExpression exp, Dictionary<string, Type> tableTypes, Dictionary<string, TableJoin> tableJoin, DbJoinType joinType)
        {
            this._exp = exp;
            this._tableTypes = tableTypes;
            this._tableJoin = tableJoin;
            this._joinType = joinType;

            this.Visit(exp.Body);
        }

        protected override Expression VisitBinary(BinaryExpression b)
        {
            if (b.Left is BinaryExpression expression)
            {
                VisitBinary(expression);

                ConditionEvaluator builder = new ConditionEvaluator(_exp, false);
                if (builder.Condition != null)
                {
                    var joinItem = this._tableJoin.Last().Value;
                    joinItem.JoinSql = builder.Condition;
                    joinItem.Arguments = builder.Arguments;
                }

                return b;
            }

            var leftM = (MemberExpression)b.Left;
            var rightM = (MemberExpression)b.Right;

            var joinObj = new TableJoin()
            {
                LeftTableAlias = ((ParameterExpression)leftM.Expression).Name,
                RightTableAlias = ((ParameterExpression)rightM.Expression).Name,
                JoinType = this._joinType
            };

            if (this._tableJoin.ContainsKey(joinObj.LeftTableAlias))
            {
                if (this._tableJoin[joinObj.LeftTableAlias].JoinType != this._joinType)
                {
                    throw new Exception(string.Format("The table[{0}] cannot use different join type.", joinObj.LeftTableAlias));
                }
            }

            joinObj.JoinSql = string.Format("{0}.{1} = {2}.{3}", joinObj.LeftTableAlias, leftM.Member.Name, joinObj.RightTableAlias, rightM.Member.Name);

            this._tableJoin[joinObj.LeftTableAlias] = joinObj;

            return b;
        }
    }

    /// <summary>
    /// 根据条件表达式计算
    /// </summary>
    internal class ConditionEvaluator : ExpressionVisitor
    {
        private bool _filter { get; set; }
        private bool _appendAlias { get; set; }
        private bool _convertNull { get; set; }
        private Stack<string> conditionParts { get; set; }
        internal List<object> Arguments { get; set; }
        internal string Condition { get; set; }

        internal List<string> Conditions
        {
            get
            {
                return this.conditionParts.Reverse().ToList();
            }
        }


        public ConditionEvaluator(LambdaExpression exp, bool filter, bool appendAlias = true, bool convertNull = true)
        {
            this._filter = filter;
            this._appendAlias = appendAlias;
            this._convertNull = convertNull;
            conditionParts = new Stack<string>();
            Arguments = new List<object>();

            this.Visit(exp.Body);
            this.Condition = this.conditionParts.Count > 0 ? this.conditionParts.First() : null;
        }

        private void WhereIn(bool isNot)
        {
            string right = this.conditionParts.Pop();
            string left = this.conditionParts.Pop();

            if (right == "NULL")
            {
                this.conditionParts.Push(this._filter ? null : string.Format("{0} {1} {2}", left, isNot ? "IS NOT" : "IS", right));
                return;
            }

            //int i = this.Arguments.Count - 1;
            //var value = this.Arguments[i];
            //if (value is ISqlBuilder)
            //{

            //}

            this.conditionParts.Push(string.Format("{0} {1} {2}", left, isNot ? "NOT IN" : "IN", right));
        }

        private void WhereLike(MethodCallExpression m, int status)
        {
            string right = this.conditionParts.Pop();
            string left = this.conditionParts.Pop();

            if (right == "NULL")
            {
                this.conditionParts.Push(this._filter ? null : string.Format("{0} IS {1}", left, right));
                return;
            }

            int i = this.Arguments.Count - 1;
            var value = this.Arguments[i];

            string tmpStr = "";
            if ((status & 1) == 1) tmpStr += "%";
            tmpStr += value;
            if ((status & 2) == 2) tmpStr += "%";

            this.Arguments[i] = tmpStr;

            this.conditionParts.Push(string.Format("{0} LIKE {1}", left, right));
        }

        protected override Expression VisitMemberAccess(MemberExpression m)
        {
            if (m.Expression == null)
            {
                return this.Visit(m.GetConstant());
            }
            switch (m.Expression.NodeType)
            {
                case ExpressionType.Parameter:
                    if (this._appendAlias)
                    {
                        this.conditionParts.Push(string.Format("{0}.{1}", m.Expression, m.Member.Name));
                    }
                    else
                    {
                        this.conditionParts.Push(m.Member.Name);
                    }
                    return m;
                case ExpressionType.Constant:
                case ExpressionType.MemberAccess:
                    return this.Visit(m.GetConstant());
            }

            return m;
        }

        protected override ReadOnlyCollection<Expression> VisitExpressionList(ReadOnlyCollection<Expression> original)
        {
            foreach (var item in original)
            {
                this.Visit(item);
            }
            return original;
        }

        protected override Expression VisitConstant(ConstantExpression c)
        {
            if (DbHelper.IsNull(c.Value))
            {
                this.conditionParts.Push("NULL");
                return c;
            }

            this.Arguments.Add(c.Value);
            this.conditionParts.Push(string.Format("{{{0}}}", this.Arguments.Count - 1));

            return c;
        }

        protected override Expression VisitMethodCall(MethodCallExpression m)
        {
            if (m.Object != null)
            {
                return this.Visit(m.GetConstant());
            }

            IEnumerable<Expression> args = this.VisitExpressionList(m.Arguments);
            if (m.Method.DeclaringType == EvaluatorEx.DbFuncType)
            {
                switch (m.Method.Name)
                {
                    case "In":
                        WhereIn(false);
                        break;
                    case "NotIn":
                        WhereIn(true);
                        break;
                    case "Like":
                        WhereLike(m, 3);
                        break;
                    case "LikeLeft":
                        WhereLike(m, 1);
                        break;
                    case "LikeRight":
                        WhereLike(m, 2);
                        break;
                    default:
                        string colName = this.conditionParts.Pop();
                        string result = EvaluatorEx.GetColumnFormat(m.Method.Name, colName);
                        if (string.IsNullOrEmpty(result)) result = colName;
                        this.conditionParts.Push(result);
                        break;
                }
            }
            return m;
        }

        protected override Expression VisitBinary(BinaryExpression b)
        {
            this.Visit(b.Left);
            this.Visit(b.Right);

            string right = this.conditionParts.Pop();
            string left = this.conditionParts.Pop();

            string opr;
            switch (b.NodeType)
            {
                case ExpressionType.Equal:
                    opr = "=";
                    break;
                case ExpressionType.NotEqual:
                    opr = "<>";
                    break;
                case ExpressionType.GreaterThan:
                    opr = ">";
                    break;
                case ExpressionType.GreaterThanOrEqual:
                    opr = ">=";
                    break;
                case ExpressionType.LessThan:
                    opr = "<";
                    break;
                case ExpressionType.LessThanOrEqual:
                    opr = "<=";
                    break;
                case ExpressionType.AndAlso:
                    opr = "AND";
                    break;
                case ExpressionType.OrElse:
                    opr = "OR";
                    break;
                case ExpressionType.Add:
                    opr = "+";
                    break;
                case ExpressionType.Subtract:
                    opr = "-";
                    break;
                case ExpressionType.Multiply:
                    opr = "*";
                    break;
                case ExpressionType.Divide:
                    opr = "/";
                    break;
                default:
                    throw new NotSupportedException(b.NodeType + "is not supported.");
            }

            if (this._convertNull && right == "NULL")
            {
                if (this._filter)
                {
                    this.conditionParts.Push(null);
                    return b;
                }

                if (opr == "=")
                    opr = "IS";
                else if (opr == "<>")
                    opr = "IS NOT";
            }

            bool leftIsNull = left == null;
            bool rightIsNull = right == null;

            if (leftIsNull && rightIsNull)
            {
                this.conditionParts.Push(null);
            }
            else if (leftIsNull)
            {
                if (b.Right.NodeType == ExpressionType.OrElse && b.NodeType != ExpressionType.OrElse)
                {
                    right = "(" + right + ")";
                }
                this.conditionParts.Push(right);
            }
            else if (rightIsNull)
            {
                if (b.Left.NodeType == ExpressionType.OrElse && b.NodeType != ExpressionType.OrElse)
                {
                    left = "(" + left + ")";
                }
                this.conditionParts.Push(left);
            }
            else
            {
                if (b.Left.NodeType == ExpressionType.OrElse && b.NodeType != ExpressionType.OrElse)
                {
                    left = "(" + left + ")";
                }
                if (b.Right.NodeType == ExpressionType.OrElse && b.NodeType != ExpressionType.OrElse)
                {
                    right = "(" + right + ")";
                }
                this.conditionParts.Push(string.Format("{0} {1} {2}", left, opr, right));
            }

            return b;
        }
    }
}
