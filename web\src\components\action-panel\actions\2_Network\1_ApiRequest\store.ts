import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfo } from './model';

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};
  const isNewAction = !currentArgs.name; // 判断是否为新增动作

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      apiId: '',
      description: '',
      inputs: [] as FlowData[],
      useRoot: isNewAction ? true : (currentArgs.useRoot ?? false), // 新增动作默认true，历史配置默认false
    } as ArgsInfo),
    ...currentArgs,
  }) as ArgsInfo;
};

export const useApiRequestStore = defineStore('ApiRequest', {
  state: () => {
    const state = { args: null } as { args: ArgsInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [
        {
          id: 'inputs',
          key: 'inputs',
          description: '输入参数',
          type: 'object',
          children: this.args.inputs || [],
        },
        {
          id: 'result',
          key: 'result',
          description: '结果',
          type: 'object',
          value: {
            type: 'variable',
            variableType: 'current',
            variableValue: 'result',
          },
        },
      ] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 根据useRoot参数决定是否使用根节点
      const resultVariable = this.variables.find((item) => item.id === 'result');
      if (resultVariable) {
        if (!args.useRoot) {
          // 不使用根节点，只输出结果变量
          actionFlowStore.setStepResultData([resultVariable]);
        } else {
          // 使用根节点，创建根节点绑定
          const resultNode = {
            id: 'result',
            key: 'result',
            description: '请求结果',
            type: 'object',
            value: {
              type: 'variable' as const,
              variableType: 'current' as const,
              variableValue: 'result',
            },
          };
          actionFlowStore.setStepResultData([resultNode]);
        }
      }
    },
  },
});
