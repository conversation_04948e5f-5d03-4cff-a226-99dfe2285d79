<template>
  <div class="variable-item">
    <t-row :gutter="16">
      <t-col :span="4">
        <t-form-item label="变量名">
          <t-input v-model="formData.key" placeholder="变量名" />
        </t-form-item>
      </t-col>
      <t-col :span="3">
        <t-form-item label="类型">
          <t-select 
            v-model="formData.value.type"
            :options="variableTypes"
            @change="onTypeChange"
          />
        </t-form-item>
      </t-col>
      <t-col :span="4">
        <t-form-item label="默认值">
          <template v-if="formData.value.type === 'text'">
            <t-input v-model="formData.value.textValue" placeholder="文本值" />
          </template>
          <template v-else-if="formData.value.type === 'number'">
            <t-input-number v-model="formData.value.numberValue" placeholder="数字值" />
          </template>
          <template v-else-if="formData.value.type === 'boolean'">
            <t-switch v-model="formData.value.booleanValue" />
          </template>
          <template v-else>
            <t-textarea 
              v-model="objectValueJson"
              placeholder="JSON格式的对象值"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </template>
        </t-form-item>
      </t-col>
      <t-col :span="4">
        <t-form-item label="描述">
          <t-input v-model="formData.description" placeholder="变量描述" />
        </t-form-item>
      </t-col>
      <t-col :span="1">
        <t-form-item label=" ">
          <t-button 
            size="small" 
            theme="danger" 
            variant="text"
            @click="$emit('delete', index)"
          >
            <template #icon>
              <delete-icon />
            </template>
          </t-button>
        </t-form-item>
      </t-col>
    </t-row>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { DeleteIcon } from 'tdesign-icons-vue-next';

interface FlowVariable {
  key: string;
  value: {
    type: string;
    textValue?: string;
    numberValue?: number;
    booleanValue?: boolean;
    objectValue?: any;
  };
  description?: string;
}

interface Props {
  variable: FlowVariable;
  index: number;
}

const props = defineProps<Props>();

const emits = defineEmits<{
  update: [index: number, variable: FlowVariable];
  delete: [index: number];
}>();

const formData = ref<FlowVariable>({ ...props.variable });

const variableTypes = [
  { label: '文本', value: 'text' },
  { label: '数字', value: 'number' },
  { label: '布尔', value: 'boolean' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' }
];

// 对象值的JSON编辑
const objectValueJson = computed({
  get: () => {
    try {
      return JSON.stringify(formData.value.value.objectValue, null, 2);
    } catch {
      return '{}';
    }
  },
  set: (value: string) => {
    try {
      formData.value.value.objectValue = JSON.parse(value);
    } catch {
      // 忽略JSON解析错误
    }
  }
});

// 类型变化处理
const onTypeChange = (newType: string) => {
  // 清除其他类型的值
  formData.value.value = { type: newType };
  
  // 设置默认值
  switch (newType) {
    case 'text':
      formData.value.value.textValue = '';
      break;
    case 'number':
      formData.value.value.numberValue = 0;
      break;
    case 'boolean':
      formData.value.value.booleanValue = false;
      break;
    case 'object':
    case 'array':
      formData.value.value.objectValue = newType === 'array' ? [] : {};
      break;
  }
};

// 监听表单数据变化
watch(
  () => formData.value,
  (newValue) => {
    emits('update', props.index, { ...newValue });
  },
  { deep: true }
);

// 监听外部数据变化
watch(
  () => props.variable,
  (newValue) => {
    formData.value = { ...newValue };
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
.variable-item {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--td-bg-color-container);
  border-radius: 8px;
  border: 1px solid var(--td-border-level-1-color);

  :deep(.t-form-item__label) {
    font-size: 12px;
    margin-bottom: 4px;
  }
}
</style>
