using GCP.Common;
using GCP.Eventbus.Infrastructure;
using GCP.Iot.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.Eventbus.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddSystemBusServices(this IServiceCollection services, IConfiguration configuration)
        {
            var redisConnectionString = configuration["redis:ConnectionString"]?.ToString();
            var instanceId = TUID.DeviceID(); // 使用设备ID作为实例标识
            var hasReids = !string.IsNullOrEmpty(redisConnectionString);

            // 添加系统消息总线
            services.AddMessageBus(new MessageBusOptions
            {
                Name = EventBusHelper.SystemEventBusName,
                Type = hasReids ? MessageBusType.Redis : MessageBusType.InMemory,
                IsEnabled = true,
                Settings = new Dictionary<string, string>
                {
                    { "Configuration", redisConnectionString ?? string.Empty },
                    { "IsBroadcast", true.ToString() },
                    { "InstanceId", instanceId },
                }
            },
            // 添加本地 IoT 消息总线
            new MessageBusOptions
            {
                Name = EventBusHelper.LocalIotEventBusName,
                Type = MessageBusType.InMemory,
                IsEnabled = true,
                Settings = new Dictionary<string, string>
                {
                    { "InstanceId", instanceId },
                }
            });

            return services;
        }

        public static IServiceCollection AddEventBusServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 注册 IoT 事件集成服务（如果 IoT 服务可用）
            try
            {
                services.AddSingleton<IotEventIntegrationService>();
            }
            catch
            {
                // IoT 服务不可用，跳过注册
            }

            // 注册事件总线初始化器
            services.AddSingleton<EventBusInitializer>();

            return services;
        }

        public static IServiceCollection AddMessageBus(this IServiceCollection services, params MessageBusOptions[] options)
        {
            services.AddSingleton<IMessageBusFactory>(sp =>
            {
                return new MessageBusFactory();
            });

            services.AddSingleton<IMessageBusManager>(sp =>
            {
                var factory = sp.GetRequiredService<IMessageBusFactory>();
                var manager = new MessageBusManager(factory);

                foreach (var option in options)
                {
                    manager.AddMessageBusAsync(option).GetAwaiter().GetResult();
                }

                return manager;
            });

            return services;
        }
    }
}