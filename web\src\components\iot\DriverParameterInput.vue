<template>
  <div class="driver-parameter-input">
    <!-- 布尔类型 - 开关 -->
    <t-switch v-if="isBooleanType" :model-value="booleanValue" @change="onBooleanChange" />

    <!-- 枚举类型 - 下拉选择 -->
    <t-select v-else-if="isEnumType" :model-value="modelValue" :options="enumOptions" @change="onValueChange" />

    <!-- 数字类型 - 数字输入框 -->
    <t-input-number v-else-if="isNumberType" :model-value="numberValue" @change="onNumberChange" />

    <!-- 字符串类型 - 文本输入框 -->
    <t-input v-else :model-value="modelValue" @input="onValueChange" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';

interface Props {
  modelValue: string | null | undefined;
  paramType: string;
  paramKey?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 调试信息
onMounted(() => {
  console.log('DriverParameterInput mounted:', {
    paramKey: props.paramKey,
    paramType: props.paramType,
    modelValue: props.modelValue,
  });
});

// 判断参数类型
const isBooleanType = computed(() => {
  return props.paramType === 'Boolean' || props.paramType === 'bool';
});

const isNumberType = computed(() => {
  return ['Int32', 'Int64', 'Double', 'Single', 'Decimal', 'int', 'long', 'double', 'float', 'decimal'].includes(
    props.paramType,
  );
});

const isEnumType = computed(() => {
  // 检查是否为枚举类型（通常枚举类型名称不是基础类型）
  const basicTypes = [
    'String',
    'Boolean',
    'Int32',
    'Int64',
    'Double',
    'Single',
    'Decimal',
    'DateTime',
    'string',
    'bool',
    'int',
    'long',
    'double',
    'float',
    'decimal',
  ];
  return !basicTypes.includes(props.paramType) && props.paramType !== 'String';
});

// 布尔值处理
const booleanValue = computed(() => {
  if (props.modelValue === null || props.modelValue === undefined || props.modelValue === '') {
    return false;
  }
  const value = props.modelValue.toString().toLowerCase();
  return value === 'true' || value === '1';
});

const onBooleanChange = (value: boolean) => {
  emit('update:modelValue', value.toString());
};

// 数字值处理
const numberValue = computed(() => {
  if (props.modelValue === null || props.modelValue === undefined || props.modelValue === '') {
    return 0;
  }
  return Number(props.modelValue);
});

const onNumberChange = (value: number) => {
  emit('update:modelValue', value.toString());
};

// 枚举选项（这里可以根据实际需求扩展）
const enumOptions = computed(() => {
  // 根据参数类型和参数键生成枚举选项
  // 这里可以根据实际的枚举类型进行扩展
  if (props.paramType === 'MasterType') {
    return [
      { label: 'TCP', value: 'Tcp' },
      { label: 'RTU', value: 'Rtu' },
      { label: 'RTU over TCP', value: 'RtuOnTcp' },
      { label: 'ASCII', value: 'Ascii' },
    ];
  }

  if (props.paramType === 'StopBits') {
    return [
      { label: '无', value: 'None' },
      { label: '1位', value: 'One' },
      { label: '1.5位', value: 'OnePointFive' },
      { label: '2位', value: 'Two' },
    ];
  }

  if (props.paramType === 'Parity') {
    return [
      { label: '无', value: 'None' },
      { label: '奇校验', value: 'Odd' },
      { label: '偶校验', value: 'Even' },
      { label: '标记', value: 'Mark' },
      { label: '空格', value: 'Space' },
    ];
  }

  // 默认返回空数组，如果是未知枚举类型，将显示为文本输入框
  return [];
});

// 通用值变更处理
const onValueChange = (value: string) => {
  emit('update:modelValue', value);
};
</script>

<style lang="less" scoped>
.driver-parameter-input {
  width: 100%;
}
</style>
