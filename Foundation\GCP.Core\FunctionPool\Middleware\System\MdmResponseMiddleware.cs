﻿using GCP.Common;
using Serilog;
using System.Runtime.Serialization;

namespace GCP.FunctionPool
{
    internal class MdmResponseMiddleware : IFunctionMiddleware
    {
        internal static async Task Handler(FunctionContext ctx, Func<Task> next)
        {
            var res = new MdmResponse();

            try
            {
                var req = ctx.GetHttpRequest();

                res.MessageId = req.Headers["Message-Id"];
                var msgHead = new MsgHead();
                msgHead.Parent_Message_Id =req.Headers["Parent-Message-Id"];
                msgHead.Message_Id = req.Headers["Message-Id"];
                msgHead.Priority = req.Headers["Priority"];
                msgHead.Message_Source_Code = req.Headers["Message-Source-Code"];
                msgHead.Queue = req.Headers["Queue"];
                msgHead.Producer_Request_Time = req.Headers["Producer-Request-Time"];
                msgHead.Producer_Client_User = req.Headers["Producer-Client-User"];
                msgHead.Producer_Client_IP = req.Headers["Producer-Client-IP"];
                ctx.globalData["$_Head"] = msgHead;

                Log.Information("MDM Head: {@MsgHead}", msgHead);
                await next();

                if (ctx.Result != null)
                {
                    if (ctx.Result is string)
                    {
                        res.Message = (string)ctx.Result;
                    }
                    else if (ctx.Result is MdmResponse)
                    {
                        res = ctx.Result as MdmResponse;   
                    }
                }
            }
            catch (CustomException ex)
            {
                res.Status = "E";
                res.Code = ex.Code ?? 500;
                res.Message = ex.Message;
            }
            catch (Exception ex)
            {
                res.Status = "E";
                res.Code = 500;
                res.Message = ex.Message;
            }

            ctx.Result = res;
        }
    }

    internal class MsgHead
    {
        public string Parent_Message_Id { get; set; }
        public string Message_Id { get; set; }
        public string Priority { get; set; }
        public string Message_Source_Code { get; set; }
        public string Queue { get; set; }
        public string Producer_Request_Time { get; set; }
        public string Producer_Client_User { get; set; }
        public string Producer_Client_IP { get; set; }
    }

    [DataContract]
    [Serializable]
    internal class MdmResponse
    {
        /// <summary>
        /// S 成功
        /// E 逻辑错误
        /// W 忽略数据
        /// </summary>
        [DataMember(Name = "status")]
        public string Status { get; set; } = "S";

        [DataMember(Name = "message")]
        public string Message { get; set; } = "";

        [DataMember(Name = "code")]
        public int? Code { get; set; } = 200;

        [DataMember(Name = "messageId")]
        public string MessageId { get; set; }

        [DataMember(Name = "requeue")]
        public string Requeue { get; set; } = "false";

        public MdmResponse()
        {
        }

        public MdmResponse(string status, string message)
        {
            Status = status;
            Message = message;
        }
    }
}
