<template>
  <t-dialog
    class="fm-dialog"
    :visible="props.isVisible"
    placement="center"
    attach="body"
    close-on-overlay-click
    @close-btn-click="onClickBack()"
    @closed="resetForm(isNewForm)"
  >
    <template #header>
      <h3>数据库信息配置</h3>
    </template>
    <!-- 中心内容 -->
    <template #body>
      <div class="fm-container">
        <div class="fm-nav">
          <div class="fm-nav-center">
            <t-steps v-model="activeTab" readonly>
              <t-step-item title="基础配置" />
              <t-step-item title="连接配置" />
            </t-steps>
          </div>
        </div>
        <div class="fm-content">
          <div v-show="activeTab === 0">
            <cmp-container full style="padding: 0 10px; height: auto">
              <cmp-card style="width: 700px; margin: 0 auto">
                <t-form
                  ref="form1"
                  :data="dataBaseData"
                  label-width="110px"
                  label-align="right"
                  style="padding: 0 10px"
                  colon
                  :rules="rules"
                  @submit="nextStep"
                >
                  <t-form-item label="数据源" name="dataProvider">
                    <t-select
                      v-model="dataBaseData.dataProvider"
                      :options="DataProviders"
                      @change="onDataProviderChange"
                    />
                  </t-form-item>
                  <t-form-item label="分组" name="group">
                    <t-auto-complete v-model="dataBaseData.group" :options="props.groupList"></t-auto-complete>
                  </t-form-item>
                  <t-form-item label="名称" name="dataSourceName">
                    <t-input v-model="dataBaseData.dataSourceName" placeholder="请输入数据库名称" />
                  </t-form-item>
                  <t-form-item label="注释" name="description">
                    <t-textarea v-model="dataBaseData.description" placeholder="请输入数据库注释" />
                  </t-form-item>
                  <t-form-item label="连接方式" name="connectBy">
                    <t-radio-group v-model="dataBaseData.connectBy" @change="onConnectByChange">
                      <t-radio :value="ConnectBy.HOST">主机名</t-radio>
                      <t-radio :value="ConnectBy.URL">连接串</t-radio>
                    </t-radio-group>
                  </t-form-item>
                </t-form>
              </cmp-card>
            </cmp-container>
          </div>
          <div v-show="activeTab === 1">
            <cmp-container full style="padding: 0 10px">
              <cmp-card style="width: 700px; margin: 0 auto">
                <!-- URL连接方式表单 -->
                <t-form
                  v-if="dataBaseData.connectBy === ConnectBy.URL"
                  ref="form2"
                  :data="dataBaseData"
                  label-width="110px"
                  label-align="right"
                  style="padding: 0 10px"
                  colon
                  :rules="rules"
                >
                  <t-form-item label="连接串" name="connectionString">
                    <t-textarea
                      v-model="dataBaseData.connectionString"
                      :placeholder="connectionStringPlaceholder"
                      :autosize="{
                        minRows: 3,
                        maxRows: 5,
                      }"
                    />
                  </t-form-item>
                </t-form>

                <!-- HOST连接方式 - Mysql Oracle PostgreSQL表单 -->
                <t-form
                  v-if="
                    dataBaseData.connectBy === ConnectBy.HOST &&
                    (dataBaseData.dataProvider === 'MySql' ||
                      dataBaseData.dataProvider === 'Oracle' ||
                      dataBaseData.dataProvider === 'PostgreSQL')
                  "
                  ref="form2"
                  :data="dataBaseData"
                  label-width="110px"
                  label-align="right"
                  style="padding: 0 10px"
                  colon
                  :rules="rules"
                >
                  <t-form-item label="主机名" name="host">
                    <t-input v-model="dataBaseData.host" placeholder="127.0.0.1" @change="updateConnectionString" />
                  </t-form-item>
                  <t-form-item label="端口" name="port">
                    <t-input-number
                      v-model="dataBaseData.port"
                      theme="normal"
                      :placeholder="getDefaultPort(dataBaseData.dataProvider)?.toString()"
                      style="width: 120px"
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item v-if="dataBaseData.dataProvider === 'Oracle'" label="服务名" name="serviceName">
                    <t-input v-model="dataBaseData.serviceName" @change="updateConnectionString" />
                  </t-form-item>
                  <t-form-item v-if="dataBaseData.dataProvider === 'Oracle'" label="选择类型" name="serviceType">
                    <t-radio-group v-model="dataBaseData.serviceType" @change="updateConnectionString">
                      <t-radio :value="1">服务名</t-radio>
                      <t-radio :value="2">SID</t-radio>
                    </t-radio-group>
                  </t-form-item>
                  <t-form-item label="初始数据库" name="originDataBase">
                    <t-input
                      v-model="dataBaseData.originDataBase"
                      style="width: 200px"
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item label="用户名" name="userName">
                    <t-input
                      v-model="dataBaseData.userName"
                      autocomplete="off"
                      placeholder="请输入用户名"
                      style="width: 200px"
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item label="密码" name="password">
                    <t-input
                      v-model="dataBaseData.password"
                      type="password"
                      autocomplete="new-password"
                      placeholder="请输入密码"
                      style="width: 200px"
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item label="连接串" name="connectionString">
                    <t-textarea
                      v-model="dataBaseData.connectionString"
                      :readonly="true"
                      :placeholder="connectionStringPlaceholder"
                    />
                  </t-form-item>
                </t-form>

                <!-- HOST连接方式 - SQL Server表单 -->
                <t-form
                  v-if="dataBaseData.connectBy === ConnectBy.HOST && dataBaseData.dataProvider === 'SQL Server'"
                  ref="form2"
                  :data="dataBaseData"
                  label-width="120px"
                  label-align="right"
                  style="padding: 0 10px"
                  colon
                  :rules="rules"
                >
                  <t-form-item label="主机名" name="host">
                    <t-input v-model="dataBaseData.host" placeholder="127.0.0.1" @change="updateConnectionString" />
                  </t-form-item>
                  <t-form-item label="初始数据库" name="originDataBase">
                    <t-input
                      v-model="dataBaseData.originDataBase"
                      style="width: 100px"
                      placeholder="master"
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item label="验证">
                    <t-select
                      v-model="dataBaseData.serviceType"
                      :options="SqlServerAuthTypes"
                      show-arrow
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item v-if="showUserName" label="用户名" name="userName">
                    <t-input
                      v-model="dataBaseData.userName"
                      placeholder="请输入用户名"
                      style="width: 200px"
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item v-if="showPassWord" label="密码" name="password">
                    <t-input
                      v-model="dataBaseData.password"
                      type="password"
                      autocomplete="new-password"
                      placeholder="请输入密码"
                      style="width: 200px"
                      @change="updateConnectionString"
                    />
                  </t-form-item>
                  <t-form-item label="连接串" name="connectionString">
                    <t-textarea
                      v-model="dataBaseData.connectionString"
                      :readonly="true"
                      :placeholder="connectionStringPlaceholder"
                    />
                  </t-form-item>
                </t-form>
              </cmp-card>
            </cmp-container>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <t-space>
          <template v-if="activeTab === 0">
            <t-button theme="default" @click="onClickBack">取消</t-button>
            <t-button theme="primary" @click="form1.submit(nextStep)">下一步</t-button>
          </template>
          <template v-else>
            <t-button theme="default" @click="activeTab = 0">上一步</t-button>
            <t-button variant="outline" @click="testConnection">测试连接</t-button>
            <t-button theme="primary" @click="onSubmit">提交</t-button>
          </template>
        </t-space>
      </div>
    </template>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'FmDialog',
};
</script>
<script setup lang="ts">
import { FormProps, MessagePlugin } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';

import { api, Services } from '@/api/system';

import { DataProviders, type DataProviderType, DefaultPorts, SqlServerAuthTypes } from './constants';
import { ConnectBy, type IDataSourceForm } from './types';
import { cloneDeep } from 'lodash-es';

const form1 = ref();
const form2 = ref();
const activeTab = ref(0);

const emit = defineEmits<{
  (e: 'back'): void;
  (e: 'save'): void;
}>();

const props = defineProps<{
  isVisible: boolean;
  isNewForm: boolean;
  templateData?: IDataSourceForm;
  groupList: string[];
  onClose: () => void;
}>();

// 重置表单
const resetForm = (isNew: boolean) => {
  activeTab.value = 0;
  dataBaseData.value = {
    id: '',
    dataSourceName: '',
    group: '',
    dataProvider: '',
    description: '',
    connectBy: ConnectBy.HOST,
    host: '',
    port: 0,
    userName: '',
    password: '',
    state: 1,
    connectionString: '',
  };
};

const dataBaseData = ref<IDataSourceForm>({
  id: '',
  dataSourceName: '',
  group: '',
  dataProvider: 'MySql',
  description: '',
  connectBy: ConnectBy.HOST,
  host: '',
  port: 0,
  userName: '',
  password: '',
  state: 1,
  connectionString: '',
});

// 获取默认端口
const getDefaultPort = (provider: DataProviderType) => {
  return DefaultPorts[provider];
};

// 数据源类型变更
const onDataProviderChange = () => {
  const provider = dataBaseData.value.dataProvider as DataProviderType;
  const port = getDefaultPort(provider);
  dataBaseData.value.port = port;

  if (provider === 'Oracle') {
    dataBaseData.value.serviceType = 1;
  } else if (provider === 'SqlServer') {
    dataBaseData.value.originDataBase = 'master';
    dataBaseData.value.serviceType = 1;
  } else if (provider === 'PostgreSQL') {
    dataBaseData.value.originDataBase = 'postgres';
  }
  updateConnectionString();
};

// 连接串占位符
const connectionStringPlaceholder = computed(() => {
  switch (dataBaseData.value.dataProvider) {
    case 'MySql':
      return 'Server=localhost;Port=3306;Database=mydb;Uid=root;Pwd=password;';
    case 'Oracle':
      return 'Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL)));User Id=system;Password=password;';
    case 'SQL Server':
      return 'Server=localhost;Database=master;User Id=sa;Password=password;';
    case 'PostgreSQL':
      return 'Server=localhost;Port=5432;Database=postgres;User Id=postgres;Password=password;';
    default:
      return '';
  }
});

// 更新连接串
const updateConnectionString = () => {
  const data = dataBaseData.value;
  if (data.connectBy === ConnectBy.URL) return;
  let connectionString = '';
  switch (data.dataProvider as DataProviderType) {
    case 'MySql':
      connectionString = `Server=${data.host || 'localhost'};Port=${data.port || DefaultPorts.MySql};Database=${data.originDataBase || ''};User=${data.userName || ''};Password=${data.password || ''};Charset=utf8;Convert Zero Datetime=True;SslMode=None;Allow User Variables=True;AllowLoadLocalInfile=true;`;
      break;
    case 'Oracle':
      if (data.serviceType === 1) {
        connectionString = `Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=${data.host})(PORT=${data.port || DefaultPorts.Oracle}))(CONNECT_DATA=(SERVICE_NAME=${data.serviceName})));User Id=${data.userName || ''};Password=${data.password || ''};`;
      } else {
        connectionString = `Data Source=${data.host}:${data.port || DefaultPorts.Oracle}/${data.serviceName};User Id=${data.userName || ''};Password=${data.password || ''};`;
      }
      break;
    case 'SqlServer':
      if (data.serviceType === 1) {
        connectionString = `Server=${data.host};Database=${data.originDataBase || 'master'};User Id=${data.userName || ''};Password=${data.password || ''};`;
      } else {
        connectionString = `Server=${data.host};Database=${data.originDataBase || 'master'};Integrated Security=SSPI;`;
      }
      break;
    case 'PostgreSQL':
      connectionString = `Host=${data.host};Port=${data.port || DefaultPorts.PostgreSQL};Database=${data.originDataBase || ''};Username=${data.userName || ''};Password=${data.password || ''};`;
      break;
    default:
      connectionString = '';
      break;
  }
  data.connectionString = connectionString;
};

watch(
  () => props.templateData,
  (val) => {
    if (val) {
      // 深拷贝模板数据以避免引用问题
      dataBaseData.value = cloneDeep(val);
      // 如果是编辑模式，需要确保所有相关字段都被正确设置
      if (!props.isNewForm) {
        // 确保端口是数字类型
        dataBaseData.value.port = parseInt(val.port as any, 10) || 0;
        // 根据数据源类型设置默认值
        if (val.dataProvider === 'Oracle' && !val.serviceType) {
          dataBaseData.value.serviceType = 1;
        } else if (val.dataProvider === 'SQL Server') {
          if (!val.originDataBase) {
            dataBaseData.value.originDataBase = 'master';
          }
          if (!val.serviceType) {
            dataBaseData.value.serviceType = 1;
          }
        } else if (val.dataProvider === 'PostgreSQL' && !val.originDataBase) {
          dataBaseData.value.originDataBase = 'postgres';
        }
        // 更新连接串
        updateConnectionString();
      }
    } else {
      resetForm(true);
    }
  },
  { immediate: true, deep: true },
);

const showUserName = computed(() => {
  return dataBaseData.value.dataProvider !== 'SQL Server' || dataBaseData.value.serviceType === 1;
});

const showPassWord = computed(() => {
  return showUserName.value;
});

// 连接方式变更
const onConnectByChange = () => {
  if (dataBaseData.value.connectBy === ConnectBy.URL) {
    // 切换到URL方式时，清空主机相关信息
    dataBaseData.value.host = '';
    dataBaseData.value.port = 0;
    dataBaseData.value.userName = '';
    dataBaseData.value.password = '';
    dataBaseData.value.serviceName = '';
    dataBaseData.value.originDataBase = '';
  } else {
    // 切换到HOST方式时，根据数据源类型设置默认值
    onDataProviderChange();
  }
};

// 表单验证规则
const rules: FormProps['rules'] = {
  dataProvider: [{ required: true, message: '数据源必填', type: 'warning' }],
  group: [{ required: true, message: '分组必填', type: 'warning' }],
  dataSourceName: [{ required: true, message: '名称必填', type: 'warning' }],
  connectBy: [{ required: true, message: '连接方式必填', type: 'warning' }],
  connectionString: [
    {
      required: true,
      message: '连接串必填',
      type: 'warning',
      validator: (val: string) => {
        return dataBaseData.value.connectBy === ConnectBy.URL ? !!val : true;
      },
    },
  ],
  host: [
    {
      required: true,
      message: '主机号必填',
      type: 'warning',
      validator: (val: string) => {
        return dataBaseData.value.connectBy === ConnectBy.HOST ? !!val : true;
      },
    },
    {
      pattern:
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^localhost$/,
      message: '请输入正确的IP地址格式或localhost',
      validator: (val: string) => {
        return dataBaseData.value.connectBy === ConnectBy.HOST;
      },
    },
  ],
  port: [
    {
      required: true,
      message: '端口必填',
      type: 'warning',
      validator: (val: number) => {
        return dataBaseData.value.connectBy === ConnectBy.HOST ? !!val : true;
      },
    },
  ],
  userName: [
    {
      required: true,
      message: '用户名必填',
      type: 'warning',
      validator: (val: string) => {
        return dataBaseData.value.connectBy === ConnectBy.HOST ? !!val : true;
      },
    },
  ],
  password: [
    {
      required: true,
      message: '密码必填',
      type: 'warning',
      validator: (val: string) => {
        return dataBaseData.value.connectBy === ConnectBy.HOST ? !!val : true;
      },
    },
  ],
};

// 下一步
const nextStep = async ({ validateResult }) => {
  if (validateResult === true) {
    activeTab.value = 1;
  }
};

// 测试连接
const testConnection = async () => {
  try {
    updateConnectionString();
    const backendData = convertToBackendModel(dataBaseData.value);
    const res = await api.run(Services.dataSourceTestConnection, { dataSource: backendData });
    if (res) {
      MessagePlugin.success('连接成功');
    }
  } catch (error) {
    MessagePlugin.error(error.message || '连接失败');
  }
};

// 数据转换函数
const convertToBackendModel = (formData: IDataSourceForm) => {
  const { host, originDataBase, userName, ...rest } = formData;
  return {
    ...rest,
    serverAddress: host,
    database: originDataBase,
    userId: userName,
  };
};

// 提交处理
const onSubmit = async () => {
  // 验证第二步的表单
  const validateResult = await form2.value.validate();
  if (validateResult !== true) {
    // 验证失败，可以给出提示或什么都不做，让表单自带的错误提示生效
    return;
  }

  try {
    // 确保使用最新的连接串（如果connectBy是HOST）
    if (dataBaseData.value.connectBy === ConnectBy.HOST) {
      updateConnectionString();
    }

    const backendData = convertToBackendModel(dataBaseData.value);
    if (props.isNewForm) {
      await api.run(Services.dataSourceAdd, backendData);
    } else {
      await api.run(Services.dataSourceUpdate, backendData);
    }
    MessagePlugin.success(`${props.isNewForm ? '新增' : '更新'}成功`);
    emit('save');
  } catch (error) {
    MessagePlugin.error(`${props.isNewForm ? '新增' : '更新'}失败: ${error.message || '请检查配置'}`);
  }
};

const onClickBack = () => {
  resetForm(props.isNewForm);
  emit('back');
};
</script>

<!--  eslint-disable-next-line vue-scoped-css/enforce-style-type -->
<style lang="less">
.t-dialog__header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  margin-top: 10px;

  .t-dialog__close {
    width: 25px;
    height: 25px;
    font-size: 25px;
  }
}

.t-dialog__footer {
  padding: 0px;
}
</style>
<style lang="less" scoped>
.fm-container {
  padding: 5px 5px;
}

.fm-content {
  padding: 5px 5px;
}

.fm-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 48px;
  font-size: 14px;
  color: var(--td-text-color-secondary);
  background: #fff;
  border-bottom: 1px solid var(--td-component-stroke);
}

.fm-nav-center {
  width: 400px;
}

.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;

  .t-button {
    width: 100px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px 20px;
}

.form-footer {
  display: none;
}
</style>
