import { api, Services } from '@/api/system';

// 定义消息事件模型
export interface LcMessageEvent {
  id?: string;
  eventName: string;
  eventType: number;
  sourceType: string;
  isEnabled: boolean;
  description?: string;
  functionId?: string;
}

// 定义消息事件映射关系模型
export interface LcMessageEventMapping {
  id?: string;
  eventId: string;
  sourceId: string;
  sourceCode?: string;
  state?: number;
}

export const useMessageSubscriptionService = () => {
  return {
    // 获取所有事件
    getAllEvents: (keyword?: string, eventType?: number, pageIndex: number = 1, pageSize: number = 20) => {
      return api.run(Services.messageSubscriptionGetAllEvents, { keyword, eventType, pageIndex, pageSize });
    },

    // 获取事件详情
    getEventById: (id: string) => {
      return api.run(Services.messageSubscriptionGetEventById, { id });
    },

    // 保存消息事件
    saveEvent: (evt: LcMessageEvent) => {
      return api.run(Services.messageSubscriptionSaveEvent, evt);
    },

    // 删除消息事件
    removeEvent: (id: string) => {
      return api.run(Services.messageSubscriptionRemoveEvent, { id });
    },

    // 更新消息事件状态
    updateEventStatus: (id: string, isEnabled: number) => {
      return api.run(Services.messageSubscriptionUpdateEventStatus, { id, isEnabled });
    },

    // 获取事件映射关系
    getMappingsByEventId: (eventId: string) => {
      return api.run(Services.messageSubscriptionGetMappingsByEventId, { eventId });
    },

    // 保存事件映射关系
    saveMapping: (mapping: LcMessageEventMapping) => {
      return api.run(Services.messageSubscriptionSaveMapping, mapping);
    },

    // 删除事件映射关系
    removeMapping: (id: string) => {
      return api.run(Services.messageSubscriptionRemoveMapping, { id });
    },

    // 获取设备列表
    getEquipments: (keyword?: string, equipmentType?: string) => {
      return api.run(Services.messageSubscriptionGetEquipments, { keyword, equipmentType });
    },

    // 获取设备类型列表
    getEquipmentTypes: () => {
      return api.run(Services.messageSubscriptionGetEquipmentTypes);
    },

    // 获取设备变量列表
    getEquipmentVariables: (equipmentId: string) => {
      return api.run(Services.messageSubscriptionGetEquipmentVariables, { equipmentId });
    },
  };
};
