<template>
  <t-row class="tree-item">
    <t-col flex="auto" class="item-info">
      <div>
        <span class="item-title">{{ props.name }}</span>
      </div>
      <div class="item-desc" :title="props.desc">{{ props.desc }}</div>
      <t-tag v-if="props.tag" theme="primary" variant="light" class="item-tag">{{ props.tag }}</t-tag>
    </t-col>
  </t-row>
</template>
<script lang="ts">
export default {
  name: 'VariableItem',
};
</script>
<script setup lang="ts">
const props = defineProps<{
  name: string;
  desc: string;
  tag?: string;
}>();
</script>
<style lang="less" scoped>
.tree-item {
  cursor: pointer;
  padding: 5px 10px 5px 0;
  flex-wrap: nowrap;
  border-radius: 4px;
  width: 100%;

  > div {
    display: inline-block;
  }

  .item-info {
    margin-left: 8px;
    position: relative;
    .item-title {
      font-size: 14px;
    }

    .item-desc {
      color: var(--td-text-color-placeholder);
      font-size: 12px;
      font-family: consolas, monospace;
    }

    .item-tag {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}
</style>
