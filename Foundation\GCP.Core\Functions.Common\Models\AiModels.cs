using Microsoft.Extensions.AI;
using System.Collections.ObjectModel;

namespace GCP.Functions.Common.Models
{
    /// <summary>
    /// 对话会话数据结构
    /// </summary>
    public class ChatConversation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string UserId { get; set; }
        public string Title { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime LastUpdatedAt { get; set; } = DateTime.Now;
        public List<ChatMessage> Messages { get; set; } = new List<ChatMessage>();
        public ObservableCollection<MessageInfo> MessageInfos { get; set; } = new ObservableCollection<MessageInfo>();
        public string ModelConfigId { get; set; }
    }

    /// <summary>
    /// 消息信息数据结构
    /// </summary>
    public class MessageInfo
    {
        public string Role { get; set; }
        public string Text { get; set; }
        public string FunctionCallInfo { get; set; }
        public string FunctionCallResult { get; set; }

        public MessageInfo() { }

        public MessageInfo(string role, string text)
        {
            Role = role;
            Text = text;
        }
    }

    /// <summary>
    /// AI模型配置数据结构
    /// </summary>
    public class ChatModelConfiguration
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; }
        public string Type { get; set; } // OpenAI, Ollama, etc.
        public string ApiKey { get; set; }
        public string ChatModelID { get; set; }
        public string EmbeddingModelID { get; set; }
        public string BaseURL { get; set; }
        public bool IsDefault { get; set; }
    }

    /// <summary>
    /// AI对话配置数据结构
    /// </summary>
    public class ChatProfile
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; }
        public string Description { get; set; }
        public string SystemPrompt { get; set; } = "You are a helpful assistant.";
        public string DatasourceId { get; set; }
        public string ModelConfigId { get; set; }
    }
} 