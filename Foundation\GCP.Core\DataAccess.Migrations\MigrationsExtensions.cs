﻿using GCP.Common;
using GCP.DataAccess;
using GCP.DataAccess.Migrations;
using Serilog;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class MigrationsExtensions
    {
        private static string GetDefaultDbProvider()
        {
            string dbProvider = DbSettings.DefaultDbProvider;
            if (DbSettings.DbProviderType == DbProviderType.MySql)
            {
                if (DbSettings.DbVersion.StartsWith('8'))
                {
                    dbProvider += "8";
                }
                else if (DbSettings.DbVersion.StartsWith('5'))
                {
                    dbProvider += "5";
                }
            }
            return dbProvider;
        }

        /// <summary>
        /// 添加框架依赖的数据库结构, 自动迁移
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection AddDbMigrationsHandler(this IServiceCollection services)
        {
            var assemblys = typeof(MigrationsExtensions).Assembly;

            if (SqlLogSettings.EnableLocalDatabase)
            {
                SqlLogSettings.ConnectionString = "Data Source=../logs.db";

                Log.Information("开始检测日志库迁移版本");
                MigrationsService logService = new MigrationsService("DuckDB", SqlLogSettings.ConnectionString);
                logService.Run(assemblys);
                Log.Information("日志库已迁移到最新版本");
            }

            Log.Information("开始检测数据库迁移版本");

            MigrationsService service = new MigrationsService(GetDefaultDbProvider(), DbSettings.DefaultConnectionString);
            service.Run(assemblys);
            //service.RunRollback(typeof(MigrationsExtensions).Assembly);
            //service.RunRollback(0, typeof(MigrationsExtensions).Assembly);
            Log.Information("数据库已迁移到最新版本");

            return services;
        }
    }
}
