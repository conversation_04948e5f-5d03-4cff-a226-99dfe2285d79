﻿using GCP.Common;

namespace GCP.FunctionPool.Flow.Models
{
    public class DataSaveData
    {
        public string Name { get; set; }
        public string DataSource { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 查询旧数据的列名
        /// </summary>
        public List<string> OldDataColumns { get; set; } = new List<string>();
        /// <summary>
        /// 是否跳过异常（是：跳过，否：异常回滚）
        /// </summary>
        public bool ExceptionSkip { get; set; } = false;
        /// <summary>
        /// 是否开启复制新增
        /// </summary>
        public bool OpenFastInsert { get; set; } = false;
        /// <summary>
        /// 批量处理数（非批量复制）
        /// </summary>
        public int BatchSize { get; set; } = 3000;
        /// <summary>
        /// 来源数据路径
        /// </summary>
        public DataValue SourceDataPath { get; set; }
        public DataSaveOperationType OperateType { get; set; }
        public DataSourceTableData ConfigureInfo { get; set; }
        /// <summary>
        /// 来源数据更新信息
        /// </summary>
        public Dictionary<string, string> SourceUpdateInfo { get; set; }
        /// <summary>
        /// 输出配置
        /// </summary>
        public DataSaveOutputConfig OutputConfig { get; set; }
    }

    public enum DataSaveOperationType
    {
        Save,
        Insert,
        Update
    }

    /// <summary>
    /// 数据保存输出类型枚举
    /// </summary>
    public enum DataSaveOutputType
    {
        /// <summary>
        /// 无输出（默认）
        /// </summary>
        None,
        /// <summary>
        /// 字典
        /// </summary>
        Dictionary,
        /// <summary>
        /// 列表
        /// </summary>
        List
    }

    /// <summary>
    /// 数据保存输出配置
    /// </summary>
    public class DataSaveOutputConfig
    {
        /// <summary>
        /// 输出类型
        /// </summary>
        public DataSaveOutputType Type { get; set; } = DataSaveOutputType.None;

        /// <summary>
        /// 字典配置（当 Type = Dictionary 时使用）
        /// </summary>
        public DataSaveDictionaryConfig DictionaryConfig { get; set; }
    }

    /// <summary>
    /// 数据保存字典配置
    /// </summary>
    public class DataSaveDictionaryConfig
    {
        /// <summary>
        /// 键字段配置（支持多列组合）
        /// </summary>
        public List<string> KeyColumns { get; set; } = new List<string>();

        /// <summary>
        /// 键分隔符（多列组合时使用）
        /// </summary>
        public string KeySeparator { get; set; } = "|";

        /// <summary>
        /// 值字段（单列）
        /// </summary>
        public string ValueColumn { get; set; }

        /// <summary>
        /// 是否返回完整对象作为值
        /// </summary>
        public bool UseFullObjectAsValue { get; set; } = false;
    }
}
