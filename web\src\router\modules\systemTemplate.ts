import { TemplateIcon, ViewListIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  // {
  //   path: '/template',
  //   component: Layout,
  //   redirect: '/template/base',
  //   name: 'SystemTemplate',
  //   meta: {
  //     title: {
  //       zh_CN: '模板',
  //       en_US: 'Template',
  //     },
  //     icon: shallowRef(TemplateIcon),
  //     orderNo: 3,
  //   },
  //   children: [
  //     {
  //       path: 'base',
  //       name: 'TemplateBase',
  //       component: () => import('@/pages/systemTemplate/base/index.vue'),
  //       meta: {
  //         title: {
  //           zh_CN: '概览',
  //           en_US: 'Overview',
  //         },
  //         icon: shallowRef(ViewListIcon),
  //       },
  //     },
  //   ],
  // },
];
