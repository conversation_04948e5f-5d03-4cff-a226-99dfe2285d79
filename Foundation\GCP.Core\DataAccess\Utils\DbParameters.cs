﻿using System.Data;
using System.Data.Common;
using System.Collections;
using GCP.Common;

namespace GCP.DataAccess
{
    public sealed class DbParameters
    {
        private Dictionary<string, IDataParameter> ParameterMap { get; set; } = new Dictionary<string, IDataParameter>();
        public IDataParameter[] Items
        {
            get { return this.ParameterMap.Select(t => t.Value).ToArray(); }
        }
        public int Count
        {
            get { return this.ParameterMap.Count; }
        }

        internal Dictionary<string, string> replaceParmNameMap { get; set; } = new Dictionary<string, string>();
        private DbProviderFactory factory { get; set; }
        public IDbProvider DbProvider { get; private set; }

        public IDataParameter this[string parameterName]
        {
            get
            {
                return this.ParameterMap[Clean(parameterName)];
            }
            set
            {
                this.ParameterMap[Clean(parameterName)] = value;
            }
        }

        public DbParameters(IDbConnection connection, object parms = null)
        {
            this.factory = connection.GetFactory();
            this.DbProvider = factory.GetDbProvider();
            Init(parms);
        }

        public DbParameters(IDbContext dbContext, object parms = null)
        {
            this.factory = dbContext.GetFactory();
            this.DbProvider = dbContext.DbProvider;
            Init(parms);
        }

        private void Init(object parms = null)
        {
            if (parms == null) return;
            this.Add(parms);
        }

        static string Clean(string name)
        {
            if (!string.IsNullOrEmpty(name))
            {
                switch (name[0])
                {
                    case '@':
                    case ':':
                    case '?':
                        return name.Substring(1);
                }
            }
            return name;
        }

        object FormatValue(object value)
        {
            if (DbProvider.GetDbProviderType() == DbProviderType.Oracle)
            {
                // oracle没有bool类型, 自动转换成数字类型
                if (value is bool v)
                {
                    return v ? 1 : 0;
                }
            }
            return value;
        }

        private IDataParameter CreateParameter(string parameterName, object value, ParameterDirection direction = ParameterDirection.Input, DbType? type = null, int? size = null)
        {
            var parm = this.factory.CreateParameter();
            parm.ParameterName = Clean(parameterName);
            parm.Value = FormatValue(value);
            parm.Direction = direction;
            if (type.HasValue) parm.DbType = type.Value;
            if (size.HasValue) parm.Size = size.Value;
            return parm;
        }

        internal void SetParametersByObjects(object[] parms, string suffix = null)
        {
            int length = parms.Length;
            int n = this.ParameterMap.Count;

            for (int i = 0; i < length; i++)
            {
                string parmName = "";
                object value = parms[i];
                string key = "{" + i + "}";
                if (value is ICollection collection)
                {
                    var arr = new ArrayList(collection);
                    List<string> list = new List<string>();
                    foreach (var item in arr)
                    {
                        parmName = "v_" + n++ + suffix;
                        list.Add(this.DbProvider.GetParameterName(parmName));
                        this.Add(parmName, item);
                    }
                    this.replaceParmNameMap[key] = "(" + string.Join(", ", list.ToArray()) + ")";
                }
                else
                {
                    parmName = "v_" + n++ + suffix;
                    this.replaceParmNameMap[key] = this.DbProvider.GetParameterName(parmName);
                    this.Add(parmName, value);
                }
            }
        }

        internal DbParameters SetParameters(params IDataParameter[] parms)
        {
            foreach (var parm in parms)
            {
                if(parm.Value is ICollection collection)
                {
                    var arr = new ArrayList(collection);
                    string parmName = parm.ParameterName;
                    string key = this.DbProvider.GetParameterName(parmName);

                    List<string> list = new List<string>();
                    int n = 0;
                    foreach (var item in arr)
                    {
                        ++n;
                        var name = parmName + n;
                        list.Add(key + n);
                        this.Add(name, item);
                    }
                    this.replaceParmNameMap[key] = "(" + string.Join(", ", list.ToArray()) + ")";
                }
                else
                {
                    this.ParameterMap[parm.ParameterName] = parm;
                }
            }
            return this;
        }

        private DbParameters SetParameters(object parms, Func<string, object, DbParameters> method = null)
        {
            if (parms == null) return this;
            if (parms is DbParameters)
            {
                var map = (parms as DbParameters).ParameterMap;
                foreach (var item in map)
                {
                    this.ParameterMap[item.Key] = item.Value;
                }
                parms = this;
            }
            else if (parms is IEnumerable<KeyValuePair<string, object>> parmList)
            {
                foreach (var parm in parmList)
                {
                    method(parm.Key, parm.Value);
                }
            }
            else
            {
                Type type = parms.GetType();
                var fields = EntityCache.GetFields(type);

                int i = 0;
                foreach (var field in fields)
                {
                    string parmName = field.Value.Property.Name;
                    object value = field.Value.GetValue(parms);
                    string key = this.DbProvider.GetParameterName(parmName);
                    if (value is ICollection collection)
                    {
                        var arr = new ArrayList(collection);
                        List<string> list = new List<string>();
                        int n = 0;
                        foreach (var item in arr)
                        {
                            ++n;
                            var name = parmName + n;
                            list.Add(key + n);
                            method(name, item);
                        }
                        this.replaceParmNameMap[key] = "(" + string.Join(", ", list.ToArray()) + ")";
                    }
                    else
                    {
                        method(parmName, value);
                    }
                }
                i++;
            }
            return this;
        }

        public DbParameters Add(string name, object value)
        {
            return this.SetParameters(this.CreateParameter(name, value));
        }
        /// <summary>
        /// 添加sql语句的参数
        /// </summary>
        /// <param name="parms">new {a=b,c=d} 或 IDataParameter 或 Dictionary </param>
        public DbParameters Add(object parms)
        {
            return this.SetParameters(parms, this.Add);
        }

        public DbParameters Add(params IDataParameter[] parms)
        {
            return this.SetParameters(parms);
        }

        public DbParameters AddInOut(string name, object value)
        {
            return this.SetParameters(this.CreateParameter(name, value, ParameterDirection.InputOutput));
        }

        public DbParameters AddInOut(object parms)
        {
            return this.SetParameters(parms, this.AddInOut);
        }

        public DbParameters AddOut(string name, DbType type, int? size = null)
        {
            return this.SetParameters(this.CreateParameter(name, null, ParameterDirection.Output, type, size));
        }

        public DbParameters AddReturn(string name, DbType type)
        {
            return this.SetParameters(this.CreateParameter(name, null, ParameterDirection.ReturnValue, type));
        }

        public T Get<T>(string name)
        {
            IDataParameter parm;
            if (this.ParameterMap.TryGetValue(name, out parm))
            {
                return parm.Value.Parse<T>();
            }
            throw new Exception(string.Format("Parameter {0} not found", name));
        }
    }
}
