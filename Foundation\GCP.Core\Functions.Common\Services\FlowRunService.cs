﻿using GCP.Common;
using GCP.DataAccess;
using LinqToDB;
using System.Text;

namespace GCP.Functions.Common.Services
{
    [Function("flowRun", "流程运行实例服务")]
    internal class FlowRunService : BaseService
    {
        [Function("getAll", "获取流程实例清单")]
        public PagingData<LcFruProc> GetAll(string keyword = null, string triggerType = null, string functionId = null,
            DateTime? beginTime = null, DateTime? endTime = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetFlowDb();
            var query = from a in db.LcFruProcs
                        where a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(triggerType) || a.TriggerType == triggerType) &&
                        (string.IsNullOrEmpty(functionId) || a.FunctionId == functionId) &&
                        (!beginTime.HasValue || a.BeginTime >= beginTime.Value) &&
                        (!endTime.HasValue || a.BeginTime <= endTime.Value)
                        select a;

            if (!string.IsNullOrEmpty(keyword))
            {
                query = from a in query
                        from b in db.LcFruSteps.InnerJoin(t => t.ProcId == a.Id)
                        from c in db.LcFruVariables.InnerJoin(t => t.ProcId == a.Id && t.StepId == b.Id)
                        where (string.IsNullOrEmpty(keyword) || c.VarValue.Contains(keyword))
                        select a;
            }

            var data = query.OrderByDescending(t => t.TimeCreate).Distinct().ToPagingData(pageIndex, pageSize);
            return data;
        }

        [Function("getSteps", "获取流程实例步骤信息")]
        public List<LcFhiDetail> GetSteps(string procId)
        {
            using var db = this.GetFlowDb();
            var data = (from a in db.LcFruSteps
                        where a.ProcId == procId
                        orderby a.SeqNo
                        select new LcFhiDetail
                        {
                            Id = a.Id,
                            ProcId = a.ProcId,
                            SeqNo = a.SeqNo,
                            FunctionId = a.FunctionId,
                            ActionId = a.ActionId,
                            StepName = a.StepName,
                            Status = a.Status,
                            BeginTime = a.BeginTime,
                            EndTime = a.EndTime,
                            Duration = a.Duration
                        }).ToList();
            return data;
        }

        [Function("getStepData", "获取流程实例步骤数据")]
        public string GetStepData(string procId, string stepId, bool hasResult = true)
        {
            using var db = this.GetFlowDb();
            var stepNo = db.LcFruSteps.Where(t => t.Id == stepId).Select(t => t.SeqNo).First();
            var variables = (from a in db.LcFruVariables
                             where a.ProcId == procId && a.StepId == stepId
                             select a).ToList();
            var logQuery = from a in db.LcFruLogs
                           where a.ProcId == procId
                           select a;
            logQuery = stepNo == 0 ? logQuery.Where(t => t.StepId == stepId || t.StepId == null) : logQuery.Where(t => t.StepId == stepId);

            var logList = logQuery.ToList();

            string inputs = variables.FirstOrDefault(t => t.VarType == "INPUT")?.VarValue;
            string outputs = variables.FirstOrDefault(t => t.VarType == "OUTPUT")?.VarValue;
            string result = null;
            if (hasResult)
            {
                result = variables.FirstOrDefault(t => t.VarType == "RESULT")?.VarValue;
            }

            var logs = logList.Select(t =>
            new
            {
                time = t.Timestamp,
                level = t.Level,
                message = t.Message,
                exception = t.Exception
            }).ToList();

            return JsonHelper.Serialize(new { inputs, outputs, result, logs });
        }

        [Function("getFlowLogs", "获取流程实例日志")]
        public string GetFlowLogs(string procId)
        {
            using var db = this.GetFlowDb();
            var stepNameDic = (from a in db.LcFruSteps
                               where a.Id == procId
                               select a).ToDictionary(t => t.Id, t => t.StepName);


            var logList = (from a in db.LcFruLogs
                           where a.ProcId == procId && a.Category == "FLOW"
                           select a).ToList();
            StringBuilder sb = new StringBuilder();
            foreach (var log in logList)
            {
                sb.Append(log.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff").TrimEnd(".000"));
                sb.Append(' ');
                sb.Append('[' + log.Level + ']');
                sb.Append(' ');
                if (stepNameDic.TryGetValue(log.StepId, out string stepName))
                {
                    sb.Append('[' + stepName + ']');
                    sb.Append(' ');
                }
                sb.AppendLine(log.Message);
            }
            return sb.ToString();
        }

        [Function("deleteErrorLogs", "删除错误流程日志")]
        public async Task DeleteErrorLogs(string[] ids)
        {
            await using var db = this.GetFlowDb();
            await db.LcFruProcs
                .Where(t => ids.Contains(t.Id) && t.Status == -1)
                .DeleteAsync();
        }

        [Function("transferFlowDataToLogs", "转移流程数据到日志")]
        [Job(JobId = "TransferFlowDataToLogs", JobCron = "0 0/3 * * * ?", JobName = "转移流程数据到日志")]
        public async Task TransferFlowDataToLogs()
        {
            int pageIndex = 1;
            int pageSize = 1000;

            await using var db = this.GetFlowDb();
            db.CommandTimeout = 60 * 2;

            while (true)
            {
                var successList = db.LcFruProcs
                .Where(t => t.Status == 1)
                .Select(t => new LcFhiProc
                {
                    Id = t.Id,
                    Creator = t.Creator,
                    TimeCreate = t.TimeCreate,
                    SolutionId = t.SolutionId,
                    ProjectId = t.ProjectId,
                    FunctionId = t.FunctionId,
                    FunctionName = t.FunctionName,
                    Version = t.Version,
                    TriggerType = t.TriggerType,
                    Status = t.Status,
                    BeginTime = t.BeginTime,
                    EndTime = t.EndTime,
                    Duration = t.Duration,
                    TotalTraffic = t.TotalTraffic,
                })
                .ToPagingData(pageIndex, pageSize)?.List;
                if (successList == null || successList.Count == 0) break;

                foreach (var proc in successList)
                {
                    // 添加流程日志头表
                    var logs = GetFlowLogs(proc.Id);
                    proc.RunLog = logs?.SubStr(0, 2000);
                    var steps = GetSteps(proc.Id);
                    foreach (var step in steps)
                    {
                        step.RunData = GetStepData(proc.Id, step.Id, false);
                    }

                    await db.BeginTransactionAsync();
                    await db.InsertAsync(proc);
                    await db.LcFruProcs.Where(t => t.Id == proc.Id).DeleteAsync();

                    // 添加流程步骤日志
                    foreach (var step in steps)
                    {
                        await db.InsertAsync(step);
                    }

                    await db.LcFruSteps.Where(t => t.ProcId == proc.Id).DeleteAsync();
                    await db.LcFruVariables.Where(t => t.ProcId == proc.Id).DeleteAsync();
                    await db.LcFruLogs.Where(t => t.ProcId == proc.Id).DeleteAsync();

                    await db.CommitTransactionAsync();
                }
            }
        }

        [Function("retry", "重试流程")]
        public async Task FlowRetry(string id)
        {
            await using var flowDb = this.GetFlowDb();
            var proc = flowDb.LcFruProcs.FirstOrDefault(t => t.Id == id && t.SolutionId == this.SolutionId && t.ProjectId == this.ProjectId);
            if (proc == null)
            {
                throw new Exception("流程实例不存在");
            }

            var firstStepId = flowDb.LcFruSteps.Where(t => t.ProcId == id && t.SeqNo == 0).Select(t => t.Id).FirstOrDefault();
            if (firstStepId == null)
            {
                throw new Exception("流程实例尚未开始执行");
            }

            var inputVariable = flowDb.LcFruVariables.Where(t => t.ProcId == id && t.StepId == firstStepId && t.VarType == "INPUT").Select(t => t.VarValue).FirstOrDefault();

            try
            {
                if (proc.TriggerType == "JOB")
                {
                    Dictionary<string, object> args = [];
                    if (!string.IsNullOrEmpty(inputVariable))
                    {
                        args = JsonHelper.Deserialize<Dictionary<string, object>>(inputVariable);
                    }

                    await FunctionHelper.TriggerJob(proc.FunctionId, args);
                    //await new JobService().TriggerByFunctionId(proc.FunctionId, args);
                }
                else if (proc.TriggerType == "API")
                {
                    await using var db = GetDb();
                    var apis = db.LcApis.Where(t => t.FunctionId == proc.FunctionId).ToList();
                    if (apis.Count == 0)
                    {
                        throw new CustomException("未找到API");
                    }
                    else if (apis.Count > 1)
                    {
                        throw new CustomException("API 对应函数不唯一");
                    }

                    Dictionary<string, object> args = [];
                    if (!string.IsNullOrEmpty(inputVariable))
                    {
                        args = JsonHelper.Deserialize<Dictionary<string, object>>(inputVariable);
                    }
                    await FunctionHelper.TriggerApi(proc.FunctionId, args, new ApiInfoDTO(apis.First()), default, null);
                }
            }
            catch (Exception ex)
            {
                throw new CustomException("重试失败：" + ex.Message, ex);
            }
        }

        [Function("continue", "继续执行流程")]
        public void FlowContinue(string id)
        {

        }

    }
}
