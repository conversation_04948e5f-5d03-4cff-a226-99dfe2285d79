/**
 * 连接方式枚举
 */
export enum ConnectBy {
  /** 通过主机名连接 */
  HOST = 'HOST',
  /** 通过连接串连接 */
  URL = 'URL',
}

/**
 * 基础数据源接口定义
 */
export interface IDataSourceBase {
  /** 数据行ID号 */
  id: string;
  /** 创建时间 */
  timeCreate?: string;
  /** 创建人 */
  creator?: string;
  /** 更新时间 */
  timeModified?: string;
  /** 更新人 */
  modifier?: string;
  /** 可用状态 1可用 0禁用 */
  state: number;
  /** 数据源名称 */
  dataSourceName: string;
  /** 数据提供者 */
  dataProvider: string;
  /** 连接方式 */
  connectBy: ConnectBy;
  /** 数据连接串 */
  connectionString?: string;
  /** 描述 */
  description?: string;
  /** 分组 */
  group?: string;
}

/**
 * 后端数据源接口定义
 */
export interface IDataSource extends IDataSourceBase {
  /** 服务器地址 */
  serverAddress?: string;
  /** 端口 */
  port?: string;
  /** 数据库 */
  database?: string;
  /** 用户名 */
  userId?: string;
  /** 密码 */
  password?: string;
}

/**
 * 前端表单数据接口定义
 */
export interface IDataSourceForm extends IDataSourceBase {
  /** 主机名（对应后端 serverAddress） */
  host?: string;
  /** 端口 */
  port?: number;
  /** 初始数据库（对应后端 database） */
  originDataBase?: string;
  /** 用户名（对应后端 userId） */
  userName?: string;
  /** 密码 */
  password?: string;
  /** 服务名（Oracle专用） */
  serviceName?: string | number;
  /** 服务类型（Oracle/SQL Server专用） */
  serviceType?: number;
}
