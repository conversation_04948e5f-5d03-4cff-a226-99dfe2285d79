<template>
  <div
    ref="dropLineRef"
    :class="dropLineClass"
    @dragover.prevent="onDragOver"
    @dragleave="onDragLeave"
    @drop.prevent="onDrop"
  >
    <div v-if="showDropLine" class="drop-line">
      <div class="drop-line-indicator"></div>
      <div class="drop-line-text">{{ dropText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useDragManager, type DropZoneInfo } from './composables/useDragManager';
import { FlowStep } from './model';

interface Props {
  parentId?: string;
  targetItem?: FlowStep;
  position: 'before' | 'after';
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const dropLineRef = ref<HTMLElement>();
const isHovering = ref(false);
const {
  isDragging,
  canDrop,
  handleDrop,
  registerDropZone,
  unregisterDropZone,
  setHoveringZone,
  isCurrentHoveringZone,
} = useDragManager();

const dropZoneId = computed(() => {
  return `line-${props.parentId || 'root'}-${props.targetItem?.id || 'empty'}-${props.position}`;
});

const dropInfo = computed<DropZoneInfo>(() => ({
  parentId: props.parentId,
  targetItem: props.targetItem,
  position: props.position,
  element: dropLineRef.value!,
}));

const canDropHere = computed(() => {
  return !props.disabled && canDrop(dropInfo.value);
});

const showDropLine = computed(() => {
  return isDragging.value && isHovering.value && canDropHere.value && isCurrentHoveringZone(dropZoneId.value);
});

const dropText = computed(() => {
  return props.position === 'before' ? '插入到此处' : '插入到此处';
});

const dropLineClass = computed(() => {
  return [
    'drop-line-zone',
    {
      'drop-line-active': isDragging.value && canDropHere.value,
      'drop-line-hover': isHovering.value && canDropHere.value,
      'drop-line-disabled': props.disabled || !canDropHere.value,
    },
  ];
});

const onDragOver = (event: DragEvent) => {
  if (!canDropHere.value) {
    event.dataTransfer!.dropEffect = 'none';
    return;
  }

  event.preventDefault();
  event.dataTransfer!.dropEffect = 'move';

  isHovering.value = true;
  setHoveringZone(dropZoneId.value);
};

const onDragLeave = (event: DragEvent) => {
  // 检查是否真的离开了放置区域
  const rect = dropLineRef.value?.getBoundingClientRect();
  if (rect) {
    const { clientX, clientY } = event;
    if (clientX < rect.left || clientX > rect.right || clientY < rect.top || clientY > rect.bottom) {
      isHovering.value = false;
      // 如果当前悬停的是这个区域，清除悬停状态
      if (isCurrentHoveringZone(dropZoneId.value)) {
        setHoveringZone(null);
      }
    }
  }
};

const onDrop = (event: DragEvent) => {
  if (!canDropHere.value) return;

  event.preventDefault();
  isHovering.value = false;
  setHoveringZone(null);
  handleDrop(dropInfo.value);
};

onMounted(() => {
  if (dropLineRef.value) {
    registerDropZone(dropZoneId.value, dropLineRef.value);
  }
});

onUnmounted(() => {
  unregisterDropZone(dropZoneId.value);
});
</script>

<style lang="less" scoped>
.drop-line-zone {
  height: 8px;
  position: relative;
  transition: all 0.15s ease;
  margin: 1px 0;

  // 拖拽激活时显示淡淡的分隔线
  &.drop-line-active {
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 16px;
      right: 16px;
      height: 1px;
      background-color: var(--td-border-level-2-color);
      transform: translateY(-50%);
      opacity: 0.4;
    }
  }

  &.drop-line-disabled {
    &::before {
      display: none;
    }
  }
}

.drop-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  pointer-events: none;
}

.drop-line-indicator {
  flex: 1;
  height: 3px;
  background-color: var(--td-brand-color);
  border-radius: 2px;
  position: relative;
  margin: 0 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 8px;
    height: 8px;
    background-color: var(--td-brand-color);
    border-radius: 50%;
    transform: translateY(-50%);
    box-shadow: 0 0 0 2px white;
  }

  &::before {
    left: -6px;
  }

  &::after {
    right: -6px;
  }
}

.drop-line-text {
  position: absolute;
  background-color: var(--td-brand-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  top: 0px;
}
</style>
