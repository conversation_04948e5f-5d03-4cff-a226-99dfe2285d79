﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20240701003000, "初始化任务管理器JOB表")]
    public class AddJobTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_JOB")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("JOB_NAME").AsAnsiString(200).WithColumnDescription("任务名称")
               .WithColumn("JOB_GROUP").AsAnsiString(200).WithColumnDescription("任务组")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("CRON_EXPRESSION").AsAnsiString(200).Nullable().WithColumnDescription("触发器表达式")
               .WithColumn("STATUS").AsInt16().Nullable().WithColumnDescription("状态 0：暂停, 1：运行")
               .WithColumn("JOB_BEGIN_TIME").AsDateTime().Nullable().WithColumnDescription("开始时间")
               .WithColumn("JOB_END_TIME").AsDateTime().Nullable().WithColumnDescription("结束时间")
               .WithColumn("LOG_RETENTION_DAYS").AsInt16().Nullable().WithColumnDescription("日志保留天数")
               .WithColumn("ALLOW_PARALLELIZATION").AsInt16().Nullable().WithColumnDescription("是否允许并（1:允许）")
               .WithColumn("ALLOW_RETRY").AsInt16().Nullable().WithColumnDescription("允许重试（1:允许）")
               .WithColumn("RETRY_COUNT").AsInt16().Nullable().WithColumnDescription("重试次数")
               .WithColumn("RETRY_DELAYS_IN_SECONDS").AsInt16().Nullable().WithColumnDescription("重试间时间（秒）")
               .WithColumn("ALLOW_ERROR_NOTIFICATION").AsInt16().Nullable().WithColumnDescription("允许错邮件通知（1:允许）")
               .WithColumn("EXCEPTION_STOP").AsInt16().Nullable().WithColumnDescription("异常停止（1:下次运行开始时间不变）")
               .WithColumn("NOTIFICATION_EMAIL").AsAnsiString(500).Nullable().WithColumnDescription("通知邮件地址")
               .WithColumn("FUNCTION_ID").AsAnsiString(36).Nullable().WithColumnDescription("函数ID")
               .WithColumn("NEXT_BEGIN_TIME").AsDateTime().Nullable().WithColumnDescription("下次开始时间")
               .WithColumn("TIMEOUT_IN_SECONDS").AsInt16().Nullable().WithColumnDescription("超时时间（秒）")
               ;

            Create.Index("LC_JOB_IDX")
                .OnTable("LC_JOB")
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .OnColumn("JOB_NAME").Ascending()
                .OnColumn("JOB_GROUP").Ascending()
                .WithOptions().Unique();


            Create.Table("LC_JOB_LOG")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("JOB_ID").AsAnsiString(200).WithColumnDescription("任务ID")
               .WithColumn("JOB_NAME").AsAnsiString(200).WithColumnDescription("任务名称")
               .WithColumn("JOB_GROUP").AsAnsiString(200).WithColumnDescription("任务组")
               .WithColumn("REQUEST_TYPE").AsAnsiString(50).Nullable().WithColumnDescription("请求类型")
               .WithColumn("HTTP_URL").AsAnsiString(200).Nullable().WithColumnDescription("请求地址")
               .WithColumn("HTTP_PARAMS").AsAnsiString(2000).Nullable().WithColumnDescription("请求参数")
               .WithColumn("JOB_RESULT").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("任务结果")
               .WithColumn("JOB_BEGIN_TIME").AsDateTime().WithColumnDescription("开始时间")
               .WithColumn("JOB_END_TIME").AsDateTime().Nullable().WithColumnDescription("结束时间")
               .WithColumn("STATUS").AsInt16().Nullable().WithColumnDescription("状态 0：失败 1：成功")
               ;

            Create.Index("JOG_ID_TIME_IDX")
                .OnTable("LC_JOB_LOG")
                .OnColumn("STATE").Ascending()
                .OnColumn("JOB_ID").Ascending()
                .OnColumn("JOB_BEGIN_TIME").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_JOB");
            Delete.Table("LC_JOB_LOG");
        }
    }
}
