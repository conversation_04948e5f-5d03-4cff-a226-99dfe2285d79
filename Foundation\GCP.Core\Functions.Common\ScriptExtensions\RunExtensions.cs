﻿using GCP.Common;
using GCP.DataAccess;
using Jint;
using Jint.Native;
using System.Collections;
using System.Text;

namespace GCP.Functions.Common
{
    public static class JavascriptRunExtensions
    {
        public static string replace(this string val, string oldStr, string newStr)
        {
            return val.Replace(oldStr, newStr);
        }

        public static string toLowerCase(this string val)
        {
            return val.ToLower();
        }

        #region js简洁调用
        /// <summary>
        /// 根据SQL获取所有数据
        /// </summary>
        /// <param name="db"></param>
        /// <param name="sqlString"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static Task<List<dynamic>> getAll(this IDbContext db, string sqlString, params object[] args)
        {
            return db.Sql<dynamic>(sqlString, args).GetListAsync();
        }

        /// <summary>
        /// 根据SQL获取单个数据
        /// </summary>
        /// <param name="db"></param>
        /// <param name="sqlString"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static Task<dynamic> get(this IDbContext db, string sqlString, params object[] args)
        {
            return db.Sql<dynamic>(sqlString, args).GetAsync();
        }

        /// <summary>
        /// 根据SQL查看数据是否存在
        /// </summary>
        /// <param name="db"></param>
        /// <param name="sqlString"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static Task<bool> exists(this IDbContext db, string sqlString, params object[] args)
        {
            return db.Sql<dynamic>(sqlString, args).ExistsAsync();
        }

        private static void PrepareInsertObj(JsObject jsObject)
        {
            if (!jsObject.TryGetValue("ID", out var id))
                jsObject.Set("ID", TUID.NewTUID());

            if (!jsObject.TryGetValue("TIME_CREATE", out var creatrd))
                jsObject.Set("TIME_CREATE", JsDate.FromObject(jsObject.Engine, DateTime.Now));

            if (!jsObject.TryGetValue("CREATOR", out var user))
                jsObject.Set("CREATOR", "SYS");

            if (!jsObject.TryGetValue("STATE", out var state))
                jsObject.Set("STATE", "A");

            //if (!jsObject.TryGetValue("SOLUTION_ID", out var SOLUTION_ID))
            //    jsObject.SetValue("SOLUTION_ID", "2648027806257152");
        }

        /// <summary>
        /// 新增数据
        /// </summary>
        /// <param name="db"></param>
        /// <param name="tableName"></param>
        /// <param name="obj"></param>
        /// <returns></returns>
        /// <exception cref="CustomException"></exception>
        public static async Task<object> insert(this IDbContext db, string tableName, object obj)
        {
            IDictionary dic = null;

            if (obj is JsObject jsObj && obj is IDictionary jsDic)
            {
                dic = jsDic;
                PrepareInsertObj(jsObj);
            }
            else
            {
                throw new CustomException("参数类型出错");
            }

            var sbColumnList = new StringBuilder(null);
            var sbParameterList = new StringBuilder(null);

            bool isFirst = true;
            var dbParams = new DbParameters(db);
            string id = "";
            foreach (DictionaryEntry field in dic)
            {
                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sbColumnList.Append(", ");
                    sbParameterList.Append(", ");
                }
                var key = field.Key.ToString();
                if (string.IsNullOrEmpty(key)) continue;
                if (key.ToUpper() == "ID")
                {
                    id = field.Value.Parse<string>();
                }
                sbColumnList.Append(key);
                sbParameterList.Append(db.DbProvider.GetParameterName(key));
                dbParams.Add(key, field.Value);
            }

            var sql = string.Format("INSERT INTO {0} ({1}) VALUES ({2})", tableName.Replace("'", "''"), sbColumnList, sbParameterList);
            await db.Sql(sql).AddParameters(dbParams).ExecuteAsync();

            return id;
        }

        private static void PrepareUpdateObj(JsObject jsObject)
        {
            if (!jsObject.TryGetValue("TIME_MODIFIED", out var modified))
                jsObject.Set("TIME_MODIFIED", JsDate.FromObject(jsObject.Engine, DateTime.Now));

            if (!jsObject.TryGetValue("MODIFIER", out var user))
                jsObject.Set("MODIFIER", "SYS");
        }


        public static async Task<bool> save(this IDbContext db, string tableName, object obj)
        {
            IDictionary dic = null;

            if (obj is IDictionary jsDic)
            {
                dic = jsDic;
            }
            else
            {
                throw new CustomException("参数类型出错");
            }

            var id = "";
            var idKey = "";
            foreach (DictionaryEntry field in dic)
            {
                var key = field.Key.ToString();
                if (string.IsNullOrEmpty(key)) continue;
                idKey = key;
                if (key.ToUpper() == "ID")
                {
                    id = field.Value.Parse<string>();
                }
            }
            tableName = tableName.Replace("'", "''");
            if (string.IsNullOrEmpty(id))
            {
                await insert(db, tableName, obj);
                return true;
            }
            var hasData = await db.Sql($"SELECT ID FROM ${tableName} WHERE ID={0}", id).ExistsAsync();
            if (hasData)
            {
                dic.Remove(idKey);
                await update(db, tableName, dic, id);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="db"></param>
        /// <param name="tableName"></param>
        /// <param name="setObj"></param>
        /// <param name="ids"></param>
        /// <returns></returns>
        /// <exception cref="CustomException"></exception>
        public static async Task<int> update(this IDbContext db, string tableName, object setObj, params string[] ids)
        {
            if (ids == null || ids.Length == 0)
            {
                throw new CustomException("只能根据ID更新, 请添加ID条件");
            }

            IDictionary dic = null;

            if (setObj is JsObject jsObj && setObj is IDictionary jsDic)
            {
                dic = jsDic;
                PrepareUpdateObj(jsObj);
            }
            else
            {
                throw new CustomException("参数类型出错");
            }

            tableName = tableName.Replace("'", "''");

            StringBuilder sb = new StringBuilder();
            sb.Append("UPDATE ");
            sb.Append(tableName);
            sb.Append(" SET ");
            bool isFirst = true;
            var dbParams = new DbParameters(db);

            foreach (DictionaryEntry item in dic)
            {
                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sb.Append(", ");
                }
                sb.Append(item.Key);
                sb.Append(" = ");
                sb.Append(db.DbProvider.GetParameterName("v_set_" + item.Key));

                dbParams.Add("v_set_" + item.Key, item.Value);
            }
            sb.Append(" WHERE 1=1");

            var sqlSb = db.Sql(sb.ToString()).AddParameters(dbParams);
            if (ids.Length == 1)
            {
                sqlSb.And("ID", ids[0]);
            }
            else
            {
                sqlSb.AndIn("ID", ids);
            }

            return await sqlSb.ExecuteAsync();
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="db"></param>
        /// <param name="tableName"></param>
        /// <param name="ids"></param>
        /// <param name="physicalDeletion">是否物理删除, 默认否</param>
        /// <returns></returns>
        /// <exception cref="CustomException"></exception>
        public static async Task<int> delete(this IDbContext db, string tableName, string[] ids, bool physicalDeletion = false)
        {
            if (ids == null || ids.Length == 0)
            {
                throw new CustomException("只能根据ID更新, 请添加ID条件");
            }

            tableName = tableName.Replace("'", "''");
            if (physicalDeletion)
            {
                var sqlSb = db.Sql("DELETE FROM " + tableName + " WHERE 1=1");
                if (ids.Length == 1)
                {
                    sqlSb.And("ID", ids[0]);
                }
                else
                {
                    sqlSb.AndIn("ID", ids);
                }
                return await sqlSb.ExecuteAsync();
            }
            else
            {
                var engine = new Engine();
                JsObject jsObject = new(engine);
                jsObject.Set("STATE", "D");
                return await update(db, tableName, jsObject, ids);
            }

        }
        #endregion
    }
}
