using GCP.Models;

namespace GCP.Common
{
    /// <summary>
    /// 通知服务接口
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// 发送通知
        /// </summary>
        /// <param name="channelId">通知通道ID</param>
        /// <param name="request">通知请求</param>
        /// <returns></returns>
        Task<bool> SendNotificationAsync(string channelId, NotificationRequest request);

        /// <summary>
        /// 测试通知通道
        /// </summary>
        /// <param name="channelId">通知通道ID</param>
        /// <returns></returns>
        Task<bool> TestNotificationChannelAsync(string channelId);

        /// <summary>
        /// 获取支持的通知通道类型
        /// </summary>
        /// <returns></returns>
        List<NotificationChannelType> GetSupportedChannelTypes();
    }
}
