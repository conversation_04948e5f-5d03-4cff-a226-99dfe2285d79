﻿using Hangfire;
using Hangfire.MemoryStorage;
using Hangfire.Redis.StackExchange;
using GCP.Common;
using GCP.DataAccess;
using GCP.Job;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class JobHandler
    {
        public static IServiceCollection AddHangfireJob(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHangfire(x =>
            {
                // 全局配置重试次数为0, 手动代码控制
                x.UseFilter(new AutomaticRetryAttribute { Attempts = 0 });

                var storageStr = configuration["hangfire:storage"];

                switch (storageStr?.ToLower())
                {
                    case "memory":
                        var options = new MemoryStorageOptions
                        {
                            FetchNextJobTimeout = TimeSpan.FromDays(1)
                        };
                        x.UseMemoryStorage(options);
                        return;
                    case "redis":
                        var connectionString = configuration["redis:ConnectionString"];
                        var noRedis = string.IsNullOrEmpty(connectionString);
                        if (noRedis)
                        {
                            throw new ArgumentException("Job处理器配置项 hangfire:storage 为 redis 时， [redis:ConnectionString] 配置项不能为空，请检查配置，或使用其他存储方式，如：memory");
                        }

                        x.UseRedisStorage(connectionString, new RedisStorageOptions
                        {
                            Prefix = "job:"
                        });
                        return;
                    default:
                        var storage = GetStorage();
                        x.UseStorage(storage);
                        break;
                }
            });

            services.AddHangfireServer(opt =>
            {
                // 计划轮询间隔 默认15秒
                //opt.SchedulePollingInterval = TimeSpan.FromSeconds(1);

                var workerCount = configuration["hangfire:workerCount"]?.ToString();
                if (workerCount != null)
                    opt.WorkerCount = workerCount.Parse<int>();
            });

            services.AddSingleton<IJob, HangfireJob>();
            return services;
        }

        public static IApplicationBuilder UseHangfireJob(this IApplicationBuilder app)
        {
            app.UseHangfireDashboard();

            return app;
        }

        private static JobStorage GetStorage()
        {
            return DbSettings.DbProviderType switch
            {
                DbProviderType.SqlServer => new Hangfire.SqlServer.SqlServerStorage(DbSettings.DefaultConnectionString),
                _ => throw new NotSupportedException("定时任务暂不支持该数据库类型"),
            };
        }
    }
}
