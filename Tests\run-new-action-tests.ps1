# 运行新添加的缓存和设备测点动作测试
Write-Host "开始运行缓存和设备测点动作测试..." -ForegroundColor Green

# 设置工作目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

Write-Host "项目根目录: $projectRoot" -ForegroundColor Yellow

# 运行缓存动作测试
Write-Host "`n=== 运行缓存动作测试 ===" -ForegroundColor Cyan
dotnet test Tests/GCP.Tests/GCP.Tests.csproj --filter "FullyQualifiedName~CacheActionTests" --no-build --verbosity normal

if ($LASTEXITCODE -ne 0) {
    Write-Host "缓存动作测试失败!" -ForegroundColor Red
    exit 1
}

# 运行设备测点动作测试
Write-Host "`n=== 运行设备测点动作测试 ===" -ForegroundColor Cyan
dotnet test Tests/GCP.Tests/GCP.Tests.csproj --filter "FullyQualifiedName~IotEquipmentActionTests" --no-build --verbosity normal

if ($LASTEXITCODE -ne 0) {
    Write-Host "设备测点动作测试失败!" -ForegroundColor Red
    exit 1
}

# 运行集成测试
Write-Host "`n=== 运行缓存和设备集成测试 ===" -ForegroundColor Cyan
dotnet test Tests/GCP.Tests/GCP.Tests.csproj --filter "FullyQualifiedName~CacheAndIotIntegrationTests" --no-build --verbosity normal

if ($LASTEXITCODE -ne 0) {
    Write-Host "集成测试失败!" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 所有测试通过! ===" -ForegroundColor Green
Write-Host "✅ 缓存动作测试通过" -ForegroundColor Green
Write-Host "✅ 设备测点动作测试通过" -ForegroundColor Green  
Write-Host "✅ 集成测试通过" -ForegroundColor Green

Write-Host "`n测试覆盖的功能:" -ForegroundColor Yellow
Write-Host "• 缓存写入/读取/删除/检查存在" -ForegroundColor White
Write-Host "• 设备测点写入/读取" -ForegroundColor White
Write-Host "• 设备所有参数读取" -ForegroundColor White
Write-Host "• 批量设备参数读取" -ForegroundColor White
Write-Host "• 项目隔离机制" -ForegroundColor White
Write-Host "• 错误处理和异常情况" -ForegroundColor White
Write-Host "• 缓存和设备数据集成" -ForegroundColor White
