using GCP.DataAccess;
using GCP.Iot.Interfaces;
using GCP.Common;
using System.Reflection;
using LinqToDB;
using Serilog;

namespace GCP.Iot.Services
{
    /// <summary>
    /// 驱动元数据服务，负责处理驱动参数和方法的反射发现与同步
    /// </summary>
    class DriverMetadataService
    {
        private readonly DriverManager _driverManager;

        public DriverMetadataService(DriverManager driverManager)
        {
            _driverManager = driverManager;
        }

        /// <summary>
        /// 同步所有驱动的元数据到数据库
        /// </summary>
        /// <returns>同步的驱动总数</returns>
        public async Task<int> SyncAllDriverMetadataAsync()
        {
            int count = 0;
            var driverTypes = _driverManager.GetAllDriverTypes();

            foreach (var driverType in driverTypes)
            {
                try
                {
                    var driverCode = GetDriverCode(driverType);
                    if (!string.IsNullOrEmpty(driverCode))
                    {
                        await SyncDriverParametersAsync(driverCode, driverType);
                        count++;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "同步驱动元数据失败: {DriverTypeFullName}", driverType.FullName);
                }
            }

            if (count > 0) Log.Information("已同步{Count}个驱动的元数据", count);
            return count;
        }

        /// <summary>
        /// 同步指定驱动的参数到数据库
        /// </summary>
        /// <param name="driverCode">驱动代码</param>
        /// <param name="driverType">驱动类型</param>
        /// <returns>同步的参数数量</returns>
        public async Task<int> SyncDriverParametersAsync(string driverCode, Type driverType)
        {
            int count = 0;

            try
            {
                // 通过反射获取驱动参数
                var parameters = GetDriverParameters(driverType);
                if (parameters == null || parameters.Count == 0)
                {
                    Log.Warning("驱动{DriverCode}没有定义参数", driverCode);
                    return 0;
                }

                await using (var db = new GcpDb())
                {
                    // 获取数据库中已存在的全局参数
                    var existingParams = await db.LcIotDrivers
                        .Where(d => d.DriverCode == driverCode && d.EquipmentId == null)
                        .ToListAsync();

                    var projects = await db.LcProjects.Where(p => p.State == 1).ToListAsync();

                    // 比较并同步参数
                    foreach (var project in projects)
                        foreach (var param in parameters)
                        {
                            var existingParam = existingParams.FirstOrDefault(p => p.ParamKey == param.Key && p.ProjectId == project.Id && p.SolutionId == project.SolutionId);

                            if (existingParam == null)
                            {
                                // 新增参数
                                var newParam = new LcIotDriver
                                {
                                    Id = TUID.NewTUID().ToString(),
                                    Creator = "SYS",
                                    TimeCreate = DateTime.Now,
                                    SolutionId = project.SolutionId,
                                    ProjectId = project.Id,
                                    State = 1,
                                    DriverCode = driverCode,
                                    ParamKey = param.Key,
                                    ParamValue = param.Value.DefaultValue?.ToString(),
                                    Description = param.Value.DisplayName,
                                    EquipmentId = null // 全局参数
                                };

                                await db.InsertAsync(newParam);
                                count++;
                                Log.Debug("为驱动{DriverCode}添加参数: {ParamKey}", driverCode, param.Key);
                            }
                        }
                }

                if (count > 0) Log.Information("已同步驱动{DriverCode}的{Count}个参数", driverCode, count);
                return count;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "同步驱动{DriverCode}参数失败", driverCode);
                throw;
            }
        }

        /// <summary>
        /// 获取驱动的所有方法信息
        /// </summary>
        /// <param name="driverCode">驱动代码</param>
        /// <returns>方法信息列表</returns>
        public List<DriverMethodInfo> GetDriverMethods(string driverCode)
        {
            try
            {
                var driverType = _driverManager.GetDriverType(driverCode);
                if (driverType == null)
                {
                    Log.Warning("找不到驱动: {DriverCode}", driverCode);
                    return new List<DriverMethodInfo>();
                }

                return GetDriverMethods(driverType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "获取驱动方法失败: {DriverCode}", driverCode);
                return new List<DriverMethodInfo>();
            }
        }

        /// <summary>
        /// 通过反射获取驱动的所有方法信息
        /// </summary>
        /// <param name="driverType">驱动类型</param>
        /// <returns>方法信息列表</returns>
        public List<DriverMethodInfo> GetDriverMethods(Type driverType)
        {
            var methods = new List<DriverMethodInfo>();

            try
            {
                var methodInfos = driverType.GetMethods(BindingFlags.Public | BindingFlags.Instance);

                foreach (var methodInfo in methodInfos)
                {
                    var attr = methodInfo.GetCustomAttribute<DriverMethodAttribute>();
                    if (attr != null)
                    {
                        var parameters = methodInfo.GetParameters()
                            .Select(p => new DriverMethodParameterInfo
                            {
                                Name = p.Name,
                                Type = p.ParameterType.Name,
                                IsRequired = !p.IsOptional,
                                DefaultValue = p.IsOptional ? p.DefaultValue : null
                            })
                            .ToList();

                        methods.Add(new DriverMethodInfo
                        {
                            Name = methodInfo.Name,
                            DisplayName = attr.Name,
                            Description = attr.Description,
                            ReturnType = methodInfo.ReturnType.Name,
                            Parameters = parameters
                        });
                    }
                }

                return methods;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "反射获取驱动方法失败: {DriverTypeFullName}", driverType.FullName);
                return methods;
            }
        }

        /// <summary>
        /// 通过反射获取驱动的参数信息
        /// </summary>
        /// <param name="driverType">驱动类型</param>
        /// <returns>参数信息字典</returns>
        private Dictionary<string, DriverParameterInfo> GetDriverParameters(Type driverType)
        {
            var parameters = new Dictionary<string, DriverParameterInfo>();

            try
            {
                var properties = driverType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

                foreach (var property in properties)
                {
                    var attr = property.GetCustomAttribute<DriverParameterAttribute>();
                    if (attr != null)
                    {
                        // 获取默认值
                        object defaultValue = null;
                        try
                        {
                            var instance = Activator.CreateInstance(driverType);
                            defaultValue = property.GetValue(instance);
                        }
                        catch
                        {
                            // 忽略获取默认值的错误
                        }

                        parameters[property.Name] = new DriverParameterInfo
                        {
                            Key = property.Name,
                            DisplayName = attr.DisplayName,
                            Type = property.PropertyType.Name,
                            DefaultValue = defaultValue
                        };
                    }
                }

                return parameters;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "反射获取驱动参数失败: {DriverTypeFullName}", driverType.FullName);
                return parameters;
            }
        }

        /// <summary>
        /// 获取驱动代码
        /// </summary>
        /// <param name="driverType">驱动类型</param>
        /// <returns>驱动代码</returns>
        private string GetDriverCode(Type driverType)
        {
            try
            {
                var instance = Activator.CreateInstance(driverType) as IDriver;
                return instance?.DriverCode;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "获取驱动代码失败: {DriverTypeFullName}", driverType.FullName);
                return null;
            }
        }
    }

    /// <summary>
    /// 驱动参数信息
    /// </summary>
    public class DriverParameterInfo
    {
        public string Key { get; set; }
        public string DisplayName { get; set; }
        public string Type { get; set; }
        public object DefaultValue { get; set; }
    }

    /// <summary>
    /// 驱动方法信息
    /// </summary>
    public class DriverMethodInfo
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Description { get; set; }
        public string ReturnType { get; set; }
        public List<DriverMethodParameterInfo> Parameters { get; set; }
    }

    /// <summary>
    /// 驱动方法参数信息
    /// </summary>
    public class DriverMethodParameterInfo
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public bool IsRequired { get; set; }
        public object DefaultValue { get; set; }
    }
}