import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfo } from './model';

const getTableData = (sourceUpdateInfo) => {
  let data = [];
  if (sourceUpdateInfo) {
    data = Object.entries(sourceUpdateInfo).map(([key, value]) => ({
      sourceDataField: key,
      tableField: value,
    }));
  }
  return data;
};

const getSourceUpdateInfo = (tableData) => {
  const sourceUpdateInfo = {};
  tableData.forEach((item) => {
    sourceUpdateInfo[item.sourceDataField] = item.tableField;
  });
  return sourceUpdateInfo;
};

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};
  const isNewAction = !currentArgs.name; // 判断是否为新增动作

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      dataSource: '',
      sourceDataPath: null,
      description: '',
      exceptionSkip: false,
      operateType: 'Save',
      configureInfo: {
        tableName: '',
        tableDescription: '',
        columns: [],
        sortColumns: [],
        conditions: null,
      },
      sourceUpdateInfo: {},
      outputConfig: {
        type: 'none', // 默认不输出
        dictionaryConfig: {
          keyColumns: [],
          keySeparator: '|',
          valueColumn: '',
          useFullObjectAsValue: false,
        },
      },
      useRoot: isNewAction ? true : (currentArgs.useRoot ?? false), // 新增动作默认true，历史配置默认false
    } as ArgsInfo),
    ...currentArgs,
  }) as ArgsInfo;
  state.updateTableData = getTableData(state.args?.sourceUpdateInfo);
};

export const useDataSaveStore = defineStore('DataSave', {
  state: () => {
    const state = {
      args: null,
      updateTableData: [],
    } as { args: ArgsInfo; updateTableData: any[] };
    initialState(state);
    return state;
  },
  getters: {
    sourceDataChildren() {
      const actionFlowStore = useActionFlowStore();
      let sourceDataChildren = [];
      if (this.args.sourceDataPath && this.args.sourceDataPath.variableValue) {
        sourceDataChildren = actionFlowStore.getVariableData(this.args.sourceDataPath.variableValue)?.children;
      }
      return sourceDataChildren;
    },
    dataSourceOptions() {
      return this.sourceDataChildren.map((item) => {
        return {
          label: item.key + (item.description ? ` (${item.description})` : ''),
          value: item.key,
        };
      });
    },
    tableColumnOptions() {
      const isSave = (this.args as ArgsInfo).operateType === 'Save';
      const isInsert = (this.args as ArgsInfo).operateType === 'Insert';
      const columns = (this.args as ArgsInfo).configureInfo?.columns;
      const tableColumnOptions = [];
      if (columns && columns.length > 0) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          if (
            column.isPrimaryKey ||
            column.isCondition ||
            (isSave && column.isInsert && column.isUpdate) ||
            (isInsert && column.isInsert)
          ) {
            const option = {
              label: column.columnName + (column.description ? ` (${column.description})` : ''),
              value: column.columnName,
            };
            tableColumnOptions.push(option);
          }
        }
      }
      return tableColumnOptions;
    },
    columnOptions() {
      // 用于字典配置的列选项，使用与tableColumnOptions相同的过滤逻辑
      const isSave = (this.args as ArgsInfo).operateType === 'Save';
      const isInsert = (this.args as ArgsInfo).operateType === 'Insert';
      const columns = (this.args as ArgsInfo).configureInfo?.columns;
      const columnOptions = [];
      if (columns && columns.length > 0) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          // 使用与tableColumnOptions相同的过滤条件
          if (
            column.isPrimaryKey ||
            column.isCondition ||
            (isSave && column.isInsert && column.isUpdate) ||
            (isInsert && column.isInsert)
          ) {
            const option = {
              label: column.columnName + (column.description ? ` (${column.description})` : ''),
              value: column.columnName,
            };
            columnOptions.push(option);
          }
        }
      }
      return columnOptions;
    },
    itemChildren() {
      const columns = (this.args as ArgsInfo).configureInfo?.columns;
      const children = [];
      if (columns && columns.length > 0) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          if (!(column.isPrimaryKey || column.isCondition || column.isQuery)) continue;

          const itemPath = `result.${column.columnName}`;
          const columnData = {
            id: column.columnName,
            key: column.columnName,
            description: column.description,
            type: column.dataType,
            value: {
              type: 'variable',
              dataType: column.dataType,
              variableType: 'current',
              variableValue: itemPath,
            },
          } as FlowData;

          children.push(columnData);
        }
      }
      return children;
    },
    currentRowChildren() {
      const columns = (this.args as ArgsInfo).configureInfo?.columns;
      const children = [];
      if (columns && columns.length > 0) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          if (!column.isQuery) continue;

          const itemPath = `item.${column.columnName}`;
          const columnData = {
            id: column.columnName,
            key: column.columnName,
            description: column.description,
            type: column.dataType,
            value: {
              type: 'variable',
              dataType: column.dataType,
              variableType: 'current',
              variableValue: itemPath,
            },
          } as FlowData;

          children.push(columnData);
        }
      }
      return children;
    },
    variables() {
      const itemKey = 'dataSourceItem';
      const currentRowKey = 'currentRow';
      const outputType = this.args.outputConfig?.type || 'none';

      const baseVariables = [
        {
          id: itemKey,
          key: itemKey,
          description: '数据集',
          type: 'object',
          children: this.sourceDataChildren || [],
        },
        {
          id: currentRowKey,
          key: currentRowKey,
          description: '当前行旧数据',
          type: 'object',
          children: this.currentRowChildren,
        },
      ] as FlowData[];

      // 根据输出类型决定是否添加结果变量
      if (outputType === 'none') {
        // 不输出，只返回基础变量
        return baseVariables;
      } else if (outputType === 'dictionary') {
        // 字典类型：返回字典对象
        baseVariables.push({
          id: 'result',
          key: 'result',
          description: '保存结果（字典）',
          type: 'object',
          value: {
            type: 'variable',
            variableType: 'current',
            variableValue: 'result',
          },
        });
      } else {
        // list 类型：返回数组（默认行为）
        const children = this.itemChildren || [];
        baseVariables.push({
          id: 'result',
          key: 'result',
          description: '保存结果',
          type: 'array',
          value: {
            type: 'variable',
            variableType: 'current',
            variableValue: 'result',
          },
          children,
        });
      }

      return baseVariables;
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      args.sourceUpdateInfo = getSourceUpdateInfo(this.updateTableData);

      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 先更新 this.args，然后再计算 variables
      this.args = args;

      // 如果输出类型为 none，不设置输出变量
      const outputType = args.outputConfig?.type || 'none';
      if (outputType === 'none') {
        actionFlowStore.setStepResultData([]);
        return;
      }

      // 根据useRoot参数决定是否使用根节点
      const resultVariable = this.variables.find((item: FlowData) => item.id === 'result');
      if (resultVariable) {
        if (!args.useRoot) {
          // 不使用根节点，只输出结果变量
          actionFlowStore.setStepResultData([resultVariable]);
        } else {
          // 使用根节点，创建根节点绑定
          let description = '保存结果';
          let type = 'array';

          // 根据输出类型设置描述和类型
          if (outputType === 'dictionary') {
            description = '保存结果（字典）';
            type = 'object';
          }

          const resultNode = {
            id: 'result',
            key: 'result',
            description,
            type,
            value: {
              type: 'variable' as const,
              variableType: 'current' as const,
              variableValue: 'result',
              dataType: type,
            },
            // 只有当 resultVariable 有 children 且不是 dictionary 类型时才包含 children
            ...(resultVariable.children && outputType !== 'dictionary' ? { children: resultVariable.children } : {}),
          };
          actionFlowStore.setStepResultData([resultNode]);
        }
      }
    },
  },
});
