using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration
{
    [Migration(20250409001000, "添加AI聊天管理相关表")]
    public class AddAiChatTables : Migration
    {
        public override void Up()
        {
            Create.Table("LC_CHAT_CONVERSATION").WithDescription("AI对话会话")
                .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行ID号")
                .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
                .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
                .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
                .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
                .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
                .WithColumn("USER_ID").AsAnsiString(36).WithColumnDescription("用户ID")
                .WithColumn("TITLE").AsAnsiString(200).WithColumnDescription("对话标题")
                .WithColumn("MODEL_CONFIG_ID").AsAnsiString(36).WithColumnDescription("模型配置ID");

            Create.Table("LC_CHAT_MESSAGE").WithDescription("AI对话消息")
                .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行ID号")
                .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
                .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
                .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
                .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
                .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
                .WithColumn("CONVERSATION_ID").AsAnsiString(36).WithColumnDescription("会话ID")
                .WithColumn("ROLE").AsAnsiString(36).WithColumnDescription("角色")
                .WithColumn("CONTENT").AsCustom("TEXT").WithColumnDescription("内容")
                .WithColumn("SEQUENCE").AsInt32().WithColumnDescription("序号");

            Create.Table("LC_CHAT_MODEL_CONFIG").WithDescription("AI模型配置")
                .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行ID号")
                .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
                .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
                .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
                .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
                .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
                .WithColumn("NAME").AsAnsiString(200).WithColumnDescription("配置名称")
                .WithColumn("TYPE").AsAnsiString(50).WithColumnDescription("模型类型")
                .WithColumn("API_KEY").AsAnsiString(200).WithColumnDescription("API密钥")
                .WithColumn("CHAT_MODEL_ID").AsAnsiString(100).WithColumnDescription("聊天模型ID")
                .WithColumn("EMBEDDING_MODEL_ID").AsAnsiString(100).WithColumnDescription("嵌入模型ID")
                .WithColumn("BASE_URL").AsAnsiString(200).WithColumnDescription("基础URL")
                .WithColumn("IS_DEFAULT").AsBoolean().WithColumnDescription("是否默认");

            Create.Table("LC_CHAT_PROFILE").WithDescription("AI对话配置文件")
                .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行ID号")
                .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
                .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
                .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
                .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
                .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
                .WithColumn("NAME").AsAnsiString(200).WithColumnDescription("配置名称")
                .WithColumn("DESCRIPTION").AsAnsiString(500).Nullable().WithColumnDescription("配置描述")
                .WithColumn("SYSTEM_PROMPT").AsCustom("TEXT").WithColumnDescription("系统提示")
                .WithColumn("DataSourceId").AsAnsiString(36).Nullable().WithColumnDescription("默认数据源ID")
                .WithColumn("MODEL_CONFIG_ID").AsAnsiString(36).WithColumnDescription("模型配置ID");

            // 创建索引
            Create.Index("IDX_CHAT_MESSAGE_CONVERSATION")
                .OnTable("LC_CHAT_MESSAGE")
                .OnColumn("CONVERSATION_ID").Ascending();

            Create.Index("IDX_CHAT_CONVERSATION_USER")
                .OnTable("LC_CHAT_CONVERSATION")
                .OnColumn("USER_ID").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_CHAT_MESSAGE");
            Delete.Table("LC_CHAT_PROFILE");
            Delete.Table("LC_CHAT_CONVERSATION");
            Delete.Table("LC_CHAT_MODEL_CONFIG");
        }
    }
} 