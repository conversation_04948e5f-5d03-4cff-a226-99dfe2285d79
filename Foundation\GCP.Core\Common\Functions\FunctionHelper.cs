﻿using Microsoft.AspNetCore.Http;

namespace GCP.Common
{
    public class FunctionHelper
    {
        /// <summary>
        /// 函数执行器
        /// </summary>
        public static IFunctionRunner Runner { get; set; }
        
        public static Func<string, Dictionary<string, object>, Task> TriggerJob { get; set; }

        public static Func<string, Dictionary<string, object>, ApiInfoDTO, CancellationToken, HttpContext, Task<object>> TriggerApi { get; set; }
    }
}
