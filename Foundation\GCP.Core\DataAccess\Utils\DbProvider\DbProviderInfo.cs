﻿using System.Data.Common;
using System.Text.RegularExpressions;

namespace GCP.DataAccess
{
    public enum DbProviderType
    {
        SQLite,
        DuckDB,
        Unknown,
        Oracle,
        MySql,
        SqlServer,
        PostgreSQL
    }

    internal static class DbProviderInfo
    {
        internal static Dictionary<string, IDbProvider> _dbProviderMap = new Dictionary<string, IDbProvider>() {
            { DbProviderType.SQLite.ToString(), new SqliteProvider() },
            { DbProviderType.DuckDB.ToString(), new DuckDbProvider() },
            { DbProviderType.Oracle.ToString(), new OracleManagedProvider() },
            { DbProviderType.SqlServer.ToString(), new SqlServerProvider() },
            { DbProviderType.MySql.ToString(), new MySqlConnectorProvider() },
            { DbProviderType.PostgreSQL.ToString(), new PostgreSqlProvider() }
        };
        static DbProviderInfo()
        {
            // .net core需要手动注册工厂
            foreach (var item in _dbProviderMap)
            {
                DbProviderFactories.RegisterFactory(item.Value.ProviderName, item.Value.Factory);
            }
        }

        internal static void Register(string name, IDbProvider dbProvider)
        {
            _dbProviderMap.Add(name, dbProvider);
            DbProviderFactories.RegisterFactory(dbProvider.ProviderName, dbProvider.Factory);
        }

        internal static DbProviderType GetDbProviderType(this IDbProvider dbProvider)
        {
            foreach (var item in _dbProviderMap)
            {
                if (item.Value == dbProvider)
                {
                    return (DbProviderType)Enum.Parse(typeof(DbProviderType), item.Key);
                }
            }
            return DbProviderType.Unknown;
        }

        internal static IDbProvider GetDbProvider(string name)
        {
            IDbProvider provider = null;
            var hasProvider = _dbProviderMap.TryGetValue(name, out provider);
            if (!hasProvider) throw new KeyNotFoundException("未找到数据库提供者 " + name);
            return provider;
        }

        internal static IDbProvider GetDbProvider(DbProviderType dbProviderType)
        {
            return GetDbProvider(dbProviderType.ToString());
        }

        static Regex orderByReg = new Regex("^order[ ]+by[ ]+[a-z_ ,.]+( asc| desc)$", RegexOptions.IgnoreCase);
        internal static string GetPagingSql(this IDbProvider dbProvider, int startIndex, int length, string sqlString, string orderBySql)
        {
            bool hasOrderBy = !string.IsNullOrEmpty(orderBySql);
            if (hasOrderBy)
            {
                if (orderBySql.IndexOf("ORDER BY", StringComparison.OrdinalIgnoreCase) == -1)
                {
                    orderBySql = "ORDER BY " + orderBySql;
                }
                if (!orderByReg.IsMatch(orderBySql))
                {
                    throw new Exception(string.Format("Custom OrderBy Sql[{0}] Invalid.", orderBySql));
                }
            }

            return dbProvider.GetPagingSql(startIndex, length, sqlString, orderBySql);
        }
    }
}
