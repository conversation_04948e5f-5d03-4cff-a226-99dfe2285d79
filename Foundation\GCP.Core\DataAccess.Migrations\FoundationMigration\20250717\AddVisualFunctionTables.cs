using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20250717001000, "添加在线函数&动作库管理表")]
    public class AddVisualFunctionTables : Migration
    {
        public override void Up()
        {

            // 在线函数管理表
            Create.Table("LC_ONLINE_FUNCTION").WithDescription("在线函数管理")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("NAME").AsAnsiString(200).WithColumnDescription("函数名称")
               .WithColumn("FUNCTION_TYPE").AsAnsiString(50).WithColumnDescription("函数类型 javascript、csharp")
               .WithColumn("DESCRIPTION").AsAnsiString(500).Nullable().WithColumnDescription("函数描述")
               .WithColumn("CODE").AsAnsiString(int.MaxValue).WithColumnDescription("函数代码")
               .WithColumn("STATUS").AsAnsiString(20).WithDefaultValue("active").WithColumnDescription("函数状态 active、inactive")
               .WithColumn("VERSION").AsInt32().WithDefaultValue(1).WithColumnDescription("函数版本")

               // 合并的参数和输出信息
               .WithColumn("INPUT_PARAMETERS_JSON").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("输入参数JSON定义")
               .WithColumn("OUTPUT_FORMAT_JSON").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("输出格式JSON定义")
               ;

            // 动作库表
            Create.Table("LC_ACTION_LIBRARY").WithDescription("动作库管理")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("NAME").AsAnsiString(200).WithColumnDescription("动作库名称")
               .WithColumn("DESCRIPTION").AsAnsiString(500).Nullable().WithColumnDescription("动作库描述")
               .WithColumn("CATEGORY").AsAnsiString(100).Nullable().WithColumnDescription("动作库分类")
               .WithColumn("TAGS").AsAnsiString(500).Nullable().WithColumnDescription("标签，逗号分隔")
               .WithColumn("VERSION").AsInt32().WithDefaultValue(1).WithColumnDescription("版本号")
               .WithColumn("STATUS").AsAnsiString(20).WithDefaultValue("active").WithColumnDescription("状态 active、inactive、draft")

               .WithColumn("FUNCTION_FLOW_ID").AsAnsiString(36).WithColumnDescription("关联的函数流ID")
               .WithColumn("FUNCTION_FLOW_VERSION").AsInt64().Nullable().WithColumnDescription("关联的函数流版本")

               .WithColumn("INPUT_SCHEMA_JSON").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("输入参数Schema JSON")
               .WithColumn("OUTPUT_SCHEMA_JSON").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("输出参数Schema JSON")
               .WithColumn("TEST_DATA_JSON").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("测试数据JSON")

               .WithColumn("EXECUTION_COUNT").AsInt64().WithDefaultValue(0).WithColumnDescription("执行次数")
               .WithColumn("LAST_EXECUTION_TIME").AsDateTime().Nullable().WithColumnDescription("最后执行时间")
               .WithColumn("AVERAGE_EXECUTION_TIME").AsInt32().Nullable().WithColumnDescription("平均执行时间(毫秒)")
               ;

            Create.Index("OF_SOLUTION_PROJECT_IDX")
                .OnTable("LC_ONLINE_FUNCTION")
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .OnColumn("STATE").Ascending();

            Create.Index("OF_NAME_TYPE_IDX")
                .OnTable("LC_ONLINE_FUNCTION")
                .OnColumn("NAME").Ascending()
                .OnColumn("FUNCTION_TYPE").Ascending();

            // 动作库索引
            Create.Index("AL_SOLUTION_PROJECT_IDX")
                .OnTable("LC_ACTION_LIBRARY")
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .OnColumn("STATE").Ascending();

            Create.Index("AL_NAME_CATEGORY_IDX")
                .OnTable("LC_ACTION_LIBRARY")
                .OnColumn("NAME").Ascending()
                .OnColumn("CATEGORY").Ascending();

            Create.Index("AL_FUNCTION_FLOW_IDX")
                .OnTable("LC_ACTION_LIBRARY")
                .OnColumn("FUNCTION_FLOW_ID").Ascending()
                .OnColumn("FUNCTION_FLOW_VERSION").Ascending();

            // 动作库执行日志表
            Create.Table("LC_ACTION_LIBRARY_LOG").WithDescription("动作库执行日志")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("ACTION_LIBRARY_ID").AsAnsiString(36).WithColumnDescription("动作库ID")
               .WithColumn("EXECUTION_ID").AsAnsiString(36).WithColumnDescription("执行实例ID")
               .WithColumn("FLOW_PROC_ID").AsAnsiString(36).Nullable().WithColumnDescription("关联的流程实例ID")
               .WithColumn("FLOW_STEP_ID").AsAnsiString(36).Nullable().WithColumnDescription("关联的流程步骤ID")

               .WithColumn("INPUT_DATA").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("输入数据JSON")
               .WithColumn("OUTPUT_DATA").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("输出数据JSON")
               .WithColumn("ERROR_MESSAGE").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("错误信息")
               .WithColumn("STACK_TRACE").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("堆栈跟踪")

               .WithColumn("STATUS").AsAnsiString(20).WithColumnDescription("执行状态 success、error、timeout")
               .WithColumn("EXECUTION_TIME_MS").AsInt32().Nullable().WithColumnDescription("执行时间(毫秒)")
               .WithColumn("START_TIME").AsDateTime().WithColumnDescription("开始时间")
               .WithColumn("END_TIME").AsDateTime().Nullable().WithColumnDescription("结束时间")

               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("执行者")
               ;

            Create.Index("ALL_ACTION_LIBRARY_IDX")
                .OnTable("LC_ACTION_LIBRARY_LOG")
                .OnColumn("ACTION_LIBRARY_ID").Ascending()
                .OnColumn("START_TIME").Descending();

            Create.Index("ALL_FLOW_PROC_IDX")
                .OnTable("LC_ACTION_LIBRARY_LOG")
                .OnColumn("FLOW_PROC_ID").Ascending()
                .OnColumn("FLOW_STEP_ID").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_ACTION_LIBRARY_LOG");
            Delete.Table("LC_ACTION_LIBRARY");
            Delete.Table("LC_ONLINE_FUNCTION");
        }
    }
}
