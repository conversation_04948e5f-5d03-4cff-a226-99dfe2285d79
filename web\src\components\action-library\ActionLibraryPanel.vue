<template>
  <div v-if="visible" class="action-library-panel-overlay">
    <div v-loading="loading" class="form-container">
      <div class="form-nav">
        <div class="nav-left">
          <t-button size="large" variant="text" class="nav-back" @click="onClickBack">
            <template #icon>
              <chevron-left-icon />
            </template>
          </t-button>
          <div class="nav-title">{{ actionLibraryTitle }}</div>
        </div>
        <div class="nav-center">
          <ul class="nav-menu">
            <li :class="{ 'nav-menu-item': true, active: currentTab === 0 }" @click="currentTab = 0">基础信息</li>
            <li :class="{ 'nav-menu-item': true, active: currentTab === 1 }" @click="currentTab = 1">函数流配置</li>
            <li :class="{ 'nav-menu-item': true, active: currentTab === 2 }" @click="currentTab = 2">参数配置</li>
            <li :class="{ 'nav-menu-item': true, active: currentTab === 3 }" @click="currentTab = 3">测试执行</li>
            <li :class="{ 'nav-menu-item': true, active: currentTab === 4 }" @click="currentTab = 4">执行日志</li>
          </ul>
        </div>
        <div class="nav-right">
          <t-button theme="default" @click="onClickTest" :disabled="!actionLibraryData.id">测 试</t-button>
          <t-button @click="onClickSave">保 存</t-button>
        </div>
      </div>
      <div v-if="visible" class="form-content">
        <div v-show="currentTab === 0">
          <action-library-info v-model="actionLibraryData" />
        </div>
        <div v-show="currentTab === 1">
          <action-library-flow-config v-model="actionLibraryData" />
        </div>
        <div v-show="currentTab === 2">
          <action-library-params-config v-model="actionLibraryData" />
        </div>
        <div v-show="currentTab === 3">
          <action-library-test v-model="actionLibraryData" @test-execute="onTestExecute" />
        </div>
        <div v-show="currentTab === 4">
          <action-library-logs :action-library-id="actionLibraryData.id" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryPanel',
};
</script>

<script setup lang="ts">
import { ref, watch, watchEffect, computed, onMounted, onUnmounted } from 'vue';
import { ChevronLeftIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import ActionLibraryInfo from './ActionLibraryInfo.vue';
import ActionLibraryFlowConfig from './ActionLibraryFlowConfig.vue';
import ActionLibraryParamsConfig from './ActionLibraryParamsConfig.vue';
import ActionLibraryTest from './ActionLibraryTest.vue';
import ActionLibraryLogs from './ActionLibraryLogs.vue';

interface ActionLibraryData {
  id?: string;
  name: string;
  description?: string;
  category?: string;
  tags?: string;
  status: string;
  functionFlowId?: string;
  functionFlowVersion?: number;
  inputSchemaJson?: string;
  outputSchemaJson?: string;
  testDataJson?: string;
  version: number;
  executionCount: number;
  lastExecutionTime?: string;
  averageExecutionTime?: number;
}

const props = defineProps<{
  visible?: boolean;
  actionLibraryId?: string;
}>();

const emits = defineEmits(['back', 'save', 'update:visible']);

const visible = ref(props.visible);
watch(
  () => visible.value,
  (val) => {
    emits('update:visible', val);
  },
);

watchEffect(() => {
  visible.value = props.visible;
});

const currentTab = ref(0);
const loading = ref(false);

// 动作库数据
const actionLibraryData = ref<ActionLibraryData>({
  name: '',
  description: '',
  category: '',
  tags: '',
  status: 'draft',
  version: 1,
  executionCount: 0,
});

const actionLibraryTitle = computed(() => {
  if (actionLibraryData.value.id) {
    return `编辑动作库 - ${actionLibraryData.value.name}`;
  } else {
    return '新建动作库';
  }
});

// 加载动作库数据
const loadActionLibrary = async (id: string) => {
  if (!id) {
    // 新建模式，重置数据
    actionLibraryData.value = {
      name: '',
      description: '',
      category: '',
      tags: '',
      status: 'draft',
      version: 1,
      executionCount: 0,
    };
    return;
  }

  loading.value = true;
  try {
    const data = await api.run(Services.actionLibraryGet, { id });
    if (data) {
      actionLibraryData.value = {
        id: data.id,
        name: data.name,
        description: data.description,
        category: data.category,
        tags: data.tags,
        status: data.status,
        functionFlowId: data.functionFlowId,
        functionFlowVersion: data.functionFlowVersion,
        inputSchemaJson: data.inputSchemaJson,
        outputSchemaJson: data.outputSchemaJson,
        testDataJson: data.testDataJson,
        version: data.version,
        executionCount: data.executionCount,
        lastExecutionTime: data.lastExecutionTime,
        averageExecutionTime: data.averageExecutionTime,
      };
    }
  } catch (error) {
    MessagePlugin.error(`加载动作库失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 监听动作库ID变化
watch(
  () => props.actionLibraryId,
  (newId) => {
    if (visible.value) {
      loadActionLibrary(newId);
    }
  },
  { immediate: true },
);

// 当面板显示时加载数据
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      currentTab.value = 0; // 重置到第一个标签页
      if (props.actionLibraryId) {
        loadActionLibrary(props.actionLibraryId);
      } else {
        // 新建模式，重置数据
        loadActionLibrary('');
      }
    }
  },
);

const onClickBack = () => {
  visible.value = false;
  emits('back');
};

const onClickSave = async () => {
  try {
    loading.value = true;

    // 验证必填字段
    if (!actionLibraryData.value.name?.trim()) {
      MessagePlugin.error('请输入动作库名称');
      currentTab.value = 0;
      return;
    }

    if (!actionLibraryData.value.functionFlowId) {
      MessagePlugin.error('请选择关联的函数流');
      currentTab.value = 1;
      return;
    }

    const result = await api.run(Services.actionLibrarySave, actionLibraryData.value);

    if (result) {
      actionLibraryData.value.id = result;
      MessagePlugin.success('保存成功');
      emits('save', actionLibraryData.value);
    }
  } catch (error) {
    MessagePlugin.error(`保存失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

const onClickTest = () => {
  currentTab.value = 3; // 切换到测试执行标签页
};

const onTestExecute = async (testData: any) => {
  try {
    loading.value = true;
    const result = await api.run(Services.actionLibraryTestExecute, {
      id: actionLibraryData.value.id,
      inputData: testData,
    });

    MessagePlugin.success('测试执行完成');

    // 刷新执行日志
    if (currentTab.value === 4) {
      // 触发日志组件刷新
    }

    return result;
  } catch (error) {
    MessagePlugin.error(`测试执行失败: ${error.message}`);
    throw error;
  } finally {
    loading.value = false;
  }
};

// ESC键关闭面板
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && visible.value) {
    onClickBack();
  }
};

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

defineExpose({
  loadActionLibrary,
});
</script>

<style lang="less" scoped>
@import '@/style/form.less';

.action-library-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: var(--td-bg-color-page);
  overflow: hidden;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-container {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;

  .form-nav {
    flex-shrink: 0;
    width: 100%;
    height: 64px;
    font-size: 14px;
    color: var(--td-text-color-secondary);
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    padding: 0 24px;

    .nav-title {
      margin-left: 12px;
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .nav-left {
      display: flex;
      align-items: center;
      flex: 0 0 auto;

      .nav-back {
        height: 40px;
        width: 40px;
        border-radius: 8px;

        &:hover {
          background: var(--td-bg-color-component-hover);
        }
      }
    }

    .nav-center {
      flex: 1;
      display: flex;
      justify-content: center;
      margin: 0 24px;

      .nav-menu {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        background: var(--td-bg-color-component);
        border-radius: 8px;
        padding: 4px;

        .nav-menu-item {
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          margin: 0;
          padding: 0 20px;
          height: 40px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;

          &:hover {
            color: var(--td-brand-color);
            background: var(--td-bg-color-container-hover);
          }

          &.active {
            color: var(--td-brand-color);
            background: var(--td-bg-color-container);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .nav-right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 0 0 auto;
    }
  }

  .form-content {
    flex: 1;
    overflow: hidden;
    background: var(--td-bg-color-page);

    > div {
      height: 100%;
      overflow-y: auto;
      padding: 32px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--td-scrollbar-color);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: var(--td-scrollbar-hover-color);
      }
    }
  }
}
</style>
