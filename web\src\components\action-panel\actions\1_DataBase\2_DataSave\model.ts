import { DataSourceTableData, FlowDataValue } from '@/components/action-panel/model';

/**
 * 输出类型枚举
 */
export type OutputType = 'none' | 'dictionary' | 'list';

/**
 * 输出配置
 */
export interface OutputConfig {
  /**
   * 输出类型
   */
  type: OutputType;
  /**
   * 字典配置（当 type = 'dictionary' 时使用）
   */
  dictionaryConfig?: DictionaryConfig;
}

/**
 * 字典配置
 */
export interface DictionaryConfig {
  /**
   * 键字段配置（支持多列组合）
   */
  keyColumns: string[];
  /**
   * 键分隔符（多列组合时使用）
   */
  keySeparator: string;
  /**
   * 值字段（单列）
   */
  valueColumn: string;
  /**
   * 是否返回完整对象作为值
   */
  useFullObjectAsValue: boolean;
}

export interface ArgsInfo {
  name: string;
  dataSource: string;
  sourceDataPath: FlowDataValue;
  description: string;
  exceptionSkip: boolean;
  openFastInsert: boolean;
  batchSize?: number;
  operateType: 'Save' | 'Insert' | 'Update';
  configureInfo?: DataSourceTableData;
  sourceUpdateInfo?: Record<string, string>;
  outputConfig?: OutputConfig;
  useRoot?: boolean; // 控制是否使用根节点，新增动作默认true，历史配置默认false
}
