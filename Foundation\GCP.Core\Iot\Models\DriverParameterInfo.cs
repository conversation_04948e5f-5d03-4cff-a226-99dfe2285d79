namespace GCP.Iot.Models
{
    /// <summary>
    /// 驱动参数信息
    /// </summary>
    public class DriverParameterInfo
    {
        /// <summary>
        /// 参数编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 参数默认值
        /// </summary>
        public string DefaultValue { get; set; }

        /// <summary>
        /// 参数当前值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 是否必填
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// 参数数据类型
        /// </summary>
        public DataTypeEnum DataType { get; set; }
    }
}