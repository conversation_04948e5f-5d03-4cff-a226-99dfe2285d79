<template>
  <div>
    <t-list split>
      <t-list-item v-for="(item, index) in variableList" :key="item.name">
        <t-list-item-meta :title="item.name" :description="item.description">
          <template #image>
            <add-icon v-if="item.operation === 'add'"></add-icon>
            <edit-icon v-else></edit-icon>
          </template>
        </t-list-item-meta>
        <template #action>
          <t-button variant="text" shape="square" @click="() => onClickEditVariable(index)">
            <edit2-icon />
          </t-button>
          <t-button variant="text" shape="square" @click="() => variableList.splice(index, 1)">
            <delete1-icon />
          </t-button>
        </template>
      </t-list-item>
    </t-list>
    <t-row style="margin-top: 16px" :gutter="16">
      <t-col :span="6">
        <t-button block variant="outline" theme="primary" @click="onClickAddVariable">新 增 变 量</t-button>
      </t-col>
      <t-col :span="6">
        <t-button block variant="outline" theme="warning" @click="() => onClickEditVariable()">修 改 变 量</t-button>
      </t-col>
    </t-row>
    <t-dialog
      v-model:visible="showVariableFormDialog"
      :header="(formData.operation === 'add' ? '新增' : '修改') + '变量'"
      :footer="false"
    >
      <t-form :rules="FORM_RULES" :data="formData" @submit="onSubmit">
        <t-form-item label="变量名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入变量名称"></t-input>
        </t-form-item>
        <t-form-item label="变量类型" name="type">
          <value-type-select v-model:type="formData.type" size="medium" :borderless="false"></value-type-select>
        </t-form-item>
        <t-form-item label="变量值" name="value">
          <value-input v-model:data-value="formData.value" size="medium" :borderless="false"></value-input>
        </t-form-item>
        <t-form-item label="描述" name="description">
          <t-textarea v-model="formData.description" />
        </t-form-item>
        <t-form-item>
          <t-space size="small">
            <t-button theme="primary" type="submit">提交</t-button>
            <t-button theme="default" variant="base" @click="showVariableFormDialog = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>
<script lang="ts">
export default {
  name: 'SetVariableActions',
};
</script>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { AddIcon, Delete1Icon, Edit2Icon, EditIcon } from 'tdesign-icons-vue-next';
import { FormProps } from 'tdesign-vue-next';
import { computed, ref, watch, watchEffect } from 'vue';

import { FlowStep } from '@/components/action-panel/model';
import ValueInput from '@/components/action-panel/ValueInput.vue';
import ValueTypeSelect from '@/components/action-panel/ValueTypeSelect.vue';

const props = defineProps<{
  value: FlowStep;
}>();
const emit = defineEmits(['update:value']);

const data = ref(props.value);

watch(
  data,
  (newValue) => {
    emit('update:value', newValue);
  },
  {
    deep: true,
  },
);

interface variableItem {
  operation: 'add' | 'edit';
  name: string;
  type: string;
  description?: string;
  value?: any;
}

const variableList = ref<variableItem[]>([]);
watchEffect(() => {
  data.value = props.value;
  variableList.value = data.value.args || [];
});

const FORM_RULES = computed(() => {
  const rules: FormProps['rules'] = {
    name: [{ required: true, message: '变量名称不能为空' }],
    type: [{ required: true, message: '变量类型不能为空' }],
  };
  if (formData.value?.operation === 'edit') {
    rules.value = [{ required: true, message: '变量值不能为空' }];
  }

  return rules;
});

const formData = ref<variableItem>({
  operation: 'add',
  name: '',
  type: 'string',
});
const showVariableFormDialog = ref(false);
const editIndex = ref(-1);
const onClickAddVariable = () => {
  showVariableFormDialog.value = true;
  editIndex.value = -1;
  formData.value = {
    operation: 'add',
    name: '',
    type: 'string',
  };
};
const onClickEditVariable = (index?: number) => {
  showVariableFormDialog.value = true;
  let item = {};
  if (index !== undefined) {
    item = variableList.value[index];
    editIndex.value = index;
  } else {
    editIndex.value = -1;
  }

  formData.value = {
    ...{
      operation: 'edit',
      name: '',
      type: 'string',
    },
    ...item,
  };
};

const onSubmit: FormProps['onSubmit'] = async (ctx) => {
  if (ctx.validateResult === true) {
    if (editIndex.value !== -1) {
      variableList.value[editIndex.value] = formData.value;
    } else {
      variableList.value.push(formData.value);
    }
    showVariableFormDialog.value = false;
  }
};

watch(
  variableList,
  (newValue) => {
    data.value.args = newValue;
  },
  {
    deep: true,
  },
);
</script>
<style lang="less" scoped>
:deep(.t-list-item__meta-avatar) {
  > .t-icon {
    font-size: 24px;
    position: relative;
    left: 16px;
    top: 15px;
  }
}
</style>
