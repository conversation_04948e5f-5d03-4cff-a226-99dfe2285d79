﻿using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    internal sealed class SqlSession {
        public string ConnectionString { get; private set; }
        public IDbProvider DbProvider { get; private set; }
        internal DbProviderFactory factory { get; set; }
        internal DbConnection connection { get; set; }
        internal IDbContext dbContext { get; set; }

        public SqlSession(IDbContext dbContext) {
            this.dbContext = dbContext;

            this.ConnectionString = dbContext.ConnectionString;
            this.DbProvider = dbContext.DbProvider;
            this.factory = dbContext.GetFactory();
        }

        public SqlSession(IDbConnection connection) {
            this.connection = (DbConnection)connection;

            this.ConnectionString = connection.ConnectionString;
            this.factory = connection.GetFactory();
            this.DbProvider = this.factory.GetDbProvider();
        }

        public DbConnection CreateConnection() {
            return this.connection ?? this.factory.CreateConnection(this.ConnectionString);
        }

        public DbParameters CreateParameters() {
            return this.dbContext == null ? this.connection.CreateParameters() : this.dbContext.CreateParameters();
        }
    }
}
