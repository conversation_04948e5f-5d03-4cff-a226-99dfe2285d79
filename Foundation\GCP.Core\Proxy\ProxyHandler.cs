﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Yarp.ReverseProxy.Configuration;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class ProxyHandler
    {
        public static IServiceCollection AddProxy(this IServiceCollection services, IConfiguration configuration)
        {
            #region 测试网页
            ////{**catch-all} 全路径匹配
            ////{*any} 斜杠/前匹配
            ////{**any:regex(.+\.(jpg|png|gif|jpeg))}  全路径正则匹配（路由约束）
            //var routeConfig = new RouteConfig
            //{
            //    RouteId = "routeAll",
            //    ClusterId = "clusterBaidu",
            //    Match = new RouteMatch
            //    {
            //        Path = "{**catch-all}"
            //    }
            //};


            //var clusterConfig = new ClusterConfig()
            //{
            //    ClusterId = "clusterBaidu",
            //    LoadBalancingPolicy = LoadBalancingPolicies.PowerOfTwoChoices, // 负载均衡
            //    Destinations = new Dictionary<string, DestinationConfig>(StringComparer.OrdinalIgnoreCase)
            //    {
            //        {
            //            "baidu",
            //            new DestinationConfig()
            //            {
            //                Address = "https://www.baidu.com/"
            //            }
            //        },
            //        {
            //            "bing",
            //            new DestinationConfig()
            //            {
            //                Address = "https://cn.bing.com/"
            //            }
            //        }
            //    }
            //};
            #endregion

            IReadOnlyList<RouteConfig> routes = new List<RouteConfig>()
            {
                //routeConfig
            };
            IReadOnlyList<ClusterConfig> clusters = new List<ClusterConfig>()
            {
                //clusterConfig
            };

            services.AddReverseProxy()
                .LoadFromMemory(routes, clusters);

            services.AddHttpForwarder();

            //update
            //httpContext.RequestServices.GetRequiredService<InMemoryConfigProvider>().Update(routes, clusters);

            return services;
        }

        public static IEndpointConventionBuilder MapProxy(
           this IEndpointRouteBuilder endpoints)
        {
            return endpoints.MapReverseProxy();
        }

        public static IApplicationBuilder UseProxy(this IApplicationBuilder app)
        {

            return app;
        }
    }
}