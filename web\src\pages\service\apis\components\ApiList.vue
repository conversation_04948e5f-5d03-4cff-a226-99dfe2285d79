<template>
  <div class="api-list-container">
    <div class="api-list">
      <t-space
        v-if="props.data?.length === 0"
        direction="vertical"
        align="center"
        style="width: 100%; margin-top: 60px"
      >
        <t-empty />
      </t-space>
      <div v-else>
        <t-input v-model="searchKeyword" placeholder="搜索" clearable style="margin-bottom: 8px">
          <template #suffixIcon>
            <search-icon />
          </template>
        </t-input>
        <t-space direction="vertical" size="small" style="width: 100%">
          <api-item
            v-for="item in dataList"
            :key="item.id"
            :method="item.requestType"
            :url="item.httpUrl"
            :name="item.apiName"
            :active="item.id === currentApi?.id || item.id === currentThirdPartyApi?.id"
            @click="onItemClick(item)"
          ></api-item>
        </t-space>
      </div>
    </div>
    <div class="api-operation">
      <t-button block variant="dashed" @click="onAddClick">添 加 接 口</t-button>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ApiList',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { SearchIcon } from 'tdesign-icons-vue-next';
import { computed, ref } from 'vue';

import { ApiInfoDto, useApiStore } from '../store';
import ApiItem from './ApiItem.vue';

const apiStore = useApiStore();
const { currentApi, currentThirdPartyApi } = storeToRefs(apiStore);
const props = defineProps<{
  data: ApiInfoDto[];
}>();
const emit = defineEmits(['add', 'click']);

const searchKeyword = ref('');

const dataList = computed(() => {
  return props.data?.filter((item) => {
    const keyword = searchKeyword.value.trim().toLowerCase();
    if (keyword === '') {
      return true;
    }
    return item.apiName.toLowerCase().includes(keyword) || item.httpUrl.toLowerCase().includes(keyword);
  });
});

const onAddClick = () => {
  emit('add');
};

const onItemClick = (item) => {
  emit('click', item);
};
</script>
<style lang="less" scoped>
.api-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .api-list {
    flex: 1;
    overflow: hidden auto;
  }
}
</style>
