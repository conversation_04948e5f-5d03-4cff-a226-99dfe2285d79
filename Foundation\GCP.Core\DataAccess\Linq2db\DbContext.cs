﻿using LinqToDB.Data;

namespace GCP.DataAccess
{
    public partial class DbContext : IDbContext
    {
        public DataConnection CreateLinqConnection()
        {
            return CreateLinqConnection(this.DbProvider.ProviderName, this.ConnectionString);
        }

        public DataConnection CreateLinqConnection(string connectionString)
        {
            return CreateLinqConnection(this.DbProvider.ProviderName, connectionString);
        }

        public DataConnection CreateLinqConnection(string providerName, string connectionString)
        {
            return new DbBase(providerName, connectionString);
        }
    }
}
