namespace GCP.Common
{
    /// <summary>
    /// 事件总线帮助类
    /// </summary>
    public class EventBusHelper
    {
        /// <summary>
        /// 系统事件总线名称
        /// </summary>
        public static string SystemEventBusName { get; set; } = "gcp-bus";
        /// <summary>
        /// 本地IOT总线名称（高并发）
        /// </summary>
        public static string LocalIotEventBusName { get; set; } = "local-iot-bus";

        /// <summary>
        /// 初始化事件的委托
        /// </summary>
        public static Func<string, Task> InitializeEvent { get; set; }

        /// <summary>
        /// 停止事件的委托
        /// </summary>
        public static Func<string, Task> StopEvent { get; set; }
    }
} 