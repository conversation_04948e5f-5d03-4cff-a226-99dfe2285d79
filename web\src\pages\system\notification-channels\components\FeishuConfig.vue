<template>
  <div class="feishu-config">
    <t-form-item label="Webhook地址" name="webHook">
      <t-input v-model="config.webHook" placeholder="请输入飞书机器人Webhook地址" />
    </t-form-item>

    <t-form-item label="签名密钥" name="secret">
      <t-input v-model="config.secret" placeholder="请输入签名密钥（可选）" />
    </t-form-item>

    <t-alert theme="info" title="配置说明">
      <p>1. Webhook地址：在飞书群中添加自定义机器人后获得</p>
      <p>2. 签名密钥：如果机器人启用了签名验证，需要填写此项</p>
      <p>3. 飞书机器人支持发送文本、富文本等多种消息类型</p>
    </t-alert>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
const props = defineProps<{
  modelValue: {
    webHook: string;
    secret: string;
  };
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
}>();

// 计算属性
const config = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});
</script>

<style scoped>
.feishu-config {
  padding: 10px 0;
}

.t-alert {
  margin-top: 20px;
}
</style>
