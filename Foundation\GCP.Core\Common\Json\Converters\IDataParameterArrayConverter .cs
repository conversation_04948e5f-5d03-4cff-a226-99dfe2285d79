﻿using System.Data;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace GCP.Common
{
    public class IDataParameterArrayConverter : JsonConverter<IDataParameter[]>
    {
        public override IDataParameter[] Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            throw new NotImplementedException();
        }

        public override void Write(Utf8JsonWriter writer, IDataParameter[] value, JsonSerializerOptions options)
        {
            writer.WriteStartArray();

            foreach (var param in value)
            {
                writer.WriteStartArray();
                writer.WriteStringValue(param.DbType.ToString());
                writer.WriteStringValue(param.ParameterName);
                writer.WriteStringValue(param.Value?.ToString() ?? string.Empty);
                writer.WriteEndArray();
            }

            writer.WriteEndArray();
        }
    }
}
