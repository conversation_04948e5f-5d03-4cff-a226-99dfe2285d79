<template>
  <div>
    <div v-if="props.eventType == 1">
      <action-form-title title="测点绑定"></action-form-title>
    </div>
    <div v-if="props.eventType == 2">
      <action-form-title title="设备绑定"></action-form-title>
    </div>
    <div v-if="props.eventType == 3">
      <action-form-title title="设备类型绑定"></action-form-title>
    </div>

    <t-space direction="vertical">
      <div>
        <t-button @click="showDialog = true"> 批量添加 </t-button>
      </div>
      <t-table :data="tableData" :columns="tableColumns" row-key="id" :hover="true" size="small">
        <template #operate="{ row }">
          <t-space size="small">
            <t-popconfirm content="确认删除吗" @confirm="onConfirmDelete(row.id)">
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-space>

    <t-dialog width="60%" v-model:visible="showDialog" header="批量添加" :on-confirm="onAddAction">
      <editor v-model:value="addText" language="plaintext" :show-line-numbers="false" style="height: 280px"></editor>
    </t-dialog>
  </div>
</template>
<script lang="ts">
export default {
  name: 'EventEquipmentForm',
};
</script>

<script setup lang="ts">
import { computed, ref } from 'vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { MessagePlugin, PrimaryTableCol, TableRowData } from 'tdesign-vue-next';
import Editor from '@/components/editor/index.vue';
import { api, Services } from '@/api/system';

const props = defineProps<{
  eventType: number;
  eventId: string;
}>();

const addText = ref('');
const showDialog = ref(false);
const onAddAction = async () => {
  await api
    .run(Services.messageEventAddMappingByEquipment, {
      text: addText.value,
      eventId: props.eventId,
      eventType: props.eventType,
    })
    .then(() => {
      MessagePlugin.success('添加成功');
      showDialog.value = false;
      fetchData();
    });
};

const onConfirmDelete = (id) => {
  api.run(Services.messageEventDeleteMapping, { id }).then(() => {
    MessagePlugin.success('删除成功');
    fetchData();
  });
};

const tableColumns = computed<PrimaryTableCol<TableRowData>[]>(() => {
  fetchData();
  let columns = [];
  if (props.eventType == 1) {
    columns = [
      {
        title: '设备编码',
        colKey: 'equipmentCode',
      },
      {
        title: '设备名称',
        colKey: 'equipmentName',
      },
      {
        title: '设备变量名',
        colKey: 'varName',
      },
      {
        title: '地址',
        colKey: 'address',
      },
    ];
  }
  if (props.eventType == 2) {
    columns = [
      {
        title: '设备编码',
        colKey: 'equipmentCode',
      },
      {
        title: '设备名称',
        colKey: 'equipmentName',
      },
      {
        title: '设备类型',
        colKey: 'equipmentType',
      },
    ];
  }
  if (props.eventType == 3) {
    columns = [
      {
        title: '设备类型',
        colKey: 'equipmentType',
      },
    ];
  }

  return [
    ...columns,
    {
      title: '操作',
      align: 'left',
      fixed: 'right',
      width: 160,
      colKey: 'operate',
    },
  ];
});

const tableData = ref<any[]>([]);
const fetchData = async () => {
  tableData.value = await api.run(Services.messageEventGetEquipmentInfoByEventId, {
    eventId: props.eventId,
    eventType: props.eventType,
  });
};

// 验证方法 - 检查是否至少有一条绑定数据
const validate = async () => {
  const hasData = tableData.value && tableData.value.length > 0;
  return {
    validateResult: hasData,
    firstError: hasData ? null : '请至少添加一条设备绑定',
  };
};

// 暴露验证方法
defineExpose({
  validate,
  getData: () => tableData.value,
});
</script>
