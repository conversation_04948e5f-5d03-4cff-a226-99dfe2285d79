﻿using System.Data.Common;

namespace GCP.DataAccess
{
    internal class SqlServerProvider : BaseDbProvider
    {
        public override string ProviderName
        {
            get
            {
                return "Microsoft.Data.SqlClient";
            }
        }

        public override bool NotUseParameter
        {
            get
            {
                return false;
            }
        }

        public override DbProviderFactory Factory => Microsoft.Data.SqlClient.SqlClientFactory.Instance;
        public override DbProviderType ProviderType => DbProviderType.SqlServer;

        public override string GetParameterName(string parameterName)
        {
            return parameterName[0] == '@' ? parameterName : "@" + parameterName;
        }

        public override string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "")
        {
            if (string.IsNullOrEmpty(orderBySql)) orderBySql = "ORDER BY GETDATE()";
            return string.Format("SELECT * FROM (SELECT t.*, ROW_NUMBER() OVER({3}) v_rowno FROM ({2}) t) t WHERE v_rowno > {0} AND v_rowno <= {0} + {1}", startIndex, length, selectSql, orderBySql);
        }

        public override string GetTimeFormatStr(DateTime time)
        {
            return "'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }

        public override string GetTimeSql()
        {
            return "select getdate()";
        }
    }
}
