<template>
  <div class="action-library-logs">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <t-form layout="inline" :data="filterForm">
        <t-form-item label="状态">
          <t-select
            v-model="filterForm.status"
            :options="statusOptions"
            placeholder="全部状态"
            clearable
            style="width: 120px"
          />
        </t-form-item>
        <t-form-item label="时间范围">
          <t-date-range-picker
            v-model="filterForm.dateRange"
            placeholder="选择时间范围"
            style="width: 280px"
          />
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" @click="loadLogs">
              <template #icon>
                <search-icon />
              </template>
              查询
            </t-button>
            <t-button theme="default" @click="resetFilter">
              <template #icon>
                <refresh-icon />
              </template>
              重置
            </t-button>
            <t-button theme="default" @click="exportLogs">
              <template #icon>
                <download-icon />
              </template>
              导出
            </t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <t-row :gutter="16">
        <t-col :span="3">
          <t-card :bordered="false" class="stat-card">
            <div class="stat-item">
              <div class="stat-value success">{{ stats.totalSuccess }}</div>
              <div class="stat-label">成功执行</div>
            </div>
          </t-card>
        </t-col>
        <t-col :span="3">
          <t-card :bordered="false" class="stat-card">
            <div class="stat-item">
              <div class="stat-value error">{{ stats.totalError }}</div>
              <div class="stat-label">执行失败</div>
            </div>
          </t-card>
        </t-col>
        <t-col :span="3">
          <t-card :bordered="false" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.avgExecutionTime }}ms</div>
              <div class="stat-label">平均耗时</div>
            </div>
          </t-card>
        </t-col>
        <t-col :span="3">
          <t-card :bordered="false" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </t-card>
        </t-col>
      </t-row>
    </div>

    <!-- 日志列表 -->
    <div class="logs-section">
      <t-table
        :data="logs"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
        row-key="id"
      >
        <template #status="{ row }">
          <t-tag :theme="getStatusTheme(row.status)">
            {{ getStatusText(row.status) }}
          </t-tag>
        </template>
        <template #executionTime="{ row }">
          <span :class="getExecutionTimeClass(row.executionTimeMs)">
            {{ row.executionTimeMs }}ms
          </span>
        </template>
        <template #startTime="{ row }">
          {{ formatDateTime(row.startTime) }}
        </template>
        <template #endTime="{ row }">
          {{ formatDateTime(row.endTime) }}
        </template>
        <template #actions="{ row }">
          <t-space>
            <t-button size="small" variant="text" @click="viewDetail(row)">
              查看详情
            </t-button>
            <t-button size="small" variant="text" @click="rerun(row)" :disabled="!row.inputData">
              重新执行
            </t-button>
            <t-dropdown :options="getMoreActions(row)" @click="onMoreAction">
              <t-button size="small" variant="text">
                更多
                <template #suffix>
                  <chevron-down-icon />
                </template>
              </t-button>
            </t-dropdown>
          </t-space>
        </template>
      </t-table>
    </div>

    <!-- 详情对话框 -->
    <t-dialog
      v-model:visible="showDetailDialog"
      header="执行日志详情"
      width="900px"
      height="700px"
      :footer="false"
    >
      <div v-if="selectedLog" class="log-detail">
        <!-- 基本信息 -->
        <t-descriptions :column="3" bordered>
          <t-descriptions-item label="执行ID">{{ selectedLog.executionId }}</t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag :theme="getStatusTheme(selectedLog.status)">
              {{ getStatusText(selectedLog.status) }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="执行时间">{{ selectedLog.executionTimeMs }}ms</t-descriptions-item>
          <t-descriptions-item label="开始时间">{{ formatDateTime(selectedLog.startTime) }}</t-descriptions-item>
          <t-descriptions-item label="结束时间">{{ formatDateTime(selectedLog.endTime) }}</t-descriptions-item>
          <t-descriptions-item label="执行者">{{ selectedLog.creator }}</t-descriptions-item>
        </t-descriptions>

        <!-- 详细数据 -->
        <t-tabs v-model="detailTab" size="medium" style="margin-top: 24px">
          <t-tab-panel value="input" label="输入数据">
            <code-preview
              :code="selectedLog.inputData || '无输入数据'"
              language="json"
              :show-copy="true"
              :max-height="300"
              :is-dark="false"
            />
          </t-tab-panel>
          
          <t-tab-panel value="output" label="输出数据">
            <code-preview
              :code="selectedLog.outputData || '无输出数据'"
              language="json"
              :show-copy="true"
              :max-height="300"
              :is-dark="false"
            />
          </t-tab-panel>
          
          <t-tab-panel v-if="selectedLog.errorMessage" value="error" label="错误信息">
            <div class="error-detail">
              <t-alert theme="error" :message="selectedLog.errorMessage" />
              <div v-if="selectedLog.stackTrace" class="stack-trace">
                <h5>堆栈跟踪</h5>
                <pre>{{ selectedLog.stackTrace }}</pre>
              </div>
            </div>
          </t-tab-panel>
        </t-tabs>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryLogs',
};
</script>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { SearchIcon, RefreshIcon, DownloadIcon, ChevronDownIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import CodePreview from '@/components/code-preview/index.vue';

interface ExecutionLog {
  id: string;
  executionId: string;
  status: string;
  executionTimeMs: number;
  startTime: string;
  endTime?: string;
  inputData?: string;
  outputData?: string;
  errorMessage?: string;
  stackTrace?: string;
  creator: string;
  flowProcId?: string;
  flowStepId?: string;
}

const props = defineProps<{
  actionLibraryId?: string;
}>();

const logs = ref<ExecutionLog[]>([]);
const loading = ref(false);
const showDetailDialog = ref(false);
const selectedLog = ref<ExecutionLog | null>(null);
const detailTab = ref('input');

// 筛选表单
const filterForm = ref({
  status: '',
  dateRange: [],
});

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100],
});

// 状态选项
const statusOptions = [
  { label: '成功', value: 'success' },
  { label: '失败', value: 'error' },
  { label: '超时', value: 'timeout' },
];

// 表格列定义
const columns = [
  { colKey: 'executionId', title: '执行ID', width: 120, ellipsis: true },
  { colKey: 'status', title: '状态', width: 80 },
  { colKey: 'executionTime', title: '执行时间', width: 100 },
  { colKey: 'startTime', title: '开始时间', width: 160 },
  { colKey: 'endTime', title: '结束时间', width: 160 },
  { colKey: 'creator', title: '执行者', width: 100 },
  { colKey: 'actions', title: '操作', width: 180, fixed: 'right' },
];

// 统计信息
const stats = computed(() => {
  const totalSuccess = logs.value.filter(log => log.status === 'success').length;
  const totalError = logs.value.filter(log => log.status === 'error').length;
  const total = logs.value.length;
  
  const avgExecutionTime = total > 0 
    ? Math.round(logs.value.reduce((sum, log) => sum + log.executionTimeMs, 0) / total)
    : 0;
    
  const successRate = total > 0 ? Math.round((totalSuccess / total) * 100) : 0;
  
  return {
    totalSuccess,
    totalError,
    avgExecutionTime,
    successRate,
  };
});

// 监听动作库ID变化
watch(
  () => props.actionLibraryId,
  (newId) => {
    if (newId) {
      resetFilter();
      loadLogs();
    }
  },
  { immediate: true }
);

// 加载日志
const loadLogs = async () => {
  if (!props.actionLibraryId) return;
  
  loading.value = true;
  try {
    const params = {
      id: props.actionLibraryId,
      status: filterForm.value.status || undefined,
      startDate: filterForm.value.dateRange[0] || undefined,
      endDate: filterForm.value.dateRange[1] || undefined,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    };
    
    const data = await api.run(Services.actionLibraryGetExecutionLogs, params);
    logs.value = data.data || [];
    pagination.value.total = data.total || 0;
  } catch (error) {
    MessagePlugin.error(`加载执行日志失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 重置筛选条件
const resetFilter = () => {
  filterForm.value = {
    status: '',
    dateRange: [],
  };
  pagination.value.current = 1;
};

// 分页变化
const onPageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  loadLogs();
};

const onPageSizeChange = (pageInfo: any) => {
  pagination.value.pageSize = pageInfo.pageSize;
  pagination.value.current = 1;
  loadLogs();
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  switch (status) {
    case 'success': return 'success';
    case 'error': return 'danger';
    case 'timeout': return 'warning';
    default: return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功';
    case 'error': return '失败';
    case 'timeout': return '超时';
    default: return status;
  }
};

// 获取执行时间样式
const getExecutionTimeClass = (time: number) => {
  if (time > 5000) return 'execution-time-slow';
  if (time > 1000) return 'execution-time-medium';
  return 'execution-time-fast';
};

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

// 查看详情
const viewDetail = (log: ExecutionLog) => {
  selectedLog.value = log;
  detailTab.value = 'input';
  showDetailDialog.value = true;
};

// 重新执行
const rerun = (log: ExecutionLog) => {
  if (!log.inputData) {
    MessagePlugin.warning('该执行记录没有输入数据');
    return;
  }
  
  // 触发重新执行事件
  MessagePlugin.info('重新执行功能需要在测试页面中实现');
};

// 获取更多操作选项
const getMoreActions = (log: ExecutionLog) => {
  return [
    {
      content: '复制执行ID',
      value: 'copy-id',
      data: log,
    },
    {
      content: '复制输入数据',
      value: 'copy-input',
      data: log,
      disabled: !log.inputData,
    },
    {
      content: '复制输出数据',
      value: 'copy-output',
      data: log,
      disabled: !log.outputData,
    },
  ];
};

// 更多操作处理
const onMoreAction = (data: any) => {
  const { value, data: log } = data;
  
  switch (value) {
    case 'copy-id':
      navigator.clipboard.writeText(log.executionId);
      MessagePlugin.success('执行ID已复制');
      break;
    case 'copy-input':
      if (log.inputData) {
        navigator.clipboard.writeText(log.inputData);
        MessagePlugin.success('输入数据已复制');
      }
      break;
    case 'copy-output':
      if (log.outputData) {
        navigator.clipboard.writeText(log.outputData);
        MessagePlugin.success('输出数据已复制');
      }
      break;
  }
};

// 导出日志
const exportLogs = () => {
  MessagePlugin.info('导出功能待实现');
};

onMounted(() => {
  if (props.actionLibraryId) {
    loadLogs();
  }
});
</script>

<style lang="less" scoped>
.action-library-logs {
  .filter-section {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
  }

  .stats-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      .stat-item {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;

          &.success {
            color: var(--td-success-color);
          }

          &.error {
            color: var(--td-error-color);
          }
        }

        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  .logs-section {
    .execution-time-fast {
      color: var(--td-success-color);
    }

    .execution-time-medium {
      color: var(--td-warning-color);
    }

    .execution-time-slow {
      color: var(--td-error-color);
    }
  }

  .log-detail {
    .error-detail {
      .stack-trace {
        margin-top: 16px;

        h5 {
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
        }

        pre {
          background: var(--td-bg-color-code);
          padding: 12px;
          border-radius: 4px;
          font-size: 12px;
          line-height: 1.4;
          overflow: auto;
          max-height: 300px;
        }
      }
    }
  }
}
</style>
