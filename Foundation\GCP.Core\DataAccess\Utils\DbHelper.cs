﻿using DuckDB.NET.Data;
using GCP.Common;
using System.Collections;
using System.Data;
using System.Data.Common;
using System.Reflection;
using System.Transactions;

namespace GCP.DataAccess
{
    /// <summary>
    /// DB操作扩展帮助类
    /// </summary>
    public static class DbHelper
    {

        public static string GetTableName<T>()
        {
            var type = typeof(T);
            return GetTableName(type);
        }

        public static string GetTableName(Type type)
        {
            var tableAttr = type.GetCustomAttribute<LinqToDB.Mapping.TableAttribute>();
            return tableAttr == null ? type.Name : tableAttr.Name;
        }
        /// <summary>
        /// 分布式事务
        /// </summary>
        /// <param name="func"></param>
        public static async Task UseTransactionScope(Func<Task> func, TransactionScopeOption scopeOption = TransactionScopeOption.Required)
        {
            TransactionScope transactionScope = null;
            try
            {
                transactionScope = new TransactionScope(scopeOption, TransactionScopeAsyncFlowOption.Enabled);

                await func().ConfigureAwait(false);

                transactionScope.Complete();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                transactionScope?.Dispose();
            }
        }

        /// <summary>
        /// 验证是否为空
        /// </summary>
        public static bool IsNull(object value)
        {
            return value == null || (value is string && value.ToString() == "") || (value is ICollection && (value as ICollection).Count == 0);
        }
        // 匹配模式
        //internal static bool IsNull(object value)
        //{
        //    return value switch
        //    {
        //        null or "" or ICollection { Count: 0 } => true,
        //        _ => false
        //    };
        //}

        /// <summary>
        /// Object中获取集合
        /// </summary>
        internal static IEnumerable GetMultiExec(object param)
        {
            return (param is IEnumerable
                && !(param is string || param is IEnumerable<KeyValuePair<string, object>>
                    )) ? (IEnumerable)param : null;
        }

        /// <summary>
        /// 获取集合子元素的类型
        /// </summary>
        internal static bool IsList(ref Type type)
        {
            bool isList = false;
            if (type.IsArray)
            {
                isList = true;
                type = type.GetElementType();
            }
            else if (type.IsGenericType)
            {
                isList = true;
                var types = type.GetGenericArguments();
                if (types.Length == 1)
                {
                    type = types[0];
                }
            }
            return isList;
        }


        /// <summary>
        /// 将Db数据设置到对应实体
        /// </summary>
        internal static void SetDbValue(this EntityField field, object instance, object value)
        {
            if (value == DBNull.Value || value == null) return;
            field.setValue(instance, DbConverter.ChangeType(value, field.UnderlyingType));
        }

        /// <summary>
        /// 将Db数据设置到对应实体栏位
        /// </summary>
        internal static void SetDbValue(this EntityFields fields, string key, object instance, object value)
        {
            EntityField field;
            if (fields.TryGetValue(key, out field))
            {
                field.SetDbValue(instance, value);
            }
        }

        /// <summary>
        /// DataReader取一条数据
        /// </summary>
        public static T Get<T>(this IDataReader dataReader, object defaultEntity = null)
        {
            return GetList<T>(dataReader, null, null, 1, defaultEntity != null ? () => (T)defaultEntity : null).FirstOrDefault();
            //T tEntity;
            //try
            //{
            //    Type type = typeof(T);
            //    if (type.IsPrimitiveType())
            //    {
            //        tEntity = default(T);
            //        if (dataReader.Read())
            //        {
            //            var obj = dataReader[0];
            //            if (obj != DBNull.Value) tEntity = (T)DbConverter.ChangeType(obj, type);

            //        }
            //    }
            //    else if (type.BaseType == null)
            //    {
            //        DynamicDictionary fields = new DynamicDictionary();

            //        if (dataReader.Read())
            //        {
            //            for (int i = 0; i < dataReader.FieldCount; i++)
            //                fields.Add(dataReader.GetName(i), dataReader.GetValue(i));
            //        }
            //        tEntity = fields.Parse<T>();
            //    }
            //    else
            //    {
            //        var fields = EntityCache.GetFields(type);
            //        tEntity = entity == null ? fields.CreateInstance<T>() : (T)entity;
            //        if (dataReader.Read())
            //        {
            //            for (int i = 0; i < dataReader.FieldCount; i++)
            //                fields.SetDbValue(dataReader.GetName(i).ToLower(), tEntity, dataReader.GetValue(i));
            //        }
            //    }
            //}
            //finally
            //{
            //    dataReader.Dispose();
            //}

            //return tEntity;
        }
        public static async Task<T> GetAsync<T>(this IDataReader dataReader, object defaultEntity = null)
        {
            return (await GetListAsync<T>(dataReader, null, null, 1, defaultEntity != null ? () => (T)defaultEntity : null).ConfigureAwait(false)).FirstOrDefault();
        }

        public static Dictionary<string, object> GetDictionary<T>(this IDataReader dataReader, Func<T, string> keySelector, Func<T, object> elementSelector, Func<IDataReader, int, string> columnNameHandle = null, int total = -1)
        {
            var deserializerFun = GetDeserializerFunc<T>(dataReader, columnNameHandle);
            var list = GetIEnumerableSync((DbDataReader)dataReader, deserializerFun);
            if (total > 0) list = list.Take(total);
            var result = list.ToDictionary(keySelector, elementSelector);
            return result;
        }

        /// <summary>
        /// DataReader转换成强类型List
        /// </summary>
        public static List<T> GetList<T>(this IDataReader dataReader, Func<T, int, T> rowDataHandle = null, Func<IDataReader, int, string> columnNameHandle = null, int total = -1, Func<object> createRowHandle = null, bool saveList = true)
        {
            var fun = GetDeserializerFunc<T>(dataReader, columnNameHandle, createRowHandle);
            var list = GetIEnumerableSync(dataReader, fun);

            if (total > 0) list = list.Take(total);
            if (rowDataHandle != null)
            {
                list = list.Select(rowDataHandle);
            }
            var result = list.ToList();
            return saveList ? result : new List<T>();
        }

        public static IEnumerable<T> GetEnumerable<T>(this IDataReader dataReader, Func<IDataReader, int, string> columnNameHandle = null, Func<object> createRowHandle = null)
        {
            var fun = GetDeserializerFunc<T>(dataReader, columnNameHandle, createRowHandle);
            return GetIEnumerableSync(dataReader, fun);
        }

        public static IAsyncEnumerable<T> GetEnumerableAsync<T>(this IDataReader dataReader, Func<IDataReader, int, string> columnNameHandle = null, Func<object> createRowHandle = null)
        {
            var fun = GetDeserializerFunc<T>(dataReader, columnNameHandle, createRowHandle);
            return GetIEnumerableAsync((DbDataReader)dataReader, fun);
        }

        private static IEnumerable<T> GetIEnumerableSync<T>(IDataReader reader, Func<IDataReader, T> func)
        {
            using (reader)
            {
                while (reader.Read())
                {
                    yield return func(reader);
                }
            }
        }

        private static async IAsyncEnumerable<T> GetIEnumerableAsync<T>(DbDataReader reader, Func<IDataReader, T> func)
        {
            await using (reader)
            {
                while (await reader.ReadAsync().ConfigureAwait(false))
                {
                    yield return func(reader);
                }
            }
        }

        private static Func<IDataReader, T> GetDeserializerFunc<T>(IDataReader dataReader, Func<IDataReader, int, string> columnNameHandle = null, Func<object> createRowHandle = null)
        {
            Type type = typeof(T);
            int length = dataReader.FieldCount;
            if (length == 0) return null;

            bool isBaseType = type.IsPrimitiveType();

            if (columnNameHandle == null)
                columnNameHandle = (r, i) => r.GetName(i);

            if (isBaseType)
            {
                if (type.IsGenericType)
                {
                    //type.GetGenericTypeDefinition() == typeof(KeyValuePair<,>)
                    throw new NoNullAllowedException("不支持该类型！");
                }
                else
                {
                    return (dr) =>
                    {
                        T tEntity = default(T);

                        var obj = dr[0];
                        if (obj != DBNull.Value) tEntity = (T)DbConverter.ChangeType(obj, type);
                        return tEntity;
                    };
                }
            }
            else if (type == typeof(IDuckDBAppenderRow))
            {
                if (createRowHandle == null)
                {
                    throw new NoNullAllowedException("DuckDB createRowHandle不能为空！");
                }

                return (dr) =>
                {
                    var row = (IDuckDBAppenderRow)createRowHandle();

                    for (int i = 0; i < length; i++)
                        row.AppendObject(dr.GetValue(i));

                    row.EndRow();
                    return (T)row;
                };
            }
            else if (type.BaseType == null)
            {
                List<string> fieldNames = new List<string>(length);
                for (int i = 0; i < length; i++)
                    fieldNames.Add(columnNameHandle(dataReader, i));

                return (dr) =>
                {
                    if (length == 1)
                    {
                        return ConvertHelper.ChangeType(dr.GetValue(0), dr.GetFieldType(0)).Parse<T>();
                    }
                    DynamicDictionary fields = new DynamicDictionary(length);
                    for (int i = 0; i < length; i++)
                        fields.Add(fieldNames[i], ConvertHelper.ChangeTypeIfLong(dr.GetValue(i), dr.GetFieldType(i)));
                    return fields.Parse<T>();
                };
            }
            else if (type == typeof(DataRow))
            {
                if (createRowHandle == null)
                {
                    DataTable dt = new DataTable();
                    for (int i = 0; i < length; i++)
                        dt.Columns.Add(columnNameHandle(dataReader, i));
                    createRowHandle = () => dt.NewRow();
                }

                return (dr) =>
                {
                    DataRow row = (DataRow)createRowHandle();
                    for (int i = 0; i < length; i++)
                        row[i] = dr.GetValue(i);
                    return (T)(object)row;
                };
            }
            else
            {
                var fields = EntityCache.GetFields(type);
                EntityField[] rowFields = new EntityField[length];
                List<int> fieldIndexs = new List<int>(length);
                for (int i = 0; i < length; i++)
                {
                    EntityField field;
                    if (fields.TryGetValue(columnNameHandle(dataReader, i).ToLower(), out field))
                    {
                        rowFields[i] = field;
                        fieldIndexs.Add(i);
                    }
                }
                if (createRowHandle == null)
                    createRowHandle = () => fields.CreateInstance();

                return (dr) =>
                {
                    T tEntity = (T)createRowHandle();
                    foreach (int i in fieldIndexs)
                        rowFields[i].SetDbValue(tEntity, dr.GetValue(i));
                    return tEntity;
                };
            }
        }

        public static async Task<Dictionary<string, object>> GetDictionaryAsync<T>(this IDataReader dataReader, Func<T, string> keySelector, Func<T, object> elementSelector, Func<IDataReader, int, string> columnNameHandle = null, int total = -1)
        {
            var deserializerFun = GetDeserializerFunc<T>(dataReader, columnNameHandle);
            var list = GetIEnumerableAsync((DbDataReader)dataReader, deserializerFun);
            if (total > 0) list = list.Take(total);
            var result = await list.ToDictionaryAsync(keySelector, elementSelector);
            return result;
        }

        public static async Task<List<T>> GetListAsync<T>(this IDataReader dataReader, Func<T, int, T> rowDataHandle = null, Func<IDataReader, int, string> columnNameHandle = null, int total = -1, Func<object> createRowHandle = null, bool saveList = true)
        {
            var deserializerFun = GetDeserializerFunc<T>(dataReader, columnNameHandle, createRowHandle);
            //List<T> list = new List<T>();
            //int count = 0;
            //using (dataReader)
            //{
            //    var reader = (DbDataReader)dataReader;
            //    while (await reader.ReadAsync())
            //    {
            //        var item = deserializerFun(dataReader);
            //        if (saveList) list.Add(item);
            //        count++;
            //        if (count == total) break;
            //    }
            //}
            //return list;

            var list = GetIEnumerableAsync((DbDataReader)dataReader, deserializerFun);

            if (total > 0) list = list.Take(total);
            if (rowDataHandle != null)
            {
                list = list.Select(rowDataHandle);
            }

            var result = await list.ToListAsync();
            return saveList ? result : new List<T>();
        }

        public static DataTable GetDataTable(this IDataReader dataReader, Func<DataRow, int, DataRow> rowDataHandle = null, Func<IDataReader, int, string> columnNameHandle = null, int total = -1, bool saveRow = true)
        {
            DataTable dt = new DataTable();
            int length = dataReader.FieldCount;
            if (length == 0) return dt;

            if (columnNameHandle == null)
                columnNameHandle = (r, i) => r.GetName(i);

            for (int i = 0; i < length; i++)
                dt.Columns.Add(columnNameHandle(dataReader, i));

            GetList<DataRow>(dataReader, (row, count) =>
            {
                if (rowDataHandle != null) row = rowDataHandle(row, count);
                if (saveRow && row != null) dt.Rows.Add(row);
                return row;
            }, columnNameHandle, total, () => dt.NewRow(), false);

            return dt;
        }

        /// <summary>
        /// List转换成DataTable
        /// </summary>
        public static DataTable GetDataTable<T>(this List<T> list)
        {
            DataTable dt = new DataTable();
            if (list == null || list.Count == 0) return dt;

            Type type = typeof(T);

            var fields = EntityCache.GetFields(type);
            foreach (var item in fields)
            {
                dt.Columns.Add(item.Key);
            }

            foreach (var item in list)
            {
                var row = dt.NewRow();
                foreach (var field in fields)
                {
                    row[field.Key] = fields.GetValue(field.Key, item);
                }
                dt.Rows.Add(row);
            }
            return dt;
        }

        /// <summary>
        /// DataTable转换成强类型List
        /// </summary>
        public static List<T> GetList<T>(this DataTable dt, Func<T, T> action = null)
        {
            List<T> list = new List<T>();
            if (dt == null) return list;

            Type type = typeof(T);
            bool notHasFunc = action == null;
            int rowsCount = dt.Rows.Count;
            int columnsCount = dt.Columns.Count;

            if (type.IsPrimitiveType())
            {
                for (int i = 0; i < rowsCount; i++)
                {
                    T tEntity = dt.Rows[i][0].Parse<T>();
                    list.Add(notHasFunc ? tEntity : action(tEntity));
                }
            }
            else
            {
                var fields = EntityCache.GetFields(type);

                EntityField[] rowFields = new EntityField[columnsCount];
                List<int> fieldIndexs = new List<int>();

                for (int i = 0; i < columnsCount; i++)
                {
                    EntityField field;
                    var column = dt.Columns[i];
                    if (fields.TryGetValue(column.ColumnName.ToLower(), out field))
                    {
                        rowFields[i] = field;
                        fieldIndexs.Add(i);
                    }
                }

                for (int i = 0; i < rowsCount; i++)
                {
                    T tEntity = fields.CreateInstance<T>();
                    var items = dt.Rows[i].ItemArray;
                    foreach (int j in fieldIndexs)
                        rowFields[j].SetDbValue(tEntity, items[j]);
                    list.Add(notHasFunc ? tEntity : action(tEntity));
                }
            }
            return list;
        }
    }
}
