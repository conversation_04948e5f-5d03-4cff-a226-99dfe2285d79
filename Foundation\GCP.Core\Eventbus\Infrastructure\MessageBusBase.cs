using System.Collections.Concurrent;
using GCP.Common;
using Serilog;

namespace GCP.Eventbus.Infrastructure
{
    abstract class MessageBusBase : IMessageBus
    {
        protected readonly MessageBusOptions Options;
        protected readonly ConcurrentDictionary<string, object> Subscriptions;
        protected readonly SemaphoreSlim ConnectionLock;
        protected bool IsDisposed;

        public string Name => Options.Name;
        public MessageBusType Type => Options.Type;
        public abstract bool IsConnected { get; }

        protected MessageBusBase(MessageBusOptions options)
        {
            Options = options;
            Subscriptions = new ConcurrentDictionary<string, object>();
            ConnectionLock = new SemaphoreSlim(1, 1);
        }

        public abstract Task ConnectAsync(CancellationToken cancellationToken = default);
        public abstract Task DisconnectAsync(CancellationToken cancellationToken = default);

        public abstract Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default);
        public abstract Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default);

        public abstract Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default);
        public virtual Task SubscribeBatchAsync(string topic, Func<List<MessageEnvelope>, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            // 基类提供基本实现，通过SubscribeAsync来间接实现批处理
            // 创建一个缓冲区来收集消息
            var buffer = new List<MessageEnvelope>();
            var bufferSemaphore = new SemaphoreSlim(1, 1);
            var batchSize = int.Parse(options.Settings.GetValueOrDefault("BatchSize", "10"));
            var batchTimeoutMs = int.Parse(options.Settings.GetValueOrDefault("BatchTimeoutMs", "1000"));
            
            // 设置定时器定期处理批次
            var timer = new Timer(async _ =>
            {
                List<MessageEnvelope> messagesToProcess = null;
                
                try
                {
                    await bufferSemaphore.WaitAsync(cancellationToken);
                    if (buffer.Count > 0)
                    {
                        messagesToProcess = [.. buffer];
                        buffer.Clear();
                    }
                }
                finally
                {
                    bufferSemaphore.Release();
                }
                
                if (messagesToProcess != null && messagesToProcess.Count > 0)
                {
                    try
                    {
                        await handler(messagesToProcess, cancellationToken).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error processing batch of {Count} messages from {Topic}", messagesToProcess.Count, topic);
                    }
                }
            }, null, batchTimeoutMs, batchTimeoutMs);
            
            // 添加取消令牌处理，在取消时停止定时器和释放资源
            cancellationToken.Register(() => 
            {
                timer.Change(Timeout.Infinite, Timeout.Infinite);
                timer.Dispose();
                bufferSemaphore.Dispose();
            });
            
            // 使用单个消息订阅，将消息添加到缓冲区
            var subscribeTask = SubscribeAsync(topic, async (message, ct) =>
            {
                if (ct.IsCancellationRequested) return;
                
                List<MessageEnvelope> messagesToProcess = null;
                
                try
                {
                    await bufferSemaphore.WaitAsync(ct);
                    buffer.Add(message);
                    
                    // 如果达到批处理大小，立即处理
                    if (buffer.Count >= batchSize)
                    {
                        messagesToProcess = [.. buffer];
                        buffer.Clear();
                    }
                }
                finally
                {
                    bufferSemaphore.Release();
                }
                
                // 如果有足够的消息形成一个批次，立即处理
                if (messagesToProcess != null)
                {
                    try
                    {
                        await handler(messagesToProcess, ct).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error processing batch of {Count} messages from {Topic}", messagesToProcess.Count, topic);
                    }
                }
                
            }, options, cancellationToken);
            
            return subscribeTask;
        }
        public abstract Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default);

        protected virtual async Task EnsureConnectedAsync(CancellationToken cancellationToken = default)
        {
            if (IsConnected) return;

            await ConnectionLock.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected)
                {
                    var maxRetries = int.Parse(Options.Settings.GetValueOrDefault("MaxRetries", "3"));
                    var retryDelay = TimeSpan.FromSeconds(double.Parse(Options.Settings.GetValueOrDefault("RetryDelaySeconds", "5")));

                    for (int i = 0; i < maxRetries; i++)
                    {
                        try
                        {
                            await ConnectAsync(cancellationToken);
                            if (IsConnected) break;
                        }
                        catch (Exception ex)
                        {
                            Log.Warning(ex, "Connection attempt {Attempt} of {MaxRetries} failed for {Name}", i + 1, maxRetries, Name);
                            if (i == maxRetries - 1) throw;
                            await Task.Delay(retryDelay, cancellationToken);
                        }
                    }
                }
            }
            finally
            {
                ConnectionLock.Release();
            }
        }

        protected virtual async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            try
            {
                await DisconnectAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error disposing message bus {Name}", Name);
            }

            ConnectionLock.Dispose();
            IsDisposed = true;
        }

        public async ValueTask DisposeAsync()
        {
            await DisposeAsyncCore();
            GC.SuppressFinalize(this);
        }

        protected void ThrowIfDisposed()
        {
            if (IsDisposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }
    }
}