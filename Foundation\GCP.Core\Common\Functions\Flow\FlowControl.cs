﻿using System.Runtime.Serialization;

namespace GCP.Common
{
    public sealed class FlowBranch
    {
        /// <summary>
        /// 下一个子步骤
        /// </summary>
        public string NextId { get; set; }

        /// <summary>
        /// 判断表达式
        /// </summary>
        public string Condition { get; set; }
    }

    [DataContract]
    [Serializable]
    public sealed class FlowControl
    {
        /// <summary>
        /// 分支控制, js代码 判断（设置决策key）
        /// </summary>
        [DataMember(Name = "branch")]
        public Dictionary<string, FlowBranch> Branch { get; set; }

        /// <summary>
        /// 循环
        /// </summary>
        [DataMember(Name = "forEach")]
        public FlowForEach ForEach { get; set; }

        /// <summary>
        /// 条件循环
        /// </summary>
        [DataMember(Name = "while")]
        public FlowWhile While { get; set; }

        /// <summary>
        /// 事务
        /// </summary>
        [DataMember(Name = "transaction")]
        public FlowTransaction Transaction { get; set; }
        
        /// <summary>
        /// 异常捕获
        /// </summary>
        [DataMember(Name = "tryCatch")]
        public FlowTryCatch TryCatch { get; set; }
    }

    [DataContract]
    [Serializable]
    public sealed class FlowTryCatch
    {
        /// <summary>
        /// 下一个子步骤
        /// </summary>
        [DataMember(Name = "nextId")]
        public string NextId { get; set; }

        /// <summary>
        /// 允许重试
        /// </summary>
        [DataMember(Name = "allowRetry")]
        public bool AllowRetry { get; set; }
        /// <summary>
        /// 重试次数
        /// </summary>
        [DataMember(Name = "retryCount")]
        public short? RetryCount { get; set; }
        /// <summary>
        /// 重试间时间（秒）
        /// </summary>
        [DataMember(Name = "retryDelaysInSeconds")]
        public short? RetryDelaysInSeconds { get; set; }
    }

    [DataContract]
    [Serializable]
    public sealed class FlowForEach
    {
        /// <summary>
        /// 是否异步
        /// </summary>
        [DataMember(Name = "async")]
        public bool Async { get; set; } = true;

        /// <summary>
        /// 多结果指定字段使用表达式, 默认使用函数结果
        /// </summary>
        [DataMember(Name = "list")]
        public string List { get; set; }

        /// <summary>
        /// 输出别名（全流程唯一）, 范围作用域, 超出范围不可用 
        /// </summary>
        [DataMember(Name = "item")]
        public List<FlowData> Item { get; set; }

        /// <summary>
        /// 下一个子步骤
        /// </summary>
        [DataMember(Name = "nextId")]
        public string NextId { get; set; }
    }

    [DataContract]
    [Serializable]
    public sealed class FlowTransaction
    {
        /// <summary>
        /// 回滚类型 DB：按数据库 DCS：按分布式补偿机制
        /// </summary>
        [DataMember(Name = "rollbackType")]
        public string RollbackType { get; set; }

        /// <summary>
        /// 补偿到上一个步骤, true需开启persistence持久化和rollbackType分布式（默认false依次补偿到初始步骤）
        /// </summary>
        [DataMember(Name = "undoToPerStep")]
        public bool UndoToPerStep { get; set; } = false;

        /// <summary>
        /// 下一个子步骤
        /// </summary>
        [DataMember(Name = "nextId")]
        public string NextId { get; set; }
    }

    [DataContract]
    [Serializable]
    public sealed class FlowWhile
    {
        /// <summary>
        /// 循环类型
        /// 次数循环：count
        /// 条件循环：while
        /// 执行一次后条件循环：doWhile
        /// 无限循环（直至中断）：infinite
        /// </summary>
        public string LoopType { get; set; }

        /// <summary>
        /// 循环次数
        /// </summary>
        public DataValue LoopCount { get; set; }

        /// <summary>
        /// 判断表达式
        /// </summary>
        [DataMember(Name = "condition")]
        public string Condition { get; set; }

        /// <summary>
        /// 下一个子步骤
        /// </summary>
        [DataMember(Name = "nextId")]
        public string NextId { get; set; }
    }
}
