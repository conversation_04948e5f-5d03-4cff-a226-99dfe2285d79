<template>
  <div class="online-function-manager">
    <!-- 头部工具栏 -->
    <div class="manager-header">
      <t-space>
        <t-button theme="primary" @click="showCreateDialog = true">
          <add-icon />
          新建函数
        </t-button>
        <t-button theme="default" @click="refreshFunctions">
          <refresh-icon />
          刷新
        </t-button>
        <t-input
          v-model="searchText"
          placeholder="搜索函数"
          clearable
          style="width: 300px"
        >
          <template #prefixIcon>
            <search-icon />
          </template>
        </t-input>
      </t-space>
    </div>

    <!-- 函数列表 -->
    <div class="function-list">
      <t-table
        :data="filteredFunctions"
        :columns="columns"
        row-key="id"
        :loading="loading"
        @row-click="onRowClick"
      >
        <template #functionType="{ row }">
          <t-tag :theme="getFunctionTypeTheme(row.functionType)">
            {{ getFunctionTypeLabel(row.functionType) }}
          </t-tag>
        </template>
        <template #status="{ row }">
          <t-tag :theme="row.status === 'active' ? 'success' : 'default'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </t-tag>
        </template>
        <template #actions="{ row }">
          <t-space>
            <t-button size="small" variant="text" @click.stop="editFunction(row)">
              <edit-icon />
              编辑
            </t-button>
            <t-button size="small" variant="text" @click.stop="testFunction(row)">
              <play-icon />
              测试
            </t-button>
            <t-button size="small" variant="text" theme="danger" @click.stop="deleteFunction(row)">
              <delete-icon />
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </div>

    <!-- 创建/编辑函数对话框 -->
    <t-dialog
      v-model:visible="showCreateDialog"
      :header="editingFunction ? '编辑函数' : '新建函数'"
      width="800px"
      @confirm="saveFunction"
      @cancel="cancelEdit"
    >
      <t-form
        ref="formRef"
        :data="functionForm"
        :rules="formRules"
        layout="vertical"
        @submit="saveFunction"
      >
        <t-row :gutter="16">
          <t-col :span="6">
            <t-form-item label="函数名称" name="name">
              <t-input v-model="functionForm.name" placeholder="请输入函数名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="函数类型" name="functionType">
              <t-select v-model="functionForm.functionType" :options="functionTypeOptions" />
            </t-form-item>
          </t-col>
        </t-row>

        <t-form-item label="函数描述" name="description">
          <t-textarea v-model="functionForm.description" placeholder="请输入函数描述" />
        </t-form-item>

        <t-form-item label="函数代码" name="code">
          <editor
            v-model:value="functionForm.code"
            :language="getEditorLanguage(functionForm.functionType)"
            style="height: 300px"
          />
        </t-form-item>

        <t-form-item label="输入参数">
          <div class="parameter-list">
            <div
              v-for="(param, index) in functionForm.inputParameters"
              :key="index"
              class="parameter-item"
            >
              <t-row :gutter="8">
                <t-col :span="4">
                  <t-input v-model="param.name" placeholder="参数名" />
                </t-col>
                <t-col :span="3">
                  <t-select v-model="param.type" :options="parameterTypeOptions" />
                </t-col>
                <t-col :span="2">
                  <t-checkbox v-model="param.required">必需</t-checkbox>
                </t-col>
                <t-col :span="4">
                  <t-input v-model="param.defaultValue" placeholder="默认值" />
                </t-col>
                <t-col :span="4">
                  <t-input v-model="param.description" placeholder="参数描述" />
                </t-col>
                <t-col :span="1">
                  <t-button size="small" variant="text" theme="danger" @click="removeParameter(index)">
                    <delete-icon />
                  </t-button>
                </t-col>
              </t-row>
            </div>
            <t-button variant="dashed" @click="addParameter">
              <add-icon />
              添加参数
            </t-button>
          </div>
        </t-form-item>

        <t-form-item label="输出格式">
          <t-row :gutter="16">
            <t-col :span="6">
              <t-select
                v-model="functionForm.outputFormat.type"
                :options="outputTypeOptions"
                placeholder="选择输出类型"
              />
            </t-col>
            <t-col :span="10">
              <t-input
                v-model="functionForm.outputFormat.description"
                placeholder="输出描述"
              />
            </t-col>
          </t-row>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 函数测试对话框 -->
    <t-dialog
      v-model:visible="showTestDialog"
      header="测试函数"
      width="600px"
      :footer="false"
    >
      <div v-if="testingFunction">
        <h4>{{ testingFunction.name }}</h4>
        <p>{{ testingFunction.description }}</p>
        
        <t-form layout="vertical">
          <t-form-item
            v-for="param in testingFunction.inputParameters"
            :key="param.name"
            :label="param.name + (param.required ? ' *' : '')"
          >
            <t-input
              v-model="testParameters[param.name]"
              :placeholder="param.description || '请输入' + param.name"
            />
          </t-form-item>
        </t-form>

        <div class="test-actions">
          <t-button theme="primary" @click="executeTest" :loading="testLoading">
            执行测试
          </t-button>
        </div>

        <div v-if="testResult" class="test-result">
          <h5>测试结果：</h5>
          <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'OnlineFunctionManager',
};
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  AddIcon,
  RefreshIcon,
  SearchIcon,
  EditIcon,
  PlayIcon,
  DeleteIcon,
} from 'tdesign-icons-vue-next';
import Editor from '@/components/editor/index.vue';

// 类型定义
interface OnlineFunction {
  id: string;
  name: string;
  functionType: 'javascript' | 'csharp';
  description: string;
  code: string;
  inputParameters: FunctionParameter[];
  outputFormat: {
    type: string;
    description: string;
  };
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

interface FunctionParameter {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: string;
  description?: string;
}

// 响应式数据
const loading = ref(false);
const searchText = ref('');
const functions = ref<OnlineFunction[]>([]);
const showCreateDialog = ref(false);
const showTestDialog = ref(false);
const editingFunction = ref<OnlineFunction | null>(null);
const testingFunction = ref<OnlineFunction | null>(null);
const testParameters = ref<Record<string, any>>({});
const testResult = ref<any>(null);
const testLoading = ref(false);

// 表单数据
const functionForm = ref<Partial<OnlineFunction>>({
  name: '',
  functionType: 'javascript',
  description: '',
  code: '',
  inputParameters: [],
  outputFormat: {
    type: 'object',
    description: '',
  },
  status: 'active',
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入函数名称' }],
  functionType: [{ required: true, message: '请选择函数类型' }],
  code: [{ required: true, message: '请输入函数代码' }],
};

// 选项数据
const functionTypeOptions = [
  { label: 'JavaScript', value: 'javascript' },
  { label: 'C#', value: 'csharp' },
];

const parameterTypeOptions = [
  { label: 'string', value: 'string' },
  { label: 'number', value: 'number' },
  { label: 'boolean', value: 'boolean' },
  { label: 'object', value: 'object' },
  { label: 'array', value: 'array' },
];

const outputTypeOptions = [
  { label: 'object', value: 'object' },
  { label: 'string', value: 'string' },
  { label: 'number', value: 'number' },
  { label: 'boolean', value: 'boolean' },
  { label: 'array', value: 'array' },
];

// 表格列定义
const columns = [
  { colKey: 'name', title: '函数名称', width: 150 },
  { colKey: 'functionType', title: '类型', width: 100 },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'status', title: '状态', width: 80 },
  { colKey: 'updatedAt', title: '更新时间', width: 150 },
  { colKey: 'actions', title: '操作', width: 200, fixed: 'right' },
];

// 计算属性
const filteredFunctions = computed(() => {
  if (!searchText.value) return functions.value;
  
  const search = searchText.value.toLowerCase();
  return functions.value.filter(func =>
    func.name.toLowerCase().includes(search) ||
    func.description.toLowerCase().includes(search)
  );
});

// 生命周期
onMounted(() => {
  loadFunctions();
});

// 方法
const loadFunctions = async () => {
  loading.value = true;
  try {
    // 这里调用API加载函数列表
    // const response = await api.get('/api/online-functions');
    // functions.value = response.data;
    
    // 模拟数据
    functions.value = [
      {
        id: '1',
        name: 'formatDate',
        functionType: 'javascript',
        description: '格式化日期字符串',
        code: 'function formatDate(date, format) { return new Date(date).toLocaleDateString(); }',
        inputParameters: [
          { name: 'date', type: 'string', required: true, description: '日期字符串' },
          { name: 'format', type: 'string', required: false, defaultValue: 'yyyy-MM-dd', description: '格式字符串' },
        ],
        outputFormat: { type: 'string', description: '格式化后的日期字符串' },
        status: 'active',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ];
  } catch (error) {
    console.error('加载函数列表失败:', error);
  } finally {
    loading.value = false;
  }
};

const refreshFunctions = () => {
  loadFunctions();
};

const onRowClick = (context: any) => {
  const { row } = context;
  editFunction(row);
};

const editFunction = (func: OnlineFunction) => {
  editingFunction.value = func;
  functionForm.value = { ...func };
  showCreateDialog.value = true;
};

const saveFunction = async () => {
  try {
    // 这里调用API保存函数
    // if (editingFunction.value) {
    //   await api.put(`/api/online-functions/${editingFunction.value.id}`, functionForm.value);
    // } else {
    //   await api.post('/api/online-functions', functionForm.value);
    // }
    
    showCreateDialog.value = false;
    await loadFunctions();
  } catch (error) {
    console.error('保存函数失败:', error);
  }
};

const cancelEdit = () => {
  editingFunction.value = null;
  functionForm.value = {
    name: '',
    functionType: 'javascript',
    description: '',
    code: '',
    inputParameters: [],
    outputFormat: { type: 'object', description: '' },
    status: 'active',
  };
};

const deleteFunction = async (func: OnlineFunction) => {
  try {
    // 这里调用API删除函数
    // await api.delete(`/api/online-functions/${func.id}`);
    await loadFunctions();
  } catch (error) {
    console.error('删除函数失败:', error);
  }
};

const testFunction = (func: OnlineFunction) => {
  testingFunction.value = func;
  testParameters.value = {};
  testResult.value = null;
  showTestDialog.value = true;
};

const executeTest = async () => {
  if (!testingFunction.value) return;
  
  testLoading.value = true;
  try {
    // 这里调用API执行函数测试
    // const response = await api.post(`/api/online-functions/${testingFunction.value.id}/test`, testParameters.value);
    // testResult.value = response.data;
    
    // 模拟测试结果
    testResult.value = {
      success: true,
      result: 'Test result here',
      executionTime: 15,
    };
  } catch (error) {
    console.error('测试函数失败:', error);
    testResult.value = {
      success: false,
      error: error.message,
    };
  } finally {
    testLoading.value = false;
  }
};

const addParameter = () => {
  functionForm.value.inputParameters?.push({
    name: '',
    type: 'string',
    required: true,
    defaultValue: '',
    description: '',
  });
};

const removeParameter = (index: number) => {
  functionForm.value.inputParameters?.splice(index, 1);
};

const getFunctionTypeTheme = (type: string) => {
  return type === 'javascript' ? 'warning' : 'primary';
};

const getFunctionTypeLabel = (type: string) => {
  return type === 'javascript' ? 'JS' : 'C#';
};

const getEditorLanguage = (type: string) => {
  return type === 'javascript' ? 'javascript' : 'csharp';
};
</script>

<style scoped>
.online-function-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.manager-header {
  padding: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
  background: var(--td-bg-color-container);
}

.function-list {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.parameter-list {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  padding: 12px;
}

.parameter-item {
  margin-bottom: 8px;
  padding: 8px;
  background: var(--td-bg-color-container);
  border-radius: 4px;
}

.parameter-item:last-child {
  margin-bottom: 0;
}

.test-actions {
  margin: 16px 0;
}

.test-result {
  margin-top: 16px;
  padding: 12px;
  background: var(--td-bg-color-code);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
}

.test-result pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
}
</style>
