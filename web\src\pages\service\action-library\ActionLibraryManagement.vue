<template>
  <div class="action-library-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">动作库管理</h1>
          <p class="page-description">管理和维护可复用的业务逻辑组件</p>
        </div>
        <div class="action-section">
          <t-button theme="primary" size="large" @click="createActionLibrary">
            <template #icon>
              <add-icon />
            </template>
            新建动作库
          </t-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="overview-section" v-if="overviewStats">
      <t-row :gutter="24">
        <t-col :span="3">
          <div class="overview-card">
            <div class="card-icon total">
              <functions1-icon />
            </div>
            <div class="card-content">
              <div class="card-value">{{ overviewStats.total }}</div>
              <div class="card-label">总动作库</div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="overview-card">
            <div class="card-icon active">
              <check-circle-icon />
            </div>
            <div class="card-content">
              <div class="card-value">{{ overviewStats.active }}</div>
              <div class="card-label">已激活</div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="overview-card">
            <div class="card-icon draft">
              <edit-icon />
            </div>
            <div class="card-content">
              <div class="card-value">{{ overviewStats.draft }}</div>
              <div class="card-label">草稿</div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="overview-card">
            <div class="card-icon executions">
              <play-circle-icon />
            </div>
            <div class="card-content">
              <div class="card-value">{{ overviewStats.totalExecutions }}</div>
              <div class="card-label">总执行次数</div>
            </div>
          </div>
        </t-col>
      </t-row>
    </div>

    <!-- 搜索和筛选栏 -->
    <div class="search-section">
      <div class="search-content">
        <div class="search-left">
          <t-input
            v-model="searchForm.keyword"
            placeholder="搜索动作库名称或描述..."
            style="width: 300px"
            @enter="loadActionLibraries"
            clearable
          >
            <template #prefix-icon>
              <search-icon />
            </template>
          </t-input>
        </div>
        <div class="search-right">
          <t-space>
            <t-select
              v-model="searchForm.category"
              :options="categoryOptions"
              placeholder="全部分类"
              clearable
              style="width: 150px"
            />
            <t-select
              v-model="searchForm.status"
              :options="statusOptions"
              placeholder="全部状态"
              clearable
              style="width: 120px"
            />
            <t-button theme="primary" @click="loadActionLibraries"> 搜索 </t-button>
            <t-button theme="default" @click="resetSearch"> 重置 </t-button>
          </t-space>
        </div>
      </div>
    </div>

    <!-- 动作库列表 -->
    <div class="table-section">
      <div class="table-header">
        <div class="table-title">
          <h3>动作库列表</h3>
          <span class="table-count">共 {{ pagination.total }} 个动作库</span>
        </div>
        <div class="table-actions">
          <t-space>
            <t-button theme="default" variant="outline" @click="loadActionLibraries">
              <template #icon>
                <refresh-icon />
              </template>
              刷新
            </t-button>
          </t-space>
        </div>
      </div>

      <t-table
        :data="actionLibraries"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
        row-key="id"
        stripe
        hover
        size="medium"
        :empty="emptyConfig"
      >
        <template #name="{ row }">
          <div class="action-library-name">
            <div class="name">{{ row.name }}</div>
            <div class="description">{{ row.description || '-' }}</div>
          </div>
        </template>
        <template #category="{ row }">
          <t-tag v-if="row.category" variant="light">{{ row.category }}</t-tag>
          <span v-else>-</span>
        </template>
        <template #status="{ row }">
          <t-tag :theme="getStatusTheme(row.status)">
            {{ getStatusText(row.status) }}
          </t-tag>
        </template>
        <template #stats="{ row }">
          <div class="stats-info">
            <div class="stat-item">
              <span class="label">执行次数:</span>
              <span class="value">{{ row.executionCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="label">平均耗时:</span>
              <span class="value">{{ row.averageExecutionTime || '-' }}ms</span>
            </div>
          </div>
        </template>
        <template #lastExecutionTime="{ row }">
          {{ formatDateTime(row.lastExecutionTime) }}
        </template>
        <template #updatedAt="{ row }">
          {{ formatDateTime(row.updatedAt) }}
        </template>
        <template #actions="{ row }">
          <div class="action-buttons">
            <t-button size="small" variant="text" theme="primary" @click="editActionLibrary(row)">
              <template #icon>
                <edit-icon />
              </template>
              编辑
            </t-button>
            <t-button size="small" variant="text" theme="success" @click="testActionLibrary(row)">
              <template #icon>
                <play-circle-icon />
              </template>
              测试
            </t-button>
            <t-button size="small" variant="text" theme="default" @click="viewStats(row)"> 统计 </t-button>
            <t-dropdown :options="getMoreActions(row)" @click="onMoreAction">
              <t-button size="small" variant="text" theme="default">
                更多
                <template #suffix>
                  <chevron-down-icon />
                </template>
              </t-button>
            </t-dropdown>
          </div>
        </template>
      </t-table>
    </div>

    <!-- 动作库编辑面板 -->
    <action-library-panel
      v-model:visible="showEditPanel"
      :action-library-id="currentActionLibraryId"
      @back="onEditPanelBack"
      @save="onEditPanelSave"
    />

    <!-- 统计信息对话框 -->
    <t-dialog v-model:visible="showStatsDialog" header="动作库统计信息" width="800px" :footer="false">
      <div v-if="statsData" class="stats-content">
        <t-row :gutter="16">
          <t-col :span="6">
            <t-card title="总执行次数" :bordered="false">
              <div class="stat-number">{{ statsData.totalExecutions }}</div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card title="成功次数" :bordered="false">
              <div class="stat-number success">{{ statsData.successExecutions }}</div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card title="失败次数" :bordered="false">
              <div class="stat-number error">{{ statsData.errorExecutions }}</div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card title="成功率" :bordered="false">
              <div class="stat-number">{{ statsData.successRate.toFixed(1) }}%</div>
            </t-card>
          </t-col>
        </t-row>

        <t-row :gutter="16" style="margin-top: 16px">
          <t-col :span="12">
            <t-card title="平均执行时间" :bordered="false">
              <div class="stat-number">{{ statsData.averageExecutionTime }}ms</div>
            </t-card>
          </t-col>
          <t-col :span="12">
            <t-card title="最后执行时间" :bordered="false">
              <div class="stat-text">{{ formatDateTime(statsData.lastExecutionTime) }}</div>
            </t-card>
          </t-col>
        </t-row>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryManagement',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import {
  SearchIcon,
  RefreshIcon,
  AddIcon,
  ChevronDownIcon,
  Functions1Icon,
  CheckCircleIcon,
  EditIcon,
  PlayCircleIcon,
} from 'tdesign-icons-vue-next';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import ActionLibraryPanel from '@/components/action-library/ActionLibraryPanel.vue';

interface ActionLibrary {
  id: string;
  name: string;
  description?: string;
  category?: string;
  tags?: string;
  status: string;
  version: number;
  executionCount: number;
  lastExecutionTime?: string;
  averageExecutionTime?: number;
  updatedAt: string;
  createdBy?: string;
}

const actionLibraries = ref<ActionLibrary[]>([]);
const loading = ref(false);
const showEditPanel = ref(false);
const currentActionLibraryId = ref<string>('');
const showStatsDialog = ref(false);
const statsData = ref<any>(null);
const categories = ref<string[]>([]);
const overviewStats = ref<any>(null);

// 空状态配置
const emptyConfig = computed(() => ({
  description:
    actionLibraries.value.length === 0 && !loading.value
      ? searchForm.value.keyword || searchForm.value.category || searchForm.value.status
        ? '没有找到符合条件的动作库'
        : '暂无动作库，点击右上角按钮创建第一个动作库'
      : '加载中...',
  action:
    actionLibraries.value.length === 0 &&
    !loading.value &&
    !searchForm.value.keyword &&
    !searchForm.value.category &&
    !searchForm.value.status
      ? {
          content: '创建动作库',
          theme: 'primary',
          onClick: createActionLibrary,
        }
      : undefined,
}));

// 搜索表单
const searchForm = ref({
  keyword: '',
  category: '',
  status: '',
});

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100],
});

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '激活', value: 'active' },
  { label: '停用', value: 'inactive' },
];

// 分类选项
const categoryOptions = computed(() => {
  return categories.value.map((cat) => ({ label: cat, value: cat }));
});

// 表格列定义
const columns = [
  { colKey: 'name', title: '动作库名称', width: 250 },
  { colKey: 'category', title: '分类', width: 120 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'stats', title: '执行统计', width: 180 },
  { colKey: 'lastExecutionTime', title: '最后执行', width: 160 },
  { colKey: 'updatedAt', title: '更新时间', width: 160 },
  { colKey: 'actions', title: '操作', width: 200, fixed: 'right' as const },
];

// 加载动作库列表
const loadActionLibraries = async () => {
  loading.value = true;
  try {
    const params = {
      keyword: searchForm.value.keyword || undefined,
      category: searchForm.value.category || undefined,
      status: searchForm.value.status || undefined,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    };

    const data = await api.run(Services.actionLibraryGetAll, params);
    actionLibraries.value = data.List || [];
    pagination.value.total = data.Paging?.Total || 0;

    // 计算概览统计
    calculateOverviewStats();
  } catch (error) {
    MessagePlugin.error(`加载动作库列表失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 计算概览统计
const calculateOverviewStats = () => {
  const total = actionLibraries.value.length;
  const active = actionLibraries.value.filter((lib) => lib.status === 'active').length;
  const draft = actionLibraries.value.filter((lib) => lib.status === 'draft').length;
  const inactive = actionLibraries.value.filter((lib) => lib.status === 'inactive').length;
  const totalExecutions = actionLibraries.value.reduce((sum, lib) => sum + (lib.executionCount || 0), 0);

  overviewStats.value = {
    total,
    active,
    draft,
    inactive,
    totalExecutions,
  };
};

// 加载分类列表
const loadCategories = async () => {
  try {
    const data = await api.run(Services.actionLibraryGetCategories);
    categories.value = data || [];
  } catch (error) {
    console.error('加载分类列表失败:', error);
  }
};

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    keyword: '',
    category: '',
    status: '',
  };
  pagination.value.current = 1;
  loadActionLibraries();
};

// 分页变化
const onPageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  loadActionLibraries();
};

const onPageSizeChange = (pageInfo: any) => {
  pagination.value.pageSize = pageInfo.pageSize;
  pagination.value.current = 1;
  loadActionLibraries();
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'danger';
    case 'draft':
      return 'warning';
    default:
      return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '激活';
    case 'inactive':
      return '停用';
    case 'draft':
      return '草稿';
    default:
      return status;
  }
};

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

// 创建动作库
const createActionLibrary = () => {
  console.log('创建新动作库');
  currentActionLibraryId.value = '';
  showEditPanel.value = true;
};

// 编辑动作库
const editActionLibrary = (actionLibrary: ActionLibrary) => {
  currentActionLibraryId.value = actionLibrary.id;
  showEditPanel.value = true;
};

// 测试动作库
const testActionLibrary = (actionLibrary: ActionLibrary) => {
  currentActionLibraryId.value = actionLibrary.id;
  showEditPanel.value = true;
  // 可以在这里设置默认打开测试标签页
};

// 查看统计
const viewStats = async (actionLibrary: ActionLibrary) => {
  try {
    const data = await api.run(Services.actionLibraryGetExecutionStats, { id: actionLibrary.id });
    statsData.value = data;
    showStatsDialog.value = true;
  } catch (error) {
    MessagePlugin.error(`获取统计信息失败: ${error.message}`);
  }
};

// 获取更多操作选项
const getMoreActions = (actionLibrary: ActionLibrary) => {
  return [
    {
      content: '复制',
      value: 'copy',
      data: actionLibrary,
    },
    {
      content: actionLibrary.status === 'active' ? '停用' : '激活',
      value: 'toggle-status',
      data: actionLibrary,
    },
    {
      content: '删除',
      value: 'delete',
      data: actionLibrary,
      theme: 'error' as const,
    },
  ];
};

// 更多操作处理
const onMoreAction = async (data: any) => {
  const { value, data: actionLibrary } = data;

  switch (value) {
    case 'copy':
      await copyActionLibrary(actionLibrary);
      break;
    case 'toggle-status':
      await toggleStatus(actionLibrary);
      break;
    case 'delete':
      await deleteActionLibrary(actionLibrary);
      break;
  }
};

// 复制动作库
const copyActionLibrary = async (actionLibrary: ActionLibrary) => {
  try {
    const newName = `${actionLibrary.name}_副本`;
    await api.run(Services.actionLibraryCopy, { id: actionLibrary.id, newName });
    MessagePlugin.success('复制成功');
    loadActionLibraries();
  } catch (error) {
    MessagePlugin.error(`复制失败: ${error.message}`);
  }
};

// 切换状态
const toggleStatus = async (actionLibrary: ActionLibrary) => {
  try {
    const newStatus = actionLibrary.status === 'active' ? 'inactive' : 'active';
    await api.run(Services.actionLibraryUpdateStatus, { id: actionLibrary.id, status: newStatus });
    MessagePlugin.success('状态更新成功');
    loadActionLibraries();
  } catch (error) {
    MessagePlugin.error(`状态更新失败: ${error.message}`);
  }
};

// 删除动作库
const deleteActionLibrary = async (actionLibrary: ActionLibrary) => {
  const confirmRes = await DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除动作库"${actionLibrary.name}"吗？此操作不可恢复。`,
    theme: 'danger',
  });

  if (confirmRes) {
    try {
      await api.run(Services.actionLibraryDelete, { id: actionLibrary.id });
      MessagePlugin.success('删除成功');
      loadActionLibraries();
    } catch (error) {
      MessagePlugin.error(`删除失败: ${error.message}`);
    }
  }
};

// 编辑面板事件
const onEditPanelBack = () => {
  showEditPanel.value = false;
  currentActionLibraryId.value = '';
};

const onEditPanelSave = () => {
  showEditPanel.value = false;
  currentActionLibraryId.value = '';
  loadActionLibraries();
};

onMounted(() => {
  loadCategories();
  loadActionLibraries();

  // 临时添加一些演示数据
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      actionLibraries.value = [
        {
          id: '1',
          name: '用户数据验证',
          description: '验证用户输入数据的格式和完整性',
          category: '数据处理',
          status: 'active',
          version: 1,
          executionCount: 156,
          lastExecutionTime: '2024-01-15T10:30:00Z',
          averageExecutionTime: 120,
          updatedAt: '2024-01-15T09:00:00Z',
          createdBy: '张三',
        },
        {
          id: '2',
          name: '邮件发送服务',
          description: '发送各类通知邮件',
          category: '通知服务',
          status: 'active',
          version: 2,
          executionCount: 89,
          lastExecutionTime: '2024-01-15T11:45:00Z',
          averageExecutionTime: 2500,
          updatedAt: '2024-01-14T16:20:00Z',
          createdBy: '李四',
        },
        {
          id: '3',
          name: '数据格式转换',
          description: '将数据在不同格式间进行转换',
          category: '数据处理',
          status: 'draft',
          version: 1,
          executionCount: 0,
          lastExecutionTime: undefined,
          averageExecutionTime: undefined,
          updatedAt: '2024-01-13T14:30:00Z',
          createdBy: '王五',
        },
      ];

      pagination.value.total = 3;
      categories.value = ['数据处理', '通知服务', 'API调用', '文件处理'];
      calculateOverviewStats();
    }, 500);
  }
});
</script>

<style lang="less" scoped>
.action-library-management {
  min-height: 100vh;
  background: var(--td-bg-color-page);

  .page-header {
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    margin-bottom: 24px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 32px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }

        .page-description {
          margin: 0;
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  .overview-section {
    max-width: 1200px;
    margin: 0 auto 24px auto;
    padding: 0 24px;

    .overview-card {
      background: var(--td-bg-color-container);
      border-radius: 12px;
      padding: 24px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
      }

      .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        &.active {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
        }

        &.draft {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
        }

        &.executions {
          background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
          color: white;
        }
      }

      .card-content {
        .card-value {
          font-size: 32px;
          font-weight: 700;
          color: var(--td-text-color-primary);
          line-height: 1;
          margin-bottom: 4px;
        }

        .card-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          font-weight: 500;
        }
      }
    }
  }

  .search-section {
    max-width: 1200px;
    margin: 0 auto 24px auto;
    padding: 0 24px;

    .search-content {
      background: var(--td-bg-color-container);
      border-radius: 12px;
      padding: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .search-left {
        flex: 1;
      }

      .search-right {
        margin-left: 24px;
      }
    }
  }

  .table-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    .table-header {
      background: var(--td-bg-color-container);
      border-radius: 12px 12px 0 0;
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--td-border-level-1-color);

      .table-title {
        display: flex;
        align-items: center;
        gap: 12px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }

        .table-count {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          background: var(--td-bg-color-component);
          padding: 4px 12px;
          border-radius: 16px;
        }
      }
    }

    :deep(.t-table) {
      border-radius: 0 0 12px 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .t-table__header {
        background: var(--td-bg-color-container-hover);
      }

      .t-table__body {
        .action-library-name {
          .name {
            font-weight: 600;
            margin-bottom: 4px;
            color: var(--td-text-color-primary);
          }

          .description {
            font-size: 12px;
            color: var(--td-text-color-secondary);
            line-height: 1.4;
          }
        }

        .stats-info {
          .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 12px;

            .label {
              color: var(--td-text-color-secondary);
            }

            .value {
              font-weight: 600;
              color: var(--td-text-color-primary);
            }
          }
        }

        .action-buttons {
          display: flex;
          align-items: center;
          gap: 8px;

          .t-button {
            transition: all 0.2s ease;

            &:hover {
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }

  .stats-content {
    .stat-number {
      font-size: 24px;
      font-weight: bold;
      text-align: center;

      &.success {
        color: var(--td-success-color);
      }

      &.error {
        color: var(--td-error-color);
      }
    }

    .stat-text {
      font-size: 16px;
      text-align: center;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .action-library-management {
    .page-header .header-content,
    .overview-section,
    .search-section,
    .table-section {
      max-width: 100%;
      padding-left: 16px;
      padding-right: 16px;
    }
  }
}

@media (max-width: 768px) {
  .action-library-management {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .search-section .search-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .search-right {
        margin-left: 0;
      }
    }

    .overview-section {
      .t-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
