<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="150px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="数据源" prop="dataSource">
              <data-source-select v-model="formData.dataSource" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="操作类型" prop="operateType">
              <t-radio-group
                v-model="formData.operateType"
                name="operateType"
                :options="operateTypeOptions"
              ></t-radio-group>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item prop="exceptionSkip">
              <template #label>
                <t-space size="small">
                  <span>异常跳过</span>
                  <t-tooltip>
                    <template #content>
                      <div>
                        <p>是：当数据保存异常时，跳过该条数据，继续保存下一条数据（在数据库事务节点下无效）</p>
                        <p>否：当数据保存异常时，回滚当前步骤之前保存的数据，并中断操作</p>
                      </div>
                    </template>
                    <help-circle-filled-icon style="color: var(--td-text-color-placeholder)" />
                  </t-tooltip>
                </t-space>
              </template>
              <t-switch v-model="formData.exceptionSkip" />
            </t-form-item>
          </t-col>
          <t-col v-if="formData.operateType === 'Save' || formData.operateType === 'Insert'" :span="6">
            <t-form-item prop="openFastInsert">
              <template #label>
                <t-space size="small">
                  <span>快速新增</span>
                  <t-tooltip>
                    <template #content>
                      <div>开启后，在新增时，因数据库不同，可能会导致约束或触发器失效</div>
                    </template>
                    <help-circle-filled-icon style="color: var(--td-text-color-placeholder)" />
                  </t-tooltip>
                </t-space>
              </template>
              <t-switch v-model="formData.openFastInsert" />
            </t-form-item>
          </t-col>
          <t-col v-if="!formData.openFastInsert" :span="6">
            <t-form-item prop="batchSize">
              <template #label>
                <t-space size="small">
                  <span>批量处理数</span>
                </t-space>
              </template>
              <t-input-number v-model="formData.batchSize" :min="1" />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item prop="sourceDataPath">
              <template #label>
                <t-space size="small">
                  <span>数据集</span>
                  <t-tooltip>
                    <template #content>
                      <div>
                        <p>选择数据集，将从数据集取数据，并保存到目标表。</p>
                      </div>
                    </template>
                    <help-circle-filled-icon style="color: var(--td-text-color-placeholder)" />
                  </t-tooltip>
                </t-space>
              </template>
              <value-input
                v-model:data-value="formData.sourceDataPath"
                :borderless="false"
                size="medium"
                only-variable
                :auto-hide-edit="false"
                :limit-types="['array', 'object']"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <data-source-table
          v-model:data="formData.configureInfo"
          :operation-type="formData.operateType"
          :data-source-id="formData.dataSource"
        ></data-source-table>
        <div v-if="formData.operateType === 'Save' || formData.operateType === 'Insert'">
          <action-form-title title="更新数据集" tip="选择表中有值的字段，将其映射到对应数据集的字段。">
          </action-form-title>
          <div>
            <t-table row-key="index" class="small-table" :columns="tableColumns" :data="tableData" size="small">
              <template #tableField>
                <t-space size="small">
                  <span>表字段</span>
                  <t-tooltip>
                    <template #content>
                      <div style="font-size: 12px">
                        <p>可选字段： 主键、唯一键、保存（更新和新增交集字段），新增（新增字段）等。</p>
                        <p>例如：源数据集中预留的字段为：user_name，表中的字段为：name。</p>
                        <p>则选择表中的 name 字段，每行数据保存时，将取值，将其映射到源数据集的 user_name 字段。</p>
                      </div>
                    </template>
                    <help-circle-filled-icon />
                  </t-tooltip>
                </t-space>
              </template>
              <template #operate="{ rowIndex }">
                <t-tooltip content="新增">
                  <t-button
                    size="small"
                    shape="square"
                    variant="text"
                    title="新增"
                    style="color: var(--td-success-color-hover)"
                    @click="() => onClickAddRow(rowIndex)"
                  >
                    <template #icon><add-icon /></template>
                  </t-button>
                </t-tooltip>

                <t-tooltip content="删除">
                  <t-button
                    size="small"
                    shape="square"
                    variant="text"
                    title="删除"
                    style="color: var(--td-error-color)"
                    @click="onClickRemoveRow(rowIndex)"
                  >
                    <template #icon><remove-icon /></template>
                  </t-button>
                </t-tooltip>
              </template>
              <template #bottomContent>
                <t-button
                  v-if="tableData.length < 1"
                  size="small"
                  style="margin-top: 6px"
                  variant="dashed"
                  block
                  @click="onClickAddRow(0)"
                  >新 增</t-button
                >
              </template>
            </t-table>
          </div>
        </div>

        <!-- 输出配置 -->
        <action-form-title title="输出配置"> </action-form-title>
        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="输出方式" prop="outputConfig.type">
              <t-select v-model="formData.outputConfig.type" placeholder="请选择输出方式">
                <t-option value="none" label="无（不输出）" />
                <t-option value="dictionary" label="字典" />
                <t-option value="list" label="列表" />
              </t-select>
            </t-form-item>
          </t-col>
        </t-row>

        <!-- 字典配置 -->
        <div v-if="formData.outputConfig.type === 'dictionary'" class="dictionary-config">
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="键字段" prop="outputConfig.dictionaryConfig.keyColumns">
                <t-select
                  v-model="formData.outputConfig.dictionaryConfig.keyColumns"
                  :options="columnOptions"
                  placeholder="请选择键字段"
                  multiple
                  clearable
                />
              </t-form-item>
            </t-col>
            <t-col :span="3">
              <t-form-item label="键分隔符" prop="outputConfig.dictionaryConfig.keySeparator">
                <t-input
                  v-model="formData.outputConfig.dictionaryConfig.keySeparator"
                  placeholder="多列组合时的分隔符"
                />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="值字段" prop="outputConfig.dictionaryConfig.valueColumn">
                <t-select
                  v-model="formData.outputConfig.dictionaryConfig.valueColumn"
                  :options="columnOptions"
                  placeholder="请选择值字段"
                  clearable
                  :disabled="formData.outputConfig.dictionaryConfig.useFullObjectAsValue"
                />
              </t-form-item>
            </t-col>
            <t-col :span="9">
              <t-form-item label="使用完整对象作为值" prop="outputConfig.dictionaryConfig.useFullObjectAsValue">
                <t-switch v-model="formData.outputConfig.dictionaryConfig.useFullObjectAsValue" />
                <span class="form-tip">启用后将忽略值字段配置，使用完整记录作为字典的值</span>
              </t-form-item>
            </t-col>
          </t-row>

          <!-- 配置示例 -->
          <div class="config-example">
            <t-alert theme="info" title="配置示例">
              <div>
                <strong>多列组合键示例：</strong><br />
                键字段：['eid', 'oid', 'mo_code']，分隔符：'|'<br />
                生成的键：'E001|O002|MO003'
              </div>
              <div style="margin-top: 8px">
                <strong>当前配置预览：</strong><br />
                <code v-if="dictionaryConfigPreview">{{ dictionaryConfigPreview }}</code>
                <span v-else class="text-placeholder">请完成配置</span>
              </div>
            </t-alert>
          </div>
        </div>

        <action-form-title title="输出参数"> </action-form-title>
        <variable-list
          :key="`output-${formData.outputConfig?.type || 'none'}`"
          v-model:data="currentStep.result"
          :show-root-node="formData.useRoot ?? false"
          :output-type="formData.outputConfig?.type || 'none'"
        ></variable-list>
      </div>
    </t-form>
  </div>
</template>
<script lang="tsx">
export default {
  name: 'DataSaveActions',
};
</script>
<script setup lang="tsx">
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { AddIcon, HelpCircleFilledIcon, RemoveIcon } from 'tdesign-icons-vue-next';
import { Select, TableProps } from 'tdesign-vue-next';
import { computed, onMounted, onUnmounted, watch } from 'vue';

import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import DataSourceSelect from '@/components/action-panel/DataSourceSelect.vue';
import DataSourceTable from '@/components/action-panel/DataSourceTable.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';

import { useDataSaveStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataSaveStore = useDataSaveStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataSaveStore.updateState();
  },
  {
    immediate: true,
  },
);

const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData, updateTableData: tableData } = storeToRefs(dataSaveStore);

// 列选项（用于字典配置）
const columnOptions = computed(() => {
  return dataSaveStore.columnOptions;
});

// 字典配置预览
const dictionaryConfigPreview = computed(() => {
  const config = formData.value.outputConfig?.dictionaryConfig;
  if (!config || !config.keyColumns?.length) {
    return '';
  }

  const keyExample = config.keyColumns.map((col) => `item.${col}`).join(` + '${config.keySeparator}' + `);
  const valueExample = config.useFullObjectAsValue
    ? 'item (完整对象)'
    : config.valueColumn
      ? `item.${config.valueColumn}`
      : '未配置';

  return `dic[${keyExample}] = ${valueExample}`;
});

let timeId = null;
onMounted(() => {
  timeId = setInterval(() => {
    setUpdateTableData();
  }, 200);
});

onUnmounted(() => {
  clearInterval(timeId);
});

let oldFormData = {};
const setUpdateTableData = () => {
  if (JSON.stringify(formData.value) === JSON.stringify(oldFormData)) {
    return;
  }

  dataSaveStore.setArgs(formData.value);
  oldFormData = cloneDeep(formData.value);
};

// 监听输出配置变化，立即更新输出参数结构
watch(
  () => formData.value.outputConfig?.type,
  (newType, oldType) => {
    if (newType !== oldType) {
      dataSaveStore.setArgs(formData.value);
    }
  },
  { immediate: false },
);

const operateTypeOptions = [
  { label: '保存', value: 'Save' },
  { label: '更新', value: 'Update' },
  { label: '新增', value: 'Insert' },
];

const tableColumns = computed<TableProps['columns']>(() => [
  {
    title: '数据集字段',
    colKey: 'sourceDataField',
    width: 200,
    edit: {
      component: Select,
      props: {
        size: 'small',
        clearable: true,
        filterable: true,
        options: dataSaveStore.dataSourceOptions,
      },
      validateTrigger: 'change',
      keepEditMode: true,
      abortEditOnEvent: ['onChange'],
      onEdited: (context) => {
        tableData.value.splice(context.rowIndex, 1, context.newRowData);
        dataSaveStore.setArgs(formData.value);
      },
      rules: [
        {
          required: true,
          message: '不能为空',
        },
      ],
    },
  },
  {
    title: 'tableField',
    colKey: 'tableField',
    width: 200,
    edit: {
      component: Select,
      props: {
        size: 'small',
        clearable: true,
        filterable: true,
        options: dataSaveStore.tableColumnOptions,
      },
      validateTrigger: 'change',
      keepEditMode: true,
      abortEditOnEvent: ['onChange'],
      onEdited: (context) => {
        tableData.value.splice(context.rowIndex, 1, context.newRowData);
        dataSaveStore.setArgs(formData.value);
      },
      rules: [
        {
          required: true,
          message: '不能为空',
        },
      ],
    },
  },
  {
    title: '操作',
    align: 'left',
    fixed: 'right',
    width: 240,
    colKey: 'operate',
  },
]);

const onClickAddRow = (index: number) => {
  const newData = {
    sourceDataField: '',
    tableField: '',
  };
  tableData.value.splice(index + 1, 0, newData);
  dataSaveStore.setArgs(formData.value);
};

const onClickRemoveRow = (index: number) => {
  tableData.value.splice(index, 1);
  dataSaveStore.setArgs(formData.value);
};
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}

.dictionary-config {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin: 16px 0;
}

.config-example {
  margin-top: 16px;
}

.form-tip {
  color: #666;
  font-size: 12px;
  margin-left: 8px;
}

.text-placeholder {
  color: #999;
  font-style: italic;
}
</style>
