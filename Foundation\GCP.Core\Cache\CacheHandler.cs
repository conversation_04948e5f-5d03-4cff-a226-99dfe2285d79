﻿using GCP.DataAccess;
using Medallion.Threading;
using Medallion.Threading.MySql;
using Medallion.Threading.Oracle;
using Medallion.Threading.Postgres;
using Medallion.Threading.Redis;
using Medallion.Threading.SqlServer;
using Microsoft.Extensions.Configuration;
using Serilog;
using StackExchange.Redis;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class CacheHandler
    {
        public static async Task<IServiceCollection> AddCacheAsync(this IServiceCollection services, IConfiguration configuration)
        {
            var cacheConfigName = "gcp-cache";
            var connectionString = configuration["redis:ConnectionString"];
            var noRedis = string.IsNullOrEmpty(connectionString);

            services.AddLZ4Compressor();
            services.AddEasyCaching(options =>
            {
                if (noRedis)
                {
                    options.UseInMemory(cacheConfigName);
                    Log.Information("Use cache with {provider}", "In-memory");
                }
                else
                {
                    options.UseRedis(config =>
                    {
                        config.DBConfig.ConfigurationOptions = ConfigurationOptions.Parse(connectionString);

                        config.SerializerName = "mymsgpack";
                    }, cacheConfigName)
                    .WithMessagePack("mymsgpack")
                    .WithCompressor();
                    Log.Information("Use cache with {provider}", "Redis");
                }
            });

            IDistributedLockProvider lockProvider;
            if (noRedis)
            {
                lockProvider = DbSettings.DbProviderType switch
                {
                    DbProviderType.MySql => new MySqlDistributedSynchronizationProvider(DbSettings.DefaultConnectionString),
                    DbProviderType.SqlServer => new SqlDistributedSynchronizationProvider(DbSettings.DefaultConnectionString),
                    DbProviderType.Oracle => new OracleDistributedSynchronizationProvider(DbSettings.DefaultConnectionString),
                    DbProviderType.PostgreSQL => new PostgresDistributedSynchronizationProvider(DbSettings.DefaultConnectionString),
                    _ => throw new NotSupportedException("分布式锁暂不支持该数据库类型 " + DbSettings.DbProviderType.ToString()),
                };
                Log.Information("Use distributed lock with {provider}", DbSettings.DbProviderType.ToString());
            }
            else
            {
                var connection = await ConnectionMultiplexer.ConnectAsync(connectionString);
                lockProvider = new RedisDistributedSynchronizationProvider(connection.GetDatabase());
                Log.Information("Use distributed lock with {provider}", "Redis");
            }
            services.AddSingleton(lockProvider);
            
            return services;
        }
    }
}