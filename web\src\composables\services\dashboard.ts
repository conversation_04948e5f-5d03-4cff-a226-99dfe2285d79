import { api, Services } from '@/api/system';

export interface TopPanelStatsBaseItem {
  title: string;
  number: string;
  upTrend: string;
  trentName: string;
  chartData: number[];
}

export interface TopPanelStatsBase {
  base: TopPanelStatsBaseItem[];
}

export interface ServiceResourcesBase {
  base: {
    apiCount: number;
    jobCount: number;
    eventCount: number;
    pointCount: number;
    rateData: any[];
  };
}

export interface CallRankBaseItem {
  serviceName: string;
  count: number;
  successRate?: number;
}
export interface CallRankBase {
  base: CallRankBaseItem[];
}

export interface ErrorRankBaseItem {
  functionId: string;
  triggerType: string;
  serviceName: string;
  count: number;
}
export interface ErrorRankBase {
  base: ErrorRankBaseItem[];
}

export const useDashboardService = () => {
  return {
    // 获取驾驶舱顶部面板统计数据
    getTopPanelStats: (triggerType: string): Promise<TopPanelStatsBase> => {
      return api.run(Services.dashboardGetTopPanelStats, { triggerType });
    },

    // 获取驾驶舱服务资源统计
    getServiceResources: (triggerType: string): Promise<ServiceResourcesBase> => {
      return api.run(Services.dashboardGetServiceResources, { triggerType });
    },

    // 获取驾驶舱统计图表数据
    getChartData: (triggerType: string, startDate?: string, endDate?: string) => {
      return api.run(Services.dashboardGetChartData, { triggerType, startDate, endDate });
    },

    // 获取驾驶舱调用次数排行
    getCallRank: (triggerType: string, timeRange: 'today' | 'yesterday' | 'week' = 'today'): Promise<CallRankBase> => {
      return api.run(Services.dashboardGetCallRank, { triggerType, timeRange });
    },

    // 获取驾驶舱错误次数排行
    getErrorRank: (triggerType: string, timeRange: 'today' | 'yesterday' | 'week' = 'today'): Promise<ErrorRankBase> => {
      return api.run(Services.dashboardGetErrorRank, { triggerType, timeRange });
    },
  };
};