﻿using GCP.Common;
using GCP.DataAccess;

namespace GCP.Functions.Common.Services
{
    [Function("solution", "解决方案服务")]
    internal class SolutionService : BaseService
    {
        [Function("getAll", "获取所有解决方案清单")]
        public List<LcSolution> GetAll()
        {
            using var db = this.GetDb();
            var data = (from a in db.LcSolutions
                        where a.State == 1
                        select a).ToList();
            return data;
        }

        [Function("add", "添加解决方案")]
        public void Add(LcSolution solution)
        {
            this.InsertData(solution);
        }


        [Function("update", "更新解决方案")]
        public void Update(LcSolution solution)
        {
            this.UpdateData(solution);
        }

        [Function("delete", "删除解决方案")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            var solution = db.LcSolutions.FirstOrDefault(a => a.Id == id);
            if (solution == null)
            {
                return;
            }
            solution.State = 0;
            this.UpdateData(solution, db);
        }
    }
}
