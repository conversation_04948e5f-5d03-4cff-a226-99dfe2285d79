﻿using System.Runtime.Serialization;

namespace GCP.Common
{
    [DataContract]
    [Serializable]
    public class ResponseBase
    {
        //[JsonProperty(PropertyName = "success")]
        [DataMember(Name = "code")]
        public int Code { get; set; } = 200;

        [DataMember(Name = "message")]
        public object Message { get; set; } = "";

        [DataMember(Name = "data")]
        public object Data { get; set; }

        public ResponseBase()
        {
        }

        public ResponseBase(object data)
        {
            Data = data;
        }

        public ResponseBase(int code, object message, object data)
        {
            Code = code;
            Message = message;
            Data = data;
        }
    }
}
