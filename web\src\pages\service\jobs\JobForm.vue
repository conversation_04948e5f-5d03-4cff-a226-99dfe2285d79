<template>
  <cmp-container full>
    <cmp-card ghost>
      <t-form
        :data="formData"
        label-align="top"
        label-width="100px"
        class="form-container form-container-center"
        :rules="FORM_RULES"
        @reset="onReset"
        @submit="onSubmit"
      >
        <div class="form-item" style="padding: 24px">
          <div class="form-container-title">基础信息</div>
          <t-row :gutter="[32, 24]">
            <t-col :span="6">
              <t-form-item label="任务名称" name="jobName">
                <t-input v-model="formData.jobName" placeholder="请输入任务名称" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="任务组" name="jobGroup">
                <t-auto-complete
                  v-model="formData.jobGroup"
                  :options="groupList"
                  placeholder="请输入任务组"
                  :filterable="false"
                />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="备注" name="description">
                <t-textarea v-model="formData.description" placeholder="请输入备注" />
              </t-form-item>
            </t-col>
          </t-row>
          <div class="form-container-title form-title-gap">触发时间</div>
          <t-row :gutter="[32, 24]">
            <t-col :span="12">
              <t-form-item label="Cron表达式" name="cronExpression">
                <t-space direction="vertical" size="small">
                  <cron-light v-model="formData.cronExpression" locale="zh"></cron-light>
                  <t-input v-model="cronExpressionStr">
                    <template #suffixIcon>
                      <t-tooltip class="refresh-btn" content="修改后，刷新表达式">
                        <t-button theme="default" variant="text" size="small" @click.stop.prevent="onClickRefreshCron">
                          <template #icon><refresh-icon></refresh-icon></template>
                        </t-button>
                      </t-tooltip>
                    </template>
                  </t-input>
                  <t-tag v-if="cronDescription" size="small" theme="success" variant="light">{{
                    cronDescription
                  }}</t-tag>
                </t-space>
              </t-form-item>
            </t-col>
            <t-col v-if="cronDescription" :span="12">
              <t-form-item label="最近5次运行时间">
                <ul>
                  <li v-for="(time, index) in nextTimes" :key="index">{{ time }}</li>
                </ul>
              </t-form-item>
            </t-col>
          </t-row>
          <t-collapse style="margin-top: 16px">
            <t-collapse-panel header="其他">
              <t-row :gutter="[32, 24]">
                <t-col :span="2">
                  <t-form-item label="异常停止" name="exceptionStop">
                    <t-switch v-model="formData.exceptionStop" :custom-value="[1, 0]" />
                  </t-form-item>
                </t-col>
                <t-col :span="10">
                  <t-space size="small" style="height: 64px; align-items: center">
                    <help-circle-filled-icon />
                    <span> 当任务执行异常时，下次运行开始时间不变。 </span>
                  </t-space>
                </t-col>
                <t-col :span="2">
                  <t-form-item label="允许并行" name="allowParallelization">
                    <t-switch v-model="formData.allowParallelization" :custom-value="[1, 0]" />
                  </t-form-item>
                </t-col>
                <t-col :span="5">
                  <t-form-item label="日志保留天数" name="logRetentionDays">
                    <t-input-number v-model="formData.logRetentionDays" />
                  </t-form-item>
                </t-col>
                <t-col :span="2">
                  <t-form-item label="超时时间(秒)" name="timeoutInSeconds">
                    <t-input-number v-model="formData.timeoutInSeconds" />
                  </t-form-item>
                </t-col>
                <t-col :span="3"></t-col>
                <t-col :span="2">
                  <t-form-item label="允许重试" name="allowRetry">
                    <t-switch v-model="formData.allowRetry" :custom-value="[1, 0]" />
                  </t-form-item>
                </t-col>
                <t-col :span="5">
                  <t-form-item label="重试次数" name="retryCount">
                    <t-input-number v-model="formData.retryCount" :disabled="!formData.allowRetry" />
                  </t-form-item>
                </t-col>
                <t-col :span="5">
                  <t-form-item label="重试间隔时间(秒)" name="retryDelaysInSeconds">
                    <t-input-number v-model="formData.retryDelaysInSeconds" :disabled="!formData.allowRetry" />
                  </t-form-item>
                </t-col>
                <t-col :span="2">
                  <t-form-item label="允许错误通知" name="allowErrorNotification">
                    <t-switch v-model="formData.allowErrorNotification" :custom-value="[1, 0]" />
                  </t-form-item>
                </t-col>
                <t-col :span="10">
                  <t-form-item label="通知邮箱" name="notificationEmail">
                    <t-input v-model="formData.notificationEmail" :disabled="!formData.allowErrorNotification" />
                  </t-form-item>
                </t-col>
              </t-row>
            </t-collapse-panel>
          </t-collapse>
          <!-- <div class="form-container-title form-title-gap">其他</div> -->
        </div>

        <div class="form-submit-container">
          <div class="form-submit-sub">
            <div class="form-submit-left">
              <t-button theme="primary" class="form-submit-confirm" type="submit"> 确认提交 </t-button>
              <t-button type="reset" class="form-submit-cancel" theme="default" variant="base"> 取消 </t-button>
            </div>
          </div>
        </div>
      </t-form>
    </cmp-card>
  </cmp-container>
</template>
<script lang="ts">
export default {
  name: 'JobForm',
};
</script>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { FormRule, MessagePlugin, SubmitContext } from 'tdesign-vue-next';
import { onMounted, ref, watch } from 'vue';
import { HelpCircleFilledIcon, RefreshIcon } from 'tdesign-icons-vue-next';

import { api, Services } from '@/api/system';

interface JobFormData extends Record<string, any> {
  id?: string;
  jobName?: string;
  jobGroup?: string;
  cronExpression?: string;
  description?: string;
  logRetentionDays?: number;
  allowParallelization?: number;
  allowRetry?: number;
  retryCount?: number;
  retryDelaysInSeconds?: number;
  allowErrorNotification?: number;
  notificationEmail?: string;
  exceptionStop?: number;
  timeoutInSeconds?: number;
}

const emits = defineEmits(['back', 'submit']);

const props = defineProps<{ data?: JobFormData; isEdit: boolean }>();

onMounted(() => {
  api.run(Services.jobGetAllGroup).then((res) => {
    groupList.value = res.data;
  });
});

const FORM_RULES: Record<string, FormRule[]> = {
  jobName: [{ required: true, message: '请输入任务名称', type: 'error' }],
  jobGroup: [{ required: true, message: '请输入任务组', type: 'error' }],
  cronExpression: [{ required: true, message: '请选择或输入Cron表达式', type: 'error' }],
};

const groupList = ref([]);
const formData = ref<JobFormData>({
  ...{
    jobName: '',
    jobGroup: '',
    cronExpression: '*/5 * * * *',
    exceptionStop: 0,
    allowParallelization: 0,
    allowRetry: 0,
    allowErrorNotification: 0,
    timeoutInSeconds: 300,
  },
  ...props.data,
});
const cronExpressionStr = ref('*/5 * * * *');

formData.value.allowParallelization = formData.value.allowParallelization || 0;
formData.value.allowRetry = formData.value.allowRetry || 0;
formData.value.allowErrorNotification = formData.value.allowErrorNotification || 0;
formData.value.timeoutInSeconds = formData.value.timeoutInSeconds || 300;

const onClickRefreshCron = () => {
  formData.value.cronExpression = cronExpressionStr.value;
};

watch(
  () => formData.value.cronExpression,
  debounce(async (val) => {
    cronExpressionStr.value = val;
    const data = await api.run(Services.jobCronInfo, {
      cron: `${val || '* * * * *'}`,
    });
    cronDescription.value = data.cronDescription;
    nextTimes.value = data.nextTimes;
    if (!formData.value.logRetentionDays) {
      formData.value.logRetentionDays = data.logRetentionDays;
    }
  }, 500),
  {
    immediate: true,
  },
);
const cronDescription = ref<string>('');
const nextTimes = ref<string[]>([]);

const onReset = () => {
  emits('back');
};
const onSubmit = async (ctx: SubmitContext) => {
  if (ctx.validateResult === true) {
    if (props.isEdit) {
      await api.run(Services.jobUpdate, formData.value).then(() => {
        MessagePlugin.success('任务修改成功');
      });
    } else {
      await api.run(Services.jobAdd, formData.value).then(() => {
        MessagePlugin.success('任务添加成功');
      });
    }

    emits('submit', formData.value);
  }
};
</script>
<style lang="less" scoped>
@import '@/style/form.less';

.form-container {
  background-color: var(--td-bg-color-container);
}

.form-item {
  width: 676px;
}
</style>
