<template>
  <cmp-container full>
    <cmp-card ghost>
      <t-form
        ref="formRef"
        :data="formData"
        :rules="FORM_RULES"
        label-align="top"
        label-width="100px"
        class="form-container form-container-center"
        @submit="onSubmit"
      >
        <div class="form-item">
          <div class="form-container-title">基础信息</div>
          <t-row :gutter="[32, 24]">
            <t-col :span="6">
              <t-form-item label="组织编码" name="orgCode">
                <t-input v-model="formData.orgCode" placeholder="请输入组织编码" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="设备名称" name="equipmentName">
                <t-input v-model="formData.equipmentName" placeholder="请输入设备名称" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="设备编码" name="equipmentCode">
                <t-input v-model="formData.equipmentCode" placeholder="请输入设备编码" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="设备类型" name="equipmentType">
                <t-input v-model="formData.equipmentType" placeholder="请输入设备类型" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="驱动编码" name="driverCode">
                <t-select v-model="formData.driverCode" placeholder="请选择驱动编码">
                  <t-option v-for="code in driverCodes" :key="code" :value="code" :label="code" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="描述" name="description">
                <t-textarea v-model="formData.description" placeholder="请输入描述" />
              </t-form-item>
            </t-col>
          </t-row>
        </div>

        <div class="form-submit-container">
          <div class="form-submit-sub">
            <div class="form-submit-left">
              <t-button theme="primary" class="form-submit-confirm" type="submit">确认提交</t-button>
              <t-button theme="default" variant="base" class="form-submit-cancel" @click="onClickBack">取消</t-button>
            </div>
          </div>
        </div>
      </t-form>
    </cmp-card>
  </cmp-container>
</template>

<script lang="ts">
export default {
  name: 'EquipmentForm',
};
</script>

<script setup lang="ts">
import { FormInstanceFunctions, FormRule, MessagePlugin } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';

import { api, Services } from '@/api/system';

const props = defineProps<{
  data?: any;
  isEdit?: boolean;
}>();

const emit = defineEmits(['back', 'submit']);

const formRef = ref<FormInstanceFunctions>();
const driverCodes = ref<string[]>([]);

const formData = ref({
  orgCode: '',
  equipmentName: '',
  equipmentCode: '',
  equipmentType: '',
  driverCode: '',
  description: '',
});

// 获取驱动编码列表
const getDriverCodes = async () => {
  try {
    const res = await api.run(Services.driverGetDriverCodes);
    driverCodes.value = res;
  } catch (error) {
    MessagePlugin.error('获取驱动编码列表失败');
  }
};

onMounted(() => {
  getDriverCodes();
});

const FORM_RULES = {
  orgCode: [{ required: true, message: '请输入组织编码', type: 'error' }],
  equipmentName: [{ required: true, message: '请输入设备名称', type: 'error' }],
  equipmentCode: [{ required: true, message: '请输入设备编码', type: 'error' }],
  equipmentType: [{ required: true, message: '请输入设备类型', type: 'error' }],
  driverCode: [{ required: true, message: '请输入驱动编码', type: 'error' }],
} as Record<string, FormRule[]>;

if (props.data) {
  formData.value = { ...props.data };
}

const onClickBack = () => {
  emit('back');
};

const onSubmit = ({ validateResult }) => {
  if (validateResult === true) {
    const service = props.isEdit ? Services.equipmentUpdate : Services.equipmentAdd;
    api.run(service, formData.value).then(() => {
      MessagePlugin.success('操作成功');
      emit('submit', formData.value);
    });
  }
};
</script>

<style lang="less" scoped>
@import '@/style/form.less';

.form-container {
  background-color: var(--td-bg-color-container);
}

.form-item {
  width: 676px;
}
</style>
