﻿using Microsoft.Data.Sqlite;
using System.Data.Common;

namespace GCP.DataAccess
{
    internal class SqliteProvider : BaseDbProvider
    {
        public override string ProviderName
        {
            get
            {
                return "Microsoft.Data.Sqlite";
            }
        }

        public override bool NotUseParameter
        {
            get
            {
                return false;
            }
        }

        public override DbProviderFactory Factory => SqliteFactory.Instance;
        public override DbProviderType ProviderType => DbProviderType.SQLite;

        public override string GetParameterName(string parameterName)
        {
            return parameterName[0] == '@' ? parameterName : "@" + parameterName;
        }

        public override string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "")
        {
            return string.Format("SELECT t.* FROM ({2}) AS t {3} LIMIT {0},{1}", startIndex, length, selectSql, orderBySql);
        }

        public override string GetTimeFormatStr(DateTime time)
        {
            return "DATETIME('" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
        }

        public override string GetTimeSql()
        {
            return "select datetime('now')";
        }
    }
}
