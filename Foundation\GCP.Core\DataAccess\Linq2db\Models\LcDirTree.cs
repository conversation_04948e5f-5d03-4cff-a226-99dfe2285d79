// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 目录树
	/// </summary>
	[Table("lc_dir_tree")]
	public class LcDirTree : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"           , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                          )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"      , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                        )] public DateTime? TimeModified { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                             )] public string?   Modifier     { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                )] public short     State        { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"  , CanBeNull = false                     )] public string    SolutionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:父目录 ID ROOT:根目录
		/// </summary>
		[Column("PARENT_ID"    , CanBeNull = false                     )] public string    ParentId     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:目录编码
		/// </summary>
		[Column("DIR_CODE"                                             )] public string?   DirCode      { get; set; } // varchar(50)
		/// <summary>
		/// Description:目录类型 P:项目子目录 A:API分类目录 F:函数分类目录 D:字典分类目录
		/// </summary>
		[Column("DIR_TYPE"     , CanBeNull = false                     )] public string    DirType      { get; set; } = null!; // char(2)
		/// <summary>
		/// Description:目录名称
		/// </summary>
		[Column("DIR_NAME"     , CanBeNull = false                     )] public string    DirName      { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                          )] public string?   Description  { get; set; } // varchar(200)
		/// <summary>
		/// Description:序号
		/// </summary>
		[Column("SEQ"                                                  )] public short     Seq          { get; set; } // smallint
		/// <summary>
		/// Description:图标
		/// </summary>
		[Column("ICON"                                                 )] public string?   Icon         { get; set; } // varchar(200)
		/// <summary>
		/// Description:是否叶子结点 Y:是
		/// </summary>
		[Column("IS_LEAF"                                              )] public char?     IsLeaf       { get; set; } // varchar(1)
		/// <summary>
		/// Description:叶子结点对应数据ID
		/// </summary>
		[Column("LEAF_ID"                                              )] public string?   LeafId       { get; set; } // varchar(36)
	}
}
