﻿using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    public interface ISqlBuilder
    {
        /// <summary>
        /// SQL语句
        /// </summary>
        string SqlString { get; }

        /// <summary>
        /// SQL语句变量
        /// </summary>
        IDataParameter[] Parameters { get; }

        /// <summary>
        /// 能否执行
        /// </summary>
        bool CanExecute { get; }
        
        /// <summary>
        /// 命令超时时间
        /// </summary>
        int CommandTimeout { get; }

        /// <summary>
        /// 创建DB连接
        /// </summary>
        /// <returns></returns>
        DbConnection CreateConnection();
    }

    public interface ISqlBuilder<T> : ISqlBuilder
    {
    }
}