<template>
  <t-row :gutter="[16, 16]" class="row-container">
    <!-- 左侧成功和错误统计 -->
    <t-col :xs="12" :xl="6" style="height: 100%">
      <t-card :title="'统计数据'" class="dashboard-chart-card" :bordered="false">
        <template #actions>
          <div class="dashboard-chart-title-container">
            <t-date-range-picker
              class="card-date-picker-container"
              theme="primary"
              mode="date"
              format="YYYY-MM-DD"
              v-model="dateRange"
              :default-value="LAST_7_DAYS"
              @change="onCurrencyChange"
            />
          </div>
        </template>
        <div id="errorStatsContainer" class="dashboard-chart-container" :style="{ width: '100%' }" />
      </t-card>
    </t-col>

    <t-col :xs="12" :xl="6">
      <t-card :title="'服务资源'" class="dashboard-chart-card resource-card" :bordered="false">
        <div class="resource-row">
          <div class="resource-stats">
            <div class="resource-item">
              <div class="resource-label">API数量</div>
              <div class="resource-value">{{ resourceStats?.apiCount ?? 0 }}</div>
            </div>
            <div class="resource-item">
              <div class="resource-label">定时任务数量</div>
              <div class="resource-value">{{ resourceStats?.jobCount ?? 0 }}</div>
            </div>
            <div class="resource-item">
              <div class="resource-label">消息队列数量</div>
              <div class="resource-value">{{ resourceStats?.eventCount ?? 0 }}</div>
            </div>
            <div class="resource-item">
              <div class="resource-label">设备测点数量</div>
              <div class="resource-value">{{ resourceStats?.pointCount ?? 0 }}</div>
            </div>
          </div>
        </div>

        <div class="divider"></div>

        <div class="service-response-container">
          <div class="response-title">调用占比</div>
          <div
            id="responseRateContainer"
            class="dashboard-chart-container"
            :style="{ width: '100%', height: `${resizeTime * 215}px` }"
          />
        </div>
      </t-card>
    </t-col>
  </t-row>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core';
import { LineChart, PieChart } from 'echarts/charts';
import { GridComponent, LegendComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { computed, nextTick, onDeactivated, onMounted, ref, watch, onUnmounted } from 'vue';

import { useSettingStore } from '@/store';
import { changeChartsTheme } from '@/utils/color';
import { LAST_7_DAYS } from '@/utils/date';
import { useDashboardService, ServiceResourcesBase } from '@/composables/services/dashboard';

echarts.use([TooltipComponent, LegendComponent, PieChart, GridComponent, LineChart, CanvasRenderer]);

const store = useSettingStore();
const resizeTime = ref(1);

const dateRange = ref<string[]>(LAST_7_DAYS);

const props = defineProps<{
  triggerType: string;
}>();

// 错误统计图表
let errorStatsContainer: HTMLElement;
let errorStatsChart: echarts.ECharts;
const errorStatsChartData = ref<{ xAxis: string[]; success: number[]; fail: number[]; other: number[] }>({
  xAxis: [],
  success: [],
  fail: [],
  other: [],
});

const fetchErrorStatsChartData = async (value: string[]) => {
  // 默认最近7天
  const res = await dashboardService.getChartData(props.triggerType, value[0], value[1]);
  errorStatsChartData.value = {
    xAxis: res.map((item: any) => item.date),
    success: res.map((item: any) => item.success),
    fail: res.map((item: any) => item.fail),
    other: res.map((item: any) => item.other ?? 0),
  };
};

const renderErrorStatsChart = () => {
  if (!errorStatsContainer) {
    errorStatsContainer = document.getElementById('errorStatsContainer');
  }
  errorStatsChart = echarts.init(errorStatsContainer);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    legend: {
      data: ['其他', '失败', '成功'],
      top: 10,
      selected: {
        其他: true,
        失败: true,
        成功: true,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: errorStatsChartData.value.xAxis,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '次数',
      },
    ],
    series: [
      {
        name: '其他',
        type: 'line',
        stack: '总量',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        symbol: 'none',
        color: '#F7BA1E',
        data: errorStatsChartData.value.other,
      },
      {
        name: '失败',
        type: 'line',
        stack: '总量',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        symbol: 'none',
        color: '#FF4D4F',
        data: errorStatsChartData.value.fail,
      },
      {
        name: '成功',
        type: 'line',
        stack: '总量',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        symbol: 'none',
        color: '#65B687',
        data: errorStatsChartData.value.success,
      },
    ],
  };

  errorStatsChart.setOption(option);
};

// 服务调用占比图表
let responseRateContainer: HTMLElement;
let responseRateChart: echarts.ECharts;
const renderResponseRateChart = () => {
  if (!responseRateContainer) {
    responseRateContainer = document.getElementById('responseRateContainer');
  }
  responseRateChart = echarts.init(responseRateContainer);

  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      bottom: 'center',
      left: '0%',
    },
    series: [
      {
        name: '调用占比',
        type: 'pie',
        radius: ['30%', '60%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
          },
        },
        data: resourceStats.value?.rateData ?? [],
      },
    ],
  };

  responseRateChart.setOption(option);
};

const renderCharts = () => {
  renderErrorStatsChart();
  renderResponseRateChart();
};

// chartSize update
const updateContainer = () => {
  if (document.documentElement.clientWidth >= 1400 && document.documentElement.clientWidth < 1920) {
    resizeTime.value = Number((document.documentElement.clientWidth / 2080).toFixed(2));
  } else if (document.documentElement.clientWidth < 1080) {
    resizeTime.value = Number((document.documentElement.clientWidth / 1080).toFixed(2));
  } else {
    resizeTime.value = 1;
  }

  errorStatsChart.resize({
    width: errorStatsContainer.clientWidth,
    height: resizeTime.value * 300,
  });

  responseRateChart.resize({
    width: responseRateContainer.clientWidth,
    height: resizeTime.value * 215,
  });
};

const dashboardService = useDashboardService();
const resourceStats = ref<ServiceResourcesBase['base'] | undefined>(undefined);
let timer: number | undefined;

const fetchResourceStats = async () => {
  const res = await dashboardService.getServiceResources(props.triggerType);
  resourceStats.value = res.base ?? {
    apiCount: 0,
    jobCount: 0,
    eventCount: 0,
    pointCount: 0,
    rateData: [],
  };
  renderCharts();
};

watch(
  () => props.triggerType,
  () => {
    onCurrencyChange(dateRange.value);
  },
);

onMounted(() => {
  renderCharts();
  nextTick(() => {
    updateContainer();
  });
  fetchResourceStats();
  fetchErrorStatsChartData(dateRange.value);
  timer = window.setInterval(fetchResourceStats, 5 * 60 * 1000);
});

const { width, height } = useWindowSize();
watch([width, height], () => {
  updateContainer();
});

onDeactivated(() => {
  storeModeWatch();
  storeBrandThemeWatch();
  storeSidebarCompactWatch();
});

const storeBrandThemeWatch = watch(
  () => store.brandTheme,
  () => {
    changeChartsTheme([errorStatsChart, responseRateChart]);
  },
);

const storeSidebarCompactWatch = watch(
  () => store.isSidebarCompact,
  () => {
    if (store.isSidebarCompact) {
      nextTick(() => {
        updateContainer();
      });
    } else {
      setTimeout(() => {
        updateContainer();
      }, 180);
    }
  },
);

const storeModeWatch = watch(
  () => store.mode,
  () => {
    [errorStatsChart, responseRateChart].forEach((item) => {
      item.dispose();
    });

    renderCharts();
  },
);

const onCurrencyChange = async (value: string[]) => {
  await fetchErrorStatsChartData(value);
  renderErrorStatsChart();
};

onUnmounted(() => {
  if (timer) clearInterval(timer);
});
</script>

<style lang="less" scoped>
.row-container {
}

.dashboard-chart {
  &-title-container {
    display: flex;
    justify-content: space-between;
    padding: 0 var(--td-comp-paddingLR-xl);
  }

  &-card {
    position: relative;
    margin-bottom: 0;
    padding: var(--td-comp-paddingTB-m) 0;
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.t-card__header) {
      padding: 0 var(--td-comp-paddingLR-xl);
    }

    :deep(.t-card__title) {
      font: var(--td-font-title-large);
      font-weight: 400;
    }

    :deep(.t-card__subtitle) {
      margin-left: var(--td-comp-margin-s);
      font: var(--td-font-body-small);
      color: var(--td-text-color-secondary);
    }

    :deep(.t-card__body) {
      width: 100%;
      height: calc(100% - 60px);
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    :deep(.t-card__footer) {
      display: flex;
      justify-content: flex-end;
      padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-xl);
      padding-top: 0;

      .t-button {
        padding: 0;
      }
    }
  }

  &-container {
    margin: 0 auto;
    flex: 1;
  }
}

.card-date-picker-container {
  :deep(.t-button) {
    margin: 0;
    padding: 0 8px;
  }
}

.resource-card {
  :deep(.t-card__body) {
    padding: 10px 20px;
  }
}

.resource-row {
  flex: 0 0 auto;
}

.resource-stats {
  display: flex;
  justify-content: space-between;
  padding: 0;
}

.resource-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px;
}

.resource-label {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  margin-bottom: 4px;
}

.resource-value {
  font-size: 24px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.divider {
  height: 1px;
  background-color: var(--td-component-stroke);
  margin: 10px 0;
}

.service-response-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.response-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--td-text-color-primary);
  margin-bottom: 10px;
  padding: 0;
}
</style>
