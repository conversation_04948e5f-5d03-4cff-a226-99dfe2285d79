<template>
  <t-dialog
    v-model:visible="showModal"
    :header="title"
    width="50%"
    :confirm-on-enter="true"
    :on-close="close"
    :on-confirm="onConfirmAnother"
  >
    <t-space direction="vertical" style="width: 100%">
      <t-form
        ref="form"
        layout="inline"
        label-align="left"
        :rules="FORM_RULES"
        :data="formData"
        :colon="true"
        @reset="onReset"
        @submit="onSubmit"
      >
        <t-form-item label="环境名称" name="environmentName">
          <t-input v-model="formData.environmentName" placeholder="请输入环境名称" @enter="onEnter"></t-input>
        </t-form-item>

        <t-form-item label="环境类型" name="environmentType">
          <t-select v-model="formData.environmentType" class="demo-select-base" clearable>
            <t-option v-for="(item, index) in typeOptions" :key="index" :value="item.value" :label="item.label">
              {{ item.label }}
            </t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="环境状态" name="state">
          <t-radio-group v-model="formData.state">
            <t-radio :value="1">启用</t-radio>
            <t-radio :value="0">停用</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="服务地址" name="serviceAddress">
          <t-input v-model="formData.serviceAddress" placeholder="请输入服务地址" @enter="onEnter"></t-input>
        </t-form-item>
        <t-form-item label="服务ID" name="serviceId">
          <t-input v-model="formData.serviceId" placeholder="请输入服务ID" @enter="onEnter"></t-input>
        </t-form-item>
        <t-form-item label="服务Secret" name="serviceSecret">
          <t-input v-model="formData.serviceSecret" placeholder="请输入服务Secret" @enter="onEnter"></t-input>
        </t-form-item>
        <t-form-item label="标签" name="tag">
          <t-input v-model="formData.tag" placeholder="请输入标签" @enter="onEnter"></t-input>
        </t-form-item>
        <t-form-item label="描述" name="description">
          <t-textarea v-model="formData.description" placeholder="请输入描述" clearable />
        </t-form-item>
      </t-form>
    </t-space>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'AddModal',
};
</script>
<script lang="ts" setup>
import { FormInstanceFunctions, FormProps, InputProps, MessagePlugin, Title } from 'tdesign-vue-next';
import { computed, reactive, ref, toRef,toRefs, watch, watchEffect } from 'vue';

import { api, Services } from '@/api/system';

const props = defineProps<{
  visible: boolean;
  title: string;
  copyData: object;
}>();
const formData = toRef(props, 'copyData');
watchEffect(() => {
  formData.value = props.copyData;
});

const emits = defineEmits(['update:visible', 'updataList']);
const typeOptions = [
  {
    value: 'PRD',
    label: '正式环境',
  },
  {
    value: 'OTHER',
    label: '其他',
  },
];
const showModal = computed({
  get() {
    return props.visible;
  },
  set(val) {
    emits('update:visible', val);
  },
});
const FORM_RULES: FormProps['rules'] = {
  environmentName: [
    {
      required: true,
      message: '环境名称不能为空',
    },
  ],
  environmentType: [
    {
      required: true,
      message: '环境类型不能为空',
    },
  ],
  state: [
    {
      required: true,
      message: '环境类型不能为空',
    },
  ],
  serviceAddress: [
    {
      required: true,
      message: '服务地址不能为空',
    },
  ],
};

const form = ref<FormInstanceFunctions>(null);
const onReset: FormProps['onReset'] = () => {
  // MessagePlugin.success('重置成功');
};
const onSubmit: FormProps['onSubmit'] = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    MessagePlugin.success('提交成功');
  } else {
    console.log('Validate Errors: ', firstError, validateResult);
    MessagePlugin.warning(firstError);
  }
};

const submitForm = async () => {
  form.value.validate({ showErrorMessage: true }).then((validateResult) => {
    if (validateResult && Object.keys(validateResult).length) { /* empty */ } else {
      const paramsObj = { ...formData.value };
      if (props.title.includes('复制')) {
        delete paramsObj.id;
      }
      api.run(props.title.includes('编辑') ? Services.publishUpdate : Services.publishAdd, paramsObj).then(() => {
        MessagePlugin.success('发布成功');
        showModal.value = false;
        resetForm();
        emits('updataList');
      });
    }
  });
};

const resetForm = () => {
  form.value.reset();
};

const onEnter: InputProps['onEnter'] = (_, { e }) => {
  e.preventDefault();
};
const close = (context) => {
  console.log('关闭弹窗，点击关闭按钮、按下ESC、点击蒙层等触发', context);
  resetForm();
};
const onConfirmAnother = (context) => {
  console.log('点击了确认按钮', context);
  submitForm();
};
</script>
<style lang="less" scoped>
.t-form__controls-content {
  width: 200px;
}
</style>