using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20250220001000, "添加设备管理相关表")]
    public class AddEquipmentTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_IOT_DRIVER").WithDescription("设备驱动参数配置")
              .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
              .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
              .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
              .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
              .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
              .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
              .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
              .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

              .WithColumn("DRIVER_CODE").AsAnsiString(50).WithColumnDescription("驱动编码")
              .WithColumn("EQUIPMENT_ID").AsAnsiString(36).Nullable().WithColumnDescription("设备ID，为空时表示驱动全局参数")
              .WithColumn("PARAM_KEY").AsAnsiString(50).WithColumnDescription("参数键")
              .WithColumn("PARAM_VALUE").AsAnsiString(200).Nullable().WithColumnDescription("参数值")
              .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
              ;

            Create.Table("LC_IOT_EQUIPMENT").WithDescription("设备定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("ORG_CODE").AsAnsiString(50).WithColumnDescription("组织编码")
               .WithColumn("EQUIPMENT_CODE").AsAnsiString(50).WithColumnDescription("设备编码")
               .WithColumn("EQUIPMENT_NAME").AsAnsiString(80).WithColumnDescription("设备名称")
               .WithColumn("DESCRIPTION").AsString().Nullable().WithColumnDescription("描述")
               .WithColumn("EQUIPMENT_TYPE").AsAnsiString(80).Nullable().WithColumnDescription("设备类型")
               .WithColumn("STATUS").AsInt16().WithDefaultValue(0).WithColumnDescription("状态，0-未激活，1-激活")
               .WithColumn("DRIVER_CODE").AsAnsiString(50).Nullable().WithColumnDescription("驱动编码")
               ;

            Create.Table("LC_IOT_EQUIPMENT_VARIABLE").WithDescription("设备变量定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("EQUIPMENT_ID").AsAnsiString(36).WithColumnDescription("设备ID")
               .WithColumn("VAR_NAME").AsAnsiString(80).WithColumnDescription("变量名称")
               .WithColumn("METHOD").AsAnsiString(80).WithColumnDescription("变量读写方法")
               .WithColumn("ADDRESS").AsAnsiString(200).Nullable().WithColumnDescription("变量地址")
               .WithColumn("EXPRESSIONS").AsAnsiString(200).Nullable().WithColumnDescription("表达式")
               .WithColumn("DATA_TYPE").AsAnsiString(50).WithColumnDescription("数据类型")

               .WithColumn("ARCHIVE_PERIOD").AsInt32().Nullable().WithColumnDescription("数据归档周期（毫秒）")
               .WithColumn("CHANGE_THRESHOLD").AsDouble().Nullable().WithColumnDescription("变化上传阈值（百分比，0-100）")
               .WithColumn("IS_UPLOAD").AsInt16().WithDefaultValue(1).WithColumnDescription("是否上传")

               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")

               ;

            Create.Index("LC_IOT_DRIVER_IDX")
                .OnTable("LC_IOT_DRIVER")
                .OnColumn("DRIVER_CODE").Ascending()
                .OnColumn("EQUIPMENT_ID").Ascending()
                .OnColumn("PARAM_KEY").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .WithOptions().Unique();

            Create.Index("LC_IOT_EQUIPMENT_CODE_IDX")
                .OnTable("LC_IOT_EQUIPMENT")
                .OnColumn("EQUIPMENT_CODE").Ascending()
                .OnColumn("ORG_CODE").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .WithOptions().Unique();

            Create.Index("LC_IOT_EQUIPMENT_VARIABLE_CODE_IDX")
                .OnTable("LC_IOT_EQUIPMENT_VARIABLE")
                .OnColumn("EQUIPMENT_ID").Ascending()
                .OnColumn("VAR_NAME").Ascending()
                .WithOptions().Unique();
        }

        public override void Down()
        {
            Delete.Table("LC_IOT_DRIVER");
            Delete.Table("LC_IOT_EQUIPMENT");
            Delete.Table("LC_IOT_EQUIPMENT_VARIABLE");
        }
    }
}