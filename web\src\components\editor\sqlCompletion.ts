import * as monaco from 'monaco-editor';
import { language as sqlLanguage } from 'monaco-editor/esm/vs/basic-languages/sql/sql.js';

monaco.languages.registerCompletionItemProvider('sql', {
  provideCompletionItems: (model, position) => {
    const suggestions = [];
    const { lineNumber, column } = position;
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });
    const contents = textBeforePointer.trim().split(/\s+/);

    // eslint-disable-next-line no-unsafe-optional-chaining
    const lastContents = contents[contents?.length - 1]; // 获取最后一段非空字符串
    if (lastContents) {
      const sqlConfigKey = ['builtinFunctions', 'keywords', 'operators'];
      const sqlConfigKeyKind = {
        builtinFunctions: monaco.languages.CompletionItemKind.Function,
        keywords: monaco.languages.CompletionItemKind.Keyword,
        operators: monaco.languages.CompletionItemKind.Operator,
      };
      sqlConfigKey.forEach((key) => {
        sqlLanguage[key].forEach((sql) => {
          suggestions.push({
            label: sql, // 显示的提示内容;默认情况下，这也是选择完成时插入的文本。
            insertText: sql, // 选择此完成时应插入到文档中的字符串或片段
            kind: sqlConfigKeyKind[key], // 设置CompletionItemKind
          });
        });
      });
    }
    return {
      suggestions,
    };
  },
});
