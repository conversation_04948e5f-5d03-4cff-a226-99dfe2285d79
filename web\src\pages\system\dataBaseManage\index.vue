<template>
  <div class="database-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">数据源管理</h1>
          <p class="page-description">
            管理和配置各种数据源连接，支持 MySQL、Oracle、SQL Server、PostgreSQL 等多种数据源
          </p>
        </div>
        <div class="action-section">
          <t-button theme="primary" size="large" @click="onClickNewDataBase">
            <template #icon>
              <add-icon />
            </template>
            新增数据源
          </t-button>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-content">
        <div class="search-form">
          <t-input
            v-model="searchText"
            type="text"
            clearable
            placeholder="输入关键词搜索数据源名称、主机地址或描述"
            style="width: 400px"
            @enter="searchEnter"
          >
            <template #prefixIcon>
              <search-icon />
            </template>
          </t-input>
        </div>
      </div>
    </div>

    <!-- 数据源列表 -->
    <div class="databases-section">
      <div class="section-header">
        <div class="section-title">
          <h3>数据源列表</h3>
          <span class="section-count">共 {{ totalCount }} 个数据源</span>
        </div>
        <div class="section-actions">
          <t-space>
            <t-button-group>
              <t-button :theme="viewMode === 'card' ? 'primary' : 'default'" @click="viewMode = 'card'">
                卡片视图
              </t-button>
              <t-button :theme="viewMode === 'table' ? 'primary' : 'default'" @click="viewMode = 'table'">
                表格视图
              </t-button>
            </t-button-group>
            <t-button theme="default" variant="outline" @click="fetchData">
              <template #icon>
                <refresh-icon />
              </template>
              刷新
            </t-button>
          </t-space>
        </div>
      </div>

      <!-- 卡片布局 -->
      <div v-if="viewMode === 'card'" class="cards-container">
        <div v-if="data.length === 0" class="empty-state">
          <t-empty description="暂无数据源连接，点击上方按钮添加">
            <template #image>
              <data-base-icon size="64px" />
            </template>
          </t-empty>
        </div>
        <div v-else>
          <div v-for="group in data" :key="group.name" class="group-section">
            <div class="group-header">
              <h4 class="group-title">{{ group.name }}</h4>
              <span class="group-count">{{ group.items.length }} 个数据源</span>
            </div>
            <div class="cards-grid">
              <div v-for="item in group.items" :key="item.id" class="database-card">
                <div class="card-header">
                  <div class="database-info">
                    <div class="database-icon" :class="getDatabaseIconClass(item.dataProvider, item.state)">
                      <mysql-logo v-if="item.dataProvider === 'MySql'" />
                      <oracle-logo v-else-if="item.dataProvider === 'Oracle'" />
                      <sql-logo v-else-if="item.dataProvider === 'SQL Server'" />
                      <postgre-sql-logo v-else-if="item.dataProvider === 'PostgreSQL'" />
                      <data-base-icon v-else />
                    </div>
                    <div class="database-details">
                      <div class="database-name" :class="{ disabled: !item.state }">{{ item.dataSourceName }}</div>
                      <div class="database-type">{{ item.dataProvider }}</div>
                    </div>
                  </div>
                  <div class="database-status">
                    <t-tag :theme="item.state ? 'success' : 'danger'" variant="light">
                      {{ item.state ? '启用' : '禁用' }}
                    </t-tag>
                  </div>
                </div>
                <div class="card-content">
                  <div class="database-description">
                    {{ item.description || '暂无描述' }}
                  </div>
                  <div class="database-meta">
                    <div class="meta-row">
                      <div class="meta-item">
                        <span class="meta-label">主机:</span>
                        <span class="meta-value">{{ item.host }}</span>
                      </div>
                      <div v-if="item.port" class="meta-item">
                        <span class="meta-label">端口:</span>
                        <span class="meta-value">{{ item.port }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-actions">
                  <t-switch
                    :value="item.state === 1"
                    @change="(val: boolean) => onStateChange(item, val)"
                    size="small"
                  />
                  <t-button size="small" variant="text" theme="primary" @click="onClickTestConnection(item)"
                    >测试</t-button
                  >
                  <t-button size="small" variant="text" theme="primary" @click="onClickEditForm(item)">编辑</t-button>
                  <t-button size="small" variant="text" theme="primary" @click="onClickMoveForm(item)">移动</t-button>
                  <t-popconfirm content="确认删除该数据源连接吗？" @confirm="() => onClickDelete(item)">
                    <t-button size="small" variant="text" theme="danger">删除</t-button>
                  </t-popconfirm>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格布局 -->
      <div v-else class="table-container">
        <div v-for="group in data" :key="group.name" class="group-table">
          <div class="table-group-header">
            <h4>{{ group.name }}</h4>
            <span class="group-count">{{ group.items.length }} 个数据源</span>
          </div>
          <t-table :data="group.items" :columns="tableColumns" row-key="id" stripe hover size="medium">
            <template #icon="{ row }">
              <div class="table-database-icon" :class="getDatabaseIconClass(row.dataProvider, row.state)">
                <mysql-logo v-if="row.dataProvider === 'MySql'" />
                <oracle-logo v-else-if="row.dataProvider === 'Oracle'" />
                <sql-logo v-else-if="row.dataProvider === 'SQL Server'" />
                <postgre-sql-logo v-else-if="row.dataProvider === 'PostgreSQL'" />
                <data-base-icon v-else />
              </div>
            </template>
            <template #name="{ row }">
              <div class="database-name-cell">
                <div class="name" :class="{ disabled: !row.state }">{{ row.dataSourceName }}</div>
                <div class="type">{{ row.dataProvider }}</div>
              </div>
            </template>
            <template #status="{ row }">
              <t-tag :theme="row.state ? 'success' : 'danger'" variant="light">
                {{ row.state ? '启用' : '禁用' }}
              </t-tag>
            </template>
            <template #actions="{ row }">
              <div class="table-actions">
                <t-switch :value="row.state === 1" @change="(val: boolean) => onStateChange(row, val)" size="small" />
                <t-divider layout="vertical" />
                <t-link theme="primary" size="small" @click="onClickTestConnection(row)">测试</t-link>
                <t-divider layout="vertical" />
                <t-link theme="primary" size="small" @click="onClickEditForm(row)">编辑</t-link>
                <t-divider layout="vertical" />
                <t-link theme="primary" size="small" @click="onClickMoveForm(row)">移动</t-link>
                <t-divider layout="vertical" />
                <t-link theme="danger" size="small" @click="onClickDelete(row)">删除</t-link>
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>

    <!-- 对话框 -->
    <fm-dialog
      :is-visible="isShowForm"
      :is-new-form="isNewForm"
      :template-data="templateData"
      :group-list="groupList"
      :on-close="() => (isShowForm = false)"
      @back="onClickBack"
      @save="onClickSave"
    />

    <t-dialog
      v-model:visible="isShowMoveDialog"
      header="移动数据源分组"
      :on-confirm="onConfirmMove"
      :style="{ width: '600px' }"
    >
      <t-select v-model="targetGroup" placeholder="请选择目标分组">
        <t-option v-for="item in groupList" :key="item" :value="item" :label="item" />
      </t-select>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { MessagePlugin } from 'tdesign-vue-next';
import { onMounted, ref, computed } from 'vue';
import { AddIcon, RefreshIcon, SearchIcon, DataBaseIcon } from 'tdesign-icons-vue-next';

import { api, Services } from '@/api/system';
import MysqlLogo from '@/assets/databases/mysql.svg';
import OracleLogo from '@/assets/databases/Oracle.svg';
import PostgreSqlLogo from '@/assets/databases/postgreSql.svg';
import SqlLogo from '@/assets/databases/SqlServer.svg';

import FmDialog from './formDialog.vue';
import { type IDataSource, type IDataSourceForm } from './types';

// 页面数据
const data = ref<{ name: string; items: IDataSourceForm[] }[]>([]);
const templateData = ref<IDataSourceForm>();
const isNewForm = ref(false);
const isShowForm = ref(false);
const groupList = ref<string[]>([]);
const viewMode = ref('card'); // 'card' | 'table'

// 移动对话框相关
const isShowMoveDialog = ref(false);
const targetGroup = ref('');
const currentMoveItem = ref<IDataSourceForm>();

// 计算总数量
const totalCount = computed(() => {
  return data.value.reduce((total, group) => total + group.items.length, 0);
});

// 表格列配置
const tableColumns = ref([
  {
    colKey: 'icon',
    title: '',
    width: 50,
  },
  {
    colKey: 'name',
    title: '数据源名称',
    width: 200,
  },
  {
    colKey: 'host',
    title: '主机地址',
    width: 150,
  },
  {
    colKey: 'port',
    title: '端口',
    width: 80,
  },
  {
    colKey: 'description',
    title: '描述',
    ellipsis: true,
  },
  {
    colKey: 'status',
    title: '状态',
    width: 80,
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 320,
    fixed: 'right' as const,
  },
]);

// 获取数据源列表
const fetchData = async () => {
  try {
    const res = (await api.run(Services.dataSourceGetAll)) as IDataSource[];
    const groupData = {};
    res.forEach((item: IDataSource) => {
      const group = item.group || '其他';
      if (!groupData[group]) {
        groupData[group] = { name: group, items: [] };
      }
      const processedItem: IDataSourceForm = {
        ...item,
        host: item.serverAddress,
        userName: item.userId,
        port: item.port ? parseInt(item.port, 10) : 0,
        originDataBase: item.database,
      };
      groupData[group].items.push(processedItem);
    });
    data.value = Object.values(groupData);
    groupList.value = Object.keys(groupData);
  } catch (error) {
    MessagePlugin.error('获取数据失败');
  }
};

onMounted(() => {
  fetchData();
});

// 搜索相关
const searchText = ref('');
const searchEnter = debounce(async () => {
  try {
    const res = (await api.run(Services.dataSourceSearch, {
      keyword: searchText.value,
    })) as Record<string, IDataSource[]>;
    const groupData = {};
    Object.entries(res).forEach(([groupName, items]) => {
      groupData[groupName] = {
        name: groupName,
        items: items.map((item: IDataSource) => ({
          ...item,
          host: item.serverAddress,
          userName: item.userId,
          port: item.port ? parseInt(item.port, 10) : 0,
          originDataBase: item.database,
        })),
      };
    });
    data.value = Object.values(groupData);
  } catch (error) {
    MessagePlugin.error('搜索失败');
  }
}, 300);

// 新增数据源
const onClickNewDataBase = () => {
  isNewForm.value = true;
  isShowForm.value = true;
  templateData.value = undefined;
};

// 编辑数据源
const onClickEditForm = async (item: IDataSourceForm) => {
  try {
    const res = await api.run(Services.dataSourceGetById, { id: item.id });
    templateData.value = {
      ...res,
      host: res.serverAddress,
      userName: res.userId,
      port: res.port ? parseInt(res.port, 10) : 0,
      originDataBase: res.database,
    };
    isNewForm.value = false;
    isShowForm.value = true;
  } catch (error) {
    MessagePlugin.error('获取数据源详情失败');
  }
};

// 删除数据源
const onClickDelete = async (item: IDataSourceForm) => {
  try {
    await api.run(Services.dataSourceDelete, { id: item.id });
    MessagePlugin.success('删除成功');
    fetchData();
  } catch (error) {
    MessagePlugin.error('删除失败');
  }
};

// 更新状态
const onStateChange = async (item: IDataSourceForm, value: boolean) => {
  try {
    await api.run(Services.dataSourceChangeState, {
      id: item.id,
      state: value ? 1 : 0,
    });
    item.state = value ? 1 : 0;
    MessagePlugin.success('状态更新成功');
    fetchData();
  } catch (error) {
    MessagePlugin.error('状态更新失败');
  }
};

// 表单操作
const onClickBack = () => {
  isShowForm.value = false;
};

const onClickSave = () => {
  fetchData();
  isShowForm.value = false;
};

// 获取数据源图标类
const getDatabaseIconClass = (dataProvider: string, state: number) => {
  const baseClass = 'database-icon';
  const typeClass =
    {
      MySql: 'mysql-icon',
      Oracle: 'oracle-icon',
      'SQL Server': 'sqlserver-icon',
      PostgreSQL: 'postgresql-icon',
    }[dataProvider] || 'default-icon';

  const stateClass = state ? 'enabled' : 'disabled';
  return `${baseClass} ${typeClass} ${stateClass}`;
};

// 测试数据源连接
const onClickTestConnection = async (item: IDataSourceForm) => {
  try {
    MessagePlugin.loading('正在测试连接...', 0);

    // 使用新的API，只传递数据源ID
    const result = await api.run(Services.dataSourceTestConnectionById, { id: item.id });
    MessagePlugin.closeAll();

    if (result) {
      MessagePlugin.success('数据源连接测试成功！');
    } else {
      MessagePlugin.error('数据源连接测试失败！');
    }
  } catch (error: any) {
    MessagePlugin.closeAll();
    MessagePlugin.error('数据源连接测试失败：' + (error?.message || '未知错误'));
  }
};

const onClickMoveForm = (item: IDataSourceForm) => {
  currentMoveItem.value = item;
  isShowMoveDialog.value = true;
};

const onConfirmMove = async () => {
  if (!currentMoveItem.value || !targetGroup.value) return;

  try {
    await api.run(Services.dataSourceMove, {
      id: currentMoveItem.value.id,
      group: targetGroup.value,
    });
    MessagePlugin.success('移动成功');
    isShowMoveDialog.value = false;
    targetGroup.value = '';
    fetchData();
  } catch (error) {
    MessagePlugin.error('移动失败');
  }
};
</script>

<!-- eslint-disable-next-line vue-scoped-css/enforce-style-type -->
<style lang="less">
.fm-dialog .t-dialog {
  width: auto;
  height: auto;
  padding: 0 0;
}

.searchBox {
  display: flex;
  align-items: center;
  justify-content: center;

  .t-button {
    margin: 0 3px;
  }
}
</style>

<style lang="less" scoped>
.database-manage-page {
  min-height: 100vh;
  background: var(--td-bg-color-page);

  .page-header {
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    margin-bottom: 24px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 32px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }

        .page-description {
          margin: 0;
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  .search-section {
    max-width: 1200px;
    margin: 0 auto 24px auto;
    padding: 0 24px;

    .search-content {
      background: var(--td-bg-color-container);
      border-radius: 6px;
      padding: 16px;

      .search-form {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }

  .databases-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    .section-header {
      background: var(--td-bg-color-container);
      border-radius: 6px 6px 0 0;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--td-border-level-1-color);

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: var(--td-text-color-primary);
        }

        .section-count {
          font-size: 12px;
          color: var(--td-text-color-placeholder);
          background: var(--td-bg-color-component);
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }

    .cards-container {
      background: var(--td-bg-color-container);
      padding: 16px;
      border-radius: 0 0 6px 6px;

      .empty-state {
        padding: 40px 0;
        text-align: center;

        :deep(.t-empty) {
          .t-empty__image {
            margin-bottom: 12px;
            opacity: 0.5;
          }

          .t-empty__description {
            color: var(--td-text-color-placeholder);
            font-size: 14px;
          }
        }
      }

      .group-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .group-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid var(--td-border-level-2-color);

          .group-title {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: var(--td-text-color-primary);
          }

          .group-count {
            font-size: 12px;
            color: var(--td-text-color-placeholder);
            background: var(--td-bg-color-component);
            padding: 2px 8px;
            border-radius: 4px;
          }
        }

        .cards-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
          gap: 16px;
        }
      }

      .database-card {
        background: var(--td-bg-color-container);
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        padding: 16px;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;

        &:hover {
          border-color: var(--td-brand-color);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .database-info {
            display: flex;
            align-items: center;
            flex: 1;

            .database-icon {
              width: 32px;
              height: 32px;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              font-size: 16px;

              &.mysql-icon.enabled {
                background: #e6f7ff;
                color: #1890ff;
              }

              &.oracle-icon.enabled {
                background: #fff2e8;
                color: #fa8c16;
              }

              &.sqlserver-icon.enabled {
                background: #f6ffed;
                color: #52c41a;
              }

              &.postgresql-icon.enabled {
                background: #f0f5ff;
                color: #2f54eb;
              }

              &.default-icon.enabled {
                background: var(--td-bg-color-component);
                color: var(--td-text-color-secondary);
              }

              &.disabled {
                background: #fff1f0;
                color: #ff4d4f;
                opacity: 0.6;
              }
            }

            .database-details {
              flex: 1;

              .database-name {
                font-size: 14px;
                font-weight: 500;
                color: var(--td-text-color-primary);
                margin-bottom: 4px;

                &.disabled {
                  color: var(--td-text-color-disabled);
                }
              }

              .database-type {
                font-size: 12px;
                color: var(--td-text-color-secondary);
              }
            }
          }

          .database-status {
            flex-shrink: 0;
          }
        }

        .card-content {
          flex: 1;
          margin-bottom: 12px;

          .database-description {
            font-size: 12px;
            color: var(--td-text-color-secondary);
            line-height: 1.5;
            margin-bottom: 8px;
            min-height: 18px;
          }

          .database-meta {
            .meta-row {
              display: flex;
              flex-wrap: wrap;
              gap: 12px;

              .meta-item {
                display: flex;
                align-items: center;
                font-size: 12px;

                .meta-label {
                  color: var(--td-text-color-placeholder);
                  margin-right: 4px;
                }

                .meta-value {
                  color: var(--td-text-color-secondary);
                  font-weight: 500;
                }
              }
            }
          }
        }

        .card-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          padding-top: 8px;
          border-top: 1px solid var(--td-border-level-2-color);
        }
      }
    }

    .table-container {
      background: var(--td-bg-color-container);
      border-radius: 0 0 6px 6px;

      .group-table {
        &:not(:last-child) {
          border-bottom: 1px solid var(--td-border-level-1-color);
        }

        .table-group-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          background: var(--td-bg-color-component);
          border-bottom: 1px solid var(--td-border-level-2-color);

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: var(--td-text-color-primary);
          }

          .group-count {
            font-size: 12px;
            color: var(--td-text-color-placeholder);
          }
        }

        .table-database-icon {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;

          &.mysql-icon.enabled {
            background: #e6f7ff;
            color: #1890ff;
          }

          &.oracle-icon.enabled {
            background: #fff2e8;
            color: #fa8c16;
          }

          &.sqlserver-icon.enabled {
            background: #f6ffed;
            color: #52c41a;
          }

          &.postgresql-icon.enabled {
            background: #f0f5ff;
            color: #2f54eb;
          }

          &.default-icon.enabled {
            background: var(--td-bg-color-component);
            color: var(--td-text-color-secondary);
          }

          &.disabled {
            background: #fff1f0;
            color: #ff4d4f;
            opacity: 0.6;
          }
        }

        .database-name-cell {
          .name {
            font-size: 14px;
            font-weight: 500;
            color: var(--td-text-color-primary);
            margin-bottom: 2px;

            &.disabled {
              color: var(--td-text-color-disabled);
            }
          }

          .type {
            font-size: 12px;
            color: var(--td-text-color-secondary);
          }
        }

        .table-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .database-manage-page {
    .page-header .header-content,
    .search-section,
    .databases-section {
      max-width: 100%;
      padding-left: 16px;
      padding-right: 16px;
    }
  }
}

@media (max-width: 768px) {
  .database-manage-page {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .databases-section .cards-container .group-section .cards-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
