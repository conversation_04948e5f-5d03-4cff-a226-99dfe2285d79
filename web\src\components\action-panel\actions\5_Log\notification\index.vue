<template>
  <div class="notification-action">
    <t-form :data="formData" :rules="rules" label-width="100px">
      <t-form-item label="通知通道" name="channelId">
        <t-select
          v-model="formData.channelId"
          placeholder="请选择通知通道"
          :loading="channelsLoading"
          @focus="loadChannels"
        >
          <t-option v-for="channel in channels" :key="channel.Id" :value="channel.Id" :label="channel.Name">
            <div class="channel-option">
              <span class="channel-name">{{ channel.Name }}</span>
              <t-tag size="small" :theme="getChannelTypeTheme(channel.Type)">
                {{ getChannelTypeLabel(channel.Type) }}
              </t-tag>
            </div>
          </t-option>
        </t-select>
      </t-form-item>

      <t-form-item label="通知标题" name="title">
        <variable-input v-model="formData.title" placeholder="请输入通知标题" :variables="variables" />
      </t-form-item>

      <t-form-item label="通知内容" name="content">
        <variable-textarea v-model="formData.content" placeholder="请输入通知内容" :variables="variables" :rows="4" />
      </t-form-item>

      <t-form-item label="异常信息" name="includeException">
        <t-checkbox v-model="formData.includeException">包含异常堆栈信息</t-checkbox>
      </t-form-item>

      <template v-if="formData.includeException">
        <t-form-item label="异常消息" name="exceptionMessage">
          <variable-input v-model="formData.exceptionMessage" placeholder="请输入异常消息" :variables="variables" />
        </t-form-item>

        <t-form-item label="堆栈跟踪" name="stackTrace">
          <variable-textarea
            v-model="formData.stackTrace"
            placeholder="请输入堆栈跟踪信息（可选）"
            :variables="variables"
            :rows="3"
          />
        </t-form-item>
      </template>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { api, Services } from '@/api/system';
import VariableInput from '@/components/common/VariableInput.vue';
import VariableTextarea from '@/components/common/VariableTextarea.vue';

// Props
const props = defineProps<{
  modelValue?: any;
  variables?: any[];
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
}>();

// 响应式数据
const channels = ref([]);
const channelsLoading = ref(false);

// 表单数据
const formData = reactive({
  channelId: '',
  title: '',
  content: '',
  includeException: false,
  exceptionMessage: '',
  stackTrace: '',
});

// 表单验证规则
const rules = {
  channelId: [{ required: true, message: '请选择通知通道', trigger: 'change' }],
  title: [{ required: true, message: '请输入通知标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入通知内容', trigger: 'blur' }],
};

// 生命周期
onMounted(() => {
  if (props.modelValue) {
    Object.assign(formData, props.modelValue);
  }
  loadChannels();
});

// 监听表单数据变化
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue });
  },
  { deep: true },
);

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(formData, newValue);
    }
  },
  { deep: true },
);

// 方法
const loadChannels = async () => {
  if (channels.value.length > 0) return;

  channelsLoading.value = true;
  try {
    const response = await api.run(Services.notificationActionGetAvailableChannels);
    channels.value = response || [];
  } catch (error) {
    console.error('加载通知通道失败:', error);
  } finally {
    channelsLoading.value = false;
  }
};

const getChannelTypeLabel = (type: string) => {
  const typeMap = {
    Email: '邮件',
    Dingtalk: '钉钉',
    Feishu: '飞书',
    Weixin: '企业微信',
  };
  return typeMap[type] || type;
};

const getChannelTypeTheme = (type: string) => {
  const themeMap = {
    Email: 'primary',
    Dingtalk: 'success',
    Feishu: 'warning',
    Weixin: 'danger',
  };
  return themeMap[type] || 'default';
};
</script>

<style scoped>
.notification-action {
  padding: 20px;
}

.channel-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.channel-name {
  flex: 1;
  margin-right: 10px;
}

:deep(.t-form-item) {
  margin-bottom: 20px;
}
</style>
