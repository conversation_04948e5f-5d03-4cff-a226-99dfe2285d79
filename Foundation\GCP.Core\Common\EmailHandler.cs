﻿using FluentEmail.MailKitSmtp;
using GCP.Common;
using Microsoft.Extensions.Configuration;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class EmailHandler
    {
        public static IServiceCollection AddEmailHandler(this IServiceCollection services, IConfiguration configuration)
        {
            var sender = configuration["email:Sender"].ToString();
            if (sender == "SMTP")
            {
                SmtpClientOptions smtp = new SmtpClientOptions
                {
                    Server = configuration["email:SMTP:Host"].ToString(),
                    Port = configuration["email:SMTP:Port"].Parse<int>(),
                    UseSsl = configuration["email:SMTP:EnableSsl"].Parse<bool>(),
                    User = configuration["email:SMTP:Username"].ToString(),
                    Password = configuration["email:SMTP:Password"].ToString(),
                    RequiresAuthentication = true
                };
                smtp.SocketOptions = smtp.UseSsl ? MailKit.Security.SecureSocketOptions.Auto : MailKit.Security.SecureSocketOptions.None;

                services.AddFluentEmail(configuration["email:DefaultFromEmail"].ToString(), configuration["email:DefaultFromName"].ToString())
                    .AddMailKitSender(smtp);
            }

            return services;
        }
    }
}
