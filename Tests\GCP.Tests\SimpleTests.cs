using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.Common;

namespace GCP.Tests
{
    /// <summary>
    /// 简化的测试示例 - 验证基本功能
    /// </summary>
    public class SimpleTests : TestBase
    {
        public SimpleTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public void TestDataBuilder_CreateTestApi_ShouldWork()
        {
            // Arrange & Act
            var api = TestDataBuilder.CreateTestApi("测试API");

            // Assert
            api.Should().NotBeNull();
            api.ApiName.Should().Be("测试API");
            api.Id.Should().NotBeNullOrEmpty();
            api.State.Should().Be(1);
        }

        [Fact]
        public void TestDataBuilder_CreateTestDataSource_ShouldWork()
        {
            // Arrange & Act
            var dataSource = TestDataBuilder.CreateTestDataSource("测试数据源");

            // Assert
            dataSource.Should().NotBeNull();
            dataSource.DataSourceName.Should().Be("测试数据源");
            dataSource.Id.Should().NotBeNullOrEmpty();
            dataSource.State.Should().Be(1);
        }

        [Fact]
        public void TestDataBuilder_CreateTestIotEquipment_ShouldWork()
        {
            // Arrange & Act
            var equipment = TestDataBuilder.CreateTestIotEquipment("测试设备");

            // Assert
            equipment.Should().NotBeNull();
            equipment.EquipmentName.Should().Be("测试设备");
            equipment.Id.Should().NotBeNullOrEmpty();
            equipment.State.Should().Be(1);
        }

        [Fact]
        public void TestDataBuilder_CreateTestProject_ShouldWork()
        {
            // Arrange & Act
            var project = TestDataBuilder.CreateTestProject("测试项目");

            // Assert
            project.Should().NotBeNull();
            project.ProjectName.Should().Be("测试项目");
            project.Id.Should().NotBeNullOrEmpty();
            project.State.Should().Be(1);
        }

        [Fact]
        public void TestDataBuilder_CreateTestSolution_ShouldWork()
        {
            // Arrange & Act
            var solution = TestDataBuilder.CreateTestSolution("测试解决方案");

            // Assert
            solution.Should().NotBeNull();
            solution.SolutionName.Should().Be("测试解决方案");
            solution.Id.Should().NotBeNullOrEmpty();
            solution.State.Should().Be(1);
        }

        [Fact]
        public void TestDataBuilder_CreateTestUser_ShouldWork()
        {
            // Arrange & Act
            var user = TestDataBuilder.CreateTestUser("test_user");

            // Assert
            user.Should().NotBeNull();
            user.UserName.Should().Be("test_user");
            user.Id.Should().NotBeNullOrEmpty();
            user.State.Should().Be(1);
        }

        [Fact]
        public void TUID_NewTUID_ShouldGenerateUniqueId()
        {
            // Arrange & Act
            var id1 = TUID.NewTUID().ToString();
            var id2 = TUID.NewTUID().ToString();

            // Assert
            id1.Should().NotBeNullOrEmpty();
            id2.Should().NotBeNullOrEmpty();
            id1.Should().NotBe(id2, "每次生成的ID应该是唯一的");
        }

        [Fact]
        public void ServiceLocator_ShouldBeConfigured()
        {
            // Arrange & Act
            var serviceLocator = GetOptionalService<ServiceLocator>();

            // Assert
            serviceLocator.Should().NotBeNull("ServiceLocator应该被正确配置");
        }

        [Fact]
        public void Configuration_ShouldBeAccessible()
        {
            // Arrange & Act
            var testSettingsSection = Configuration.GetSection("TestSettings");
            var useInMemoryDbValue = testSettingsSection["UseInMemoryDatabase"];
            var mockExternalServicesValue = testSettingsSection["MockExternalServices"];

            // Assert
            Configuration.Should().NotBeNull("配置对象应该可用");

            // 验证配置可以被读取（不管具体值是什么）
            var connectionStrings = Configuration.GetSection("ConnectionStrings");
            connectionStrings.Should().NotBeNull("连接字符串配置节应该存在");

            Output.WriteLine($"UseInMemoryDatabase: {useInMemoryDbValue ?? "null"}");
            Output.WriteLine($"MockExternalServices: {mockExternalServicesValue ?? "null"}");
            Output.WriteLine("配置测试完成");
        }

        [Fact]
        public void Logger_ShouldBeAvailable()
        {
            // Arrange & Act
            var logger = GetOptionalService<Microsoft.Extensions.Logging.ILogger<SimpleTests>>();

            // Assert
            logger.Should().NotBeNull("日志服务应该可用");
            
            // 测试日志输出
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "这是一条测试日志消息");
            Output.WriteLine("日志测试完成");
        }

        [Fact]
        public void DateTime_ShouldWorkCorrectly()
        {
            // Arrange
            var startTime = DateTime.Now;

            // Act
            System.Threading.Thread.Sleep(10); // 等待10毫秒
            var endTime = DateTime.Now;

            // Assert
            endTime.Should().BeAfter(startTime, "结束时间应该晚于开始时间");
            (endTime - startTime).Should().BeGreaterThan(TimeSpan.Zero, "时间差应该大于零");
        }

        [Fact]
        public void String_Operations_ShouldWork()
        {
            // Arrange
            var testString = "GCP测试系统";

            // Act & Assert
            testString.Should().NotBeNullOrEmpty();
            testString.Should().Contain("GCP");
            testString.Should().Contain("测试");
            testString.Should().StartWith("GCP");
            testString.Should().EndWith("系统");
        }

        [Fact]
        public void Collections_ShouldWork()
        {
            // Arrange
            var testList = new List<string> { "API", "工作流", "IoT", "事件" };

            // Act & Assert
            testList.Should().HaveCount(4);
            testList.Should().Contain("API");
            testList.Should().Contain("工作流");
            testList.Should().NotContain("未知模块");
        }

        [Fact]
        public void Dictionary_ShouldWork()
        {
            // Arrange
            var testDict = new Dictionary<string, object>
            {
                { "name", "测试" },
                { "count", 100 },
                { "enabled", true }
            };

            // Act & Assert
            testDict.Should().HaveCount(3);
            testDict.Should().ContainKey("name");
            testDict.Should().ContainValue("测试");
            testDict["count"].Should().Be(100);
            testDict["enabled"].Should().Be(true);
        }

        [Fact]
        public async Task Async_Operations_ShouldWork()
        {
            // Arrange
            var startTime = DateTime.Now;

            // Act
            await Task.Delay(50); // 异步等待50毫秒
            var endTime = DateTime.Now;

            // Assert
            endTime.Should().BeAfter(startTime);
            (endTime - startTime).Should().BeGreaterThan(TimeSpan.FromMilliseconds(40));
        }

        [Fact]
        public void Exception_Handling_ShouldWork()
        {
            // Arrange & Act & Assert
            Action action = () => throw new InvalidOperationException("测试异常");
            
            action.Should().Throw<InvalidOperationException>()
                .WithMessage("测试异常");
        }

        [Fact]
        public void CustomException_ShouldWork()
        {
            // Arrange & Act & Assert
            Action action = () => throw new CustomException("自定义测试异常");
            
            action.Should().Throw<CustomException>()
                .WithMessage("自定义测试异常");
        }

        [Fact]
        public void Null_Checks_ShouldWork()
        {
            // Arrange
            string? nullString = null;
            string nonNullString = "非空字符串";

            // Act & Assert
            nullString.Should().BeNull();
            nonNullString.Should().NotBeNull();
            nonNullString.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public void Numeric_Operations_ShouldWork()
        {
            // Arrange
            var a = 10;
            var b = 20;

            // Act
            var sum = a + b;
            var product = a * b;

            // Assert
            sum.Should().Be(30);
            product.Should().Be(200);
            a.Should().BeLessThan(b);
            b.Should().BeGreaterThan(a);
        }

        [Fact]
        public void Boolean_Logic_ShouldWork()
        {
            // Arrange
            var isTrue = true;
            var isFalse = false;

            // Act & Assert
            isTrue.Should().BeTrue();
            isFalse.Should().BeFalse();
            (isTrue && !isFalse).Should().BeTrue();
            (isTrue || isFalse).Should().BeTrue();
        }
    }
}
