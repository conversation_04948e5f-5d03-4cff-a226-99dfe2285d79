﻿using System.Data.Common;

namespace GCP.DataAccess
{
    internal class MySqlConnectorProvider : BaseDbProvider
    {
        public override string ProviderName
        {
            get
            {
                return "MySqlConnector";
            }
        }

        public override bool NotUseParameter
        {
            get
            {
                return false;
            }
        }

        public override DbProviderFactory Factory => MySqlConnector.MySqlConnectorFactory.Instance;

        public override DbProviderType ProviderType => DbProviderType.MySql;

        public override string GetParameterName(string parameterName)
        {
            return parameterName[0] == '@' ? parameterName : "@" + parameterName;
        }

        public override string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "")
        {
            return string.Format("SELECT t.* FROM ({2}) as t {3} LIMIT {0},{1}", startIndex, length, selectSql, orderBySql);
        }

        public override string GetTimeFormatStr(DateTime time)
        {
            return "'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }

        public override string GetTimeSql()
        {
            return "select current_timestamp()";
        }

        public override string GetTableSizeSql(string tableName, string schemaName = null)
        {
            var sql = @"
select
    table_name,
    truncate(data_length / 1024 / 1024, 2) as TABLE_SIZE
from
    information_schema.tables
where 1=1
";
            if (!string.IsNullOrEmpty(schemaName))
            {
                sql += " and table_schema = '" + schemaName.Replace("'", "''") + "'";
            }
            if (!string.IsNullOrEmpty(tableName))
            {
                sql += " and table_name = '" + tableName.Replace("'", "''") + "'";
            }
            return sql;
        }
    }
}
