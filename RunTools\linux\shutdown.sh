#!/bin/bash

# 定义默认端口号列表，如果多个端口空格隔开，如：6981 8080 8081
default_ports=(6981)
echo "寻找端口进程: ${default_ports[*]}"

# 检查是否有传入参数，如果没有则使用默认端口号列表
if [ $# -eq 0 ]; then
    ports=("${default_ports[@]}")
else
    ports=("$@")
fi

# 根据传入的端口号kill对应的进程
for port in "${ports[@]}"; do
    process_id=$(lsof -t -i:$port)
    if [ -n "$process_id" ]; then
        echo "找到占用端口 $port 的进程，进程ID为：$process_id"
        kill -SIGTERM $process_id
        echo "发送关闭信号，并等待3秒后检查进程状态"
        sleep 3

        # 检查进程是否已经终止
        if ps -p $process_id > /dev/null; then
            echo "进程 $process_id 仍在运行，发送 SIGKILL 强制关闭进程"
            kill -SIGKILL $process_id
        else
            echo "进程 $process_id 已经自行终止"
        fi
    else
        echo "未找到占用端口 $port 的进程"
    fi
done

