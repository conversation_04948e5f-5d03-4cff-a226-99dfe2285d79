import { api, Services } from '@/api/system';

// 定义消息事件模型
export interface LcMessageEvent {
  id?: string;
  dirCode?: string;
  eventName: string;
  eventType?: number;
  sourceType: number;
  isEnabled: number;
  description?: string;
  functionId?: string;
  settings?: string;
}

// 定义消息事件映射关系模型
export interface LcMessageEventMapping {
  id?: string;
  eventId: string;
  sourceId: string;
  sourceCode?: string;
}

export const useMessageEventService = () => {
  return {
    // 获取事件详情
    getById: (id: string) => {
      return api.run(Services.messageEventGetById, { id });
    },

    // 获取事件清单
    getAll: (keyword?: string, pageIndex: number = 1, pageSize: number = 20) => {
      return api.run(Services.messageEventGetAll, { keyword, pageIndex, pageSize });
    },

    // 获取目录下的事件列表
    getByDirCode: (dirId: string) => {
      return api.run(Services.messageEventGetByDirCode, { dirId });
    },

    // 新增事件
    add: (evt: LcMessageEvent) => {
      return api.run(Services.messageEventAdd, evt);
    },

    // 更新事件
    update: (evt: LcMessageEvent) => {
      return api.run(Services.messageEventUpdate, evt);
    },

    // 删除事件
    delete: (id: string) => {
      return api.run(Services.messageEventDelete, { id });
    },

    // 更新事件状态
    updateState: (ids: string[], isEnabled: number) => {
      return api.run(Services.messageEventUpdateState, { ids, isEnabled });
    },

    // 获取事件映射关系
    getMappings: (eventId: string) => {
      return api.run(Services.messageEventGetMappings, { eventId });
    },

    // 新增事件映射
    addMapping: (mapping: LcMessageEventMapping) => {
      return api.run(Services.messageEventAddMapping, mapping);
    },

    // 更新事件映射
    updateMapping: (mapping: LcMessageEventMapping) => {
      return api.run(Services.messageEventUpdateMapping, mapping);
    },

    // 删除事件映射
    deleteMapping: (id: string) => {
      return api.run(Services.messageEventDeleteMapping, { id });
    },
  };
};
