﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20230619000001, "新增动态网关")]
    public class AddProxyTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_API_CLUSTER").WithDescription("API网关集群定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("LOAD_BALANCING_POLICY").AsAnsiString(50).Nullable().WithColumnDescription("负载均衡策略")
               .WithColumn("SERVERS").AsAnsiString(500).Nullable().WithColumnDescription("目标服务器, 支持多个逗号分隔")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               ;

            Create.Table("LC_SERVER").WithDescription("服务器定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")
               
               .WithColumn("HOST").AsAnsiString(200).Nullable().WithColumnDescription("服务器主机")
               .WithColumn("ADDRESS").AsAnsiString(200).Nullable().WithColumnDescription("服务器地址")
               .WithColumn("HEALTH").AsAnsiString(200).Nullable().WithColumnDescription("健康检查地址")
               ;
        }

        public override void Down()
        {
            Delete.Table("LC_API_CLUSTER");
            Delete.Table("LC_SERVER");
        }
    }
}
