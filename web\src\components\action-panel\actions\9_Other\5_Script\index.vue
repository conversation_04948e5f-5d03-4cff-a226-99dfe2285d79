<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <script-editor-panel v-model:script="formData.script" style="margin-top: 8px"></script-editor-panel>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'BranchActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { watch } from 'vue';

import ScriptEditorPanel from '@/components/action-panel/ScriptEditorPanel.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { useDataBranchStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataStore = useDataBranchStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataStore.updateState();
  },
  {
    immediate: true,
  },
);
const { args: formData } = storeToRefs(dataStore);

watch(
  () => formData.value,
  (newValue) => {
    dataStore.setArgs(newValue);
  },
  {
    deep: true,
  },
);
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}
</style>
