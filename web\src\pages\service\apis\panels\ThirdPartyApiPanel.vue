<template>
  <cmp-row>
    <cmp-card size="small" :span="4" bordered title="第三方API">
      <api-list :data="apiDataList" @add="onAddApi" @click="onApiClick"></api-list>
    </cmp-card>
    <cmp-card :span="8" bordered>
      <third-party-api-info @save="handleSaveApi" @delete="handleDeleteApi"></third-party-api-info>
    </cmp-card>
  </cmp-row>
</template>
<script lang="ts">
export default {
  name: 'ThirdPartyApiPanel',
};
</script>
<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { api, Services } from '@/api/system';

import ApiList from '../components/ApiList.vue';
import ThirdPartyApiInfo from '../components/ThirdPartyApiInfo.vue';
import { ApiInfoDto, useApiStore } from '../store';

const apiStore = useApiStore();
onMounted(() => {
  fetchApiDataList();
});
const apiDataList = ref<ApiInfoDto[]>([]);
const fetchApiDataList = () => {
  api.run(Services.apiGetAll, { apiType: 2 }).then((data) => {
    apiDataList.value = data;
  });
};
const onAddApi = () => {
  if (apiDataList.value.length > 0 && apiDataList.value.some((item) => item.id === null)) return;
  const newApi = {
    id: null,
    state: 1,
    apiType: 2,
    requestType: 'GET',
    httpUrl: '',
    apiName: '接口名称 *',
  } as ApiInfoDto;
  apiDataList.value.push(newApi);
  apiStore.setCurrentThirdPartyApi(newApi);
};

const onApiClick = (apiInfo) => {
  apiStore.setCurrentThirdPartyApi(apiInfo);
};

const handleSaveApi = async (apiInfo: ApiInfoDto) => {
  apiInfo.apiType = 2;
  await apiStore.saveApi(apiInfo);
  fetchApiDataList();
};

const handleDeleteApi = async (id) => {
  await apiStore.deleteApi(id);
  fetchApiDataList();
};
</script>
<style lang="less" scoped></style>
