using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.FunctionPool.Flow.Services;
using GCP.FunctionPool.Flow.Models;
using GCP.DataAccess;
using GCP.Common;
using Microsoft.Extensions.DependencyInjection;
using EasyCaching.Core;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 缓存动作测试类
    /// </summary>
    public class CacheActionTests : DatabaseTestBase
    {
        public CacheActionTests(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<DataCache>();
            
            // 配置内存缓存用于测试
            services.AddEasyCaching(options =>
            {
                options.UseInMemory("test-cache");
            });
        }

        #region 缓存写入测试

        [Fact]
        public async Task CacheWrite_WithValidData_ShouldWriteSuccessfully()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            var writeData = new DataCacheWrite
            {
                Name = "测试缓存写入",
                Description = "写入测试数据",
                CacheKey = new DataValue { Type = "text", TextValue = "test_key_001" },
                CacheValue = new DataValue { Type = "text", TextValue = "test_value_001" },
                ExpirationSeconds = new DataValue { Type = "text", TextValue = "300" }
            };

            // Act
            await dataCache.WriteCache(writeData);

            // Assert
            var cache = GetService<IEasyCachingProvider>();
            var cachedValue = await cache.GetAsync<object>("Project:test-project-001:Cache:test_key_001");
            cachedValue.HasValue.Should().BeTrue("缓存应该写入成功");
            cachedValue.Value.Should().Be("test_value_001", "缓存值应该匹配");
            
            Output.WriteLine($"缓存写入成功: {cachedValue.Value}");
        }

        [Fact]
        public async Task CacheWrite_WithoutExpiration_ShouldWriteWithLongExpiration()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            var writeData = new DataCacheWrite
            {
                Name = "测试永久缓存",
                Description = "写入永久缓存数据",
                CacheKey = new DataValue { Type = "text", TextValue = "permanent_key" },
                CacheValue = new DataValue { Type = "text", TextValue = "permanent_value" },
                ExpirationSeconds = null // 不设置过期时间
            };

            // Act
            await dataCache.WriteCache(writeData);

            // Assert
            var cache = GetService<IEasyCachingProvider>();
            var cachedValue = await cache.GetAsync<object>("Project:test-project-001:Cache:permanent_key");
            cachedValue.HasValue.Should().BeTrue("永久缓存应该写入成功");
            cachedValue.Value.Should().Be("permanent_value", "缓存值应该匹配");
        }

        [Fact]
        public async Task CacheWrite_WithEmptyKey_ShouldThrowException()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            var writeData = new DataCacheWrite
            {
                Name = "测试空键",
                Description = "测试空缓存键",
                CacheKey = new DataValue { Type = "text", TextValue = "" },
                CacheValue = new DataValue { Type = "text", TextValue = "some_value" }
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<CustomException>(() => dataCache.WriteCache(writeData));
            exception.Message.Should().Contain("缓存键不能为空");
        }

        #endregion

        #region 缓存读取测试

        [Fact]
        public async Task CacheRead_WithExistingKey_ShouldReturnValue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            // 先写入缓存
            var cache = GetService<IEasyCachingProvider>();
            await cache.SetAsync("Project:test-project-001:Cache:existing_key", "existing_value", TimeSpan.FromMinutes(5));

            var readData = new DataCacheRead
            {
                Name = "测试缓存读取",
                Description = "读取已存在的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "existing_key" },
                DefaultValue = new DataValue { Type = "text", TextValue = "default_value" }
            };

            // Act
            var result = await dataCache.ReadCache(readData);

            // Assert
            result.Should().Be("existing_value", "应该返回缓存中的值");
            Output.WriteLine($"缓存读取成功: {result}");
        }

        [Fact]
        public async Task CacheRead_WithNonExistingKey_ShouldReturnDefaultValue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            var readData = new DataCacheRead
            {
                Name = "测试缓存读取",
                Description = "读取不存在的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "non_existing_key" },
                DefaultValue = new DataValue { Type = "text", TextValue = "default_value" }
            };

            // Act
            var result = await dataCache.ReadCache(readData);

            // Assert
            result.Should().Be("default_value", "应该返回默认值");
            Output.WriteLine($"返回默认值: {result}");
        }

        #endregion

        #region 缓存删除测试

        [Fact]
        public async Task CacheRemove_WithExistingKey_ShouldRemoveSuccessfully()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            // 先写入缓存
            var cache = GetService<IEasyCachingProvider>();
            await cache.SetAsync("Project:test-project-001:Cache:remove_key", "remove_value", TimeSpan.FromMinutes(5));

            var removeData = new DataCacheRemove
            {
                Name = "测试缓存删除",
                Description = "删除已存在的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "remove_key" }
            };

            // Act
            await dataCache.RemoveCache(removeData);

            // Assert
            var cachedValue = await cache.GetAsync<object>("Project:test-project-001:Cache:remove_key");
            cachedValue.HasValue.Should().BeFalse("缓存应该被删除");
            Output.WriteLine("缓存删除成功");
        }

        #endregion

        #region 缓存存在检查测试

        [Fact]
        public async Task CacheExists_WithExistingKey_ShouldReturnTrue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            // 先写入缓存
            var cache = GetService<IEasyCachingProvider>();
            await cache.SetAsync("Project:test-project-001:Cache:exists_key", "exists_value", TimeSpan.FromMinutes(5));

            var existsData = new DataCacheExists
            {
                Name = "测试缓存存在检查",
                Description = "检查已存在的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "exists_key" }
            };

            // Act
            var result = await dataCache.ExistsCache(existsData);

            // Assert
            result.Should().BeTrue("缓存应该存在");
            Output.WriteLine($"缓存存在检查结果: {result}");
        }

        [Fact]
        public async Task CacheExists_WithNonExistingKey_ShouldReturnFalse()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            var existsData = new DataCacheExists
            {
                Name = "测试缓存存在检查",
                Description = "检查不存在的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "non_exists_key" }
            };

            // Act
            var result = await dataCache.ExistsCache(existsData);

            // Assert
            result.Should().BeFalse("缓存应该不存在");
            Output.WriteLine($"缓存存在检查结果: {result}");
        }

        #endregion

        #region 项目隔离测试

        [Fact]
        public async Task CacheKey_WithDifferentProjects_ShouldHaveDifferentPrefixes()
        {
            // Arrange
            await InitializeTestDataAsync();
            var cache = GetService<IEasyCachingProvider>();

            // 直接测试缓存键的生成
            var key1 = "Project:project-001:Cache:test_key";
            var key2 = "Project:project-002:Cache:test_key";

            // Act
            await cache.SetAsync(key1, "value1", TimeSpan.FromMinutes(5));
            await cache.SetAsync(key2, "value2", TimeSpan.FromMinutes(5));

            var result1 = await cache.GetAsync<string>(key1);
            var result2 = await cache.GetAsync<string>(key2);

            // Assert
            result1.HasValue.Should().BeTrue();
            result2.HasValue.Should().BeTrue();
            result1.Value.Should().Be("value1");
            result2.Value.Should().Be("value2");

            Output.WriteLine($"缓存键1 {key1}: {result1.Value}");
            Output.WriteLine($"缓存键2 {key2}: {result2.Value}");
        }

        [Fact]
        public async Task CacheOperations_WithDifferentProjects_ShouldBeIsolated()
        {
            // Arrange
            await InitializeTestDataAsync();

            // 创建两个独立的DataCache实例
            var dataCache1 = new DataCache();
            var dataCache2 = new DataCache();

            // 设置不同的项目上下文
            SetTestContext(dataCache1, "project-001");
            SetTestContext(dataCache2, "project-002");

            var writeData = new DataCacheWrite
            {
                Name = "测试项目隔离",
                Description = "不同项目的缓存应该隔离",
                CacheKey = new DataValue { Type = "text", TextValue = "isolation_key" },
                CacheValue = new DataValue { Type = "text", TextValue = "project_001_value" }
            };

            var readData = new DataCacheRead
            {
                Name = "测试项目隔离读取",
                Description = "读取其他项目的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "isolation_key" },
                DefaultValue = new DataValue { Type = "text", TextValue = "not_found" }
            };

            // Act
            Output.WriteLine("开始项目隔离测试...");

            await dataCache1.WriteCache(writeData); // 项目1写入
            Output.WriteLine("项目1写入完成");

            // 验证项目1可以读取自己的数据
            var project1Result = await dataCache1.ReadCache(readData);
            Output.WriteLine($"项目1读取结果: {project1Result}");

            // 项目2尝试读取项目1的数据
            var project2Result = await dataCache2.ReadCache(readData);
            Output.WriteLine($"项目2读取结果: {project2Result}");

            // Assert
            project1Result.Should().Be("project_001_value", "项目1应该能读取自己写入的缓存");
            project2Result.Should().Be("not_found", "不同项目的缓存应该隔离，读取不到其他项目的缓存");

            Output.WriteLine($"项目1读取结果: {project1Result}");
            Output.WriteLine($"项目2读取结果: {project2Result}");

            // 验证实际的缓存键
            var cache = GetService<IEasyCachingProvider>();
            var key1 = "Project:project-001:Cache:isolation_key";
            var key2 = "Project:project-002:Cache:isolation_key";

            var cached1 = await cache.GetAsync<object>(key1);
            var cached2 = await cache.GetAsync<object>(key2);

            cached1.HasValue.Should().BeTrue("项目1的缓存键应该存在");
            cached2.HasValue.Should().BeFalse("项目2的缓存键不应该存在");

            Output.WriteLine($"项目1缓存键 {key1}: {cached1.HasValue}");
            Output.WriteLine($"项目2缓存键 {key2}: {cached2.HasValue}");
        }

        #endregion

        /// <summary>
        /// 设置测试上下文
        /// </summary>
        private void SetTestContext(DataCache service, string projectId = "test-project-001")
        {
            var context = new FunctionContext
            {
                globalData = new Dictionary<string, object>
                {
                    ["ProjectId"] = projectId
                },
                Args = new Dictionary<string, object>(),
                Persistence = false,
                trackId = TUID.NewTUID().ToString()
            };

            var dbContext = GetService<IDbContext>();
            context.LocalDbContext.Value = dbContext;

            // 初始化Current属性
            context.Current = new FunctionProvider
            {
                FunctionName = "测试缓存动作",
                Level = 0,
                SeqNo = 0,
                IsFlow = false
            };

            // 通过反射设置Context属性
            var contextProperty = typeof(DataCache).GetProperty("Context");
            contextProperty?.SetValue(service, context);

            // 设置Cache属性
            var cacheProperty = typeof(DataCache).GetProperty("Cache");
            cacheProperty?.SetValue(service, GetService<IEasyCachingProvider>());
        }
    }
}
