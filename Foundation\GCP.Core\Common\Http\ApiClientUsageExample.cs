using RestSharp;

namespace GCP.Common
{
    /// <summary>
    /// API客户端使用示例
    /// </summary>
    public class ApiClientUsageExample
    {
        /// <summary>
        /// 示例1：使用预定义的API类型
        /// </summary>
        public async Task Example1_PredefinedApiTypes()
        {
            var manager = GlobalApiClientManager.Instance;

            // 快速API调用（30秒超时）
            var fastClient = manager.GetClient(ApiTypes.Fast);
            var fastRequest = new RestRequest("https://api.example.com/quick", Method.Get);
            var fastResponse = await fastClient.ExecuteAsync(fastRequest);

            // 长时间运行的API调用（1小时超时）
            var longRunningClient = manager.GetClient(ApiTypes.LongRunning);
            var longRequest = new RestRequest("https://api.example.com/process", Method.Post);
            var longResponse = await longRunningClient.ExecuteAsync(longRequest);

            // 文件上传API调用（30分钟超时）
            var uploadClient = manager.GetClient(ApiTypes.FileUpload);
            var uploadRequest = new RestRequest("https://api.example.com/upload", Method.Post);
            uploadRequest.AddFile("file", "/path/to/file.pdf");
            var uploadResponse = await uploadClient.ExecuteAsync(uploadRequest);
        }

        /// <summary>
        /// 示例2：注册自定义API配置
        /// </summary>
        public void Example2_CustomApiConfig()
        {
            var manager = GlobalApiClientManager.Instance;

            // 注册微信API配置
            manager.RegisterApiConfig("wechat", new ApiClientConfig
            {
                TimeoutInSeconds = 60,
                BaseUrl = "https://api.weixin.qq.com",
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "User-Agent", "MyApp/1.0" }
                }
            });

            // 注册支付宝API配置
            manager.RegisterApiConfig("alipay", new ApiClientConfig
            {
                TimeoutInSeconds = 120,
                BaseUrl = "https://openapi.alipay.com",
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "Accept", "application/json" }
                }
            });

            // 使用自定义配置
            var wechatClient = manager.GetClient("wechat");
            var alipayClient = manager.GetClient("alipay");
        }

        /// <summary>
        /// 示例3：运行时覆盖超时时间
        /// </summary>
        public async Task Example3_OverrideTimeout()
        {
            var manager = GlobalApiClientManager.Instance;

            // 使用快速API配置，但覆盖超时时间为5分钟
            var client = manager.GetClient(ApiTypes.Fast, overrideTimeoutSeconds: 300);
            
            var request = new RestRequest("https://api.example.com/special", Method.Post);
            var response = await client.ExecuteAsync(request);
        }

        /// <summary>
        /// 示例4：获取统计信息和缓存管理
        /// </summary>
        public void Example4_StatsAndCacheManagement()
        {
            var manager = GlobalApiClientManager.Instance;

            // 获取统计信息
            var stats = manager.GetStats();
            Console.WriteLine($"缓存的客户端数量: {stats.CachedClientCount}");
            Console.WriteLine($"注册的API类型: {string.Join(", ", stats.RegisteredApiTypes)}");

            // 清理特定类型的缓存
            manager.ClearCache(ApiTypes.Fast);

            // 清理所有缓存
            manager.ClearAllCache();
        }

        /// <summary>
        /// 示例5：在业务服务中使用
        /// </summary>
        public class MyBusinessService
        {
            private readonly ApiClientManager _apiManager;

            public MyBusinessService()
            {
                _apiManager = GlobalApiClientManager.Instance;
                
                // 注册业务特定的API配置
                _apiManager.RegisterApiConfig("my_service", new ApiClientConfig
                {
                    TimeoutInSeconds = 180, // 3分钟
                    DefaultHeaders = new Dictionary<string, string>
                    {
                        { "Content-Type", "application/json; charset=utf-8" },
                        { "X-Service-Name", "MyBusinessService" },
                        { "X-Version", "1.0" }
                    }
                });
            }

            public async Task<string> CallExternalApi(string endpoint, object data)
            {
                var client = _apiManager.GetClient("my_service");
                var request = new RestRequest(endpoint, Method.Post);
                request.AddJsonBody(data);

                var response = await client.ExecuteAsync(request);
                
                if (response.IsSuccessful)
                {
                    return response.Content ?? "";
                }
                
                throw new Exception($"API调用失败: {response.ErrorMessage}");
            }

            public async Task<string> CallFastApi(string endpoint)
            {
                // 对于需要快速响应的API，使用快速配置
                var client = _apiManager.GetClient(ApiTypes.Fast);
                var request = new RestRequest(endpoint, Method.Get);

                var response = await client.ExecuteAsync(request);
                return response.Content ?? "";
            }

            public async Task<string> CallLongRunningApi(string endpoint, object data)
            {
                // 对于长时间运行的API，使用长时间配置
                var client = _apiManager.GetClient(ApiTypes.LongRunning);
                var request = new RestRequest(endpoint, Method.Post);
                request.AddJsonBody(data);

                var response = await client.ExecuteAsync(request);
                return response.Content ?? "";
            }
        }

        /// <summary>
        /// 示例6：高级配置示例
        /// </summary>
        public void Example6_AdvancedConfiguration()
        {
            var manager = GlobalApiClientManager.Instance;

            // 配置需要认证的API
            manager.RegisterApiConfig("authenticated_api", new ApiClientConfig
            {
                TimeoutInSeconds = 120,
                BaseUrl = "https://secure-api.example.com",
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "Authorization", "Bearer your-token-here" },
                    { "X-API-Key", "your-api-key-here" }
                }
            });

            // 配置需要特殊User-Agent的API
            manager.RegisterApiConfig("special_api", new ApiClientConfig
            {
                TimeoutInSeconds = 60,
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "User-Agent", "Mozilla/5.0 (compatible; MyBot/1.0)" },
                    { "Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8" }
                }
            });
        }
    }

    /// <summary>
    /// 配置管理器示例
    /// </summary>
    public static class ApiConfigurationManager
    {
        /// <summary>
        /// 初始化所有API配置
        /// </summary>
        public static void InitializeConfigurations()
        {
            var manager = GlobalApiClientManager.Instance;

            // 第三方服务配置
            manager.RegisterApiConfig("wechat_pay", new ApiClientConfig
            {
                TimeoutInSeconds = 30,
                BaseUrl = "https://api.mch.weixin.qq.com",
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "Accept", "application/json" }
                }
            });

            manager.RegisterApiConfig("alipay", new ApiClientConfig
            {
                TimeoutInSeconds = 30,
                BaseUrl = "https://openapi.alipay.com",
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" }
                }
            });

            // 内部服务配置
            manager.RegisterApiConfig("user_service", new ApiClientConfig
            {
                TimeoutInSeconds = 60,
                BaseUrl = "http://internal-user-service",
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "X-Internal-Service", "true" }
                }
            });

            manager.RegisterApiConfig("order_service", new ApiClientConfig
            {
                TimeoutInSeconds = 120,
                BaseUrl = "http://internal-order-service",
                DefaultHeaders = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "X-Internal-Service", "true" }
                }
            });
        }
    }
}
