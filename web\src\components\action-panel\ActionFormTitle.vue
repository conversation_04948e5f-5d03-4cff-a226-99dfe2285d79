<template>
  <div class="toolbar">
    <t-space align="center">
      <span class="title">
        <div class="line"></div>

        {{ props.title }}
      </span>
      <t-tooltip v-if="props.tip" :content="props.tip">
        <help-circle-filled-icon style="color: var(--td-text-color-placeholder)" />
      </t-tooltip>
      <slot name="titleSuffix"></slot>
    </t-space>
    <div class="toolbar-right">
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ActionFormTitle',
};
</script>
<script setup lang="ts">
import { HelpCircleFilledIcon } from 'tdesign-icons-vue-next';

const props = defineProps<{
  title: string;
  tip?: string;
}>();
</script>
<style lang="less" scoped>
.toolbar {
  margin-top: 16px;
  margin-bottom: 8px;
  position: relative;
  padding-left: 8px;

  .line {
    border-left: 3px solid var(--td-text-color-placeholder);
    border-radius: 3px;
    height: 14px;
    position: absolute;
    top: 10px;
    left: -2px;
  }

  .title {
    font-size: 16px;
    line-height: 32px;
    font-weight: 400;
    color: var(--td-text-color-primary);
    display: inline-block;
  }

  .toolbar-right {
    float: right;
  }
}
</style>
