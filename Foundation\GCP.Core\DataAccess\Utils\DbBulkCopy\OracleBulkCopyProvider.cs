﻿using Oracle.ManagedDataAccess.Client;
using System.Data;

namespace GCP.DataAccess
{
    internal static class OracleBulkCopyProvider
    {
        private static OracleBulkCopy PrepareOptions(IDbConnection connection, string tableName, IDbTransaction transaction = null)
        {
            OracleBulkCopy bulkCopy = new OracleBulkCopy((OracleConnection)connection, transaction == null ? OracleBulkCopyOptions.UseInternalTransaction : OracleBulkCopyOptions.Default);
            bulkCopy.BatchSize = 800;
            bulkCopy.DestinationTableName = tableName;

            bulkCopy.BulkCopyTimeout = 0;
            return bulkCopy;
        }

        internal static void OracleBulkCopy(this IDbConnection connection, string tableName, IDataReader reader, IDbTransaction transaction = null)
        {
            using OracleBulkCopy bulkCopy = PrepareOptions(connection, tableName, transaction);
            reader = new OracleBulkReader(reader);
            bulkCopy.WriteToServer(reader);
        }

        internal static void OracleBulkCopy(this IDbConnection connection, string tableName, DataTable dt, IDbTransaction transaction = null)
        {
            using OracleBulkCopy bulkCopy = PrepareOptions(connection, tableName, transaction);
            bulkCopy.WriteToServer(dt);
        }

        internal static async Task<int> OracleBatchExecute(this IDbConnection connection, SqlBuilder<int>[] sqlBuilders, IDbTransaction tran = null)
        {
            var rowsAffected = 0;
            if (sqlBuilders == null || sqlBuilders.Length == 0)
            {
                return 0;
            }

            var groupedLists = sqlBuilders
                .GroupBy(t => t.SqlString)
                .ToDictionary(g => g.Key, g => g.Select(t => t.Parameters).ToList());

            foreach (var (sql, parameters) in groupedLists)
            {
                if (parameters == null || !parameters.Any())
                {
                    continue;
                }

                var firstParameterSet = parameters.First();
                var paramDefinitionCount = firstParameterSet.Length;
                
                var currentGroupExecutionCount = parameters.Count;

                await using var command = new OracleCommand(sql, (OracleConnection)connection);
                if (tran != null)
                {
                    command.Transaction = (OracleTransaction)tran;
                }
                command.ArrayBindCount = currentGroupExecutionCount;
                command.BindByName = true;
                for (var i = 0; i < paramDefinitionCount; i++)
                {
                    var firstParamInfo = firstParameterSet[i];

                    var oracleParameter = new OracleParameter(firstParamInfo.ParameterName, OracleTypeMapper.MapDbTypeToOracleDbType(firstParamInfo.DbType));
                    oracleParameter.Value = parameters.Select(t =>
                    {
                        var val = t[i].Value;
                        if (val == null || (val is string && val.ToString() == ""))
                        {
                            return DBNull.Value;
                        }

                        return val;
                    }).ToArray();
                    command.Parameters.Add(oracleParameter);
                }
                rowsAffected += await command.ExecuteNonQueryAsync().ConfigureAwait(false);
            }

            return rowsAffected;
        }
    }

    internal static class OracleTypeMapper
    {
        public static OracleDbType MapDbTypeToOracleDbType(DbType dbType, object value = null)
        {
            switch (dbType)
            {
                case DbType.AnsiString: return OracleDbType.Varchar2;
                case DbType.AnsiStringFixedLength: return OracleDbType.Char;
                case DbType.Binary: return OracleDbType.Raw; // Or Blob if large
                case DbType.Boolean: return OracleDbType.Boolean; // Requires Oracle DB 23c+ or use Number(1)
                case DbType.Byte: return OracleDbType.Byte;
                case DbType.Currency: return OracleDbType.Decimal; // Or Number
                case DbType.Date: return OracleDbType.Date;
                case DbType.DateTime: return OracleDbType.TimeStamp;
                case DbType.DateTime2: return OracleDbType.TimeStamp;
                case DbType.DateTimeOffset: return OracleDbType.TimeStampTZ; // TimeStamp with Time Zone
                case DbType.Decimal:
                    // For OracleDecimal, it's better to use OracleDbType.Decimal directly
                    // and pass an OracleDecimal object if precision is critical.
                    // Otherwise, mapping to Number might be more general.
                    if (value is decimal || value is float || value is double)
                        return OracleDbType.Decimal; // Or Number
                    return OracleDbType.Decimal; // Or Number if you don't have OracleDecimal
                case DbType.Double: return OracleDbType.Double; // BinaryDouble
                case DbType.Guid: return OracleDbType.Raw; // Typically stored as RAW(16)
                case DbType.Int16: return OracleDbType.Int16;
                case DbType.Int32: return OracleDbType.Int32;
                case DbType.Int64: return OracleDbType.Int64;
                case DbType.Object:
                    // This is tricky. Could be various LOBs or user-defined types.
                    // For CLOB/BLOB, often it's better to set OracleDbType explicitly
                    // based on knowledge of the object type.
                    if (value is byte[]) return OracleDbType.Blob; // Guess
                    if (value is string) return OracleDbType.Clob; // Guess
                    return OracleDbType.Blob; // Defaulting, but needs care
                case DbType.SByte: return OracleDbType.Int16; // No direct SByte, promote
                case DbType.Single: return OracleDbType.Single; // BinaryFloat
                case DbType.String: return OracleDbType.Varchar2; // Or NChar, NVarchar2 if Unicode handling is specific
                case DbType.StringFixedLength: return OracleDbType.Char;    // Or NChar
                case DbType.Time: return OracleDbType.IntervalDS; // Interval Day to Second, or map to Date/TimeStamp if only time part used
                case DbType.UInt16: return OracleDbType.Int32;   // Promote
                case DbType.UInt32: return OracleDbType.Int64;   // Promote
                case DbType.UInt64: return OracleDbType.Decimal; // Promote to a type that can hold it
                case DbType.VarNumeric: return OracleDbType.Decimal; // Or Number
                case DbType.Xml: return OracleDbType.XmlType;

                // DbTypes without direct common equivalents or needing special handling:
                // case DbType.TimeSpan: // map to IntervalDS or Number
                // case DbType.Array: // Oracle specific collection types
                // case DbType.Structured: // Oracle specific object types

                default:
                    throw new ArgumentOutOfRangeException(nameof(dbType), $"Unsupported DbType: {dbType}");
            }
        }
    }

    internal class OracleBulkReader : IDataReader
    {
        public OracleBulkReader(IDataReader reader)
        {
            _reader = reader;
        }

        private readonly IDataReader _reader;

        #region IDataReader Members

        public void Close()
        {
            _reader.Close();
        }

        public int Depth
        {
            get { return _reader.Depth; }
        }

        public DataTable GetSchemaTable()
        {
            return _reader.GetSchemaTable();
        }

        public bool IsClosed
        {
            get { return _reader.IsClosed; }
        }

        public bool NextResult()
        {
            return _reader.NextResult();
        }

        public bool Read()
        {
            return _reader.Read();
        }

        public int RecordsAffected
        {
            get { return _reader.RecordsAffected; }
        }

        #endregion

        #region IDisposable Members

        public void Dispose()
        {
            _reader.Dispose();
        }

        #endregion

        #region IDataRecord Members

        public int FieldCount
        {
            get { return _reader.FieldCount; }
        }

        public bool GetBoolean(int i)
        {
            return _reader.GetBoolean(i);
        }

        public byte GetByte(int i)
        {
            return _reader.GetByte(i);
        }

        public long GetBytes(int i, long fieldOffset, byte[] buffer, int bufferoffset, int length)
        {
            if (_reader.GetFieldType(i) == typeof(Guid))
            {
                Guid temp = _reader.GetGuid(i);
                temp.ToByteArray().CopyTo(buffer, bufferoffset);
                return length;
            }
            return _reader.GetBytes(i, fieldOffset, buffer, bufferoffset, length);
        }

        public char GetChar(int i)
        {
            return _reader.GetChar(i);
        }

        public long GetChars(int i, long fieldoffset, char[] buffer, int bufferoffset, int length)
        {
            return _reader.GetChars(i, fieldoffset, buffer, bufferoffset, length);
        }

        public IDataReader GetData(int i)
        {
            return _reader.GetData(i);
        }

        public string GetDataTypeName(int i)
        {
            string result = _reader.GetDataTypeName(i);
            return result.Equals("uniqueidentifier", StringComparison.OrdinalIgnoreCase) ? "image" : result;
        }

        public DateTime GetDateTime(int i)
        {
            return _reader.GetDateTime(i);
        }

        public decimal GetDecimal(int i)
        {
            return _reader.GetDecimal(i);
        }

        public double GetDouble(int i)
        {
            return _reader.GetDouble(i);
        }

        public Type GetFieldType(int i)
        {
            Type result = _reader.GetFieldType(i);
            result = result == typeof(Guid) ? typeof(byte[]) : result;
            return result;
        }

        public float GetFloat(int i)
        {
            return _reader.GetFloat(i);
        }

        public Guid GetGuid(int i)
        {
            return _reader.GetGuid(i);
        }

        public short GetInt16(int i)
        {
            return _reader.GetInt16(i);
        }

        public int GetInt32(int i)
        {
            return _reader.GetInt32(i);
        }

        public long GetInt64(int i)
        {
            return _reader.GetInt64(i);
        }

        public string GetName(int i)
        {
            return _reader.GetName(i);
        }

        public int GetOrdinal(string name)
        {
            return _reader.GetOrdinal(name);
        }

        public string GetString(int i)
        {
            return _reader.GetString(i);
        }

        Byte[] _guidResult = new byte[16];
        public object GetValue(int i)
        {
            Type fieldType = _reader.GetFieldType(i);
            object result = _reader.GetValue(i);
            if (fieldType == typeof(Guid))
            {
                _guidResult = ((Guid)result).ToByteArray();
                return _guidResult;
            }
            return result;
        }

        public int GetValues(object[] values)
        {
            return _reader.GetValues(values);
        }

        public bool IsDBNull(int i)
        {
            return _reader.IsDBNull(i);
        }

        public object this[string name]
        {
            get { return _reader[name]; }
        }

        public object this[int i]
        {
            get { return _reader; }

        }



        #endregion
    }
}
