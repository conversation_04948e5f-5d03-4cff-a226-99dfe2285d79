﻿using GCP.Common;
using GCP.DataAccess;
using System.Diagnostics;

namespace GCP.FunctionPool
{
    class TimeMiddleware : IFunctionMiddleware
    {
        public static async Task Handler(FunctionContext ctx, Func<Task> next)
        {
            Stopwatch sw = new Stopwatch();
            if (!ctx.Persistence)
            {
                sw.Start();
                await next();
                sw.Stop();
                ctx.Current.ElapsedMilliseconds = sw.ElapsedMilliseconds;
                await ctx.Log.Write($"函数 {ctx.Current.Path} 消耗时间为 {sw.Elapsed.FormatString()}");
                return;
            }


            var beginTime = DateTime.Now;
            var stepId = TUID.NewTUID().ToString();
            var hasError = false;
            ctx.Current.StepId = stepId;
            try
            {
                sw.Start();
                await next();
            }
            catch (CustomSkipException)
            {
                sw.Stop();
                throw;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                sw.Stop();

                hasError = true;
                ctx.Current.HasError = hasError;
                ctx.Current.StepId = stepId;
                ctx.Current.Result = ex.Message;
                var isCustomError = ex is CustomException;
                var message = $"{ctx.Current.StepName} 执行异常：{ex.Message}";
                if (!isCustomError)
                {
                    await ctx.SqlLog.Write(new LcFruLog
                    {
                        Id = TUID.NewTUID().ToString(),
                        ProcId = ctx.trackId,
                        StepId = ctx.Current.StepId,
                        Category = "ACTION",
                        Timestamp = DateTime.Now,
                        Level = "ERROR",
                        Message = message,
                        Exception = ex.StackTrace,
                    });
                }

                await ctx.SqlLog.AddFunctionContextVariable();
                throw new CustomSkipException(message);
            }
            finally
            {
                sw.Stop();

                ctx.Current.StepId = stepId;
                var step = new LcFruStep
                {
                    Id = stepId,
                    ProcId = ctx.trackId,
                    FunctionId = ctx.Current.Path,
                    ActionId = ctx.Current.ActionId,
                    StepName = ctx.Current.IsFlow ? ctx.Current.StepName : ctx.Current.FunctionName,
                    SeqNo = ctx.Current.IsFlow ? ctx.Current.SeqNo : 0,
                    Status = (short)(hasError ? -1 : 1),
                    BeginTime = beginTime,
                    EndTime = DateTime.Now,
                    Duration = (int)sw.ElapsedMilliseconds,
                };
                await ctx.SqlLog.Write(step);

                await ctx.SqlLog.AddFunctionInputVariable();
                
                ctx.Current.ElapsedMilliseconds = sw.ElapsedMilliseconds;
                ctx.Current.ArgsBuilder = null;
            }
        }
    }
}
