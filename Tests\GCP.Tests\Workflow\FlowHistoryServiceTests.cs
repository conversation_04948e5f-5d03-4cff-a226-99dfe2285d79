using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.Functions.Common.Services;
using GCP.DataAccess;
using LinqToDB;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 工作流历史服务测试类
    /// </summary>
    public class FlowHistoryServiceTests : DatabaseTestBase
    {
        public FlowHistoryServiceTests(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<FlowHistoryService>();
        }

        [Fact]
        public async Task GetAll_ShouldReturnFlowHistoryInstances()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            // Act
            var result = flowHistoryService.GetAll();

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            Output.WriteLine($"返回的工作流历史实例数量: {result.Count}");
        }

        [Fact]
        public async Task GetAll_WithKeywordFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            // Act
            var result = flowHistoryService.GetAll(keyword: "测试", triggerType: "API", functionId: "test-function-hist-001");

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            
            if (result.Any())
            {
                result.All(p => p.FunctionName.Contains("测试")).Should().BeTrue("所有结果都应该包含关键词");
            }
        }

        [Fact]
        public async Task GetAll_WithTriggerTypeFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            // Act
            var result = flowHistoryService.GetAll(triggerType: "API");

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            
            if (result.Any())
            {
                result.All(p => p.TriggerType == "API").Should().BeTrue("所有结果都应该是API触发类型");
            }
        }

        [Fact]
        public async Task GetAll_WithTimeRangeFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            var beginTime = DateTime.Now.AddDays(-1);
            var endTime = DateTime.Now.AddDays(1);

            // Act
            var result = flowHistoryService.GetAll(beginTime: beginTime, endTime: endTime);

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            
            if (result.Any())
            {
                result.All(p => p.BeginTime >= beginTime && p.BeginTime <= endTime)
                    .Should().BeTrue("所有结果都应该在指定时间范围内");
            }
        }

        [Fact]
        public async Task GetAll_WithRunLogFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            // Act
            var result = flowHistoryService.GetAll(runLog: "成功");

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            
            if (result.Any())
            {
                result.Where(p => !string.IsNullOrEmpty(p.RunLog))
                    .All(p => p.RunLog.Contains("成功"))
                    .Should().BeTrue("所有有日志的结果都应该包含指定内容");
            }
        }

        [Fact]
        public async Task GetAll_WithHasRunLogFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            // Act
            var result = flowHistoryService.GetAll(hasRunLog: true);

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            
            if (result.Any())
            {
                result.All(p => !string.IsNullOrEmpty(p.RunLog))
                    .Should().BeTrue("所有结果都应该有运行日志");
            }
        }

        [Fact]
        public async Task GetAll_WithDurationFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            var minDuration = 1000; // 1秒

            // Act
            var result = flowHistoryService.GetAll(duration: minDuration);

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            
            if (result.Any())
            {
                result.Where(p => p.Duration.HasValue)
                    .All(p => p.Duration >= minDuration)
                    .Should().BeTrue("所有有持续时间的结果都应该大于等于指定值");
            }
        }

        [Fact]
        public async Task GetSteps_ShouldReturnFlowHistorySteps()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            var testProcId = "test-history-proc-001";

            // Act
            var result = flowHistoryService.GetSteps(testProcId);

            // Assert
            result.Should().NotBeNull("应该返回历史步骤列表");
            
            if (result?.Any() == true)
            {
                result.All(s => s.ProcId == testProcId).Should().BeTrue("所有步骤都应该属于指定的流程实例");
                result.Should().BeInAscendingOrder(s => s.SeqNo, "步骤应该按序号排序");
            }
        }

        [Fact]
        public async Task GetSteps_WithValidProcId_ShouldReturnSteps()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            var testProcId = "test-history-proc-001";

            // Act
            var result = flowHistoryService.GetSteps(testProcId);

            // Assert
            result.Should().NotBeNull("应该返回步骤列表");
            Output.WriteLine($"返回的历史步骤数量: {result.Count}");
        }

        [Fact]
        public async Task CalculateDailyStats_ShouldCalculateStatistics()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            var beginTime = DateTime.Now.Date.AddDays(-1);

            // Act
            var action = async () => await flowHistoryService.CalculateDailyStats(beginTime);

            // Assert
            await action.Should().NotThrowAsync("计算每日统计不应该抛出异常");
            Output.WriteLine("每日统计计算完成");
        }

        [Fact]
        public async Task CalculateDailyStats_WithoutBeginTime_ShouldUseDefaultTime()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            // Act
            var action = async () => await flowHistoryService.CalculateDailyStats(null);

            // Assert
            await action.Should().NotThrowAsync("使用默认时间计算每日统计不应该抛出异常");
            Output.WriteLine("使用默认时间的每日统计计算完成");
        }

        [Fact]
        public async Task GetAll_WithComplexFilters_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowHistoryService = GetService<FlowHistoryService>();
            SetTestContext(flowHistoryService);

            var beginTime = DateTime.Now.AddDays(-1);
            var endTime = DateTime.Now.AddDays(1);

            // Act
            var result = flowHistoryService.GetAll(
                keyword: "测试",
                triggerType: "API",
                functionId: "test-function-hist-001",
                beginTime: beginTime,
                endTime: endTime,
                hasRunLog: true);

            // Assert
            result.Should().NotBeNull("应该返回历史实例列表");
            Output.WriteLine($"复杂过滤条件返回的历史实例数量: {result.Count}");
        }

        /// <summary>
        /// 设置测试上下文
        /// </summary>
        private void SetTestContext(FlowHistoryService flowHistoryService)
        {
            // 通过反射设置私有字段或属性
            var solutionIdProperty = typeof(FlowHistoryService).BaseType?.GetProperty("SolutionId");
            var projectIdProperty = typeof(FlowHistoryService).BaseType?.GetProperty("ProjectId");

            solutionIdProperty?.SetValue(flowHistoryService, "test-solution-001");
            projectIdProperty?.SetValue(flowHistoryService, "test-project-001");
        }

        protected override async Task SeedTestDataAsync(GcpDb db)
        {
            try
            {
                // 创建基础测试数据 - 使用唯一ID避免冲突
                var timestamp = DateTime.Now.Ticks.ToString().Substring(10); // 使用时间戳后8位
                var solutionId = $"test-sol-hist-{timestamp}";
                var projectId = $"test-proj-hist-{timestamp}";
                var functionId = "test-function-hist-001"; // 使用固定ID

                var solution = TestDataBuilder.CreateTestSolution("工作流历史测试解决方案");
                solution.Id = solutionId;
                await db.InsertAsync(solution);

                var project = TestDataBuilder.CreateTestProject("工作流历史测试项目", solutionId);
                project.Id = projectId;
                await db.InsertAsync(project);

                // 创建测试函数
                var function = TestDataBuilder.CreateTestFunction("测试工作流历史函数", solutionId, projectId);
                function.Id = functionId;
                await db.InsertAsync(function);

                // 使用流程数据库创建工作流历史数据
                using var flowDb = GetService<GcpFlowDb>();

                // 创建工作流历史实例
                var historyProc = TestDataBuilder.CreateTestFlowHistoryProc(functionId, solutionId, projectId);
                historyProc.Id = $"test-hist-proc1-{timestamp}";
                historyProc.RunLog = "工作流执行成功";
                await flowDb.InsertAsync(historyProc);

                // 创建另一个历史实例用于测试过滤
                var historyProc2 = TestDataBuilder.CreateTestFlowHistoryProc(functionId, solutionId, projectId);
                historyProc2.Id = $"test-hist-proc2-{timestamp}";
                historyProc2.TriggerType = "JOB";
                historyProc2.Status = -1; // 失败状态
                historyProc2.RunLog = "工作流执行失败";
                await flowDb.InsertAsync(historyProc2);

                Output.WriteLine("工作流历史测试数据初始化完成");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"工作流历史测试数据初始化失败: {ex.Message}");
                throw;
            }
        }
    }
}
