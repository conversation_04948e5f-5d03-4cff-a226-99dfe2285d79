using System.Text.Json;
using Confluent.Kafka;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using Serilog;

namespace GCP.Eventbus.Providers
{
    class KafkaMessageBus : MessageBusBase
    {
        private IProducer<string, string> _producer;
        private readonly Dictionary<string, IConsumer<string, string>> _consumers;
        private readonly Dictionary<string, CancellationTokenSource> _consumerTokens;
        private readonly SemaphoreSlim _connectionLock = new SemaphoreSlim(1, 1);

        public override bool IsConnected => _producer != null;

        public KafkaMessageBus(MessageBusOptions options) 
            : base(options)
        {
            _consumers = new Dictionary<string, IConsumer<string, string>>();
            _consumerTokens = new Dictionary<string, CancellationTokenSource>();
        }

        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (IsConnected) return;

                var config = new ProducerConfig
                {
                    BootstrapServers = Options.Settings.GetValueOrDefault("BootstrapServers", "localhost:9092"),
                    ClientId = Options.Settings.GetValueOrDefault("ClientId", "GCP.Eventbus"),
                    Acks = Acks.All
                };

                _producer = new ProducerBuilder<string, string>(config).Build();
                Log.Information("Connected to Kafka: {BootstrapServers}", config.BootstrapServers);
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected) return;

                foreach (var token in _consumerTokens.Values)
                {
                    await token.CancelAsync();
                }
                _consumerTokens.Clear();

                foreach (var consumer in _consumers.Values)
                {
                    consumer.Close();
                    consumer.Dispose();
                }
                _consumers.Clear();

                _producer?.Dispose();
                _producer = null;

                Log.Information("Disconnected from Kafka");
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var envelope = new MessageEnvelope
            {
                Payload = message,
            };
            if (headers is not null) envelope.Headers = headers;

            var json = JsonHelper.Serialize(envelope);
            var kafkaMessage = new Message<string, string>
            {
                Key = envelope.MessageId,
                Value = json,
                Headers = new Headers()
            };

            //foreach (var header in headers)
            //{
            //    kafkaMessage.Headers.Add(header.Key, System.Text.Encoding.UTF8.GetBytes(header.Value?.ToString() ?? string.Empty));
            //}

            await _producer!.ProduceAsync(topic, kafkaMessage, cancellationToken);
            Log.Debug("Published message {MessageId} to {Topic}", envelope.MessageId, topic);
        }

        public override async Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var msgArr = messages.ToArray();
            foreach (var message in msgArr)
            {
                var envelope = new MessageEnvelope
                {
                    Payload = message,
                };
                if (headers is not null) envelope.Headers = headers;

                var json = JsonSerializer.Serialize(envelope);
                var kafkaMessage = new Message<string, string>
                {
                    Key = envelope.MessageId,
                    Value = json,
                    Headers = new Headers()
                };

                foreach (var header in envelope.Headers)
                {
                    kafkaMessage.Headers.Add(header.Key, System.Text.Encoding.UTF8.GetBytes(header.Value?.ToString() ?? string.Empty));
                }

                await _producer!.ProduceAsync(topic, kafkaMessage, cancellationToken);
            }

            Log.Debug("Published {Count} messages to {Topic}", msgArr.Count(), topic);
        }

        public override async Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var groupId = options.Settings.GetValueOrDefault("GroupId", "default");
            var config = new ConsumerConfig
            {
                BootstrapServers = Options.Settings.GetValueOrDefault("BootstrapServers", "localhost:9092"),
                GroupId = groupId,
                AutoOffsetReset = AutoOffsetReset.Latest,
                EnableAutoCommit = false
            };

            var consumer = new ConsumerBuilder<string, string>(config).Build();
            consumer.Subscribe(topic);

            var cts = new CancellationTokenSource();
            var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token, cancellationToken);

            await _connectionLock.WaitAsync(linkedCts.Token);
            try
            {
                _consumers[topic] = consumer;
                _consumerTokens[topic] = cts;
            }
            finally
            {
                _connectionLock.Release();
            }

            _ = Task.Run(async () =>
            {
                try
                {
                    while (!linkedCts.Token.IsCancellationRequested)
                    {
                        try
                        {
                            var result = consumer.Consume(linkedCts.Token);
                            if (result != null)
                            {
                                var envelope = JsonSerializer.Deserialize<MessageEnvelope>(result.Message.Value);
                                if (envelope != null)
                                {
                                    foreach (var header in result.Message.Headers)
                                    {
                                        envelope.Headers[header.Key] = System.Text.Encoding.UTF8.GetString(header.GetValueBytes());
                                    }

                                    await handler(envelope, linkedCts.Token).ConfigureAwait(false);
                                    consumer.Commit(result);
                                }
                            }
                        }
                        catch (ConsumeException ex)
                        {
                            Log.Error(ex, "Error consuming message from {Topic}", topic);
                        }

                        await Task.Delay(100, cancellationToken); // 避免空轮询消耗资源
                    }
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error in consumer loop for {Topic}", topic);
                }
                finally
                {
                    try
                    {
                        consumer.Close();
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error closing consumer for {Topic}", topic);
                    }
                }
            }, linkedCts.Token);

            Log.Information("Subscribed to {Topic} with group {GroupId}", topic, groupId);
        }

        public override async Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (_consumers.TryGetValue(topic, out var consumer))
                {
                    if (_consumerTokens.TryGetValue(topic, out var cts))
                    {
                        await cts.CancelAsync();
                        _consumerTokens.Remove(topic);
                    }

                    consumer.Close();
                    consumer.Dispose();
                    _consumers.Remove(topic);

                    Log.Information("Unsubscribed from {Topic}", topic);
                }
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task SubscribeBatchAsync(string topic, Func<List<MessageEnvelope>, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var groupId = options.Settings.GetValueOrDefault("GroupId", "default");
            var batchSize = int.Parse(options.Settings.GetValueOrDefault("BatchSize", "10"));
            var maxPollIntervalMs = int.Parse(options.Settings.GetValueOrDefault("MaxPollIntervalMs", "300000")); // 默认5分钟
            
            var config = new ConsumerConfig
            {
                BootstrapServers = Options.Settings.GetValueOrDefault("BootstrapServers", "localhost:9092"),
                GroupId = groupId,
                AutoOffsetReset = AutoOffsetReset.Latest,
                EnableAutoCommit = false,
                MaxPollIntervalMs = maxPollIntervalMs,
                // 增加拉取记录数以支持批处理
                MaxPartitionFetchBytes = 1048576, // 1MB
                FetchMaxBytes = 52428800, // 50MB 
                // 注意：批量大小由代码控制，不依赖于Kafka客户端配置
            };

            var consumer = new ConsumerBuilder<string, string>(config).Build();
            consumer.Subscribe(topic);

            var cts = new CancellationTokenSource();
            var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token, cancellationToken);

            await _connectionLock.WaitAsync(linkedCts.Token);
            try
            {
                _consumers[topic] = consumer;
                _consumerTokens[topic] = cts;
            }
            finally
            {
                _connectionLock.Release();
            }

            _ = Task.Run(async () =>
            {
                try
                {
                    while (!linkedCts.Token.IsCancellationRequested)
                    {
                        try
                        {
                            // 使用Consume批量获取消息
                            var consumeResult = consumer.Consume(linkedCts.Token);
                            if (consumeResult != null)
                            {
                                // 使用原生批量拉取获取更多消息
                                var batch = new List<MessageEnvelope>();
                                var result = consumeResult;
                                
                                // 处理第一条消息
                                var envelope = JsonSerializer.Deserialize<MessageEnvelope>(result.Message.Value);
                                if (envelope != null)
                                {
                                    foreach (var header in result.Message.Headers)
                                    {
                                        envelope.Headers[header.Key] = System.Text.Encoding.UTF8.GetString(header.GetValueBytes());
                                    }
                                    batch.Add(envelope);
                                }
                                
                                // 尝试非阻塞方式继续消费
                                while (batch.Count < batchSize)
                                {
                                    var pollResult = consumer.Consume(TimeSpan.FromMilliseconds(50));
                                    if (pollResult == null) break;
                                    
                                    var nextEnvelope = JsonSerializer.Deserialize<MessageEnvelope>(pollResult.Message.Value);
                                    if (nextEnvelope != null)
                                    {
                                        foreach (var header in pollResult.Message.Headers)
                                        {
                                            nextEnvelope.Headers[header.Key] = System.Text.Encoding.UTF8.GetString(header.GetValueBytes());
                                        }
                                        batch.Add(nextEnvelope);
                                        result = pollResult; // 保存最后一个消费结果用于提交
                                    }
                                }
                                
                                // 处理批量消息
                                if (batch.Count > 0)
                                {
                                    await handler(batch, linkedCts.Token).ConfigureAwait(false);
                                    // 提交最后一个消息的偏移量
                                    consumer.Commit(result);
                                    
                                    Log.Debug("Processed batch of {Count} messages from {Topic}", batch.Count, topic);
                                }
                            }
                        }
                        catch (ConsumeException ex)
                        {
                            Log.Error(ex, "Error consuming message from {Topic}", topic);
                        }

                        await Task.Delay(100, cancellationToken); // 避免空轮询消耗资源
                    }
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error in consumer loop for {Topic}", topic);
                }
                finally
                {
                    try
                    {
                        consumer.Close();
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error closing consumer for {Topic}", topic);
                    }
                }
            }, linkedCts.Token);

            Log.Information("Subscribed to {Topic} with batch mode, group {GroupId}", topic, groupId);
        }

        protected override async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            try
            {
                await _connectionLock.WaitAsync();
                try
                {
                    foreach (var cts in _consumerTokens.Values)
                    {
                        await cts.CancelAsync();
                    }
                    _consumerTokens.Clear();

                    foreach (var consumer in _consumers.Values)
                    {
                        consumer.Close();
                        consumer.Dispose();
                    }
                    _consumers.Clear();

                    _producer?.Dispose();
                    _producer = null;
                }
                finally
                {
                    _connectionLock.Release();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error during KafkaMessageBus disposal");
            }

            _connectionLock.Dispose();
            await base.DisposeAsyncCore();
        }
    }
} 