﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<OutputPath>..\..\dist\</OutputPath>
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
		<IsAotCompatible>False</IsAotCompatible>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<OutputPath>..\..\dist\</OutputPath>
		<IsAotCompatible>False</IsAotCompatible>
	</PropertyGroup>

	<ItemGroup>
		<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
			<_Parameter1>GCP.Tests</_Parameter1>
		</AssemblyAttribute>
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="Ai\**" />
	  <EmbeddedResource Remove="Ai\**" />
	  <None Remove="Ai\**" />
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="DataAccess.Migrations\FoundationMigration\20221108\AddVersionTable.cs" />
		<Compile Remove="Functions.Common\Models\AiModels.cs" />
		<Compile Remove="Functions.Common\Services\AiClientService.cs" />
		<Compile Remove="Functions.Common\Services\AiConversationService.cs" />
		<Compile Remove="Functions.Common\Services\AiModelConfigService.cs" />
		<Compile Remove="Functions.Common\Services\AiProfileService.cs" />
		<Compile Remove="Functions.Common\Services\AiService.cs" />
	</ItemGroup>

	<ItemGroup>
		<!--Ai-->
		<!--<PackageReference Include="ModelContextProtocol" Version="0.1.0-preview.6" />
		<PackageReference Include="ModelContextProtocol.AspNetCore" Version="0.1.0-preview.6" />
		<PackageReference Include="Microsoft.Extensions.AI" Version="9.3.0-preview.1.25161.3" />
		<PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.3.0-preview.1.25161.3" />
		<PackageReference Include="Microsoft.Extensions.AI.Ollama" Version="9.3.0-preview.1.25161.3" />-->
		<!--Notice-->
		<PackageReference Include="FluentEmail.Core" Version="3.0.2" />
		<PackageReference Include="FluentEmail.MailKit" Version="3.0.2" />
		<PackageReference Include="FluentEmail.Smtp" Version="3.0.2" />
		<!--Common-->
		<PackageReference Include="CronExpressionDescriptor" Version="2.44.0" />
		<PackageReference Include="Cronos" Version="0.11.0" />
		<PackageReference Include="DeviceId" Version="6.9.0" />
		<PackageReference Include="DeviceId.Linux" Version="6.9.0" />
		<PackageReference Include="DeviceId.Mac" Version="6.9.0" />
		<PackageReference Include="DeviceId.Windows.Wmi" Version="6.9.0" />
		<PackageReference Include="ExcelDataReader" Version="3.7.0" />
		<PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
		<PackageReference Include="IdGen" Version="3.0.7" />
		<PackageReference Include="Jint" Version="4.4.0" />
		<PackageReference Include="Polly.Core" Version="8.6.2" />
		<PackageReference Include="Polly.RateLimiting" Version="8.6.2" />
		<PackageReference Include="RestSharp" Version="112.1.0" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
		<PackageReference Include="SmartFormat" Version="3.6.0" />
		<PackageReference Include="System.Private.Uri" Version="4.3.2" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="9.0.7" />
		<PackageReference Include="TimeZoneConverter" Version="7.0.0" />
		<!--DataAccess-->
		<PackageReference Include="linq2db" Version="5.4.1" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
		<PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.7" />
		<PackageReference Include="MySqlConnector" Version="2.4.0" />
		<PackageReference Include="Npgsql" Version="9.0.3" />
		<PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.9.0" />
		<PackageReference Include="System.Linq.Async" Version="6.0.3" />
		<PackageReference Include="DuckDB.NET.Data.Full" Version="1.3.2" />
		<!--DataAccess.Migrations-->
		<PackageReference Include="FluentMigrator" Version="7.1.0" />
		<PackageReference Include="FluentMigrator.Runner.MySql" Version="7.1.0" />
		<PackageReference Include="FluentMigrator.Runner.Oracle" Version="7.1.0" />
		<PackageReference Include="FluentMigrator.Runner.Postgres" Version="7.1.0" />
		<PackageReference Include="FluentMigrator.Runner.SQLite" Version="7.1.0" />
		<PackageReference Include="FluentMigrator.Runner.SqlServer" Version="7.1.0" />
		<!--Cache-->
		<PackageReference Include="DistributedLock.MySql" Version="1.0.2" />
		<PackageReference Include="DistributedLock.Oracle" Version="1.0.4" />
		<PackageReference Include="DistributedLock.Postgres" Version="1.3.0" />
		<PackageReference Include="DistributedLock.Redis" Version="1.0.3" />
		<PackageReference Include="DistributedLock.SqlServer" Version="1.0.6" />
		<PackageReference Include="EasyCaching.Core" Version="1.9.2" />
		<PackageReference Include="EasyCaching.InMemory" Version="1.9.2" />
		<PackageReference Include="EasyCaching.Redis" Version="1.9.2" />
		<PackageReference Include="EasyCaching.Serialization.MessagePack" Version="1.9.2" />
		<PackageReference Include="EasyCaching.Extensions.EasyCompressor" Version="2.1.0" />
		<PackageReference Include="EasyCompressor" Version="2.1.0" />
		<PackageReference Include="EasyCompressor.LZ4" Version="2.1.0" />
		<PackageReference Include="MessagePack" Version="3.1.4" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.7" />
		<!--Eventbus-->
		<PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.41" />
		<PackageReference Include="Confluent.Kafka" Version="2.11.0" />
		<PackageReference Include="MQTTnet" Version="5.0.1.1416" />
		<PackageReference Include="MQTTnet.AspNetCore" Version="5.0.1.1416" />
		<PackageReference Include="Disruptor" Version="6.0.1" />
		<PackageReference Include="TouchSocket" Version="3.1.12" />
		<!--Job-->
		<PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
		<PackageReference Include="Hangfire.Core" Version="1.8.20" />
		<PackageReference Include="Hangfire.MemoryStorage" Version="1.8.1.2" />
		<PackageReference Include="HangFire.Redis.StackExchange" Version="1.12.0" />
		<PackageReference Include="Hangfire.SqlServer" Version="1.8.20" />
		<!--Proxy-->
		<PackageReference Include="Yarp.ReverseProxy" Version="2.3.0" />
		<!--Iot-->
		<PackageReference Include="OPCFoundation.NetStandard.Opc.Ua.Client" Version="1.5.376.235" />
		<!--<PackageReference Include="IoTClient" Version="1.0.42" />-->
		<PackageReference Include="Talk.BACnet" Version="1.0.2" />
		<PackageReference Include="System.IO.Ports" Version="9.0.7" />
	</ItemGroup>

	<ItemGroup>
		<None Update="Obfuscar.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>

	<!-- Condition="'$(Configuration)' == 'Release'" -->
	<Target Name="ObfuscarTask" AfterTargets="AfterBuild" Condition="'$(Configuration)' == 'Release'">
		<PropertyGroup>
			<ObfuscateCommand>obfuscar.console Obfuscar.xml</ObfuscateCommand>
		</PropertyGroup>
		<Exec WorkingDirectory="$(OutputPath)" Command="$(ObfuscateCommand)" />
		<Copy SourceFiles="$(OutputPath)Protector\GCP.Core.dll" DestinationFolder="$(OutputPath)" Condition="Exists('$(OutputPath)Protector\GCP.Core.dll')" />
		<Copy SourceFiles="$(OutputPath)Protector\GCP.Core.pdb" DestinationFolder="$(OutputPath)" Condition="Exists('$(OutputPath)Protector\GCP.Core.pdb')" />
		<Delete Files="$(OutputPath)Obfuscar.xml" />
	</Target>
</Project>
