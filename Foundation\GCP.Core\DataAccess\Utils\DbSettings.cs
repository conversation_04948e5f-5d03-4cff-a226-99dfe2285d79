﻿namespace GCP.DataAccess
{
    public static class DbSettings
    {
        /// <summary>
        /// 默认数据库连接
        /// </summary>
        public static string DefaultConnectionString { get; set; }

        /// <summary>
        /// 命令超时时间
        /// </summary>
        public static int CommandTimeout { get; set; } = 30;


        private static string defaultDbProvider = "MySql";
        /// <summary>
        /// 默认数据库驱动
        /// </summary>
        public static string DefaultDbProvider
        {
            get { return defaultDbProvider; }
            set { defaultDbProvider = value; }
        }

        public static string DefaultLinqDbProvider
        {
            get
            {
                return GetLinqDbProvider(defaultDbProvider);
            }
        }

        /// <summary>
        /// 默认数据库类型
        /// </summary>
        public static DbProviderType DbProviderType
        {
            get
            {
                return (DbProviderType)Enum.Parse(typeof(DbProviderType), defaultDbProvider);
            }
            set
            {
                defaultDbProvider = value.ToString();
            }
        }

        public static string DbVersion { get; set; } = "";

        /// <summary>
        /// 是否开启SQL调试(默认false)
        /// </summary>
        public static bool OpenDebug
        {
            get { return DbDebug.IsOpen; }
            set { DbDebug.IsOpen = value; }
        }

        public static string GetLinqDbProvider(string dbProvider)
        {
            if (dbProvider == "MySql")
            {
                return "MySqlConnector";
            }
            else
            {
                return dbProvider;
            }
        }
    }
}
