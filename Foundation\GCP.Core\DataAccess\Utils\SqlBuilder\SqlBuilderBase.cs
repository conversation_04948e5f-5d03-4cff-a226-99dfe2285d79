﻿using GCP.Common;
using System.Data;
using System.Data.Common;
using System.Text;
using System.Collections.Concurrent;

namespace GCP.DataAccess
{
    public abstract class SqlBuilderBase : ISqlBuilder
    {
        static ConcurrentDictionary<string, string> dicInsertSqlCache = new ConcurrentDictionary<string, string>(64, 64);

        public string SqlString
        {
            get { return GetSqlString(); }
            internal set { sqlBuilder = new StringBuilder(value); }
        }

        public IDataParameter[] Parameters
        {
            get { return GetParameters(); }
        }

        public bool CanExecute { get; set; }

        public string ExecutableSql
        {
            get
            {
                if (this.sqlBuilder.Length == 0) return "";
                return SqlDebug.GetExecutableSql(this.session.DbProvider, this.sqlBuilder.ToString(), this.dbParameters.Items);
            }
        }

        internal DbParameters dbParameters { get; set; }

        internal SqlSession session { get; set; }

        internal StringBuilder sqlBuilder { get; set; }

        internal bool isFilter { get; set; }

        public int CommandTimeout { get; set; }

        public SqlBuilderBase(IDbContext dbContext)
        {
            Init(dbContext.CreateParameters(), new SqlSession(dbContext));
            this.CommandTimeout = dbContext.CommandTimeout;
        }

        public SqlBuilderBase(DbConnection connection)
        {
            Init(connection.CreateParameters(), new SqlSession(connection));
        }

        internal SqlBuilderBase(SqlSession session)
        {
            Init(session.CreateParameters(), session);
            if (session.dbContext != null)
                this.CommandTimeout = session.dbContext.CommandTimeout;
        }

        public DbConnection CreateConnection()
        {
            return this.session.CreateConnection();
        }


        internal virtual void Init(DbParameters dbParameters, SqlSession session)
        {
            this.dbParameters = dbParameters;
            this.session = session;
            this.sqlBuilder = new StringBuilder();
            this.CommandTimeout = DbSettings.CommandTimeout;
        }

        internal virtual void BeforeGet()
        {
        }

        internal virtual string GetSqlString()
        {
            BeforeGet();
            return this.session.DbProvider.NotUseParameter ? this.ExecutableSql : this.sqlBuilder.ToString().Trim();
        }

        internal virtual IDataParameter[] GetParameters()
        {
            BeforeGet();
            return this.session.DbProvider.NotUseParameter ? null : this.dbParameters.Items;
        }

        internal static string GetInsertSql(IDbProvider dbProvider, object obj, string tableName = null)
        {
            var type = obj.GetType();
            DbHelper.IsList(ref type);
            string id = dbProvider.GetHashCode() + "|" + type.GetHashCode();
            
            return dicInsertSqlCache.GetOrAdd(id, key => {
                var fields = EntityCache.GetFields(type);
                var sbColumnList = new StringBuilder(null);
                var sbParameterList = new StringBuilder(null);

                bool isFirst = true;
                foreach (var field in fields)
                {
                    if (isFirst)
                    {
                        isFirst = false;
                    }
                    else
                    {
                        sbColumnList.Append(", ");
                        sbParameterList.Append(", ");
                    }
                    sbColumnList.Append(field.Key);
                    sbParameterList.Append(dbProvider.GetParameterName(field.Value.Property.Name));
                }

                return string.Format("INSERT INTO {0} ({1}) VALUES ({2})", 
                    tableName ?? DbHelper.GetTableName(type), sbColumnList, sbParameterList);
            });
        }

        internal static string GetInsertSql(DbConnection connection, object obj, string tableName = null)
        {
            return GetInsertSql(connection.GetDbProvider(), obj, tableName);
        }

        internal static SqlBuilder<int> GetUpdateSql(DbConnection connection, string tableName, object setObj, object whereObj = null, object ignoreSetColumns = null, string idColumnName = "id")
        {
            return GetUpdateSql(new SqlSession(connection), tableName, setObj, whereObj, ignoreSetColumns, idColumnName);
        }
        internal static SqlBuilder<int> GetUpdateSql(SqlSession session, string tableName, object setObj, object whereObj = null, object ignoreSetColumns = null, string idColumnName = "id")
        {
            var hasIgnoreSetColumns = ignoreSetColumns != null;
            var hasIdColumn = idColumnName != null;
            var ignoreSetColumnNames = new List<string>();
            if (hasIgnoreSetColumns)
            {
                ignoreSetColumnNames = EntityCache.GetFields(ignoreSetColumns.GetType()).Select(t => t.Key).ToList();
            }

            if (hasIdColumn && !ignoreSetColumnNames.Contains(idColumnName.ToLower()))
            {
                ignoreSetColumnNames.Add(idColumnName.ToLower());
            }

            var setFields = EntityCache.GetFields(setObj.GetType());
            //var whereFields = EntityCache.GetFields(whereObj.GetType());

            Dictionary<string, object> dic = new Dictionary<string, object>();
            StringBuilder sb = new StringBuilder();
            sb.Append("UPDATE ");
            sb.Append(tableName);
            sb.Append(" SET ");
            bool isFirst = true;

            foreach (var item in setFields)
            {
                if (ignoreSetColumnNames.Contains(item.Key)) continue;

                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sb.Append(", ");
                }
                sb.Append(item.Key);
                sb.Append(" = ");
                sb.Append(session.DbProvider.GetParameterName("v_set_" + item.Key));

                dic.Add("v_set_" + item.Key, item.Value.GetValue(setObj));
            }
            sb.Append(" WHERE 1=1");
            var sqlSb = new SqlBuilder<int>(session).Init(sb.ToString(), dic);
            //if (whereObj != null && hasIdColumn && setFields.TryGetValue(idColumnName.ToLower(), out var idField))
            //{
            //    var idObj = idField.GetValue(whereObj);
            //    if (idObj != null)
            //    {
            //        sqlSb.And(idColumnName, idObj);
            //        return sqlSb;
            //    }
            //}
            if (hasIdColumn && whereObj == null)
            {
                return sqlSb.AndId(setObj, idColumnName);
            }
            return sqlSb.AndId(whereObj, idColumnName);


            //foreach (var item in whereFields)
            //{
            //    sb.Append(" AND ");
            //    sb.Append(item.Key);
            //    sb.Append(" = ");
            //    sb.Append(session.DbProvider.GetParameterName("v_where_" + item.Key));

            //    dic.Add("v_where_" + item.Key, item.Value.GetValue(whereObj));
            //}
            //return new SqlBuilder<int>(session).Init(sb.ToString(), dic);
        }

        internal static SqlBuilder<int> GetDeleteSql(DbConnection connection, string tableName, object whereObj, string idColumnName = null)
        {
            return GetDeleteSql(new SqlSession(connection), tableName, whereObj, idColumnName);
        }
        internal static SqlBuilder<int> GetDeleteSql(SqlSession session, string tableName, object whereObj, string idColumnName = null)
        {
            var sb = new SqlBuilder<int>(session).Init("DELETE FROM " + tableName + " WHERE 1=1");

            return sb.AndId(whereObj, idColumnName);
        }

        public override int GetHashCode()
        {
            int hashCode = 17;
            unchecked
            {
                hashCode = hashCode * 23 + this.session.ConnectionString.GetHashCode();
                hashCode = hashCode * 23 + this.session.DbProvider.GetHashCode();
                hashCode = hashCode * 23 + this.SqlString.GetHashCode();
                if (this.Parameters != null)
                {
                    foreach (var parm in this.Parameters)
                    {
                        hashCode = hashCode * 23 + parm.ParameterName.GetHashCode();
                        hashCode = hashCode * 23 + parm.Value.GetHashCode();
                    }
                }
            }
            return hashCode;
        }

        public override string ToString()
        {
            return this.ExecutableSql;
        }
    }
}
