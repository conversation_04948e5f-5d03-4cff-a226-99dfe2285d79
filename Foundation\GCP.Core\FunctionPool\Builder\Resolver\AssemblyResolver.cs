﻿using GCP.Common;
using Serilog;
using System.Reflection;

namespace GCP.FunctionPool.Builder
{
    class AssemblyResolver
    {
        internal static void Handler(string path, string name, Assembly assembly, FunctionInfo assemblyInfo = null, FunctionResolveType resolverType = FunctionResolveType.PURE)
        {
            if (assemblyInfo == null)
                assemblyInfo = new FunctionInfo(path, name);

            var funcPaths = new List<string>();

            switch (resolverType)
            {
                case FunctionResolveType.ASSEMBLY:
                    CustomAttrFuncResolver(path, assembly, funcPaths, assemblyInfo);
                    break;
                case FunctionResolveType.PURE:
                    if (PureFuncResolver(path, assembly, assemblyInfo))
                    {
                        funcPaths.Add(path);
                    }
                    break;
                case FunctionResolveType.MIDDLEWARE:
                    if (PureFuncResolver(path, assembly, assemblyInfo, "Handler", FunctionResolveType.MIDDLEWARE))
                    {
                        funcPaths.Add(path);
                    }
                    break;
                case FunctionResolveType.FLOW:
                    break;
                default:
                    break;
            }

            if (funcPaths.Count == 0)
            {
                assemblyInfo.UnloadFunction();
                return;
            }

            FunctionInfo.dicAssemlyFunc.AddOrUpdate(assembly, funcPaths, (key, t) => funcPaths);
        }

        /// <summary>
        /// 自定义属性函数解析
        /// </summary>
        /// <param name="assembly"></param>
        /// <param name="path"></param>
        /// <param name="startupRunDic"></param>
        /// <param name="funcPaths"></param>
        /// <param name="assemblyInfo"></param>
        static void CustomAttrFuncResolver(string path, Assembly assembly, List<string> funcPaths, FunctionInfo assemblyInfo)
        {
            // 集成 函数接口, 方法标示函数属性
            var types = assembly.GetTypes().Where(IsFuntctionType).ToArray();

            foreach (var item in types)
            {
                var classAttrs = new List<FunctionAttribute>();
                SetClassInheritedAttribute(item, classAttrs);

                var classPath = "";
                if (classAttrs.Count > 0)
                {
                    classAttrs.Reverse();
                    var rootAttr = classAttrs.First();
                    var spaceCharacter = rootAttr.SpaceCharacters;

                    classPath = classAttrs.Select(t => t.Path).Join(spaceCharacter) + spaceCharacter;
                    if (rootAttr.AutoGenerateApi)
                    {
                        classPath = '/' + classPath;
                    }
                }

                foreach (var methodInfo in item.GetMethods())
                {
                    var attrInfo = methodInfo.GetCustomAttribute(typeof(FunctionAttribute)) as FunctionAttribute;

                    if (attrInfo == null)
                        continue;

                    FunctionInfo subAssemblyInfo = new FunctionInfo();
                    if (!subAssemblyInfo.LoadFunction(assembly, methodInfo, FunctionResolveType.ASSEMBLY))
                        continue;

                    var methodPath = classPath + attrInfo.Path;
                    subAssemblyInfo.LoadContext = assemblyInfo.LoadContext;
                    subAssemblyInfo.Path = methodPath;
                    subAssemblyInfo.FunctionName = attrInfo.Description;

                    FunctionCompiler.DicFunction.AddOrUpdate(methodPath, subAssemblyInfo, (key, t) => subAssemblyInfo);
                    funcPaths.Add(methodPath);

                    if (attrInfo.StartupRun && FunctionRunner.StartupRunDic != null)
                    {
                        FunctionRunner.StartupRunDic.TryAdd(attrInfo.Description ?? methodPath, async () =>
                        {
                            await new FunctionRunner().Execute(methodPath).ConfigureAwait(false);
                        });
                    }

                    var jobInfo = methodInfo.GetCustomAttribute(typeof(JobAttribute)) as JobAttribute;
                    if (jobInfo != null)
                    {
                        if (string.IsNullOrWhiteSpace(jobInfo.JobId) || string.IsNullOrWhiteSpace(jobInfo.JobCron))
                        {
                            Log.Warning("函数 {path} 定时任务Id 或 定时任务cron表达式 不能为空", methodPath);
                        }
                        else
                        {
                            subAssemblyInfo.JobId = jobInfo.JobId;
                            subAssemblyInfo.JobName = jobInfo.JobName;

                            jobInfo.Path = methodPath;
                            FunctionRunner.LocalJobs.Add(jobInfo);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 纯函数解析
        /// </summary>
        /// <param name="assembly"></param>
        /// <param name="path"></param>
        /// <param name="assemblyInfo"></param>
        /// <returns></returns>
        static bool PureFuncResolver(string path, Assembly assembly, FunctionInfo assemblyInfo, string funcName = null, FunctionResolveType resolverType = FunctionResolveType.PURE)
        {
            var methods = assembly.DefinedTypes.FirstOrDefault(t => !t.Name.StartsWith("<>"))?.GetMethods(BindingFlags.Static | BindingFlags.Public | BindingFlags.InvokeMethod);

            MethodInfo methodInfo = funcName == null ? methods.FirstOrDefault() : methods.FirstOrDefault(t => t.Name.ToLower() == funcName.ToLower());

            if (methodInfo == null)
            {
                Log.Error("未找到调用函数 {path}", path);
                assemblyInfo.UnloadFunction();
                return false;
            }

            if (assemblyInfo.LoadFunction(assembly, methodInfo, resolverType))
            {
                FunctionCompiler.DicFunction.AddOrUpdate(path, assemblyInfo, (key, t) => assemblyInfo);
                return true;
            }
            return false;
        }

        internal static bool IsFuntctionType(Type t)
        {
            // || !t.IsPublic
            if (!typeof(GCP.Common.IFunctionService).IsAssignableFrom(t) || t.IsAbstract || t.IsGenericType)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 获取类继承属性清单
        /// </summary>
        /// <param name="t"></param>
        /// <param name="path"></param>
        /// <returns></returns>
        internal static void SetClassInheritedAttribute(Type t, List<FunctionAttribute> parts)
        {
            var attrInfo = t.GetCustomAttribute(typeof(FunctionAttribute)) as FunctionAttribute;
            if (attrInfo == null)
            {
                return;
            }

            parts.Add(attrInfo);
            if (t.BaseType == null)
            {
                return;
            }
            else
            {
                SetClassInheritedAttribute(t.BaseType, parts);
            }
        }
    }
}
