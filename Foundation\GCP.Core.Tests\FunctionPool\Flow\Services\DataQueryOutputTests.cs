using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.FunctionPool.Flow.Services;
using GCP.Functions.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace GCP.Core.Tests.FunctionPool.Flow.Services
{
    public class DataQueryOutputTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly DbContext _dbContext;
        private readonly FunctionContext _functionContext;

        public DataQueryOutputTests()
        {
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            
            _serviceProvider = services.BuildServiceProvider();
            
            // 创建测试数据库上下文
            _dbContext = new DbContext();
            _dbContext.SetDefaultConnection("Data Source=test_dataquery_output.db", "SQLite");
            
            // 创建测试表和数据
            CreateTestData();
            
            // 创建功能上下文
            _functionContext = new FunctionContext
            {
                db = _dbContext,
                globalData = new Dictionary<string, object>(),
                Current = new StepInfo { StepName = "DataQueryOutputTest" },
                SqlLog = new TestSqlLog()
            };
        }

        private void CreateTestData()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS test_items (
                    id INTEGER PRIMARY KEY,
                    eid TEXT NOT NULL,
                    oid TEXT NOT NULL,
                    mo_code TEXT NOT NULL,
                    item_name TEXT,
                    quantity INTEGER,
                    status TEXT
                );
                
                DELETE FROM test_items;
                
                INSERT INTO test_items (id, eid, oid, mo_code, item_name, quantity, status) VALUES 
                (1, 'E001', 'O001', 'MO001', 'Item A', 100, 'active'),
                (2, 'E001', 'O002', 'MO002', 'Item B', 200, 'active'),
                (3, 'E002', 'O001', 'MO003', 'Item C', 150, 'inactive'),
                (4, 'E002', 'O003', 'MO001', 'Item D', 300, 'active'),
                (5, 'E003', 'O002', 'MO004', 'Item E', 250, 'active');
            ";
            
            _dbContext.ExecuteNonQuery(sql);
        }

        [Fact]
        public async Task QueryData_WithListOutput_ShouldReturnList()
        {
            // Arrange
            var queryData = new DataQueryData
            {
                Name = "TestListQuery",
                DataSource = "test-datasource",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT * FROM test_items WHERE status = 'active'"
                },
                OutputConfig = new OutputConfig
                {
                    Type = OutputType.List
                }
            };

            var dataQueryService = new DataQuery
            {
                Context = _functionContext
            };

            // Act
            var result = await dataQueryService.QueryData(queryData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<dynamic>>(result);
            var list = result as IList<dynamic>;
            Assert.Equal(4, list.Count); // 4 active items
        }

        [Fact]
        public async Task QueryData_WithSingleOutput_ShouldReturnSingleRecord()
        {
            // Arrange
            var queryData = new DataQueryData
            {
                Name = "TestSingleQuery",
                DataSource = "test-datasource",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT * FROM test_items WHERE id = 1"
                },
                OutputConfig = new OutputConfig
                {
                    Type = OutputType.Single
                }
            };

            var dataQueryService = new DataQuery
            {
                Context = _functionContext
            };

            // Act
            var result = await dataQueryService.QueryData(queryData);

            // Assert
            Assert.NotNull(result);
            Assert.IsNotType<IList<dynamic>>(result);
            
            var item = result as IDictionary<string, object>;
            Assert.NotNull(item);
            Assert.Equal(1, item["id"]);
            Assert.Equal("E001", item["eid"]);
        }

        [Fact]
        public async Task QueryData_WithDictionaryOutput_SingleKey_ShouldReturnDictionary()
        {
            // Arrange
            var queryData = new DataQueryData
            {
                Name = "TestDictionaryQuery",
                DataSource = "test-datasource",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT * FROM test_items"
                },
                OutputConfig = new OutputConfig
                {
                    Type = OutputType.Dictionary,
                    DictionaryConfig = new DictionaryConfig
                    {
                        KeyColumns = new List<string> { "mo_code" },
                        ValueColumn = "id",
                        KeySeparator = "|",
                        UseFullObjectAsValue = false
                    }
                }
            };

            var dataQueryService = new DataQuery
            {
                Context = _functionContext
            };

            // Act
            var result = await dataQueryService.QueryData(queryData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<Dictionary<string, object>>(result);
            
            var dictionary = result as Dictionary<string, object>;
            Assert.Equal(5, dictionary.Count);
            Assert.True(dictionary.ContainsKey("MO001"));
            Assert.True(dictionary.ContainsKey("MO002"));
            Assert.Equal(1, dictionary["MO001"]); // 第一个 MO001 的 id
        }

        [Fact]
        public async Task QueryData_WithDictionaryOutput_MultipleKeys_ShouldReturnDictionary()
        {
            // Arrange
            var queryData = new DataQueryData
            {
                Name = "TestMultiKeyDictionaryQuery",
                DataSource = "test-datasource",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT * FROM test_items"
                },
                OutputConfig = new OutputConfig
                {
                    Type = OutputType.Dictionary,
                    DictionaryConfig = new DictionaryConfig
                    {
                        KeyColumns = new List<string> { "eid", "oid", "mo_code" },
                        ValueColumn = "id",
                        KeySeparator = "|",
                        UseFullObjectAsValue = false
                    }
                }
            };

            var dataQueryService = new DataQuery
            {
                Context = _functionContext
            };

            // Act
            var result = await dataQueryService.QueryData(queryData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<Dictionary<string, object>>(result);
            
            var dictionary = result as Dictionary<string, object>;
            Assert.Equal(5, dictionary.Count);
            Assert.True(dictionary.ContainsKey("E001|O001|MO001"));
            Assert.True(dictionary.ContainsKey("E001|O002|MO002"));
            Assert.True(dictionary.ContainsKey("E002|O001|MO003"));
            Assert.Equal(1, dictionary["E001|O001|MO001"]);
            Assert.Equal(2, dictionary["E001|O002|MO002"]);
        }

        [Fact]
        public async Task QueryData_WithDictionaryOutput_FullObject_ShouldReturnDictionaryWithFullObjects()
        {
            // Arrange
            var queryData = new DataQueryData
            {
                Name = "TestFullObjectDictionaryQuery",
                DataSource = "test-datasource",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT * FROM test_items WHERE status = 'active'"
                },
                OutputConfig = new OutputConfig
                {
                    Type = OutputType.Dictionary,
                    DictionaryConfig = new DictionaryConfig
                    {
                        KeyColumns = new List<string> { "mo_code" },
                        KeySeparator = "|",
                        UseFullObjectAsValue = true
                    }
                }
            };

            var dataQueryService = new DataQuery
            {
                Context = _functionContext
            };

            // Act
            var result = await dataQueryService.QueryData(queryData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<Dictionary<string, object>>(result);
            
            var dictionary = result as Dictionary<string, object>;
            Assert.Equal(4, dictionary.Count); // 4 active items
            
            var firstItem = dictionary["MO001"] as IDictionary<string, object>;
            Assert.NotNull(firstItem);
            Assert.Equal(1, firstItem["id"]);
            Assert.Equal("E001", firstItem["eid"]);
            Assert.Equal("Item A", firstItem["item_name"]);
        }

        [Fact]
        public async Task QueryData_WithDictionaryOutput_CustomSeparator_ShouldUseCustomSeparator()
        {
            // Arrange
            var queryData = new DataQueryData
            {
                Name = "TestCustomSeparatorQuery",
                DataSource = "test-datasource",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT * FROM test_items LIMIT 2"
                },
                OutputConfig = new OutputConfig
                {
                    Type = OutputType.Dictionary,
                    DictionaryConfig = new DictionaryConfig
                    {
                        KeyColumns = new List<string> { "eid", "oid" },
                        ValueColumn = "mo_code",
                        KeySeparator = "_",
                        UseFullObjectAsValue = false
                    }
                }
            };

            var dataQueryService = new DataQuery
            {
                Context = _functionContext
            };

            // Act
            var result = await dataQueryService.QueryData(queryData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<Dictionary<string, object>>(result);

            var dictionary = result as Dictionary<string, object>;
            Assert.True(dictionary.ContainsKey("E001_O001"));
            Assert.True(dictionary.ContainsKey("E001_O002"));
            Assert.Equal("MO001", dictionary["E001_O001"]);
            Assert.Equal("MO002", dictionary["E001_O002"]);
        }

        [Fact]
        public async Task QueryData_WithCountOutput_ShouldReturnCount()
        {
            // Arrange
            var queryData = new DataQueryData
            {
                Name = "TestCountQuery",
                DataSource = "test-datasource",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT * FROM test_items WHERE status = 'active'"
                },
                OutputConfig = new OutputConfig
                {
                    Type = OutputType.Count
                }
            };

            var dataQueryService = new DataQuery
            {
                Context = _functionContext
            };

            // Act
            var result = await dataQueryService.QueryData(queryData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<int>(result);
            Assert.Equal(4, result); // 4 active items
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
            _serviceProvider?.Dispose();
            
            // 清理测试数据库文件
            if (File.Exists("test_dataquery_output.db"))
            {
                File.Delete("test_dataquery_output.db");
            }
        }
    }

    public class TestSqlLog : ISqlLog
    {
        public Task Info(string message, bool isImportant = false) => Task.CompletedTask;
        public Task Warn(string message, bool isImportant = false) => Task.CompletedTask;
        public Task Error(string message, bool isImportant = false) => Task.CompletedTask;
    }
}
