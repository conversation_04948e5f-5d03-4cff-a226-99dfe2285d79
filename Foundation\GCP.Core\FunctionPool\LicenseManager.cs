﻿using GCP.Common;
using Serilog;

namespace GCP.FunctionPool
{
    public class LicenseKey
    {
        public string DeviceId { get; set; }
        public string Product { get; set; }
        public decimal Version { get; set; }
        /// <summary>
        /// 代表产品那些模块（多个按照,分隔）可用，*代表所有
        /// </summary>
        public string Features { get; set; }
    }

    public class LicenseData
    {
        public string User { get; set; }
        public List<LicenseKey> LicenseKeys { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }
    }

    internal class LicenseManager
    {
        private static readonly string _publicKey1 = @"
<RSAKeyValue><Modulus>pPH8CxtTPFY9prels0ght1VsST+3Ts0+8AEFBUDYUitQtzWHKZXl0EbqEZl7JEYXID82LeMPFqpSbJRbotR8G/kYeVMOS+T/1yh/eOoHMq+74n+NoEvbNCiLq2MzbNdWvVyV9QXIrU575EN4dMGx0nSc8cgW/YHKLEbnuPQYdHYufh6khDgPrwBb/GiCBtAG65LVLd6NCLmQKEbdQlsxESQDqZ6asREnTcRklgL4Ut0sDjZ7NumJO/gbXP70dJ/ErGqiiIVOjrB8WEJmqGKgreu74ISUkUs1VZD/q+h+uQKE99hEyQ2XZCi9q3WI2uj5CBlsaChejAcBGvCxbO8UXQ==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>
";
        private static readonly string _privateKey2 = @"
<RSAKeyValue><Modulus>p2V9W2z6Dco00told/Jr3Xuw0ydSrjUDVX3sNk9l4O+a5RfJDxrYIZvJKG8XJgwpHUW+XxlNQZhprX3kL1F4KqKrU9vREGcRVw7p0IPZP7S7OpwV5h21DBjsitJ1O/yGv1n90ocTbmFBcco5Rm+aXE9M8BUgSQfph3uPl9ETvBrHZKxuQGHv3Xw1bThOu0quuXbEbuR7OF+NKgM7829kDpIXQkPR5TaYM9eLs4c2Rnt57vZlKYtHfB/2bgOfgxVIKw86c8/EUudqCIvSeH5CYRyV0FwNb+vfS1KUPoFyT2Xf6hTrbpaVDO02obFkG9t7Ybr9zW0CwEvAcv+gfPhO4Q==</Modulus><Exponent>AQAB</Exponent><P>125dbR08RNPuENYtTvaaFcq7ESw0arXPXvte68ho52YpNWF5pcfZo7PvQmsCa+uAf2so1H3SohsrobZ53TdfiGC27GWob434pgPpG7fSvs+/ErADxBTJmZ7aJG4yCdPhA8WH88igiMNhw0kCfu1IrKyyTlwrv/gNZeOJn/aCcUc=</P><Q>xutwosg3SB7CLZt7vjIlia1l5dL0Uj4TpeTYoK+qdTojNeCblT7sYrdnVZNA7XWq9RR7i5vAwLQJ9E07aB7ns9Zh8vOxit9htkYgc7qnaIWA8HdcYn+JXbOZpooPUoJh5lml654CGjqeG8CPwRGbMW+CXalwYL6YiLOPXUGrkpc=</Q><DP>YhlUm8JLVoA3ER2mZiWvRqsZghliCRGfS34l3DJXar+/lho7/bzCo2PlWJZH9QK5ccxHPHPLPZwnwyzH029Kplx8D6u/BoVcq6RuOvv7EH6jYIwcOubIYpbOePZg/M6p0UDaxq0eRQjLdNTDOS8t/8toKnyYQ4Nd/n6dheUHwzM=</DP><DQ>InGaK1Wn3OEWlncZ0jE0tsZDoqwIikUdedhKemhwh6PtfOLLjeg7XkyLpBG/aF67n3xQeDgcBEIohxjodHsFe+pfQ4ZW88kCTmGONPvZCQw0625/nL6gtozmyiOVy5+v/AcB5+6QRqJnxj5tpey6hrDcTgOM5fOazfDpnihE4Tk=</DQ><InverseQ>I2npoAwe86g5r9aEpue4SHtiY6pE+fb5tl+41emgaaKMkbqg+8h5hkC7pKhacZ1Y3EnPHLKIGekUpm3bIUGb4/pYPLliWBgfXHXuptpebg7CNgLt0rCbWyagwYkpqVyrUP9Jq4x8b9YuW/rYHlk9Q9EViBEzfqV1wI7oi3/ibuQ=</InverseQ><D>XiDfoK1i3qY5lec88nnK1bxb7iBKidMdkNFCJjf3XcjeHLyHPoslxaBIOQhex/JGnEM/jVI48w21Pu1vhnwVSkQ2eu08XjDWu1GTgMDjRMNoFMj4N3G1AJxzRoyZh6W0T51W8S8ragIePtIr6sOO1SFybf4zLV3Zn2mHkSLsKfUr8wSfohoyXT1cIJCpmrG/XknVP2Me1nwAVx7GCmPxebyLv35zL5m1ID4CzIbgi8G3jKVeW2BVojYZCS4N3uvuJLz+9Z4ObZonhFzTO2rLmpvpjEQ5yHnu1y7Pl0UqFfHmrLXHOiGel242tz+rmu5R7WpeSjHXKP2pSDIk1OW70Q==</D></RSAKeyValue>
";

        private static readonly string _product = "GCP";
        private static readonly decimal _version = 1.0M;

        /// <summary>
        /// 激活数据
        /// </summary>
        internal static LicenseData ActivationData { get; set; }

        private static bool? _isActivated;
        /// <summary>
        /// 是否激活
        /// </summary>
        internal static bool? IsActivated
        {
            get { return _isActivated; }
            set
            {
                if (!_isActivated.HasValue)
                {
                    _isActivated = value;
                }
            }
        }

        private static readonly string _licenseKeyFilePath;

        static LicenseManager()
        {
            string userPath = Environment.GetEnvironmentVariable("HOME");
            if (string.IsNullOrEmpty(userPath))
            {
                userPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            }
            _licenseKeyFilePath = Path.Combine(userPath, "gcp", "licenses", "key.lic");
        }

        /// <summary>
        /// 生成注册码
        /// </summary>
        /// <returns></returns>
        internal static string GenerateRegistrationCode()
        {
            List<string> deviceIds = [TUID.DeviceID()];
            List<string> list = new List<string>();
            foreach (var deviceId in deviceIds)
            {
                list.Add($"{deviceId}|{_product}|{_version}");
            }
            var code = JsonHelper.Serialize(list);
            var result = CryptoHelper.RSA_Encrypt(_publicKey1, code);
            return result;
        }

        /// <summary>
        /// 激活码激活
        /// </summary>
        /// <param name="activationCode"></param>
        internal static async Task ActivateLicense(string activationCode, bool isSave = true)
        {
            try
            {
                var decryptedData = CryptoHelper.RSA_Decrypt(_privateKey2, activationCode);
                ActivationData = JsonHelper.Deserialize<LicenseData>(decryptedData);
            }
            catch (Exception ex)
            {
                throw new CustomException("激活失败，请检查注册码是否正确", ex);
            }

            var currDate = DateTime.Now.Date;
            if (ActivationData.LicenseKeys.Any(t => t.DeviceId == TUID.DeviceID() && t.Product == _product && t.Version == _version) && 
                ActivationData.ValidFrom <= currDate && (ActivationData.ValidTo == null || ActivationData.ValidTo >= currDate))
            {
                IsActivated = true;
                Log.Information("本机激活成功");
            }

            if (isSave)
            {
                var encryptContent = CryptoHelper.HexEncode(activationCode);
                await File.WriteAllTextAsync(_licenseKeyFilePath, encryptContent);
            }
        }

        /// <summary>
        /// 载入许可证
        /// </summary>
        internal static async Task LoadLicense()
        {
            if (IsActivated.HasValue && IsActivated.Value)
            {
                return;
            }

            if (File.Exists(_licenseKeyFilePath))
            {
                var fileContent = await File.ReadAllTextAsync(_licenseKeyFilePath);
                var activationCode = CryptoHelper.HexDecode(fileContent);
                await ActivateLicense(activationCode, false);
            }
            else
            {
                string directoryPath = Path.GetDirectoryName(_licenseKeyFilePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                Log.Error("未找到许可证文件");
            }
        }
    }
}
