using System.Text.Json;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using Serilog;
using StackExchange.Redis;

namespace GCP.Eventbus.Providers
{
    class RedisMessageBus : MessageBusBase
    {
        private IConnectionMultiplexer _connection;
        private ISubscriber _subscriber;
        private readonly Dictionary<string, List<ChannelMessageQueue>> _subscriptions;
        private readonly SemaphoreSlim _connectionLock = new SemaphoreSlim(1, 1);

        public override bool IsConnected => _connection?.IsConnected == true;

        public RedisMessageBus(MessageBusOptions options) 
            : base(options)
        {
            _subscriptions = new Dictionary<string, List<ChannelMessageQueue>>();
        }

        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (IsConnected) return;

                var configuration = Options.Settings.GetValueOrDefault("Configuration", "localhost:6379");
                var configurationOptions = ConfigurationOptions.Parse(configuration);
                configurationOptions.AbortOnConnectFail = false;

                _connection = await ConnectionMultiplexer.ConnectAsync(configurationOptions);
                _subscriber = _connection.GetSubscriber();

                Log.Information("Connected to Redis: {Configuration}", configuration);
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected) return;

                foreach (var queues in _subscriptions.Values)
                {
                    foreach (var queue in queues)
                    {
                        await queue.UnsubscribeAsync();
                    }
                }
                _subscriptions.Clear();

                _connection?.Dispose();
                _connection = null;
                _subscriber = null;

                Log.Information("Disconnected from Redis");
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var envelope = new MessageEnvelope
            {
                Payload = message,
            };
            if (headers is not null) envelope.Headers = headers;

            var json = JsonHelper.Serialize(envelope);
            var useConsumerGroups = bool.Parse(Options.Settings.GetValueOrDefault("UseConsumerGroups", "false"));

            if (useConsumerGroups)
            {
                // 使用 Redis Stream 发布消息
                var streamKey = $"stream:{topic}";
                var messageData = new NameValueEntry[] { new NameValueEntry("data", json) };
                await _connection!.GetDatabase().StreamAddAsync(streamKey, messageData);
                
                // 同时发布到普通频道，支持广播模式
                await _subscriber!.PublishAsync(RedisChannel.Literal(topic), json);
            }
            else
            {
                // 仅使用普通的发布订阅
                await _subscriber!.PublishAsync(RedisChannel.Literal(topic), json);
            }

            Log.Debug("Published message {MessageId} to {Topic}", envelope.MessageId, topic);
        }

        public override async Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var tasks = new List<Task>();
            var msgArr = messages.ToArray();
            foreach (var message in msgArr)
            {
                var envelope = new MessageEnvelope
                {
                    Payload = message,
                };
                if (headers is not null) envelope.Headers = headers;

                var json = JsonHelper.Serialize(envelope);
                tasks.Add(_subscriber!.PublishAsync(RedisChannel.Literal(topic), json));
            }

            await Task.WhenAll(tasks);
            Log.Debug("Published {Count} messages to {Topic}", msgArr.Count(), topic);
        }

        public override async Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var useConsumerGroups = bool.Parse(Options.Settings.GetValueOrDefault("UseConsumerGroups", "false"));
            var groupId = Options.Settings.GetValueOrDefault("GroupId", "default");
            var instanceId = Options.Settings.GetValueOrDefault("InstanceId", Guid.NewGuid().ToString());
            var isBroadcast = options.Settings.GetValueOrDefault("IsBroadcast", "false").Equals("true", StringComparison.OrdinalIgnoreCase);

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (isBroadcast)
                {
                    // 广播模式：每个实例都会收到消息
                    var queue = await _subscriber!.SubscribeAsync(RedisChannel.Literal(topic));
                    queue.OnMessage(async channelMessage =>
                    {
                        try
                        {
                            var envelope = JsonSerializer.Deserialize<MessageEnvelope>(channelMessage.Message!);
                            if (envelope != null)
                            {
                                await handler(envelope, cancellationToken).ConfigureAwait(false);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "Error processing broadcast message from {Topic}", topic);
                        }
                    });

                    if (!_subscriptions.ContainsKey(topic))
                    {
                        _subscriptions[topic] = new List<ChannelMessageQueue>();
                    }
                    _subscriptions[topic].Add(queue);

                    Log.Information("Subscribed to broadcast topic {Topic}", topic);
                }
                else if (useConsumerGroups)
                {
                    // 消费组模式：同一组内的实例负载均衡
                    var streamKey = $"stream:{topic}";
                    var consumerGroup = $"group:{groupId}";
                    var consumer = $"{instanceId}";

                    try
                    {
                        // 创建消费组（如果不存在）
                        await _connection!.GetDatabase().StreamCreateConsumerGroupAsync(
                            streamKey, consumerGroup, StreamPosition.NewMessages);
                    }
                    catch (RedisServerException) 
                    { 
                        // 消费组可能已存在，忽略错误
                    }

                    // 启动消费任务
                    _ = Task.Run(async () =>
                    {
                        while (!cancellationToken.IsCancellationRequested)
                        {
                            try
                            {
                                var messages = await _connection!.GetDatabase().StreamReadGroupAsync(
                                    streamKey,
                                    consumerGroup,
                                    consumer,
                                    count: 1,
                                    noAck: false
                                );

                                foreach (var message in messages)
                                {
                                    try
                                    {
                                        var json = message.Values.First(x => x.Name == "data").Value.ToString();
                                        var envelope = JsonSerializer.Deserialize<MessageEnvelope>(json);
                                        if (envelope != null)
                                        {
                                            await handler(envelope, cancellationToken).ConfigureAwait(false);
                                            // 确认消息已处理
                                            await _connection!.GetDatabase().StreamAcknowledgeAsync(
                                                streamKey, consumerGroup, message.Id);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Log.Error(ex, "Error processing message {MessageId} from {Topic}", 
                                            message.Id, topic);
                                    }
                                }

                                await Task.Delay(100, cancellationToken); // 避免空轮询消耗资源
                            }
                            catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
                            {
                                Log.Error(ex, "Error in consumer group reading from {Topic}", topic);
                                await Task.Delay(1000, cancellationToken); // 发生错误时延长等待时间
                            }
                        }
                    }, cancellationToken);

                    Log.Information("Subscribed to topic {Topic} with consumer group {Group}", topic, groupId);
                }
                else
                {
                    // 默认模式：使用普通的发布订阅
                    var queue = await _subscriber!.SubscribeAsync(RedisChannel.Literal(topic));
                    queue.OnMessage(async channelMessage =>
                    {
                        try
                        {
                            var envelope = JsonSerializer.Deserialize<MessageEnvelope>(channelMessage.Message!);
                            if (envelope != null)
                            {
                                await handler(envelope, cancellationToken).ConfigureAwait(false);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "Error processing message from {Topic}", topic);
                        }
                    });

                    if (!_subscriptions.ContainsKey(topic))
                    {
                        _subscriptions[topic] = new List<ChannelMessageQueue>();
                    }
                    _subscriptions[topic].Add(queue);

                    Log.Information("Subscribed to topic {Topic} in default mode", topic);
                }
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (_subscriptions.TryGetValue(topic, out var queues))
                {
                    foreach (var queue in queues)
                    {
                        await queue.UnsubscribeAsync();
                    }
                    _subscriptions.Remove(topic);
                    Log.Information("Unsubscribed from {Topic}", topic);
                }
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        protected override async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            try
            {
                await _connectionLock.WaitAsync();
                try
                {
                    foreach (var queues in _subscriptions.Values)
                    {
                        foreach (var queue in queues)
                        {
                            await queue.UnsubscribeAsync();
                        }
                    }
                    _subscriptions.Clear();
                
                    if (_connection != null)
                    {
                        _connection.Dispose();
                        _connection = null;
                        _subscriber = null;
                    }
                }
                finally
                {
                    _connectionLock.Release();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error disposing Redis subscriptions");
            }

            _connectionLock.Dispose();
            await base.DisposeAsyncCore();
        }
    }
} 