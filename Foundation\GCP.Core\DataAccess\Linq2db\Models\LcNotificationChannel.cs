using LinqToDB.Mapping;

namespace GCP.DataAccess
{
    /// <summary>
    /// 通知通道配置
    /// </summary>
    [Table("lc_notification_channel")]
    public class LcNotificationChannel : IBaseEntity
    {
        /// <summary>
        /// Description:数据行 ID 号
        /// </summary>
        [Column("ID", CanBeNull = false, IsPrimaryKey = true)] public string Id { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:创建时间
        /// </summary>
        [Column("TIME_CREATE")] public DateTime TimeCreate { get; set; } // datetime
        /// <summary>
        /// Description:创建人
        /// </summary>
        [Column("CREATOR", CanBeNull = false)] public string Creator { get; set; } = null!; // varchar(80)
        /// <summary>
        /// Description:更新时间
        /// </summary>
        [Column("TIME_MODIFIED")] public DateTime? TimeModified { get; set; } // datetime
        /// <summary>
        /// Description:更新数据行的用户
        /// </summary>
        [Column("MODIFIER")] public string Modifier { get; set; } = null!; // varchar(80)
        /// <summary>
        /// Description:可用状态
        /// </summary>
        [Column("STATE")] public short State { get; set; } // smallint

        /// <summary>
        /// Description:解决方案 ID
        /// </summary>
        [Column("SOLUTION_ID")] public string SolutionId { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:项目 ID
        /// </summary>
        [Column("PROJECT_ID")] public string ProjectId { get; set; } = null!; // varchar(36)

        /// <summary>
        /// Description:通道名称
        /// </summary>
        [Column("CHANNEL_NAME")] public string ChannelName { get; set; } = null!; // varchar(100)
        /// <summary>
        /// Description:通道类型：Email、Dingtalk、Feishu、Weixin
        /// </summary>
        [Column("CHANNEL_TYPE")] public string ChannelType { get; set; } = null!; // varchar(20)
        /// <summary>
        /// Description:描述
        /// </summary>
        [Column("DESCRIPTION")] public string? Description { get; set; } // varchar(500)
        /// <summary>
        /// Description:是否启用
        /// </summary>
        [Column("IS_ENABLED")] public bool IsEnabled { get; set; } // boolean
        /// <summary>
        /// Description:配置参数JSON
        /// </summary>
        [Column("CONFIG_JSON")] public string ConfigJson { get; set; } = null!; // varchar(2000)
        /// <summary>
        /// Description:发送间隔秒数
        /// </summary>
        [Column("INTERVAL_SECONDS")] public int IntervalSeconds { get; set; } // int
    }
}
