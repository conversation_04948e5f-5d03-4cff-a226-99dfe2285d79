using GCP.Common;
using GCP.FunctionPool.Flow.Models;
using GCP.Iot.Models;
using GCP.Iot.Services;
using System.Text;

namespace GCP.FunctionPool.Flow.Services
{
    class DataIotEquipment : DataBaseService
    {
        private readonly EquipmentCommunicationManager _communicationManager = ServiceLocator.Current.GetService(typeof(EquipmentCommunicationManager)) as EquipmentCommunicationManager;

        [Function("iotVariableWrite", "设备测点赋值")]
        public async Task WriteVariable(DataIotVariableWrite data)
        {
            var engine = this.GetEngine();
            
            // 获取参数值
            var equipmentId = GetDataValue(data.EquipmentId, engine)?.ToString();
            var address = GetDataValue(data.Address, engine)?.ToString();
            var value = GetDataValue(data.Value, engine);

            // 参数验证
            if (string.IsNullOrEmpty(equipmentId))
            {
                throw new CustomException("设备Id不能为空");
            }
            if (string.IsNullOrEmpty(address))
            {
                throw new CustomException("变量地址不能为空");
            }
            if (value == null)
            {
                throw new CustomException("写入值不能为空");
            }

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"设备Id: {equipmentId}");
                this.Context.Current.ArgsBuilder.AppendLine($"地址: {address}");
                this.Context.Current.ArgsBuilder.AppendLine($"值: {value}");
            }

            // 执行写入操作
            var equipmentTask = _communicationManager.GetEquipmentTask(equipmentId);
            if (equipmentTask != null)
            {
                var result = await equipmentTask.WriteVariableAsync(address, value);
                if (result.Status != OperationStatus.Success)
                {
                    var ex = new CustomException($"设备测点写入失败: {result.ErrorMessage}");
                    await this.Context.SqlLog.Error(ex, $"设备测点写入失败: {result.ErrorMessage}", true);
                    throw ex;
                }

                await this.Context.SqlLog.Info($"设备测点写入成功: 设备{equipmentId}, 地址{address}, 值{value}", true);
            }
            else
            {
                var errorMsg = $"未找到设备通信任务: {equipmentId}";
                var ex = new CustomException(errorMsg);
                await this.Context.SqlLog.Error(ex, errorMsg, true);
                throw ex;
            }
        }

        [Function("iotVariableRead", "设备测点读取")]
        public async Task<object> ReadVariable(DataIotVariableRead data)
        {
            var engine = this.GetEngine();
            
            // 获取参数值
            var equipmentId = GetDataValue(data.EquipmentId, engine)?.ToString();
            var variableName = GetDataValue(data.VariableName, engine)?.ToString();

            // 参数验证
            if (string.IsNullOrEmpty(equipmentId))
            {
                throw new CustomException("设备ID不能为空");
            }
            if (string.IsNullOrEmpty(variableName))
            {
                throw new CustomException("变量名称不能为空");
            }

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"设备ID: {equipmentId}");
                this.Context.Current.ArgsBuilder.AppendLine($"变量名称: {variableName}");
            }

            try
            {
                // 执行读取操作
                var equipmentTask = _communicationManager.GetEquipmentTask(equipmentId);
                if (equipmentTask != null)
                {
                    var value = equipmentTask.GetVariableValue(variableName);
                    return value;
                }
                else
                {
                    var errorMsg = $"未找到设备通信任务: {equipmentId}";
                    var ex = new CustomException(errorMsg);
                    await this.Context.SqlLog.Error(ex, errorMsg, true);
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                await this.Context.SqlLog.Error(ex, $"设备测点读取异常: {ex.Message}", true);
                throw new CustomException($"设备测点读取失败: {ex.Message}");
            }
        }

        [Function("iotEquipmentRead", "设备所有参数读取")]
        public async Task<Dictionary<string, object>> ReadEquipmentAllVariables(DataIotEquipmentRead data)
        {
            var engine = this.GetEngine();
            
            // 获取参数值
            var equipmentId = GetDataValue(data.EquipmentId, engine)?.ToString();

            // 参数验证
            if (string.IsNullOrEmpty(equipmentId))
            {
                throw new CustomException("设备ID不能为空");
            }

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"设备ID: {equipmentId}");
            }

            try
            {
                // 执行读取操作
                var equipmentTask = _communicationManager.GetEquipmentTask(equipmentId);
                if (equipmentTask != null)
                {
                    var values = equipmentTask.GetCurrentValues();
                    return values ?? new Dictionary<string, object>();
                }
                else
                {
                    var errorMsg = $"未找到设备通信任务: {equipmentId}";
                    var ex = new CustomException(errorMsg);
                    await this.Context.SqlLog.Error(ex, errorMsg, true);
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                await this.Context.SqlLog.Error(ex, $"设备所有参数读取异常: {ex.Message}", true);
                throw new CustomException($"设备所有参数读取失败: {ex.Message}");
            }
        }

        [Function("iotMultiEquipmentRead", "批量设备参数读取")]
        public async Task<Dictionary<string, Dictionary<string, object>>> ReadMultiEquipmentVariables(DataIotMultiEquipmentRead data)
        {
            var engine = this.GetEngine();
            
            // 获取参数值
            var equipmentIdsObj = GetDataValue(data.EquipmentIds, engine);
            
            // 参数验证和转换
            if (equipmentIdsObj == null)
            {
                throw new CustomException("设备ID数组不能为空");
            }

            string[] equipmentIds;
            if (equipmentIdsObj is string id)
            {
                equipmentIds = [id];
            }
            else if(equipmentIdsObj is string[] stringArray)
            {
                equipmentIds = stringArray;
            }
            else if (equipmentIdsObj is System.Collections.IEnumerable enumerable)
            {
                equipmentIds = enumerable.Cast<object>().Select(x => x?.ToString()).Where(x => !string.IsNullOrEmpty(x)).ToArray();
            }
            else
            {
                throw new CustomException("设备ID数组格式不正确");
            }

            if (equipmentIds.Length == 0)
            {
                throw new CustomException("设备ID数组不能为空");
            }

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"设备ID数组: [{string.Join(", ", equipmentIds)}]");
            }

            try
            {
                var result = new Dictionary<string, Dictionary<string, object>>();
                
                foreach (var equipmentId in equipmentIds)
                {
                    var equipmentTask = _communicationManager.GetEquipmentTask(equipmentId);
                    if (equipmentTask != null)
                    {
                        var values = equipmentTask.GetCurrentValues();
                        if (values != null && values.Count > 0)
                        {
                            result[equipmentId] = values;
                        }
                    }
                    else
                    {
                        await this.Context.SqlLog.Warn($"未找到设备通信任务: {equipmentId}", true);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                await this.Context.SqlLog.Error(ex, $"批量设备参数读取异常: {ex.Message}", true);
                throw new CustomException($"批量设备参数读取失败: {ex.Message}");
            }
        }
    }
}
