namespace GCP.Iot.Models
{
    public class VariableValueChangedEventArgs : EventArgs
    {
        public string EquipmentId { get; }
        public string VariableName { get; }
        public object OldValue { get; }
        public object NewValue { get; }
        public DateTime Timestamp { get; }

        public VariableValueChangedEventArgs(string equipmentId, string variableName, object oldValue, object newValue)
        {
            EquipmentId = equipmentId;
            VariableName = variableName;
            OldValue = oldValue;
            NewValue = newValue;
            Timestamp = DateTime.Now;
        }
    }
}