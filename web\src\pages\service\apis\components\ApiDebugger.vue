<template>
  <t-dialog :header="apiInfo.apiName" width="80%" :footer="false" class="api-debugger-dialog">
    <t-space direction="vertical" style="width: 100%">
      <t-row :gutter="[8, 8]">
        <t-col :span="12">
          <t-input-adornment class="api-url-input">
            <template #prepend>
              <t-select
                v-model="debugInfo.requestType"
                auto-width
                :options="methodOptions"
                :default-value="apiInfo.requestType"
              ></t-select>
            </template>
            <t-input v-model="debugInfo.httpUrl" placeholder="请输入API地址" />
            <template #append>
              <t-button theme="primary" @click="sendRequest">发送</t-button>
            </template>
          </t-input-adornment>
        </t-col>
      </t-row>

      <t-row :gutter="8">
        <t-col :span="12">
          <t-tabs v-model="activeTab">
            <t-tab-panel :value="1" label="参数">
              <t-table :data="paramsData" rowKey="id" bordered size="small" stripe>
                <t-table-column width="80">
                  <template #cell="{ row }">
                    <t-checkbox v-model="row.enabled" />
                  </template>
                </t-table-column>
                <t-table-column width="160" field="key" header="参数名">
                  <template #cell="{ row }">
                    <t-input v-model="row.key" placeholder="名称" />
                  </template>
                </t-table-column>
                <t-table-column field="value" header="参数值">
                  <template #cell="{ row }">
                    <t-input v-model="row.value.textValue" placeholder="值" />
                  </template>
                </t-table-column>
                <t-table-column width="120" field="description" header="描述">
                  <template #cell="{ row }">
                    {{ row.description }}
                  </template>
                </t-table-column>
                <t-table-column width="100" fixed="right">
                  <template #cell="{ row, rowIndex }">
                    <t-space>
                      <t-button size="small" variant="text" @click="removeParam(rowIndex)">删除</t-button>
                    </t-space>
                  </template>
                </t-table-column>
              </t-table>
              <t-button style="margin-top: 8px" size="small" variant="dashed" block @click="addParam"
                >添加参数</t-button
              >
            </t-tab-panel>
            <t-tab-panel :value="2" label="请求头">
              <t-table :data="headersData" rowKey="id" bordered size="small" stripe>
                <t-table-column width="80">
                  <template #cell="{ row }">
                    <t-checkbox v-model="row.enabled" />
                  </template>
                </t-table-column>
                <t-table-column width="160" field="key" header="名称">
                  <template #cell="{ row }">
                    <t-input v-model="row.key" placeholder="名称" />
                  </template>
                </t-table-column>
                <t-table-column field="value" header="值">
                  <template #cell="{ row }">
                    <t-input v-model="row.value.textValue" placeholder="值" />
                  </template>
                </t-table-column>
                <t-table-column width="120" field="description" header="描述">
                  <template #cell="{ row }">
                    {{ row.description }}
                  </template>
                </t-table-column>
                <t-table-column width="100" fixed="right">
                  <template #cell="{ row, rowIndex }">
                    <t-space>
                      <t-button size="small" variant="text" @click="removeHeader(rowIndex)">删除</t-button>
                    </t-space>
                  </template>
                </t-table-column>
              </t-table>
              <t-button style="margin-top: 8px" size="small" variant="dashed" block @click="addHeader"
                >添加请求头</t-button
              >
            </t-tab-panel>
            <t-tab-panel :value="3" label="请求体">
              <div v-if="isBodyObject">
                <t-table :data="bodyData" rowKey="id" bordered size="small" stripe>
                  <t-table-column width="80">
                    <template #cell="{ row }">
                      <t-checkbox v-model="row.enabled" />
                    </template>
                  </t-table-column>
                  <t-table-column width="160" field="key" header="参数名">
                    <template #cell="{ row }">
                      <t-input v-model="row.key" placeholder="名称" />
                    </template>
                  </t-table-column>
                  <t-table-column field="value" header="参数值">
                    <template #cell="{ row }">
                      <t-input v-model="row.value.textValue" placeholder="值" />
                    </template>
                  </t-table-column>
                  <t-table-column width="120" field="description" header="描述">
                    <template #cell="{ row }">
                      {{ row.description }}
                    </template>
                  </t-table-column>
                  <t-table-column width="100" fixed="right">
                    <template #cell="{ row, rowIndex }">
                      <t-space>
                        <t-button size="small" variant="text" @click="removeBodyParam(rowIndex)">删除</t-button>
                      </t-space>
                    </template>
                  </t-table-column>
                </t-table>
                <t-button style="margin-top: 8px" size="small" variant="dashed" block @click="addBodyParam"
                  >添加参数</t-button
                >
              </div>
              <div v-else>
                <t-radio-group v-model="bodyMode" style="margin-bottom: 8px">
                  <t-radio-button value="form">表单</t-radio-button>
                  <t-radio-button value="raw">原始</t-radio-button>
                </t-radio-group>
                <t-select v-if="bodyMode === 'raw'" v-model="rawBodyType" style="width: 150px; margin-bottom: 8px">
                  <t-option value="json" label="JSON"></t-option>
                  <t-option value="text" label="Text"></t-option>
                  <t-option value="xml" label="XML"></t-option>
                </t-select>
                <div v-if="bodyMode === 'raw'">
                  <t-textarea
                    v-model="rawBody"
                    placeholder="请输入请求体内容"
                    :autosize="{ minRows: 10, maxRows: 15 }"
                  ></t-textarea>
                </div>
                <div v-else>
                  <t-table :data="formBodyData" rowKey="id" bordered size="small" stripe>
                    <t-table-column width="80">
                      <template #cell="{ row }">
                        <t-checkbox v-model="row.enabled" />
                      </template>
                    </t-table-column>
                    <t-table-column width="160" field="key" header="参数名">
                      <template #cell="{ row }">
                        <t-input v-model="row.key" placeholder="名称" />
                      </template>
                    </t-table-column>
                    <t-table-column field="value" header="参数值">
                      <template #cell="{ row }">
                        <t-input v-model="row.value.textValue" placeholder="值" />
                      </template>
                    </t-table-column>
                    <t-table-column width="100" fixed="right">
                      <template #cell="{ row, rowIndex }">
                        <t-space>
                          <t-button size="small" variant="text" @click="removeFormBodyParam(rowIndex)">删除</t-button>
                        </t-space>
                      </template>
                    </t-table-column>
                  </t-table>
                  <t-button style="margin-top: 8px" size="small" variant="dashed" block @click="addFormBodyParam"
                    >添加参数</t-button
                  >
                </div>
              </div>
            </t-tab-panel>
            <t-tab-panel :value="4" label="Mock">
              <t-form>
                <t-form-item label="启用Mock">
                  <t-switch v-model="mockEnabled" />
                </t-form-item>
                <t-form-item v-if="mockEnabled" label="Mock类型">
                  <t-radio-group v-model="mockType">
                    <t-radio value="auto">自动生成</t-radio>
                    <t-radio value="custom">自定义</t-radio>
                  </t-radio-group>
                </t-form-item>
                <t-form-item v-if="mockEnabled && mockType === 'custom'" label="自定义Mock数据">
                  <t-textarea
                    v-model="customMockData"
                    placeholder="请输入Mock数据(JSON格式)"
                    :autosize="{ minRows: 10, maxRows: 15 }"
                  ></t-textarea>
                </t-form-item>
              </t-form>
            </t-tab-panel>
          </t-tabs>
        </t-col>
        <t-col :span="12">
          <t-tabs v-model="responseTab">
            <t-tab-panel :value="1" label="响应">
              <t-loading :loading="loading" style="height: 100%">
                <t-row style="margin-bottom: 8px">
                  <t-col>
                    <t-space>
                      <t-tag v-if="responseStatusCode" :theme="getStatusCodeTheme">{{ responseStatusCode }}</t-tag>
                      <span>{{ responseTimeInfo }}</span>
                    </t-space>
                  </t-col>
                </t-row>
                <t-tabs v-if="responseData" v-model="responseDetailTab">
                  <t-tab-panel :value="1" label="响应体">
                    <div class="response-container">
                      <pre>{{ formattedResponse }}</pre>
                    </div>
                  </t-tab-panel>
                  <t-tab-panel :value="2" label="响应头">
                    <t-table :data="responseHeadersData" rowKey="key" size="small" bordered stripe>
                      <t-table-column field="key" header="名称" width="180"></t-table-column>
                      <t-table-column field="value" header="值"></t-table-column>
                    </t-table>
                  </t-tab-panel>
                </t-tabs>
                <div v-else class="no-response">
                  <t-empty image="info-circle" description="尚未发送请求或请求未得到响应" />
                </div>
              </t-loading>
            </t-tab-panel>
          </t-tabs>
        </t-col>
      </t-row>
    </t-space>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { getRandomId } from '@/components/action-panel/utils';
import { FlowData, FlowDataValue } from '@/components/action-panel/model';
import { ApiInfoDto } from '../store';

const props = defineProps<{
  apiInfo: ApiInfoDto;
}>();

// 请求方法选项
const methodOptions = computed(() =>
  ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].map((value) => ({ label: value, value })),
);

// API调试数据
const debugInfo = ref({
  requestType: '',
  httpUrl: '',
});

// 标签页状态
const activeTab = ref(1);
const responseTab = ref(1);
const responseDetailTab = ref(1);

// 请求参数
const paramsData = ref<(FlowData & { enabled: boolean })[]>([]);
const headersData = ref<(FlowData & { enabled: boolean })[]>([]);
const bodyData = ref<(FlowData & { enabled: boolean })[]>([]);
const formBodyData = ref<(FlowData & { enabled: boolean })[]>([]);

// 请求体相关
const bodyMode = ref('form');
const rawBodyType = ref('json');
const rawBody = ref('');
const isBodyObject = computed(() => {
  const requestType = debugInfo.value.requestType;
  return requestType === 'POST' || requestType === 'PUT' || requestType === 'PATCH';
});

// Mock 相关
const mockEnabled = ref(false);
const mockType = ref('auto');
const customMockData = ref('');

// 响应相关
const loading = ref(false);
const responseData = ref(null);
const responseStatusCode = ref('');
const responseTime = ref(0);
const responseHeadersData = ref([]);
const responseTimeInfo = computed(() => {
  return responseTime.value ? `${responseTime.value}ms` : '';
});

const getStatusCodeTheme = computed(() => {
  const code = parseInt(responseStatusCode.value);
  if (code >= 200 && code < 300) return 'success';
  if (code >= 300 && code < 400) return 'warning';
  if (code >= 400) return 'danger';
  return 'default';
});

const formattedResponse = computed(() => {
  if (!responseData.value) return '';
  try {
    return JSON.stringify(responseData.value, null, 2);
  } catch {
    return responseData.value;
  }
});

// 监听apiInfo变化
watch(
  () => props.apiInfo,
  () => {
    initDebugger();
  },
);

// 初始化调试器
const initDebugger = () => {
  const { apiInfo } = props;

  debugInfo.value = {
    requestType: apiInfo.requestType,
    httpUrl: apiInfo.httpUrl,
  };

  // 初始化参数
  resetParams();

  // 重置响应
  responseData.value = null;
  responseStatusCode.value = '';
  responseTime.value = 0;
  responseHeadersData.value = [];
};

// 重置参数
const resetParams = () => {
  try {
    // 解析查询参数
    const queryParams = JSON.parse(props.apiInfo.queryParameters || '[]');
    paramsData.value = queryParams.map((param) => ({
      ...param,
      enabled: true,
      value: {
        ...param.value,
        type: param.value?.type || 'text',
        textValue: param.value?.textValue || '',
      },
    }));

    // 解析请求头
    const headers = JSON.parse(props.apiInfo.headers || '[]');
    headersData.value = headers.map((header) => ({
      ...header,
      enabled: true,
      value: {
        ...header.value,
        type: header.value?.type || 'text',
        textValue: header.value?.textValue || '',
      },
    }));

    // 解析请求体
    const body = JSON.parse(props.apiInfo.body || '[]');
    bodyData.value = body.map((item) => ({
      ...item,
      enabled: true,
      value: {
        ...item.value,
        type: item.value?.type || 'text',
        textValue: item.value?.textValue || '',
      },
    }));

    // 初始化表单请求体
    formBodyData.value = [];
    // 如果没有请求体，默认添加一个空行
    if (bodyData.value.length === 0) {
      addBodyParam();
    }
    if (formBodyData.value.length === 0) {
      addFormBodyParam();
    }
  } catch (error) {
    console.error('初始化参数失败', error);
    paramsData.value = [];
    headersData.value = [];
    bodyData.value = [];
    formBodyData.value = [];
  }
};

// 添加参数
const addParam = () => {
  const newParam: FlowData & { enabled: boolean } = {
    id: getRandomId(),
    key: '',
    type: 'string',
    description: '',
    enabled: true,
    value: {
      type: 'text',
      textValue: '',
    },
  };
  paramsData.value.push(newParam);
};

// 删除参数
const removeParam = (index: number) => {
  paramsData.value.splice(index, 1);
};

// 添加请求头
const addHeader = () => {
  const newHeader: FlowData & { enabled: boolean } = {
    id: getRandomId(),
    key: '',
    type: 'string',
    description: '',
    enabled: true,
    value: {
      type: 'text',
      textValue: '',
    },
  };
  headersData.value.push(newHeader);
};

// 删除请求头
const removeHeader = (index: number) => {
  headersData.value.splice(index, 1);
};

// 添加请求体参数
const addBodyParam = () => {
  const newParam: FlowData & { enabled: boolean } = {
    id: getRandomId(),
    key: '',
    type: 'string',
    description: '',
    enabled: true,
    value: {
      type: 'text',
      textValue: '',
    },
  };
  bodyData.value.push(newParam);
};

// 删除请求体参数
const removeBodyParam = (index: number) => {
  bodyData.value.splice(index, 1);
};

// 添加表单请求体参数
const addFormBodyParam = () => {
  const newParam: FlowData & { enabled: boolean } = {
    id: getRandomId(),
    key: '',
    type: 'string',
    description: '',
    enabled: true,
    value: {
      type: 'text',
      textValue: '',
    },
  };
  formBodyData.value.push(newParam);
};

// 删除表单请求体参数
const removeFormBodyParam = (index: number) => {
  formBodyData.value.splice(index, 1);
};

// 自动生成Mock数据
const generateMockData = () => {
  let mockData = {};

  try {
    // 根据响应模板生成mock数据
    const responseTemplate = JSON.parse(props.apiInfo.response || '[]');

    function generateValueByType(item: FlowData): any {
      const type = item.type;

      switch (type) {
        case 'string':
          return `Mock_${item.key}`;
        case 'number':
        case 'decimal':
          return Math.floor(Math.random() * 100);
        case 'bool':
        case 'boolean':
          return Math.random() > 0.5;
        case 'date':
        case 'datetime':
          return new Date().toISOString();
        case 'array':
          if (item.children && item.children.length > 0) {
            const arrayItems = [];
            for (let i = 0; i < 3; i++) {
              const arrayItem = {};
              for (const child of item.children) {
                arrayItem[child.key] = generateValueByType(child);
              }
              arrayItems.push(arrayItem);
            }
            return arrayItems;
          }
          return [];
        case 'object':
          if (item.children && item.children.length > 0) {
            const obj = {};
            for (const child of item.children) {
              obj[child.key] = generateValueByType(child);
            }
            return obj;
          }
          return {};
        default:
          return `Mock_${item.key}`;
      }
    }

    // 从响应模板构建mock数据
    for (const item of responseTemplate) {
      mockData[item.key] = generateValueByType(item);
    }

    // 如果是标准响应格式，构造完整响应
    if (mockData['code'] !== undefined && mockData['data'] !== undefined) {
      return mockData;
    } else {
      // 如果没有标准格式，构造标准格式
      return {
        code: 200,
        message: '操作成功',
        data: mockData,
      };
    }
  } catch (error) {
    console.error('生成Mock数据失败', error);
    return {
      code: 200,
      message: 'Mock数据生成成功',
      data: {},
    };
  }
};

// 发送请求
const sendRequest = async () => {
  loading.value = true;
  responseData.value = null;
  responseStatusCode.value = '';
  responseTime.value = 0;
  responseHeadersData.value = [];

  try {
    const startTime = new Date().getTime();

    // 如果启用Mock
    if (mockEnabled.value) {
      setTimeout(() => {
        const endTime = new Date().getTime();
        responseTime.value = endTime - startTime;

        if (mockType.value === 'custom' && customMockData.value) {
          try {
            responseData.value = JSON.parse(customMockData.value);
          } catch {
            responseData.value = customMockData.value;
          }
        } else {
          responseData.value = generateMockData();
        }

        responseStatusCode.value = '200';
        responseHeadersData.value = [
          { key: 'Content-Type', value: 'application/json; charset=utf-8' },
          { key: 'Server', value: 'Mock Server' },
          { key: 'Date', value: new Date().toString() },
        ];

        loading.value = false;
      }, 500);
      return;
    }

    // 构建URL和查询参数
    let url = debugInfo.value.httpUrl;
    if (!url.startsWith('http')) {
      url = (props.apiInfo.baseUrl || '') + url;
    }

    // 添加查询参数
    const queryParams = new URLSearchParams();
    paramsData.value.forEach((param) => {
      if (param.enabled && param.key && param.value?.textValue) {
        queryParams.append(param.key, param.value.textValue);
      }
    });

    const queryString = queryParams.toString();
    if (queryString && (debugInfo.value.requestType === 'GET' || debugInfo.value.requestType === 'DELETE')) {
      url += (url.includes('?') ? '&' : '?') + queryString;
    }

    // 请求头
    const headers = new Headers();
    headersData.value.forEach((header) => {
      if (header.enabled && header.key && header.value?.textValue) {
        headers.append(header.key, header.value.textValue);
      }
    });

    // 请求体
    let body = null;
    if (debugInfo.value.requestType !== 'GET' && debugInfo.value.requestType !== 'DELETE') {
      if (bodyMode.value === 'raw') {
        body = rawBody.value;
        if (rawBodyType.value === 'json') {
          headers.append('Content-Type', 'application/json');
        } else if (rawBodyType.value === 'xml') {
          headers.append('Content-Type', 'application/xml');
        } else {
          headers.append('Content-Type', 'text/plain');
        }
      } else if (bodyMode.value === 'form') {
        const formData = new FormData();
        formBodyData.value.forEach((param) => {
          if (param.enabled && param.key && param.value?.textValue) {
            formData.append(param.key, param.value.textValue);
          }
        });
        body = formData;
      } else {
        // 对象模式，构建JSON
        const bodyObj = {};
        bodyData.value.forEach((param) => {
          if (param.enabled && param.key) {
            bodyObj[param.key] = param.value?.textValue || '';
          }
        });
        body = JSON.stringify(bodyObj);
        headers.append('Content-Type', 'application/json');
      }
    }

    // 发送请求
    const response = await fetch(url, {
      method: debugInfo.value.requestType,
      headers,
      body,
    });

    const endTime = new Date().getTime();
    responseTime.value = endTime - startTime;
    responseStatusCode.value = response.status.toString();

    // 处理响应头
    const headerEntries = [];
    response.headers.forEach((value, key) => {
      headerEntries.push({ key, value });
    });
    responseHeadersData.value = headerEntries;

    // 处理响应体
    const contentType = response.headers.get('content-type') || '';
    if (contentType.includes('application/json')) {
      responseData.value = await response.json();
    } else {
      responseData.value = await response.text();
    }
  } catch (error) {
    console.error('请求失败', error);
    MessagePlugin.error('请求失败: ' + (error as Error).message);
    responseData.value = { error: (error as Error).message };
    responseStatusCode.value = 'Error';
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="less" scoped>
.api-debugger-dialog {
  :deep(.t-dialog__body) {
    padding: 16px;
  }
}

.api-url-input {
  width: 100%;
  margin-bottom: 8px;
}

.response-container {
  max-height: 500px;
  overflow: auto;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px;
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.no-response {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
