using System.ComponentModel.DataAnnotations;

namespace GCP.Functions.Common.VisualFunction
{
    /// <summary>
    /// 可视化函数步骤
    /// </summary>
    public class VisualFunctionStep
    {
        /// <summary>
        /// 步骤ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 函数名称
        /// </summary>
        [Required]
        public string FunctionName { get; set; } = string.Empty;

        /// <summary>
        /// 函数类型
        /// </summary>
        public VisualFunctionType FunctionType { get; set; } = VisualFunctionType.Builtin;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 参数列表
        /// </summary>
        public List<VisualFunctionParameter> Parameters { get; set; } = new();

        /// <summary>
        /// 输出变量名
        /// </summary>
        public string? OutputVariable { get; set; }

        /// <summary>
        /// 执行顺序
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// 可视化函数参数
    /// </summary>
    public class VisualFunctionParameter
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 参数类型
        /// </summary>
        public VisualParameterType Type { get; set; } = VisualParameterType.Text;

        /// <summary>
        /// 参数值
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; } = true;

        /// <summary>
        /// 参数描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 可视化函数配置
    /// </summary>
    public class VisualFunctionConfiguration
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 配置名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 函数步骤列表
        /// </summary>
        public List<VisualFunctionStep> Steps { get; set; } = new();

        /// <summary>
        /// 输入参数定义
        /// </summary>
        public List<VisualFunctionInputParameter> InputParameters { get; set; } = new();

        /// <summary>
        /// 输出参数定义
        /// </summary>
        public VisualFunctionOutputParameter? OutputParameter { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// 可视化函数输入参数定义
    /// </summary>
    public class VisualFunctionInputParameter
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 参数类型
        /// </summary>
        public string Type { get; set; } = "string";

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; } = true;

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 可视化函数输出参数定义
    /// </summary>
    public class VisualFunctionOutputParameter
    {
        /// <summary>
        /// 输出类型
        /// </summary>
        public string Type { get; set; } = "object";

        /// <summary>
        /// 输出描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 可视化函数执行请求
    /// </summary>
    public class VisualFunctionExecuteRequest
    {
        /// <summary>
        /// 函数步骤列表
        /// </summary>
        [Required]
        public List<VisualFunctionStep> Steps { get; set; } = new();

        /// <summary>
        /// 输入数据
        /// </summary>
        public Dictionary<string, object> InputData { get; set; } = new();

        /// <summary>
        /// 是否只生成表达式（不执行）
        /// </summary>
        public bool GenerateExpressionOnly { get; set; } = false;

        /// <summary>
        /// 是否使用编译表达式（默认true，测试模式可设为false获取详细步骤信息）
        /// </summary>
        public bool UseCompiledExpression { get; set; } = true;
    }

    /// <summary>
    /// 可视化函数执行响应
    /// </summary>
    public class VisualFunctionExecuteResponse
    {
        /// <summary>
        /// 执行结果
        /// </summary>
        public object? Result { get; set; }

        /// <summary>
        /// 生成的C#表达式
        /// </summary>
        public string? GeneratedExpression { get; set; }

        /// <summary>
        /// 执行时间（毫秒）
        /// </summary>
        public long ExecutionTimeMs { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 步骤执行详情
        /// </summary>
        public List<VisualFunctionStepResult> StepResults { get; set; } = new();
    }

    /// <summary>
    /// 可视化函数步骤执行结果
    /// </summary>
    public class VisualFunctionStepResult
    {
        /// <summary>
        /// 步骤ID
        /// </summary>
        public string StepId { get; set; } = string.Empty;

        /// <summary>
        /// 步骤名称
        /// </summary>
        public string StepName { get; set; } = string.Empty;

        /// <summary>
        /// 执行结果
        /// </summary>
        public object? Result { get; set; }

        /// <summary>
        /// 执行时间（毫秒）
        /// </summary>
        public long ExecutionTimeMs { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 步骤输入数据
        /// </summary>
        public Dictionary<string, object>? InputData { get; set; }

        /// <summary>
        /// 输出变量名
        /// </summary>
        public string? OutputVariable { get; set; }
    }

    /// <summary>
    /// 函数类型枚举
    /// </summary>
    public enum VisualFunctionType
    {
        /// <summary>
        /// 内置函数
        /// </summary>
        Builtin = 0,

        /// <summary>
        /// C#函数
        /// </summary>
        CSharp = 1,

        /// <summary>
        /// JavaScript函数
        /// </summary>
        JavaScript = 2
    }

    /// <summary>
    /// 参数类型枚举
    /// </summary>
    public enum VisualParameterType
    {
        /// <summary>
        /// 固定文本值
        /// </summary>
        Text = 0,

        /// <summary>
        /// 变量引用
        /// </summary>
        Variable = 1,

        /// <summary>
        /// 上一步结果
        /// </summary>
        PreviousResult = 2
    }
}
