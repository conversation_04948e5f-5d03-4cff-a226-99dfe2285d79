<template>
  <div style="width: 100%" @mouseover.stop="showEdit = true" @blur="showEdit = false">
    <t-select
      v-if="(autoHideEdit && showEdit) || !autoHideEdit"
      v-bind="targetAttrs"
      v-model="value"
      v-model:popup-visible="popupVisible"
      class="value-type-select"
    >
      <template #suffix>
        <t-button
          :class="'required-btn' + (required ? '' : ' not-active')"
          theme="default"
          variant="base"
          size="small"
          :disabled="disabled"
          @click.stop.prevent="onClickRequired"
          >*</t-button
        >
      </template>
    </t-select>
    <div v-else :class="'value-input-text' + (disabled ? ' is-disabled' : '')">
      <div v-if="value">
        {{ VALUE_TYPE_MAP[value] }}
        <div class="suffix">
          <t-button
            :class="'required-btn' + (required ? '' : ' not-active')"
            theme="default"
            variant="base"
            size="small"
            :disabled="disabled"
            @click.stop.prevent="onClickRequired"
            >*</t-button
          >
        </div>
      </div>
      <div v-else class="value-input-text-placeholder">{{ placeholder }}</div>
    </div>
  </div>
</template>
<script lang="tsx">
export default {
  name: 'ValueTypeSelect',
};
</script>
<script setup lang="tsx">
import { SelectProps, Tag } from 'tdesign-vue-next';
import { computed, ref, useAttrs, watch, watchEffect } from 'vue';

import { VALUE_TYPE_MAP } from './constants';

interface ValueTypeSelectProps extends Omit<SelectProps, 'options'> {
  type: string;
  required?: boolean;
  autoHideEdit?: boolean;
}

const props = withDefaults(defineProps<ValueTypeSelectProps>(), {
  type: 'text',
  borderless: true,
  size: 'small',
  showArrow: false,
  placeholder: '选择类型',
  autoHideEdit: true,
});
const attrs: Partial<ValueTypeSelectProps> = useAttrs();
const targetAttrs = computed<ValueTypeSelectProps>(() => {
  return { ...attrs, ...props, ...{ options } };
});

const showEdit = ref(false);

const emits = defineEmits(['update:type', 'update:required']);

const popupVisible = ref(false);
const value = ref(props.type || 'object');
const required = ref(props.required || false);
const options = [
  {
    group: '复杂类型',
    children: [
      {
        label: '字典',
        value: 'object',
        content: (
          <div>
            字典 <Tag size="small" content="{ key: value... }"></Tag>
          </div>
        ),
      },
      {
        label: '数组',
        value: 'array',
        content: (
          <div>
            数组 <Tag size="small" content="[ 1, 2... ]"></Tag>
          </div>
        ),
      },
      // { label: '任意类型', value: 'any' },
    ],
  },
  {
    group: '基本类型',
    children: [
      {
        label: '文本',
        value: 'string',
        content: (
          <div>
            文本 <Tag size="small">abc...</Tag>
          </div>
        ),
      },
      {
        label: '数值',
        value: 'decimal',
        content: (
          <div>
            数值 <Tag size="small">1.38</Tag>
          </div>
        ),
      },
      {
        label: '整数',
        value: 'int',
        content: (
          <div>
            整型 <Tag size="small">300</Tag>
          </div>
        ),
      },
      {
        label: '短整型',
        value: 'short',
        content: (
          <div>
            短整型 <Tag size="small">0 ~ 65,535</Tag>
          </div>
        ),
      },
      {
        label: '长整型',
        value: 'long',
        content: (
          <div>
            长整型 <Tag size="small">0 ~ 2^63-1</Tag>
          </div>
        ),
      },
      {
        label: '日期',
        value: 'Date',
        content: (
          <div>
            日期 <Tag size="small">2012-12-21</Tag>
          </div>
        ),
      },
      {
        label: '日期时间',
        value: 'DateTime',
        content: (
          <div>
            日期时间 <Tag size="small">2012-12-21 03:14:35</Tag>
          </div>
        ),
      },
      {
        label: '布尔值',
        value: 'bool',
        content: (
          <div>
            布尔值 <Tag size="small">True | False</Tag>
          </div>
        ),
      },
      {
        label: '字符',
        value: 'char',
        content: (
          <div>
            字符 <Tag size="small">Y, N...</Tag>
          </div>
        ),
      },
      { label: '双精度浮点数', value: 'double' },
      { label: '单精度浮点数', value: 'float' },
      { label: '二进制数据', value: 'byte[]' },
    ],
  },
];
watchEffect(() => {
  value.value = props.type || 'object';
  required.value = props.required || false;
});
watch(value, (val) => {
  emits('update:type', val);
});
watch(required, (val) => {
  emits('update:required', val);
});

const onClickRequired = () => {
  popupVisible.value = false;
  required.value = !required.value;
};
</script>
<style lang="less" scoped>
.value-type-select,
.value-input-text {
  &.is-disabled {
    color: var(--td-text-color-disabled);
  }
  &:hover:not(:has(.t-is-disabled)) {
    .required-btn.not-active {
      display: inline-flex;
    }
  }

  .required-btn {
    height: 19px;
    color: var(--td-error-color) !important;
    border-color: transparent !important;

    &.not-active {
      color: var(--td-text-color-disabled) !important;
      background-color: #fff;
      border-color: #fff;
      display: none;
    }
  }
}

.value-input-text {
  font-size: 12px;
  height: 24px;
  padding: 1px 8px;
  border: 1px solid transparent;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  position: relative;
  color: var(--td-text-color-primary);
  // &.is-disabled {
  //   border: none;
  //   background-color: var(--td-bg-color-component-disabled);
  // }
  .suffix {
    position: absolute;
    top: -1px;
    right: 8px;
  }
  .value-input-text-placeholder {
    color: #999;
  }
}
</style>
