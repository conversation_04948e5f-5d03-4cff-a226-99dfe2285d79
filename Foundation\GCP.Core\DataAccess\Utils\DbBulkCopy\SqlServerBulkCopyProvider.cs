﻿using Microsoft.Data.SqlClient;
using System.Data;

namespace GCP.DataAccess
{
    internal static class SqlServerBulkCopyProvider
    {
        private static SqlBulkCopy PrepareOptions(IDbConnection connection, string tableName, IDbTransaction transaction = null)
        {
            SqlBulkCopy bulkCopy = new SqlBulkCopy((SqlConnection)connection, SqlBulkCopyOptions.KeepNulls | SqlBulkCopyOptions.KeepIdentity, (SqlTransaction)transaction);
            bulkCopy.BatchSize = 800;
            bulkCopy.DestinationTableName = tableName;

            bulkCopy.BulkCopyTimeout = 0;
            return bulkCopy;
        }

        internal static void SqlServerBulkCopy(this IDbConnection connection, string tableName, IDataReader reader, IDbTransaction transaction = null)
        {
            using (SqlBulkCopy bulkCopy = PrepareOptions(connection, tableName, transaction))
            {
                bulkCopy.WriteToServer(reader);
            }
        }

        internal static void SqlServerBulkCopy(this IDbConnection connection, string tableName, DataTable dt, IDbTransaction transaction = null)
        {
            using (SqlBulkCopy bulkCopy = PrepareOptions(connection, tableName, transaction))
            {
                bulkCopy.WriteToServer(dt);
            }
        }
    }
}
