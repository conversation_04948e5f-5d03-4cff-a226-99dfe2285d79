﻿using System.ComponentModel;
using GCP.Common;
using GCP.DataAccess;
using ModelContextProtocol.Server;

namespace GCP.Core.Ai.McpServer
{
    [McpServerToolType]
    public static class DbTools
    {
        [McpServerTool,
        Description("执行SQL查询并返回结果列表。适用于SELECT语句。")]
        public static List<dynamic> QueryList(
            [Description("数据源Id")]
            string dataSourceId,
            [Description("SQL查询语句")]
            string sqlQuery)
        {
            return QueryPaged(dataSourceId, sqlQuery, 0, 2000, "");
        }

        //[McpServerTool,
        //Description("执行SQL查询并返回分页结果。适用于需要分页的SELECT语句。")]
        public static List<dynamic> QueryPaged(
            [Description("数据源Id")]
            string dataSourceId,
            [Description("SQL查询语句")]
            string sqlQuery,
            [Description("起始索引")]
            int startIndex = 0,
            [Description("返回记录数")]
            int length = 10,
            [Description("排序SQL片段，例如：'id DESC'")]
            string orderBySql = "")
        {
            try
            {
                var dataSource = GetDataSourceById(dataSourceId);

                var db = new DbContext();
                db.SetDefaultConnection(dataSource.ConnectionString, dataSource.DataProvider);
                var result = db.Sql(sqlQuery).GetListPaged(startIndex, length, orderBySql);
                return result;
            }
            catch (Exception ex)
            {
                return new List<dynamic> { new { Error = $"查询执行错误: {ex.Message}" } };
            }
        }

        //[McpServerTool,
        //Description("执行SQL命令并返回受影响的行数。适用于INSERT、UPDATE、DELETE语句。")]
        //public static object ExecuteCommand(
        //    [Description("SQL命令语句")]
        //    string sqlCommand,
        //    [Description("连接字符串，默认使用DuckDB")]
        //    string connectionString = "Data Source=../logs.db")
        //{
        //    try
        //    {
        //        var db = new DbContext();
        //        db.SetDefaultConnection(connectionString, "DuckDB");
        //        int affectedRows = db.Sql(sqlCommand).Execute();
        //        return new { AffectedRows = affectedRows };
        //    }
        //    catch (Exception ex)
        //    {
        //        return new { Error = $"命令执行错误: {ex.Message}" };
        //    }
        //}

        [McpServerTool,
        Description("执行SQL查询并返回单个值。适用于聚合函数如COUNT、SUM等。")]
        public static object QueryValue(
            [Description("数据源Id")]
            string dataSourceId,
            [Description("SQL查询语句")]
            string sqlQuery)
        {
            try
            {
                var dataSource = GetDataSourceById(dataSourceId);

                var db = new DbContext();
                db.SetDefaultConnection(dataSource.ConnectionString, dataSource.DataProvider);
                // 通过执行查询并获取第一条记录的第一个字段来获取单个值
                var result = db.Sql(sqlQuery).Get();
                return new { Value = result };
            }
            catch (Exception ex)
            {
                return new { Error = $"查询执行错误: {ex.Message}" };
            }
        }

        [McpServerTool,
        Description("列出数据库中的所有表")]
        public static List<dynamic> ListTables(
            [Description("数据源Id")]
            string dataSourceId)
        {
            try
            {
                var dataSource = GetDataSourceById(dataSourceId);

                if (dataSource.DataProvider == "DuckDB")
                {
                    var db = new DbContext();
                    db.SetDefaultConnection(dataSource.ConnectionString, dataSource.DataProvider);

                    // DuckDB特定的获取表列表的SQL
                    string sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'main'";
                    var tables = db.Sql(sql).GetList<string>();

                    return tables.Select(t => new { TableName = t }).ToList<dynamic>();
                }
                else
                {
                    var schemas = new List<string>();
                    if (!string.IsNullOrEmpty(dataSource.Database) && dataSource.DataProvider == "Oracle")
                    {
                        schemas.Add(dataSource.Database);
                    }
                    using var db = new DbBase(dataSource.DataProvider, dataSource.ConnectionString);
                    var tables = db.GetSchema(includedSchemas: schemas.ToArray()).Tables;
                    return tables.Select(t => new { t.TableName, t.Description }).ToList<dynamic>();
                }
            }
            catch (Exception ex)
            {
                return new List<dynamic> { $"获取表列表错误: {ex.Message}" };
            }
        }

        private static LcDataSource GetDataSourceById(
            string dataSourceId)
        {
            using var db = new GcpDb();
            var data = db.LcDataSources
                .Where(t => t.Id == dataSourceId)
                .FirstOrDefault();

            if(data == null)
            {
                throw new CustomException("未找到数据源");
            }
            else
            {
                return data;
            }
        }


        [McpServerTool, Description("获取数据源Id")]
        public static string GetDataSourceId(
            [Description("数据源名称")]
            string dataSourceName)
        {
            using var db = new GcpDb();
            var list = db.LcDataSources
                .Where(t => t.State == 1 && t.DataSourceName.Contains(dataSourceName))
                .ToList();

            if (list.Count == 0)
            {
                throw new CustomException("未找到数据源");
            }
            else if (list.Count > 1)
            {
                throw new CustomException("找到多个数据源，" + string.Join(",", list.Select(t => t.DataSourceName)));
            }
            else
            {
                return list.First().Id;
            }
        }

        [McpServerTool, Description("搜索表结构信息")]
        public static dynamic SearchTableSchemas(
            [Description("数据源Id")]
            string dataSourceId,
            [Description("表名")]
            string tableName)
        {
            try
            {
                var dataSource = GetDataSourceById(dataSourceId);

                tableName = tableName?.ToLower();

                if(dataSource.DataProvider == "DuckDB")
                {
                    var db = new DbContext();
                    db.SetDefaultConnection(dataSource.ConnectionString, dataSource.DataProvider);

                    // DuckDB特定的获取表列表的SQL
                    string sql = $@"
                    SELECT 
                        table_name
                        column_name, 
                        data_type,
                        is_nullable,
                        column_default
                    FROM 
                        information_schema.columns 
                    WHERE 
                       table_schema = 'main'
                    ORDER BY 
                        ordinal_position";
                    var tables = db.Sql(sql).GetList();
                    return tables.Where(t => t.table_name.ToLower() == tableName).FirstOrDefault();
                }
                else
                {
                    var schemas = new List<string>();
                    if (!string.IsNullOrEmpty(dataSource.Database))
                    {
                        schemas.Add(dataSource.Database);
                    }

                    using var db = new DbBase(dataSource.DataProvider, dataSource.ConnectionString);
                    var tables = db.GetSchema(includedSchemas: schemas.ToArray()).Tables;

                    var result = tables
                        .Where(t => t.TableName.ToLower() == tableName)
                        .FirstOrDefault();

                    if (result != null)
                    {
                        // 处理Description BUG
                        result.Description = result.Description?.TrimStart("Description:");
                        return result;
                    }
                    else
                    {
                        return new { Error = "未找到表" };
                    }
                }

            }
            catch (Exception ex)
            {
                throw new Exception($"搜索表结构错误: {ex.Message}");
            }
        }
    }
}
