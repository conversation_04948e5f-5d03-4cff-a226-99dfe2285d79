using System.Threading.Channels;
using GCP.Common;

namespace GCP.Eventbus.Extensions
{
    public static class MessageBusExtensions
    {
        /// <summary>
        /// 异步订阅消息，直接返回IAsyncEnumerable用于await foreach
        /// </summary>
        /// <param name="messageBus">消息总线</param>
        /// <param name="topic">主题</param>
        /// <param name="options">消费者选项</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步可枚举集合，可直接用于await foreach循环消费消息</returns>
        public static async Task<IAsyncEnumerable<MessageEnvelope>> SubscribeAsEnumerableAsync(
            this IMessageBus messageBus,
            string topic,
            ConsumerOptions options,
            CancellationToken cancellationToken = default)
        {
            // 创建有界通道
            var channelOptions = new BoundedChannelOptions(100)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = true
            };
            
            var channel = Channel.CreateBounded<MessageEnvelope>(channelOptions);
            
            // 订阅消息并写入通道
            await messageBus.SubscribeAsync(topic, 
                async (message, ct) => 
                {
                    await channel.Writer.WriteAsync(message, ct);
                }, 
                options, 
                cancellationToken);
            
            // 在取消时关闭通道
            cancellationToken.Register(() => channel.Writer.TryComplete());
            
            // 返回通道的异步枚举器
            return channel.Reader.ReadAllAsync(cancellationToken);
        }
        
        /// <summary>
        /// 异步批量订阅消息，直接返回IAsyncEnumerable用于await foreach
        /// </summary>
        /// <param name="messageBus">消息总线</param>
        /// <param name="topic">主题</param>
        /// <param name="options">消费者选项</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步可枚举集合，可直接用于await foreach循环消费批量消息</returns>
        public static async Task<IAsyncEnumerable<List<MessageEnvelope>>> SubscribeBatchAsEnumerableAsync(
            this IMessageBus messageBus,
            string topic,
            ConsumerOptions options,
            CancellationToken cancellationToken = default)
        {
            // 创建有界通道
            var channelOptions = new BoundedChannelOptions(10)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = true
            };
            
            var channel = Channel.CreateBounded<List<MessageEnvelope>>(channelOptions);
            
            // 订阅批量消息并写入通道
            await messageBus.SubscribeBatchAsync(topic, 
                async (messages, ct) => 
                {
                    await channel.Writer.WriteAsync(messages, ct);
                }, 
                options, 
                cancellationToken);
            
            // 在取消时关闭通道
            cancellationToken.Register(() => channel.Writer.TryComplete());
            
            // 返回通道的异步枚举器
            return channel.Reader.ReadAllAsync(cancellationToken);
        }
    }
} 