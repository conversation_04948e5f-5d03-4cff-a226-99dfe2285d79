<template>
  <cmp-container full>
    <cmp-card no-fill>
      <cmp-table-tree-container
        style="height: calc(-90px + 100vh)"
        name="全局变量"
        dir-type="D"
        :columns="columns"
        :get-default-data="getTableDefaultData"
        :get-by-dir-code="getVariablesByDirCode"
        :save="saveVariable"
        :remove="removeVariable"
        :get-by-id="getVariableById"
        :get-current-data="getCurrentData"
        @validate="onValidate"
      >
        <template #item="{ item }">
          <variable-item
            :name="getVariable(item.leafId)?.dictName || item.label"
            :desc="getVariable(item.leafId)?.dictCode || item.value"
            :tag="getVariable(item.leafId)?.dictType"
          />
        </template>
        <template #form="{ currentData }">
          <variable-form ref="variableFormRef" :data="currentData as VariableData" />
        </template>
      </cmp-table-tree-container>
    </cmp-card>
  </cmp-container>
</template>

<script lang="ts">
export default {
  name: 'GlobalVariables',
};
</script>

<script setup lang="ts">
import { type PrimaryTableCol } from 'tdesign-vue-next';
import { ref } from 'vue';

import { api, Services } from '@/api/system';
import CmpTableTreeContainer from '@/components/cmp-table-tree-container/CmpTableTreeContainer.vue';

import VariableForm, { VariableData } from './VariableForm.vue';
import VariableItem from './VariableItem.vue';

// 表格列定义
const columns: PrimaryTableCol[] = [
  { colKey: 'dictCode', title: '变量代码' },
  { colKey: 'dictName', title: '变量名称' },
  { colKey: 'dictType', title: '值类型' },
  { colKey: 'dictValue', title: '变量值', ellipsis: true },
  { colKey: 'op', title: '操作', width: 160, fixed: 'right' },
];

const tableData = ref<VariableData[]>([]);
const variableFormRef = ref();

// 获取变量
const getVariable = (id: string) => {
  return tableData.value.find((v) => v.id === id);
};

// 获取变量默认数据
const getTableDefaultData = (currentNode: any): VariableData => {
  return {
    id: '',
    dictCode: '',
    dictName: '',
    dictValue: '',
    dictType: 'TEXT',
    description: '',
    dirCode: currentNode.id,
    groupCode: 'SYS_PARAM',
    seq: tableData.value.length + 1,
  };
};

// 获取目录下的变量列表
const getVariablesByDirCode = async (dirId: string) => {
  const data = await api.run(Services.dataDictionaryGetVariablesByDirCode, {
    dirId,
  });
  tableData.value = data;
  return data;
};

// 获取单个变量
const getVariableById = (id: string) => {
  return api.run(Services.dataDictionaryGet, {
    id,
  });
};

// 保存变量
const saveVariable = (varData: VariableData) => {
  return api.run(Services.dataDictionarySave, varData);
};

// 删除变量
const removeVariable = (id: string) => {
  return api.run(Services.dataDictionaryRemove, {
    id,
  });
};

// 保存变量回调
const onValidate = (varData: VariableData) => {
  return variableFormRef.value?.validate();
};

// 获取当前数据
const getCurrentData = () => {
  const data = variableFormRef.value?.getData();
  return {
    data,
    dirData: {
      dirCode: data.dictCode,
      dirName: data.dictName,
      description: data.description,
    },
  };
};
</script>

<style lang="less" scoped></style>
