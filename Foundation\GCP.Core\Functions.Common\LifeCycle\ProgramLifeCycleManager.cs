﻿namespace GCP.Functions.Common
{
    public class ProgramLifeCycleManager
    {
        /// <summary>
        /// 程序启动时执行
        /// </summary>
        public static void Startup()
        {
        }

        /// <summary>
        /// 程序关闭时执行
        /// </summary>
        public static void Shutdown()
        {
        }

        /// <summary>
        /// 程序发生错误时执行
        /// </summary>
        /// <param name="ex"></param>
        public static void OnError(Exception ex)
        {
        }
    }
}
