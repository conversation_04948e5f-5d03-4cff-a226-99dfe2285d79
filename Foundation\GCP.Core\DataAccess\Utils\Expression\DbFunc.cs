﻿using System.Collections;

namespace GCP.DataAccess
{
    public class DbFunc
    {
        /// <summary>
        /// 求和
        /// </summary>
        public static int Sum(object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 最大值
        /// </summary>
        public static int Max(object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 最小值
        /// </summary>
        public static int Min(object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 平均值
        /// </summary>
        public static int Avg(object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 统计记录数
        /// </summary>
        public static int Count(object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 大写
        /// </summary>
        public static T Upper<T>(T val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 小写
        /// </summary>
        public static T Lower<T>(T val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 查找包含
        /// </summary>
        public static bool In(object column, ICollection val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 查找包含子查询
        /// </summary>
        //public static bool In(object column, ISqlBuilder val)
        //{
        //    throw new NotSupportedException("Cannot call DB method");
        //}

        /// <summary>
        /// 查找不包含
        /// </summary>
        public static bool NotIn(object column, ICollection val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 查找相似
        /// </summary>
        public static bool Like(object column, object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 查找左相似
        /// </summary>
        public static bool LikeLeft(object column, object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }

        /// <summary>
        /// 查找右相似
        /// </summary>
        public static bool LikeRight(object column, object val)
        {
            throw new NotSupportedException("Cannot call DB method");
        }
    }
}
