<template>
  <t-row :gutter="[16, 16]" class="row-container">
    <t-col :xs="12" :xl="6">
      <t-card :title="'调用次数'" class="dashboard-rank-card" :bordered="false">
        <template #actions>
          <t-radio-group default-value="today" variant="default-filled" @change="onCallRankRangeChange">
            <t-radio-button value="today">今日</t-radio-button>
            <t-radio-button value="yesterday">昨日</t-radio-button>
            <t-radio-button value="week">本周</t-radio-button>
          </t-radio-group>
        </template>
        <t-table :data="SERVICE_CALL_LIST" :columns="SERVICE_CALL_COLUMNS" style="height: 280px">
          <template #index="{ rowIndex }">
            <span :class="getRankClass(rowIndex)">
              {{ rowIndex + 1 }}
            </span>
          </template>
          <template #successRate="{ row }">
            <span>{{ row.successRate }}%</span>
          </template>
        </t-table>
      </t-card>
    </t-col>
    <t-col :xs="12" :xl="6">
      <t-card :title="'错误次数'" class="dashboard-rank-card" :bordered="false">
        <template #actions>
          <t-radio-group default-value="today" variant="default-filled" @change="onErrorRankRangeChange">
            <t-radio-button value="today">今日</t-radio-button>
            <t-radio-button value="yesterday">昨日</t-radio-button>
            <t-radio-button value="week">本周</t-radio-button>
          </t-radio-group>
        </template>
        <t-table :data="SERVICE_ERROR_LIST" :columns="SERVICE_ERROR_COLUMNS" style="height: 280px">
          <template #index="{ rowIndex }">
            <span :class="getRankClass(rowIndex)">
              {{ rowIndex + 1 }}
            </span>
          </template>
          <template #operation="{ row }">
            <t-link theme="primary" @click="rehandleClickOp(row)">查看</t-link>
          </template>
        </t-table>
      </t-card>
    </t-col>
  </t-row>
</template>

<script setup lang="ts">
import type { TdBaseTableProps } from 'tdesign-vue-next';

import { useDashboardService, CallRankBaseItem, ErrorRankBaseItem } from '@/composables/services/dashboard';
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router/dist/vue-router';

const props = defineProps<{
  triggerType: string;
}>();

const dashboardService = useDashboardService();
const SERVICE_CALL_LIST = ref<CallRankBaseItem[]>([]);
const SERVICE_ERROR_LIST = ref<ErrorRankBaseItem[]>([]);
const callRankTimeRange = ref<'today' | 'yesterday' | 'week'>('today');
const errorRankTimeRange = ref<'today' | 'yesterday' | 'week'>('today');
let timer: number | undefined;

const fetchRankData = async () => {
  await Promise.all([fetchCallRank(), fetchErrorRank()]);
};

const fetchCallRank = async () => {
  SERVICE_CALL_LIST.value = [];
  var data = await dashboardService.getCallRank(props.triggerType, callRankTimeRange.value);
  SERVICE_CALL_LIST.value = data.base;
};

const fetchErrorRank = async () => {
  SERVICE_ERROR_LIST.value = [];
  var data = await dashboardService.getErrorRank(props.triggerType, errorRankTimeRange.value);
  SERVICE_ERROR_LIST.value = data.base;
};

const onCallRankRangeChange = (val: string) => {
  callRankTimeRange.value = val as any;
  fetchCallRank();
};
const onErrorRankRangeChange = (val: string) => {
  errorRankTimeRange.value = val as any;
  fetchErrorRank();
};

watch(
  () => props.triggerType,
  () => {
    fetchRankData();
  },
);

onMounted(() => {
  fetchRankData();
  timer = window.setInterval(fetchRankData, 5 * 60 * 1000);
});
onUnmounted(() => {
  if (timer) clearInterval(timer);
});

const SERVICE_CALL_COLUMNS: TdBaseTableProps['columns'] = [
  {
    align: 'center',
    colKey: 'index',
    title: '排名',
    width: 70,
    fixed: 'left',
  },
  {
    align: 'left',
    ellipsis: true,
    colKey: 'serviceName',
    title: '任务名',
    width: 150,
  },
  {
    align: 'center',
    colKey: 'count',
    title: '调用次数',
    width: 90,
  },
  {
    align: 'center',
    colKey: 'successRate',
    title: '成功率',
    width: 80,
  },
];

const SERVICE_ERROR_COLUMNS: TdBaseTableProps['columns'] = [
  {
    align: 'center',
    colKey: 'index',
    title: '排名',
    width: 70,
    fixed: 'left',
  },
  {
    align: 'left',
    ellipsis: true,
    colKey: 'serviceName',
    width: 150,
    title: '任务名',
  },
  {
    align: 'center',
    colKey: 'count',
    title: '错误次数',
    width: 90,
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 70,
    fixed: 'right',
  },
];

const getRankClass = (index: number) => {
  return ['dashboard-rank', { 'dashboard-rank__top': index < 3 }];
};

const router = useRouter();
const rehandleClickOp = (row) => {
  console.log(row);
  router.push({
    path: '/system/log',
    query: {
      triggerType: row.triggerType,
      functionId: row.functionId,
    },
  });
};

// 需要暴露给模板
defineExpose({
  onCallRankRangeChange,
  onErrorRankRangeChange,
});
</script>

<style lang="less" scoped>
.dashboard-rank-card {
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);

  :deep(.t-card__header) {
    padding: 0;
  }

  :deep(.t-card__title) {
    font: var(--td-font-title-large);
    font-weight: 400;
  }

  :deep(.t-card__body) {
    padding: 0;
    margin-top: var(--td-comp-margin-xxl);
  }
}

.dashboard-rank__cell {
  display: inline-flex;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  color: white;
  font-size: 14px;
  background-color: var(--td-gray-color-5);
  align-items: center;
  justify-content: center;
  font-weight: 700;

  &--top {
    background: var(--td-brand-color);
  }
}
</style>
