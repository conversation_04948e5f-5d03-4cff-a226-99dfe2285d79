﻿using DuckDB.NET.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    internal class DuckDbProvider : BaseDbProvider
    {
        public override string ProviderName
        {
            get
            {
                return "DuckDB.NET.Data";
            }
        }

        public override bool NotUseParameter
        {
            get
            {
                return false;
            }
        }

        public override DbProviderFactory Factory => DuckDBClientFactory.Instance;
        public override DbProviderType ProviderType => DbProviderType.SQLite;

        public override string GetParameterName(string parameterName)
        {
            return parameterName[0] == '$' ? parameterName : "$" + parameterName;
        }

        public override string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "")
        {
            return string.Format("SELECT t.* FROM ({2}) AS t {3} LIMIT {1} OFFSET {0}", startIndex, length, selectSql, orderBySql);
        }

        public override string GetTimeFormatStr(DateTime time)
        {
            return "strptime('" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','%c')";
        }

        public override string GetTimeSql()
        {
            return "SELECT CURRENT_TIMESTAMP";
        }
    }
}
