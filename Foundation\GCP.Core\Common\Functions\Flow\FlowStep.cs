﻿using System.Runtime.Serialization;

namespace GCP.Common
{
    [DataContract]
    [Serializable]
    public sealed class FlowStep
    {
        /// <summary>
        /// 步骤id
        /// </summary>
        [DataMember(Name = "id")]
        public string Id { get; set; }

        /// <summary>
        /// 步骤名称（主要用于日志）
        /// </summary>
        [DataMember(Name = "name")]
        public string Name { get; set; }

        /// <summary>
        /// 函数路径
        /// </summary>
        [DataMember(Name = "function")]
        public string Function { get; set; }

        /// <summary>
        /// 补偿函数路径（执行失败后进行补偿）
        /// </summary>
        [DataMember(Name = "undoFunction")]
        public string UndoFunction { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [DataMember(Name = "version")]
        public int? Version { get; set; }

        /// <summary>
        /// 每个步骤单独中间件, 可多个
        /// </summary>
        [DataMember(Name = "middleware")]
        public List<string> Middleware { get; set; }

        /// <summary>
        /// 函数入参
        /// </summary>
        [DataMember(Name = "args")]
        public Dictionary<string, object> Args { get; set; }

        /// <summary>
        /// 函数出参, 结果标准化
        /// </summary>
        [DataMember(Name = "result")]
        public List<FlowData> Result { get; set; }

        /// <summary>
        /// 下个步骤ID
        /// </summary>
        [DataMember(Name = "nextId")]
        public string NextId { get; set; }

        /// <summary>
        /// 是否等待执行完成, 再执行下一步
        /// </summary>
        [DataMember(Name = "wait")]
        public bool Wait { get; set; } = true;

        /// <summary>
        /// 控制类型
        /// </summary>
        [DataMember(Name = "controlType")]
        public string ControlType { get; set; }

        /// <summary>
        /// 只存在一个控制行为
        /// </summary>
        [DataMember(Name = "control")]
        public FlowControl Control { get; set; }

        /// <summary>
        /// 步骤行为
        /// </summary>
        [DataMember(Name = "behavior")]
        public FunctionExecuteBehavior Behavior { get; set; }
    }
}
