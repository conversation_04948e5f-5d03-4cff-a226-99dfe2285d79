﻿using System.Security.Cryptography;
using System.Text;

namespace GCP.Common
{
    /// <summary>
    /// 加密帮助类
    /// </summary>
    static class CryptoHelper
    {
        #region 密码加密/比较

        /// <summary>
        /// 将密码加密为32位不重复密文
        /// </summary>
        public static string PasswordEncrypt(string password)
        {
            if (string.IsNullOrEmpty(password)) return "";
            return EncryptPassword(password, Guid.NewGuid().ToString().Replace("-", "").Substring(8, 16).ToUpper());
        }

        /// <summary>
        /// 比较密码与密文是否相同
        /// </summary>
        public static bool PasswordVerify(string password, string encryptPassword)
        {
            return EncryptPassword(password, encryptPassword.Substring(16)) == encryptPassword;
        }

        private static string EncryptPassword(string password, string key)
        {
            return Md5To16(SHA_256(password + key)) + key;
        }
        #endregion

        #region 16进制编码/解码

        /// <summary>
        /// 字符串转换为16进制
        /// </summary>
        public static string HexEncode(string str)
        {
            return HexEncode(Encoding.UTF8.GetBytes(str));
        }

        /// <summary>
        /// byte数组转换为16进制
        /// </summary>
        public static string HexEncode(byte[] bytes)
        {
            return BitConverter.ToString(bytes).Replace("-", "");
            //if (bytes == null || bytes.Length == 0) return "";
            //StringBuilder sb = new StringBuilder();
            //foreach (byte t in bytes)
            //    sb.Append(t.ToString("X2"));
            //return sb.ToString();
        }

        /// <summary>
        /// 16进制转换为字符串
        /// </summary>
        public static string HexDecode(string hexStr)
        {
            return Encoding.UTF8.GetString(HexToBytes(hexStr));
        }

        /// <summary>
        /// 16进制转换byte数组
        /// </summary>
        public static byte[] HexToBytes(string hexStr)
        {
            hexStr = hexStr.Replace(" ", "");
            if ((hexStr.Length % 2) != 0)
                hexStr += " ";
            byte[] bytes = new byte[hexStr.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
                bytes[i] = Convert.ToByte(hexStr.Substring(i * 2, 2), 16);
            return bytes;
        }

        #endregion

        #region Base64编码/解码

        /// <summary>
        /// Base64编码
        /// </summary>
        public static string Base64Encode(string value)
        {
            return Base64Encode(value, Encoding.UTF8);
        }
        public static string Base64Encode(string value, Encoding encode)
        {
            return Base64Encode(encode.GetBytes(value));
        }
        public static string Base64Encode(byte[] bytes)
        {
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// Base64解码
        /// </summary>
        public static string Base64Decode(string value)
        {
            return Base64Decode(value, Encoding.UTF8);
        }
        public static string Base64Decode(string value, Encoding encode)
        {
            return encode.GetString(Base64ToBytes(value));
        }
        /// <summary>
        /// Base64转换byte数组
        /// </summary>
        public static byte[] Base64ToBytes(string value)
        {
            return Convert.FromBase64String(value);
        }


        /// <summary>
        /// 改进版Base64编码[Replace('+', '_').Replace('/', '-').Replace('=', '.')]
        /// </summary>
        public static string Base64UrlEncode(string value)
        {
            return Base64UrlEncode(value, Encoding.UTF8);
        }
        public static string Base64UrlEncode(string value, Encoding encode)
        {
            return Base64Encode(value, encode).Replace('+', '_').Replace('/', '-').Replace('=', '.');
        }

        /// <summary>
        /// 改进版Base64解码[Replace('_', '+').Replace('-', '/').Replace('.', '=')]
        /// </summary>
        public static string Base64UrlDecode(string value)
        {
            return Base64UrlDecode(value, Encoding.UTF8);
        }
        public static string Base64UrlDecode(string value, Encoding encode)
        {
            return Base64Decode(value.Replace('_', '+').Replace('-', '/').Replace('.', '='), encode);
        }
        #endregion

        #region 加密基础

        private static string HashCrypto(HashAlgorithm algorithm, byte[] bytes)
        {
            byte[] result = algorithm.ComputeHash(bytes);
            return BitConverter.ToString(result).Replace("-", "");
        }

        private static byte[] StreamCrypto(ICryptoTransform cryptoTransform, byte[] bytes)
        {
            using (MemoryStream ms = new MemoryStream())
            using (CryptoStream cs = new CryptoStream(ms, cryptoTransform, CryptoStreamMode.Write))
            {
                cs.Write(bytes, 0, bytes.Length);
                cs.FlushFinalBlock();
                return ms.ToArray();
            }
        }
        #endregion

        #region RSA加密/解密
        /// <summary>
        /// RSA密钥对生成
        /// </summary>
        /// <returns>私钥、公钥</returns>
        public static (string, string) RSA_Keys()
        {
            var rsa = RSA.Create();
            return (rsa.ToXmlString(true), rsa.ToXmlString(false));
        }

        /// <summary>
        /// RSA加密
        /// </summary>
        public static string RSA_Encrypt(string publicKey, string value)
        {
            using var rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(publicKey);
            var bytes = Encoding.UTF8.GetBytes(value);
            var encryptedBytes = rsa.Encrypt(bytes, false);
            return Convert.ToBase64String(encryptedBytes);
        }

        /// <summary>
        /// RSA解密
        /// </summary>
        public static string RSA_Decrypt(string privateKey, string value)
        {
            using var rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(privateKey);
            var bytes = Convert.FromBase64String(value);
            var decryptedBytes = rsa.Decrypt(bytes, false);
            return Encoding.UTF8.GetString(decryptedBytes);
        }
        #endregion

        #region SHA1/SHA256/SHA512加密

        /// <summary>
        /// SHA1加密
        /// </summary>
        public static string SHA_1(string password)
        {
            using SHA1 sha1 = SHA1.Create();
            return HashCrypto(sha1, Encoding.UTF8.GetBytes(password));
        }

        /// <summary>
        /// SHA256加密
        /// </summary>
        public static string SHA_256(string password)
        {
            using SHA256 sha256 = SHA256.Create();
            return HashCrypto(sha256, Encoding.UTF8.GetBytes(password));
        }

        /// <summary>
        /// SHA512加密
        /// </summary>
        public static string SHA_512(string password)
        {
            using SHA512 sha512 = SHA512.Create();
            return HashCrypto(sha512, Encoding.UTF8.GetBytes(password));
        }

        #endregion

        #region MD5 32/16位加密

        /// <summary>
        /// MD5 32位加密
        /// </summary>
        public static string Md5(string password)
        {
            return Md5(Encoding.UTF8.GetBytes(password));
        }

        /// <summary>
        /// MD5 16位加密
        /// </summary>
        public static string Md5To16(string password)
        {
            return Md5(password).Substring(8, 16);
        }

        /// <summary>
        /// MD5字节加密
        /// </summary>
        public static string Md5(byte[] bytes)
        {
            using MD5 md5 = MD5.Create();
            return HashCrypto(md5, bytes);
        }

        #endregion

        #region DES加密/解密
        private static readonly byte[] Key8 = { 123, 45, 67, 89, 101, 93, 145, 167 };
        private static readonly byte[] Iv8 = { 234, 12, 56, 78, 90, 112, 222, 156 };

        /// <summary>
        /// DES加密
        /// </summary>
        public static string EncryptAES(string value)
        {
            return EncryptAES(value, Key8, Iv8);
        }

        /// <summary>
        /// DES加密
        /// </summary>
        /// <param name="value">待加密的密文</param>
        /// <param name="key">密匙</param>
        public static string EncryptAES(string value, string key)
        {
            if (string.IsNullOrEmpty(key)) return EncryptAES(value);
            return EncryptAES(value, Encoding.UTF8.GetBytes(Md5(key).Substring(8, 8)), Iv8);
        }

        public static string EncryptAES(string value, byte[] key, byte[] iv)
        {
            if (string.IsNullOrEmpty(value)) return "";
            return Convert.ToBase64String(EncryptAES(Encoding.UTF8.GetBytes(value), key, iv));
        }

        public static byte[] EncryptAES(byte[] bytes, byte[] key, byte[] iv)
        {
            using var provider = Aes.Create();
            return StreamCrypto(provider.CreateEncryptor(key, iv), bytes);
        }

        /// <summary>
        /// DES解密
        /// </summary>
        public static string DecryptAES(string value)
        {
            return DecryptAES(value, Key8, Iv8);
        }

        /// <summary>
        /// DES解密
        /// </summary>
        /// <param name="value">待解密的密文</param>
        /// <param name="key">密匙（8位）</param>
        public static string DecryptAES(string value, string key)
        {
            if (string.IsNullOrEmpty(key)) return DecryptAES(value);
            return DecryptAES(value, Encoding.UTF8.GetBytes(Md5(key).Substring(8, 8)), Iv8);
        }

        public static string DecryptAES(string value, byte[] key, byte[] iv)
        {
            try
            {
                if (string.IsNullOrEmpty(value)) return "";
                return Encoding.UTF8.GetString(DecryptAES(Convert.FromBase64String(value), key, iv));
            }
            catch
            {
                return "";
            }
        }

        public static byte[] DecryptAES(byte[] bytes, byte[] key, byte[] iv)
        {
            using var provider = Aes.Create();
            return StreamCrypto(provider.CreateDecryptor(key, iv), bytes);
        }

        #endregion

        #region 3DES加密/解密
        private static readonly byte[] Key24 = { 192, 135, 201, 17, 219, 142, 19, 103, 111, 248, 165, 21, 189, 19, 234, 128, 153, 86, 227, 254, 14, 123, 209, 167 };
        private static readonly byte[] Iv24 = { 211, 18, 154, 237, 129, 200, 1, 110, 195, 210, 68, 255, 163, 101, 226, 177, 132, 245, 181, 117, 148, 223, 199, 123 };

        /// <summary>
        /// DES加密
        /// </summary>
        public static string Encrypt3DES(string value)
        {
            return Encrypt3DES(value, Key24, Iv24);
        }

        public static string Encrypt3DES(string value, string key)
        {
            if (string.IsNullOrEmpty(key)) return Encrypt3DES(value);
            return Encrypt3DES(value, Encoding.UTF8.GetBytes(Md5(key).Substring(8)), Iv24);
        }

        public static string Encrypt3DES(string value, byte[] key, byte[] iv)
        {
            if (string.IsNullOrEmpty(value)) return "";
            return Convert.ToBase64String(Encrypt3DES(Encoding.UTF8.GetBytes(value), key, iv));
        }

        public static byte[] Encrypt3DES(byte[] bytes, byte[] key, byte[] iv)
        {
            using var provider = TripleDES.Create();
            return StreamCrypto(provider.CreateEncryptor(key, iv), bytes);
        }

        /// <summary>
        /// 3DES解密
        /// </summary>
        public static string Decrypt3DES(string value)
        {
            return Decrypt3DES(value, Key24, Iv24);
        }

        public static string Decrypt3DES(string value, string key)
        {
            if (string.IsNullOrEmpty(key)) return Decrypt3DES(value);
            return Decrypt3DES(value, Encoding.UTF8.GetBytes(Md5(key).Substring(8)), Iv24);
        }

        public static string Decrypt3DES(string value, byte[] key, byte[] iv)
        {
            try
            {
                if (string.IsNullOrEmpty(value)) return "";
                return Encoding.UTF8.GetString(Decrypt3DES(Convert.FromBase64String(value), key, iv));
            }
            catch
            {
                return "";
            }
        }

        public static byte[] Decrypt3DES(byte[] bytes, byte[] key, byte[] iv)
        {
            using var provider = TripleDES.Create();
            return StreamCrypto(provider.CreateDecryptor(key, iv), bytes);
        }

        #endregion

        #region 自定义加密/解密

        /// <summary>
        /// 加密
        /// </summary>
        public static string Encrypt(string value)
        {
            return Base64UrlEncode(Encrypt3DES(value));
        }

        public static string Encrypt(string value, string key)
        {
            if (string.IsNullOrEmpty(key)) return Encrypt(value);
            return Base64UrlEncode(Encrypt3DES(value, key));
        }

        /// <summary>
        /// 解密
        /// </summary>
        public static string Decrypt(string value)
        {
            return Decrypt3DES(Base64UrlDecode(value));
        }

        public static string Decrypt(string value, string key)
        {
            if (string.IsNullOrEmpty(key)) return Decrypt(value);
            return Decrypt3DES(Base64UrlDecode(value), key);
        }

        #endregion
    }
}
