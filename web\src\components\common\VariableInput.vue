<template>
  <div class="variable-input">
    <t-input v-model="inputValue" :placeholder="placeholder" @input="handleInput" @blur="handleBlur">
      <template #suffix>
        <t-dropdown :options="variableOptions" @click="handleVariableSelect">
          <t-button variant="text" size="small">
            <template #icon><FunctionsIcon /></template>
          </t-button>
        </t-dropdown>
      </template>
    </t-input>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FunctionsIcon } from 'tdesign-icons-vue-next';

// Props
const props = defineProps<{
  modelValue?: string;
  placeholder?: string;
  variables?: any[];
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

// 响应式数据
const inputValue = ref(props.modelValue || '');

// 计算属性
const variableOptions = computed(() => {
  if (!props.variables || props.variables.length === 0) {
    return [{ content: '暂无可用变量', value: '', disabled: true }];
  }

  return props.variables.map((variable) => ({
    content: `${variable.name} (${variable.type})`,
    value: `{{${variable.name}}}`,
    disabled: false,
  }));
});

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue || '';
  },
);

// 方法
const handleInput = (value: string) => {
  inputValue.value = value;
  emit('update:modelValue', value);
};

const handleBlur = () => {
  emit('update:modelValue', inputValue.value);
};

const handleVariableSelect = (data: any) => {
  if (data.value && !data.disabled) {
    const cursorPosition = inputValue.value.length;
    const newValue = inputValue.value + data.value;
    inputValue.value = newValue;
    emit('update:modelValue', newValue);
  }
};
</script>

<style scoped>
.variable-input {
  width: 100%;
}
</style>
