import { isEmpty } from 'lodash-es';
import { defineStore } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';

import { api, Services } from '@/api/system';
import { FlowData } from '@/components/action-panel/model';

export interface ApiInfoDto {
  id: string;
  state: number;
  requestType: string;
  httpUrl: string;
  apiName: string;
  description: string;
  functionId: string | null;
  clusterId: string | null;
  hosts: string | null;
  headers: string | null;
  queryParameters: string | null;
  apiType: number;
  body: string;
  baseUrl: string;
  response: string;
  responseId: string | null;
}

export const useApiStore = defineStore('ApiInfoStore', {
  state: () => {
    return {
      currentApi: {} as ApiInfoDto,
      currentThirdPartyApi: {} as ApiInfoDto,
      isShowActionPanel: false,
      functionId: '',
      actionPanelRef: null as any,
      actionData: {} as FlowData[],
    };
  },
  getters: {
    variables() {
      const baseVariables = [
        {
          id: 'errorMessage',
          key: 'errorMessage',
          description: '错误信息',
          type: 'string',
        },
        {
          id: 'success',
          key: 'success',
          description: '是否成功',
          type: 'bool',
        },
        {
          id: 'result',
          key: 'result',
          description: '结果',
          type: 'object',
        },
      ] as FlowData[];

      // 如果有当前API，添加API的请求参数和响应数据
      if (this.currentApi) {
        try {
          // 添加请求参数
          const params = JSON.parse(this.currentApi.queryParameters || '[]');
          if (params && params.length > 0) {
            baseVariables.push({
              id: 'params',
              key: 'params',
              description: '请求参数',
              type: 'object',
              children: params,
            });
          }

          // 添加请求头
          const headers = JSON.parse(this.currentApi.headers || '[]');
          if (headers && headers.length > 0) {
            baseVariables.push({
              id: 'headers',
              key: 'headers',
              description: '请求头',
              type: 'object',
              children: headers,
            });
          }

          // 添加请求体
          const body = JSON.parse(this.currentApi.body || '[]');
          if (body && body.length > 0) {
            baseVariables.push({
              id: 'body',
              key: 'body',
              description: '请求体',
              type: 'object',
              children: body,
            });
          }

          // 添加响应数据
          const response = JSON.parse(this.currentApi.response || '[]');
          if (response && response.length > 0) {
            baseVariables.push({
              id: 'response',
              key: 'response',
              description: '响应数据',
              type: 'object',
              children: response,
            });
          }
        } catch (error) {
          console.error('解析API数据失败:', error);
        }
      }

      return baseVariables;
    },
  },
  actions: {
    initActionPanel(funcId: string) {
      this.isShowActionPanel = true;
      this.functionId = funcId;
      this.actionPanelRef.reload(funcId);
    },
    setCurrentApi(api: ApiInfoDto) {
      this.actionData = [];
      this.currentApi = api;
    },
    setCurrentThirdPartyApi(api: ApiInfoDto) {
      this.currentThirdPartyApi = api;
    },
    setActionData(data: FlowData[]) {
      this.actionData = data;
    },
    async saveApi(apiInfo: ApiInfoDto) {
      if (isEmpty(apiInfo.id)) {
        await api.run(Services.apiAdd, { api: apiInfo }).then(() => {
          MessagePlugin.success('保存成功');
        });
      } else {
        await api.run(Services.apiUpdate, { api: apiInfo }).then(() => {
          MessagePlugin.success('保存成功');
        });
      }
    },
    async deleteApi(id) {
      await api.run(Services.apiDelete, { id }).then(() => {
        MessagePlugin.success('删除成功');
      });
    },
  },
});
