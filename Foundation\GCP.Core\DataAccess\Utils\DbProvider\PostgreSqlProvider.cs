﻿using System.Data.Common;

namespace GCP.DataAccess
{
    internal class PostgreSqlProvider : BaseDbProvider
    {
        public override string ProviderName
        {
            get
            {
                return "Npgsql";
            }
        }

        public override bool NotUseParameter
        {
            get
            {
                return false;
            }
        }

        public override DbProviderFactory Factory => Npgsql.NpgsqlFactory.Instance;
        public override DbProviderType ProviderType => DbProviderType.PostgreSQL;

        public override string GetParameterName(string parameterName)
        {
            return parameterName[0] == ':' ? parameterName : ":" + parameterName;
        }

        public override string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "")
        {
            return string.Format("SELECT t.* FROM ({2}) as t {3} LIMIT {1} OFFSET {0}", startIndex, length, selectSql, orderBySql);
        }

        public override string GetTimeFormatStr(DateTime time)
        {
            return "TO_DATE('" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','YYYY-MM-DD HH24:MI:SS')";
        }

        public override string GetTimeSql()
        {
            return "SELECT CURRENT_TIMESTAMP";
        }
    }
}
