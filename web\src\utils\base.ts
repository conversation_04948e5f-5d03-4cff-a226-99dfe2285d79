/* eslint-disable no-nested-ternary */
// eslint-disable-next-line no-underscore-dangle
const _isScalar = (value: any): boolean => {
  return (
    value === undefined ||
    value === null ||
    typeof value === 'string' ||
    typeof value === 'number' ||
    typeof value === 'boolean'
  );
};

export const compute = (value: any): any => {
  return typeof value === 'function' ? value() : value;
};

export const computeObject = (object: any): any => {
  object = compute(object);

  if (_isScalar(object)) {
    return object;
  }

  const mapped: { [key: string]: any } = Array.isArray(object) ? [] : {};

  Object.keys(object).forEach((key) => {
    const value = object[key];

    if (_isScalar(value)) {
      mapped[key] = value;
    } else if ((typeof FormData === 'object' || typeof FormData === 'function') && value instanceof FormData) {
      mapped[key] = value;
    } else if (
      (typeof URLSearchParams === 'object' || typeof URLSearchParams === 'function') &&
      value instanceof URLSearchParams
    ) {
      mapped[key] = value;
    } else if (typeof value === 'object') {
      mapped[key] = computeObject(value);
    } else {
      mapped[key] = compute(value);
    }
  });

  return mapped;
};

/**
 * 检查是否为空（支持基础类型、数组、对象）
 * @param val
 */
export function isEmpty(val: any) {
  return !(val
    ? typeof val === 'object'
      ? Array.isArray(val)
        ? !!val.length
        : !!Object.keys(val).length
      : true
    : false);
}

/**
 * 格式化时间
 * @param milliseconds
 */
export function formatDuration(milliseconds) {
  const time = new Date(milliseconds);
  let result = '';

  if (time.getUTCDate() - 1 > 0) {
    result += ` ${time.getUTCDate() - 1} days`;
  }

  if (time.getUTCHours() > 0) {
    result += ` ${time.getUTCHours()} hours`;
  }

  if (time.getUTCMinutes() > 0) {
    result += ` ${time.getUTCMinutes()} minutes`;
  }

  if (time.getUTCDate() - 1 === 0 && time.getUTCSeconds() > 0) {
    result += ` ${time.getUTCSeconds()} s`;
  }

  if (time.getUTCHours() === 0 && time.getUTCMilliseconds() > 0) {
    result += ` ${time.getUTCMilliseconds()} ms`;
  }

  if (result === '') {
    result = '0 ms';
  }

  return result.trim();
}
