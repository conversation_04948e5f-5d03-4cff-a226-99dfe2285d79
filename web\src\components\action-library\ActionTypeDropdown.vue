<template>
  <t-dropdown 
    :options="actionTypeOptions" 
    @click="handleAddAction"
    placement="top"
  >
    <slot />
  </t-dropdown>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const emits = defineEmits<{
  addAction: [actionType: string];
}>();

const actionTypeOptions = ref([
  {
    content: 'JavaScript代码',
    value: 'javascript',
    description: '执行自定义JavaScript代码'
  },
  {
    content: 'HTTP请求',
    value: 'http',
    description: '发送HTTP请求'
  },
  {
    content: '数据库查询',
    value: 'database',
    description: '执行数据库查询'
  },
  {
    content: '条件判断',
    value: 'condition',
    description: '根据条件执行不同分支'
  },
  {
    content: '循环处理',
    value: 'loop',
    description: '循环处理数据'
  },
  {
    content: '数据转换',
    value: 'transform',
    description: '转换数据格式'
  },
  {
    content: '延时等待',
    value: 'delay',
    description: '延时指定时间'
  },
  {
    content: '发送邮件',
    value: 'email',
    description: '发送邮件通知'
  },
  {
    content: '文件操作',
    value: 'file',
    description: '读取或写入文件'
  },
  {
    content: '调用函数',
    value: 'function',
    description: '调用其他函数'
  }
]);

const handleAddAction = (data: any) => {
  emits('addAction', data.value);
};
</script>

<style lang="less" scoped>
// 样式由父组件控制
</style>
