﻿using System.Data;
using System.Linq.Expressions;
using System.Data.Common;

namespace GCP.DataAccess
{
    /// <summary>
    /// POCO Extension（高级扩展, 动态POCO序列化）
    /// </summary>
    public static class POCOExtension
    {
        public static T Get<T>(this DbConnection connection, string sqlString, CommandType cmdType = CommandType.Text, DbTransaction tran = null, int commandTimeout = 30, params IDataParameter[] parms)
        {
            return connection.ExecuteReader(tran, sqlString, cmdType, CommandBehavior.SingleRow, commandTimeout, parms).Get<T>();
        }
        public static T Get<T>(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text)
        {
            return connection.ExecuteReader(sqlString, parms, cmdType, CommandBehavior.SingleRow).Get<T>();
        }
        public static T Get<T>(this ISqlBuilder<T> sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return Get<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }
        public static T Get<T>(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return Get<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }


        public static List<T> GetList<T>(this DbConnection connection, string sqlString, CommandType cmdType = CommandType.Text, DbTransaction tran = null, int commandTimeout = 30, params IDataParameter[] parms)
        {
            return connection.ExecuteReader(tran, sqlString, cmdType, CommandBehavior.Default, commandTimeout, parms).GetList<T>();
        }
        public static List<T> GetList<T>(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text)
        {
            return connection.ExecuteReader(sqlString, parms, cmdType).GetList<T>();
        }
        public static List<T> GetList<T>(this ISqlBuilder<T> sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return GetList<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }
        public static List<T> GetList<T>(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return GetList<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }

        public static Dictionary<string, object> GetDictionary<T>(this DbConnection connection, Func<T, string> keySelector, Func<T, object> elementSelector, string sqlString, CommandType cmdType = CommandType.Text, DbTransaction tran = null, int commandTimeout = 30, params IDataParameter[] parms)
        {
            return connection.ExecuteReader(tran, sqlString, cmdType, CommandBehavior.Default, commandTimeout, parms).GetDictionary<T>(keySelector, elementSelector);
        }
        public static Dictionary<string, object> GetDictionary<T>(this ISqlBuilder<T> sqlBuilder, Func<T, string> keySelector, Func<T, object> elementSelector, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return GetDictionary<T>(connection ?? sqlBuilder.CreateConnection(), keySelector, elementSelector, sqlBuilder.SqlString, cmdType, tran, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }
        public static Dictionary<string, object> GetDictionary<T>(this ISqlBuilder sqlBuilder, Func<T, string> keySelector, Func<T, object> elementSelector, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return GetDictionary<T>(connection ?? sqlBuilder.CreateConnection(), keySelector, elementSelector, sqlBuilder.SqlString, cmdType, tran, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }


        public static List<T> GetListPaged<T>(this DbConnection connection, string sqlString, IDataParameter[] parms, int startIndex, int length, string orderBySql = "", DbTransaction tran = null, int commandTimeout = 30)
        {
            sqlString = DbProviderInfo.GetPagingSql(connection.GetDbProvider(), startIndex, length, sqlString, orderBySql);
            return connection.ExecuteReader(tran, sqlString, CommandType.Text, CommandBehavior.Default, commandTimeout, parms).GetList<T>(total: length);
        }
        public static List<T> GetListPaged<T>(this DbConnection connection, string sqlString, object parms, int startIndex, int length, string orderBySql = "", DbTransaction tran = null)
        {
            return GetListPagedBase<T>(new SqlBuilder<T>(connection).Init(sqlString, parms), startIndex, length, orderBySql, connection, tran);
        }
        public static List<T> GetListPaged<T>(this ISqlBuilder<T> sqlBuilder, int startIndex, int length, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return GetListPagedBase<T>(sqlBuilder, startIndex, length, orderBySql, connection, tran);
        }
        public static List<T> GetListPaged<T>(this ISqlBuilder sqlBuilder, int startIndex, int length, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return GetListPagedBase<T>(sqlBuilder, startIndex, length, orderBySql, connection, tran);
        }
        private static List<T> GetListPagedBase<T>(this ISqlBuilder sqlBuilder, int startIndex, int length, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return GetListPaged<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, sqlBuilder.Parameters, startIndex, length, orderBySql, tran);
        }


        public static PagingData<T> GetPagingData<T>(this DbConnection connection, string sqlString, IDataParameter[] parms, int pageSize, int pageIndex, string orderBySql = "", DbTransaction tran = null)
        {
            return GetPagingData<T>(new SqlBuilder<T>(connection).Init(sqlString).AddParameters(parms), pageSize, pageIndex, orderBySql, connection, tran);
        }
        public static PagingData<T> GetPagingData<T>(this DbConnection connection, string sqlString, object parms, int pageSize, int pageIndex, string orderBySql = "", DbTransaction tran = null)
        {
            return GetPagingData<T>(new SqlBuilder<T>(connection).Init(sqlString, parms), pageSize, pageIndex, orderBySql, connection, tran);
        }
        public static PagingData<T> GetPagingData<T>(this ISqlBuilder<T> sqlBuilder, int pageSize, int pageIndex, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return GetPagingDataBase<T>(sqlBuilder, pageSize, pageIndex, orderBySql, connection, tran);
        }
        public static PagingData<T> GetPagingData<T>(this ISqlBuilder sqlBuilder, int pageSize, int pageIndex, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return GetPagingDataBase<T>(sqlBuilder, pageSize, pageIndex, orderBySql, connection, tran);
        }
        private static PagingData<T> GetPagingDataBase<T>(this ISqlBuilder sqlBuilder, int pageSize, int pageIndex, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            if (connection == null) connection = sqlBuilder.CreateConnection();
            var wasClosed = connection.State == ConnectionState.Closed;
            try
            {
                if (wasClosed) connection.Open();
                var pagingData = new PagingData<T>();
                pagingData.Paging = new PagingInfo(sqlBuilder.Count(connection, tran), pageSize, pageIndex);
                pagingData.List = sqlBuilder.GetListPaged<T>((pageIndex - 1) * pageSize, pageSize, orderBySql, connection, tran);
                return pagingData;
            }
            finally
            {
                if (wasClosed) connection.Close();
            }
        }

        public static IEnumerable<List<T>> GetPagedIEnumerable<T>(this DbConnection connection, string sqlString, IDataParameter[] parms, int pageSize, string orderBySql = "", DbTransaction tran = null)
        {
            foreach (var dataPage in GetPagedIEnumerableBase<T>(new SqlBuilder<T>(connection).Init(sqlString).AddParameters(parms), pageSize, orderBySql, connection, tran))
            {
                yield return dataPage;
            }
        }
        public static IEnumerable<List<T>> GetPagedIEnumerable<T>(this DbConnection connection, string sqlString, object parms, int pageSize, string orderBySql = "", DbTransaction tran = null)
        {
            foreach (var dataPage in GetPagedIEnumerableBase<T>(new SqlBuilder<T>(connection).Init(sqlString, parms), pageSize, orderBySql, connection, tran))
            {
                yield return dataPage;
            }
        }
        public static IEnumerable<List<T>> GetPagedIEnumerable<T>(this ISqlBuilder<T> sqlBuilder, int pageSize, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            foreach (var dataPage in GetPagedIEnumerableBase<T>(sqlBuilder, pageSize, orderBySql, connection, tran))
            {
                yield return dataPage;
            }
        }

        public static IEnumerable<List<T>> GetPagedIEnumerable<T>(this ISqlBuilder sqlBuilder, int pageSize, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            foreach (var dataPage in GetPagedIEnumerableBase<T>(sqlBuilder, pageSize, orderBySql, connection, tran))
            {
                yield return dataPage;
            }
        }
        private static IEnumerable<List<T>> GetPagedIEnumerableBase<T>(this ISqlBuilder sqlBuilder, int pageSize, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            int pageIndex = 1;
            while (true)
            {
                var dataPage = sqlBuilder.GetListPaged<T>((pageIndex - 1) * pageSize, pageSize, orderBySql, connection, tran);
                if (dataPage.Count == 0)
                {
                    break;
                }
                yield return dataPage;
                pageIndex++;
            }
        }


        public static int Insert<T>(this DbConnection connection, T obj, DbTransaction tran = null, string tableName = null)
        {
            return connection.Execute(SqlBuilderBase.GetInsertSql(connection, obj, tableName), obj, CommandType.Text, tran);
        }
        public static int Insert<T>(this DbTransaction tran, T obj, string tableName = null)
        {
            return Insert(tran.Connection, obj, tran, tableName);
        }
        public static int Insert<T>(this IDbContext dbContext, T obj, string tableName = null, DbTransaction tran = null)
        {
            return Insert(dbContext.CreateConnection(), obj, tran, tableName);
        }

        public static int Update<T>(this DbConnection connection, T setObj, object whereObj = null, string tableName = null, object ignoreSetColumns = null, string idColumnName = "id", DbTransaction tran = null)
        {
            return SqlBuilderBase.GetUpdateSql(connection, tableName ?? DbHelper.GetTableName(setObj.GetType()), setObj, whereObj, ignoreSetColumns, idColumnName).Execute(tran);
        }
        public static int Update<T>(this DbTransaction tran, T setObj, object whereObj = null, string tableName = null, object ignoreSetColumns = null, string idColumnName = "id")
        {
            return Update(tran.Connection, setObj, whereObj, tableName, ignoreSetColumns, idColumnName, tran);
        }
        public static int Update<T>(this IDbContext dbContext, T setObj, object whereObj = null, string tableName = null, object ignoreSetColumns = null, string idColumnName = "id", DbTransaction tran = null)
        {
            return Update(dbContext.CreateConnection(), setObj, whereObj, tableName, ignoreSetColumns, idColumnName, tran);
        }

        public static int Update<T>(this DbConnection connection, object setObj, Expression<Func<T, bool>> where, string tableName = null, object ignoreSetColumns = null, DbTransaction tran = null)
        {
            return ExecuteSqlBuilder.Update(connection, setObj, where, tableName, ignoreSetColumns).Execute(tran);
        }
        public static int Update<T>(this DbTransaction tran, object setObj, Expression<Func<T, bool>> where, string tableName = null, object ignoreSetColumns = null)
        {
            return Update(tran.Connection, setObj, where, tableName, ignoreSetColumns, tran);
        }
        public static int Update<T>(this IDbContext dbContext, object setObj, Expression<Func<T, bool>> where, string tableName = null, object ignoreSetColumns = null, DbTransaction tran = null)
        {
            return Update(dbContext.CreateConnection(), setObj, where, tableName, ignoreSetColumns, tran);
        }

        public static int Update<T>(this DbConnection connection, Expression<Func<T, SetColunms>> set, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return ExecuteSqlBuilder.Update(connection, set, where, tableName).Execute(tran);
        }
        public static int Update<T>(this DbTransaction tran, Expression<Func<T, SetColunms>> set, Expression<Func<T, bool>> where, string tableName = null)
        {
            return Update(tran.Connection, set, where, tableName, tran);
        }
        public static int Update<T>(this IDbContext dbContext, Expression<Func<T, SetColunms>> set, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return Update(dbContext.CreateConnection(), set, where, tableName, tran);
        }



        public static int Delete<T>(this DbConnection connection, object whereObj, string tableName = null, string idColumnName = "id", DbTransaction tran = null)
        {
            return SqlBuilderBase.GetDeleteSql(connection, tableName ?? DbHelper.GetTableName(whereObj.GetType()), whereObj, idColumnName).Execute(connection, tran);
        }
        public static int Delete<T>(this DbTransaction tran, object whereObj, string tableName = null, string idColumnName = "id")
        {
            return Delete<T>(tran.Connection, whereObj, tableName, idColumnName, tran);
        }
        public static int Delete<T>(this IDbContext dbContext, object whereObj, string tableName = null, string idColumnName = "id", DbTransaction tran = null)
        {
            return Delete<T>(dbContext.CreateConnection(), whereObj, tableName, idColumnName, tran);
        }
        public static int Delete<T>(this DbConnection connection, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return ExecuteSqlBuilder.Delete(connection, where, tableName).Execute(tran);
        }
        public static int Delete<T>(this DbTransaction tran, Expression<Func<T, bool>> where, string tableName = null)
        {
            return Delete(tran.Connection, where, tableName, tran);
        }
        public static int Delete<T>(this IDbContext dbContext, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return Delete(dbContext.CreateConnection(), where, tableName, tran);
        }

        /// <summary>
        /// 获取数据库时间
        /// </summary>
        public static DateTime GetDbTime(this DbConnection connection)
        {
            return connection.Get<DateTime>(connection.GetDbProvider().GetTimeSql());
        }
        public static DateTime GetDbTime(this IDbContext db)
        {
            using (var connection = db.CreateConnection())
            {
                return connection.Get<DateTime>(connection.GetDbProvider().GetTimeSql());
            }
        }

        /// <summary>
        /// 自动包裹sql语句计算数量 select count(1) from ( + SqlString + ) t
        /// </summary>
        public static int Count(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            string sqlString = "select count(1) from (" + sqlBuilder.SqlString + ") t";
            return Get<int>(connection ?? sqlBuilder.CreateConnection(), sqlString, CommandType.Text, tran, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }


        /// <summary>
        /// 数据是否存在
        /// </summary>
        public static bool Exists(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            return sqlBuilder.ExecuteScalar(connection, tran) != null;
        }
    }
}
