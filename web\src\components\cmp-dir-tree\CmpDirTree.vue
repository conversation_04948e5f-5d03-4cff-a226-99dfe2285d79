<template>
  <div class="cmp-dir-tree">
    <cmp-list
      ref="listRef"
      :type="props.type"
      :name="props.name"
      :fetch-data="fetchData"
      :fetch-search-data="fetchSearchData"
      :fetch-tree-node-data="fetchTreeNodeData"
      @click="onSelect"
    >
      <template #header>
        <div class="header-actions">
          <t-button variant="text" size="small" @click="onAddRoot">
            <template #icon><add-icon /></template>
          </t-button>
        </div>
      </template>
      <template #item="{ item }">
        <slot v-if="item.isLeaf" class="dir-name" name="item" :item="item"></slot>
        <div v-if="!$slots.item || !item.isLeaf" class="dir-item">
          <span class="dir-name">{{ item.label }}</span>

          <div v-if="!item.isLeaf" class="dir-actions">
            <t-button variant="text" size="small" @click.stop="onAdd(item)">
              <template #icon><add-icon /></template>
            </t-button>
            <t-button variant="text" size="small" @click.stop="onEdit(item)">
              <template #icon><edit-icon /></template>
            </t-button>
            <t-button variant="text" size="small" @click.stop="onDelete(item)">
              <template #icon><delete-icon /></template>
            </t-button>
          </div>
        </div>
      </template>
    </cmp-list>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="isEdit ? '编辑分组' : '新增分组'"
      :on-confirm="onDialogConfirm"
      :on-close="onDialogClose"
    >
      <t-form ref="formRef" :data="formData" :rules="rules">
        <t-form-item v-if="currentItem?.label && !isEdit" label="父级" name="parentId">
          {{ currentItem?.label }}
        </t-form-item>
        <t-form-item label="编码" name="dirCode">
          <t-input v-model="formData.dirCode" placeholder="请输入编码" />
        </t-form-item>
        <t-form-item label="名称" name="dirName">
          <t-input v-model="formData.dirName" placeholder="请输入名称" />
        </t-form-item>
        <t-form-item label="描述" name="description">
          <t-textarea v-model="formData.description" placeholder="请输入描述" />
        </t-form-item>
        <!-- <t-form-item label="类型" name="isLeaf">
          <t-radio-group v-model="formData.isLeaf">
            <t-radio value="N">目录</t-radio>
            <t-radio value="Y">叶子节点</t-radio>
          </t-radio-group>
        </t-form-item> -->
      </t-form>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'CmpDirTree',
};
</script>

<script setup lang="ts">
import { AddIcon, DeleteIcon, EditIcon } from 'tdesign-icons-vue-next';
import type { FormRule } from 'tdesign-vue-next';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';

import { useDirTreeService } from '@/composables/services/dirTree';

import CmpList from '../cmp-list/CmpList.vue';
import type { BusinessItem } from '../cmp-list/constants';

interface Props {
  name?: string;
  type?: 'list' | 'tree';
  dirType?: string;
}

const props = withDefaults(defineProps<Props>(), {
  name: '目录树',
  type: 'tree',
  dirType: '',
});

const emit = defineEmits(['select']);

// 服务实例
const dirTreeService = useDirTreeService();

// 列表引用
const listRef = ref();

// 获取目录树数据
const fetchData = async () => {
  const result = await dirTreeService.getTree(props.dirType);
  return result || [];
};

// 搜索数据
const fetchSearchData = async (keyword: string, listData: any[]) => {
  const data = await dirTreeService.getLeafList(props.dirType, keyword);
  return data;
};

// 获取子节点数据
const fetchTreeNodeData = async (key: string) => {
  const children = await dirTreeService.getChildren(key);
  return children || [];
};

// 选择节点
const onSelect = (item: BusinessItem) => {
  emit('select', item);
};

// 表单相关
const dialogVisible = ref(false);
const isEdit = ref(false);
const currentItem = ref<any>(null);
const formRef = ref();
const formData = ref({
  dirName: '',
  dirCode: '',
  description: '',
  isLeaf: 'N' as 'Y' | 'N',
});

const rules = {
  dirName: [{ required: true, message: '请输入名称', type: 'error' }],
  dirCode: [{ required: true, message: '请输入编码', type: 'error' }],
} as Record<string, FormRule[]>;

// 新增根目录
const onAddRoot = () => {
  isEdit.value = false;
  currentItem.value = { id: 'ROOT' }; // 使用ROOT作为根目录的父ID
  formData.value = {
    dirName: '',
    dirCode: '',
    description: '',
    isLeaf: 'N',
  };
  dialogVisible.value = true;
};

// 新增目录
const onAdd = (item: any) => {
  isEdit.value = false;
  currentItem.value = item;
  formData.value = {
    dirName: '',
    dirCode: '',
    description: '',
    isLeaf: 'N',
  };
  dialogVisible.value = true;
};

// 编辑目录
const onEdit = (item: any) => {
  isEdit.value = true;
  currentItem.value = item;
  formData.value = {
    dirName: item.label,
    dirCode: item.value,
    description: item.description || '',
    isLeaf: item.isLeaf || 'N',
  };
  dialogVisible.value = true;
};

// 删除目录
const onDelete = async (item: any) => {
  const confirmDia = DialogPlugin.confirm({
    header: '警告',
    body: `确认删除该【${item.label}】分组吗？`,
    onConfirm: async () => {
      try {
        await dirTreeService.delete(item.id);
        MessagePlugin.success('删除成功');
        loadData();
      } finally {
        confirmDia.hide();
      }
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 对话框确认
const onDialogConfirm = async () => {
  const validateResult = await formRef.value?.validate();
  if (validateResult !== true) return;

  const data = {
    id: isEdit.value ? currentItem.value.id : '',
    parentId: isEdit.value ? currentItem.value.parentId : currentItem.value.id,
    dirName: formData.value.dirName,
    dirCode: formData.value.dirCode,
    description: formData.value.description,
    dirType: props.dirType,
    isLeaf: formData.value.isLeaf,
  };

  await dirTreeService.save(data);
  MessagePlugin.success(isEdit.value ? '编辑成功' : '新增成功');
  dialogVisible.value = false;
  loadData();
};

// 对话框关闭
const onDialogClose = () => {
  formRef.value?.reset();
  dialogVisible.value = false;
};

// 加载数据
const loadData = () => {
  if (listRef.value && typeof listRef.value.loadData === 'function') {
    listRef.value.loadData();
  }
};

onMounted(() => {
  loadData();
});

// 暴露方法
defineExpose({
  loadData,
});
</script>

<style lang="less" scoped>
.cmp-dir-tree {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-actions {
    display: flex;
    justify-content: flex-end;
  }

  .dir-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;

    .dir-name {
      flex: 1;
    }

    .dir-actions {
      display: none;
    }

    &:hover .dir-actions {
      display: flex;
      gap: 4px;
    }
  }
}
</style>
