<template>
  <t-list :class="'action-list' + (parentId ? ' bordered' : '')" v-bind="targetAttrs">
    <!-- 空容器的放置区域 -->
    <drag-drop-zone v-if="dataList.length === 0" position="inside" :parent-id="parentId">
      <div class="empty-drop-zone">
        <div class="empty-drop-zone-text">拖拽动作到此处</div>
      </div>
    </drag-drop-zone>

    <template v-for="(item, index) in dataList" :key="item.id">
      <!-- 第一个项目前的放置线 -->
      <!-- <drop-line v-if="index === 0" :target-item="item" position="before" :parent-id="parentId" /> -->

      <action-list-item
        :item="item"
        :active-id="activeId"
        :parent-id="parentId"
        :draggable="draggable !== false"
        @add-step="onClickAddStep"
      />

      <!-- 每个项目后的放置线 -->
      <drop-line v-if="index === dataList.length - 1" :target-item="item" position="after" :parent-id="parentId" />
    </template>

    <template #footer>
      <action-panel-dropdown @add-action="onClickAddAction">
        <t-button block variant="dashed">添 加 动 作</t-button>
      </action-panel-dropdown>
    </template>
  </t-list>
</template>
<script lang="ts">
export default {
  name: 'ActionList',
};
</script>
<script setup lang="ts">
import { ListProps } from 'tdesign-vue-next';
import { computed, useAttrs } from 'vue';

import ActionListItem from './ActionListItem.vue';
import ActionPanelDropdown from './ActionPanelDropdown.vue';
import DragDropZone from './DragDropZone.vue';
import DropLine from './DropLine.vue';
import { getComponentConfig } from './actions';
import { FlowStep } from './model';
import { useActionFlowStore } from './store/index';
import { getRandomId } from './utils';

export interface ActionListProps extends Omit<ListProps, 'options'> {
  startId?: string;
  activeId?: string;
  parentId?: string;
  draggable?: boolean;
}
const actionFlowStore = useActionFlowStore();

const props = withDefaults(defineProps<ActionListProps>(), {
  size: 'small',
});
const attrs: Partial<ActionListProps> = useAttrs();
const targetAttrs = computed<ActionListProps>(() => {
  return { ...attrs, ...props };
});

const dataList = computed(() => {
  const result = actionFlowStore.getActionListByStartId(props.startId);
  return result;
});

const addStep = (flowStep: FlowStep) => {
  if (props.startId) {
    if (dataList.value.length > 0) {
      dataList.value[dataList.value.length - 1].nextId = flowStep.id;
    }
  } else if (props.parentId) {
    actionFlowStore.setStepNextId(props.parentId, flowStep.id, true);
  }

  actionFlowStore.addFlowStep(flowStep);

  setTimeout(() => {
    actionFlowStore.setCurrentStep(flowStep);
  }, 300);
};

const onClickAddAction = (item: any) => {
  const config = getComponentConfig(item.value);
  config.componentName = item.value;
  const flowStep: FlowStep = {
    id: getRandomId(),
    name: config.name,
    function: config.function,
    controlType: config.controlType || null,
    config,
  };

  addStep(flowStep);
};

const onClickAddStep = ({
  sourceStep,
  targetStep,
  position,
}: {
  sourceStep: FlowStep;
  targetStep: FlowStep;
  position: 'before' | 'inside' | 'after';
}) => {
  addStep(sourceStep);
  actionFlowStore.moveStep(sourceStep, targetStep, position);
};
</script>
<style lang="less" scoped>
.action-list {
  width: 100%;

  &.bordered {
    border-left: 1px dashed var(--td-border-level-2-color);
    border-bottom: 1px dashed var(--td-border-level-2-color);
  }
  :deep(> .t-list__inner) {
    padding-top: 2px;
  }
}

.empty-drop-zone {
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--td-border-level-2-color);
  border-radius: 4px;
  margin: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--td-brand-color);
    background-color: var(--td-brand-color-light);
  }
}

.empty-drop-zone-text {
  color: var(--td-text-color-placeholder);
  font-size: 14px;
}
</style>

<style lang="less"></style>
