using System.Collections.Concurrent;

namespace GCP.Iot.Services
{
    class EquipmentTypeConfig
    {
        /// <summary>
        /// 设备类型
        /// </summary>
        public string EquipmentType { get; set; }

        internal ConcurrentDictionary<string, EquipmentCommunicationTask> Equipments { get; } = new();
        private readonly ConcurrentDictionary<string, DateTime> _lastEquipmentEventTime = new();

        internal bool ShouldTriggerEvent(string equipmentId, int? archivePeriod)
        {
            if (archivePeriod is null or <= 0)
                return true;

            var now = DateTime.Now;
            var lastEventTime = _lastEquipmentEventTime.GetOrAdd(equipmentId, now);

            // 检查是否在防抖时间内
            if ((now - lastEventTime).TotalMilliseconds >= archivePeriod)
            {
                _lastEquipmentEventTime[equipmentId] = now;
                return true;
            }

            return false;
        }

        public Task<Dictionary<string, Dictionary<string, object>>> GetBatchValuesAsync()
        {
            var result = Equipments.Values
                .Select(equipment => new KeyValuePair<string, Dictionary<string, object>>(
                    equipment.EquipmentId,
                    equipment.GetCurrentValues()
                ))
                .Where(pair => pair.Value.Count > 0)
                .ToDictionary(pair => pair.Key, pair => pair.Value);

            return Task.FromResult(result);
        }
    }
}