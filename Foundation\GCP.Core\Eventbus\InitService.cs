﻿using GCP.Common;
using GCP.Eventbus.Infrastructure;
using Serilog;

namespace GCP.Core.Eventbus
{
    public class InitService : IFunctionService
    {
        [Function("/gcp/eventbus/init", "初始化事件总线服务", startupRun: true)]
        public async Task Init()
        {
            var _eventBusInitializer = ServiceLocator.Current.GetService(typeof(EventBusInitializer)) as EventBusInitializer;
            try
            {
                await _eventBusInitializer.InitializeAllActiveEventsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "自动初始化事件失败");
            }
        }
    }
}
