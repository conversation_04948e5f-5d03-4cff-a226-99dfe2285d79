﻿using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using System.Dynamic;

namespace GCP.FunctionPool.Flow.Services
{
    class DataQuery : DataBaseService
    {
        private string ToJoinType(JoinType joinType) => joinType switch
        {
            JoinType.Inner => "INNER JOIN",
            JoinType.Left => "LEFT JOIN",
            JoinType.Right => "RIGHT JOIN",
            JoinType.Full => "FULL JOIN",
            _ => ""
        };

        [Function("dataCustomizeQuery", "数据库查询")]
        public async Task<object> QueryCustomizeData(DataQueryData data)
        {
            return await QueryData(data).ConfigureAwait(false);
        }

        [Function("dataQuery", "数据库自动查询")]
        public async Task<object> QueryData(DataQueryData data)
        {
            var db = GetDataContext(data.DataSource);
            var engine = this.GetEngine();

            var sb = new SqlBuilder<object>(db);
            var orderByList = new List<string>();

            if (data.OperateType == "sql")
            {
                sb.Init(data.SqlInfo.Sql);

                if (data.SqlInfo.Parameters != null)
                {
                    var dbParams = new DbParameters(db);

                    foreach (var parameter in data.SqlInfo.Parameters)
                    {
                        var value = GetDataValue(parameter.ParamValue, engine);
                        dbParams.Add(parameter.ParamName, value);
                    }
                    sb.SetParameters(dbParams);
                }
            }
            else if (data.OperateType == "configure")
            {
                var mainTable = data.ConfigureInfo.First().TableData;
                //var mainTableAlias = "t1";
                //mainTable.Columns.Select(c =>
                //{
                //    if (c.IsCustomize)
                //    {
                //        return c.ColumnName;
                //    }
                //    return mainTableAlias + "." + c.ColumnName;
                //});

                sb.Append("SELECT ");
                // mainTableAlias + "." +
                sb.Append(mainTable.Columns.Where(t => !t.IsCustomize).Select(t => t.ColumnName).Join(","));
                sb.Append(" FROM " + mainTable.TableName);

                if (mainTable.SortColumns is { Count: > 0 })
                {
                    // 取消排序前缀，mysql view视图加上别名，分页会报错，table分页不会报错
                    // mainTableAlias + "." +
                    orderByList.AddRange(mainTable.SortColumns.Select(t => t.ColumnName + " " + t.Order));
                }

                List<DataSourceTableData> joinTables = [];
                if (data.ConfigureInfo.Count > 1)
                {
                    joinTables = data.ConfigureInfo.Skip(1).Select(t => t.TableData).ToList();
                    foreach (var table in joinTables)
                    {
                        if (table.JoinType == JoinType.None) continue;
                        var joinType = ToJoinType(table.JoinType);
                        sb.Append(joinType + " " + table.TableName + " ON 1=1");
                        sb.SetCondition(table.JoinConditions, table.TableName, engine: engine, globalData: this.Context.globalData);
                    }
                }

                sb.Append(" WHERE 1=1");
                sb.SetCondition(mainTable.Conditions, null, engine: engine, globalData: this.Context.globalData);
                foreach (var table in joinTables)
                {
                    sb.SetCondition(table.Conditions, table.TableName, engine: engine, globalData: this.Context.globalData);
                    if (table.SortColumns is { Count: > 0 })
                    {
                        // 取消排序前缀，mysql view视图加上别名，分页会报错，table分页不会报错
                        // table.TableName + "." +
                        orderByList.AddRange(table.SortColumns.Select(t => t.ColumnName + " " + t.Order));
                    }
                }
            }

            var orderBy = string.Join(",", orderByList);
            int pageSize = data.PageSize.GetDataValue(engine, globalData: this.Context.globalData, defaultValue: 500);
            var pageIndex = data.PageIndex.GetDataValue(engine, globalData: this.Context.globalData, defaultValue: 1);

            if (this.Context.Persistence)
            {
                await using var conn = db.CreateConnection();
                this.Context.Current.ArgsBuilder = new System.Text.StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"DbProvider: {db.DbProvider.ProviderType.ToString()}");
                this.Context.Current.ArgsBuilder.AppendLine($"DataSource: {conn.DataSource}");
                this.Context.Current.ArgsBuilder.AppendLine($"Database: {conn.Database}");

                if (data.AutoPaged || data.IsPaging)
                {
                    this.Context.Current.ArgsBuilder.AppendLine($"PageSize: {pageSize}");
                }

                if (data.IsPaging)
                {
                    this.Context.Current.ArgsBuilder.AppendLine($"PageIndex: {pageIndex}");
                }

                if (orderByList.Count > 0)
                {
                    this.Context.Current.ArgsBuilder.AppendLine($"OrderBy: {orderBy}");
                }
                this.Context.Current.ArgsBuilder.AppendLine();
                this.Context.Current.ArgsBuilder.AppendLine(sb.ExecutableSql);
            }


            try
            {
                if (data.AutoPaged)
                {
                    return sb.GetPagedIEnumerableAsync(pageSize, orderBy);
                }
                else if (data.IsPaging)
                {
                    if (data.HasTotal)
                    {
                        var result = await sb.GetPagingDataAsync(pageSize, pageIndex, orderBy).ConfigureAwait(false);
                        await this.Context.SqlLog.Info($"查询成功 {data.Name}, 查询数据量 {result.List.Count} 总数 {result.Total}", true);
                        var expando = new ExpandoObject();
                        var dictionary = (IDictionary<string, object>)expando;
                        dictionary.Add("list", result.List);
                        dictionary.Add("total", result.Total);
                        return dictionary;
                    }
                    else
                    {
                        var result = await sb.GetListPagedAsync((pageIndex - 1) * pageSize, pageSize, orderBy).ConfigureAwait(false);
                        await this.Context.SqlLog.Info($"查询成功 {data.Name}, 查询数据量 {result.Count}", true);

                        // 根据输出配置处理结果
                        return ProcessOutputResult(result, data.OutputConfig);
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(orderBy)) sb.Append("ORDER BY " + orderBy);
                    if (data.OutputConfig.Type == OutputType.Single)
                    {
                        return await sb.GetAsync().ConfigureAwait(false);
                    }
                    else if (data.OutputConfig.Type == OutputType.Count)
                    {
                        return await sb.CountAsync().ConfigureAwait(false);
                    }

                    var result = await sb.GetListAsync().ConfigureAwait(false);
                    await this.Context.SqlLog.Info($"查询成功 {data.Name}, 查询数据量 {result.Count}", true);

                    // 根据输出配置处理结果
                    return ProcessOutputResult(result, data.OutputConfig);
                }
            }
            catch
            {
                await this.Context.SqlLog.Warn("查询失败," + " SQL：" + sb.ExecutableSql, true);
                throw;
            }
        }

        /// <summary>
        /// 根据输出配置处理查询结果
        /// </summary>
        private static object ProcessOutputResult(IList<dynamic> result, OutputConfig outputConfig)
        {
            if (outputConfig == null || outputConfig.Type == OutputType.List)
            {
                return result;
            }

            if (outputConfig.Type == OutputType.Single)
            {
                return result.FirstOrDefault();
            }

            if (outputConfig.Type == OutputType.Dictionary)
            {
                return BuildDictionary(result, outputConfig.DictionaryConfig);
            }

            if (outputConfig.Type == OutputType.Count)
            {
                return result.Count;
            }

            return result;
        }

        /// <summary>
        /// 构建字典输出
        /// </summary>
        private static Dictionary<string, object> BuildDictionary(IList<dynamic> result, DictionaryConfig config)
        {
            var dictionary = new Dictionary<string, object>();

            if (config == null || config.KeyColumns == null || config.KeyColumns.Count == 0)
            {
                return dictionary;
            }

            foreach (var item in result)
            {
                if (item is IDictionary<string, object> dict)
                {
                    // 构建键
                    var key = BuildDictionaryKey(dict, config.KeyColumns, config.KeySeparator);
                    if (string.IsNullOrEmpty(key)) continue;

                    // 构建值
                    object value;
                    if (config.UseFullObjectAsValue)
                    {
                        value = dict;
                    }
                    else if (!string.IsNullOrEmpty(config.ValueColumn) && dict.TryGetValue(config.ValueColumn, out var columnValue))
                    {
                        value = columnValue;
                    }
                    else
                    {
                        value = dict;
                    }

                    dictionary[key] = value;
                }
            }

            return dictionary;
        }

        /// <summary>
        /// 构建字典键（支持多列组合）
        /// </summary>
        private static string BuildDictionaryKey(IDictionary<string, object> item, List<string> keyColumns, string separator)
        {
            var keyParts = new List<string>();

            foreach (var column in keyColumns)
            {
                if (item.TryGetValue(column, out var columnValue))
                {
                    var value = columnValue?.ToString() ?? "";
                    keyParts.Add(value);
                }
                else
                {
                    keyParts.Add("");
                }
            }

            return string.Join(separator, keyParts);
        }
    }
}
