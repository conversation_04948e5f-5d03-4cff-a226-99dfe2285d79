// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 流程运行实例变量
	/// </summary>
	[Table("lc_fru_variable")]
	public class LcFruVariable
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"       , CanBeNull = false, IsPrimaryKey = true)] public string Id       { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:流程运行实例 ID
		/// </summary>
		[Column("PROC_ID"  , CanBeNull = false                     )] public string ProcId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:流程运行步骤实例 ID
		/// </summary>
		[Column("STEP_ID"  , CanBeNull = false                     )] public string StepId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:参数类型 INPUT/OUTPUT/RESULT/CONTEXT
		/// </summary>
		[Column("VAR_TYPE" , CanBeNull = false                     )] public string VarType  { get; set; } = null!; // varchar(10)
		/// <summary>
		/// Description:变量名称
		/// </summary>
		[Column("VAR_NAME" , CanBeNull = false                     )] public string VarName  { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:变量值
		/// </summary>
		[Column("VAR_VALUE", CanBeNull = false                     )] public string VarValue { get; set; } = null!; // longtext
	}
}
