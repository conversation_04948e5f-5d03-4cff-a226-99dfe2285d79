# GCP 测试运行脚本
param(
    [string]$TestCategory = "All",
    [switch]$Coverage = $false,
    [switch]$Verbose = $false,
    [string]$Output = "console"
)

Write-Host "=== GCP 系统测试运行器 ===" -ForegroundColor Green
Write-Host "测试类别: $TestCategory" -ForegroundColor Yellow
Write-Host "生成覆盖率: $Coverage" -ForegroundColor Yellow
Write-Host "详细输出: $Verbose" -ForegroundColor Yellow
Write-Host ""

# 设置测试项目路径
$TestProjectPath = "GCP.Tests"
$TestResultsPath = "TestResults"

# 创建测试结果目录
if (!(Test-Path $TestResultsPath)) {
    New-Item -ItemType Directory -Path $TestResultsPath | Out-Null
}

# 构建测试命令
$TestCommand = "dotnet test $TestProjectPath"

# 添加过滤器
switch ($TestCategory) {
    "Simple" { $TestCommand += " --filter `"FullyQualifiedName~SimpleTests`"" }
    "Api" { $TestCommand += " --filter `"FullyQualifiedName~Api`"" }
    "Workflow" { $TestCommand += " --filter `"FullyQualifiedName~Workflow`"" }
    "Functions" { $TestCommand += " --filter `"FullyQualifiedName~Functions`"" }
    "IoT" { $TestCommand += " --filter `"FullyQualifiedName~IoT`"" }
    "Events" { $TestCommand += " --filter `"FullyQualifiedName~Events`"" }
    "Integration" { $TestCommand += " --filter `"FullyQualifiedName~Integration`"" }
    "All" { }
    default { 
        Write-Host "未知的测试类别: $TestCategory" -ForegroundColor Red
        Write-Host "可用类别: Simple, Api, Workflow, Functions, IoT, Events, Integration, All" -ForegroundColor Yellow
        exit 1
    }
}

# 添加日志级别
if ($Verbose) {
    $TestCommand += " --verbosity detailed"
} else {
    $TestCommand += " --verbosity normal"
}

# 添加测试结果输出
$TestCommand += " --logger `"trx;LogFileName=TestResults.trx`""
$TestCommand += " --results-directory $TestResultsPath"

# 添加覆盖率收集
if ($Coverage) {
    $TestCommand += " --collect:`"XPlat Code Coverage`""
}

# 显示将要执行的命令
Write-Host "执行命令: $TestCommand" -ForegroundColor Cyan
Write-Host ""

# 记录开始时间
$StartTime = Get-Date

try {
    # 执行测试
    Invoke-Expression $TestCommand
    $ExitCode = $LASTEXITCODE
    
    # 记录结束时间
    $EndTime = Get-Date
    $Duration = $EndTime - $StartTime
    
    Write-Host ""
    Write-Host "=== 测试执行完成 ===" -ForegroundColor Green
    Write-Host "执行时间: $($Duration.TotalSeconds.ToString('F2')) 秒" -ForegroundColor Yellow
    Write-Host "退出代码: $ExitCode" -ForegroundColor $(if ($ExitCode -eq 0) { "Green" } else { "Red" })
    
    # 显示测试结果文件位置
    $TrxFile = Join-Path $TestResultsPath "TestResults.trx"
    if (Test-Path $TrxFile) {
        Write-Host "测试结果文件: $TrxFile" -ForegroundColor Yellow
    }
    
    # 显示覆盖率文件位置
    if ($Coverage) {
        $CoverageFiles = Get-ChildItem -Path $TestResultsPath -Filter "coverage.cobertura.xml" -Recurse
        if ($CoverageFiles.Count -gt 0) {
            Write-Host "覆盖率文件: $($CoverageFiles[0].FullName)" -ForegroundColor Yellow
        }
    }
    
    # 如果测试失败，显示错误信息
    if ($ExitCode -ne 0) {
        Write-Host ""
        Write-Host "测试执行失败！请检查上面的错误信息。" -ForegroundColor Red
        Write-Host "常见问题解决方案:" -ForegroundColor Yellow
        Write-Host "1. 检查项目引用是否正确" -ForegroundColor White
        Write-Host "2. 确认测试数据库连接配置" -ForegroundColor White
        Write-Host "3. 验证 InternalsVisibleTo 配置" -ForegroundColor White
        Write-Host "4. 检查测试依赖包版本" -ForegroundColor White
    } else {
        Write-Host ""
        Write-Host "所有测试执行成功！" -ForegroundColor Green
    }
    
} catch {
    Write-Host ""
    Write-Host "执行测试时发生错误: $($_.Exception.Message)" -ForegroundColor Red
    $ExitCode = 1
}

# 生成简单的HTML报告
if ($Output -eq "html" -and (Test-Path $TrxFile)) {
    Write-Host ""
    Write-Host "正在生成HTML报告..." -ForegroundColor Cyan
    
    $HtmlReport = @"
<!DOCTYPE html>
<html>
<head>
    <title>GCP 测试报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>GCP 系统测试报告</h1>
        <p><strong>测试类别:</strong> $TestCategory</p>
        <p><strong>执行时间:</strong> $($Duration.TotalSeconds.ToString('F2')) 秒</p>
        <p><strong>生成时间:</strong> $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')</p>
        <p><strong>状态:</strong> <span class="$(if ($ExitCode -eq 0) { 'success' } else { 'error' })">$(if ($ExitCode -eq 0) { '成功' } else { '失败' })</span></p>
    </div>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <p>详细的测试结果请查看 TRX 文件: <code>$TrxFile</code></p>
        $(if ($Coverage) { "<p>代码覆盖率报告已生成</p>" } else { "" })
    </div>
    
    <div class="summary">
        <h2>快速开始</h2>
        <p>要运行特定类别的测试，请使用以下命令:</p>
        <ul>
            <li><code>.\run-tests.ps1 -TestCategory Simple</code> - 运行基础测试</li>
            <li><code>.\run-tests.ps1 -TestCategory Api</code> - 运行API测试</li>
            <li><code>.\run-tests.ps1 -Coverage</code> - 运行所有测试并生成覆盖率</li>
        </ul>
    </div>
</body>
</html>
"@
    
    $HtmlFile = Join-Path $TestResultsPath "TestReport.html"
    $HtmlReport | Out-File -FilePath $HtmlFile -Encoding UTF8
    Write-Host "HTML报告已生成: $HtmlFile" -ForegroundColor Green
}

Write-Host ""
exit $ExitCode
