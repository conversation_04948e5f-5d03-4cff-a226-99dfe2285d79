/* eslint-disable simple-import-sort/imports */
import { createApp } from 'vue';
import TDesign from 'tdesign-vue-next';
import CronLightPlugin from '@vue-js-cron/light';
import ContextMenu from '@imengyu/vue3-context-menu';
import CmpPlugins from '@/components/cmp-plugins';
import '@/components/editor/loader';

import App from './App.vue';
import router from './router';
import { store } from './store';
import i18n from './locales';

import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css';
import '@vue-js-cron/light/dist/light.css';
import 'tdesign-vue-next/es/style/index.css';
import '@/style/index.less';
import './permission';

const app = createApp(App);
app.use(CronLightPlugin);
app.use(TDesign);
app.use(ContextMenu);
app.use(store);
app.use(router);
app.use(i18n);
app.use(CmpPlugins);

app.mount('#app');
