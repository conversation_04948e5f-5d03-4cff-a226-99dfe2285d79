using GCP.Common;
using GCP.DataAccess;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("driver", "设备驱动参数管理服务")]
    class IotDriverService : BaseService
    {
        [Function("getById", "获取驱动参数详情")]
        public LcIotDriver GetById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcIotDrivers.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                throw new CustomException("未找到驱动参数");
            }
            return data;
        }

        [Function("getAll", "获取驱动参数清单")]
        public List<LcIotDriver> GetAll(string driverCode, string equipmentId = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcIotDrivers
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        a.DriverCode == driverCode &&
                        (a.EquipmentId == null || a.EquipmentId == equipmentId)
                        orderby a.TimeCreate descending
                        select a).ToList();
            if (string.IsNullOrEmpty(equipmentId)) return data;
            
            var equipmentParams = data.Where(t => t.EquipmentId != null).ToList();
            if (equipmentParams.Count <= 0) return data;

            var equipmentKeys = equipmentParams.Select(t => t.ParamKey).ToList();
            var globalParams = data.Where(t => t.EquipmentId == null).ToList();
            equipmentParams.AddRange(globalParams.Where(t => !equipmentKeys.Contains(t.ParamKey)));
            return equipmentParams;
        }

        [Function("add", "新增驱动参数")]
        public void Add(LcIotDriver driver)
        {
            driver.SolutionId = this.SolutionId;
            driver.ProjectId = this.ProjectId;

            using var db = this.GetDb();
            var existedDriver = db.LcIotDrivers
                .FirstOrDefault(t => t.SolutionId == driver.SolutionId &&
                                     t.ProjectId == driver.ProjectId &&
                                     t.DriverCode == driver.DriverCode &&
                                     t.EquipmentId == driver.EquipmentId &&
                                     t.ParamKey == driver.ParamKey);

            if (existedDriver != null)
            {
                throw new CustomException($"参数键 {driver.ParamKey} 已存在");
            }

            this.InsertData(driver);
        }

        [Function("update", "更新驱动参数")]
        public bool Update(LcIotDriver driver)
        {
            using var db = this.GetDb();
            var data = db.LcIotDrivers.FirstOrDefault(t => t.Id == driver.Id);
            if (data == null)
            {
                return false;
            }

            var existedDriver = db.LcIotDrivers
                .FirstOrDefault(t => t.Id != driver.Id &&
                                     t.SolutionId == data.SolutionId &&
                                     t.ProjectId == data.ProjectId &&
                                     t.DriverCode == driver.DriverCode &&
                                     t.EquipmentId == driver.EquipmentId &&
                                     t.ParamKey == driver.ParamKey);

            if (existedDriver != null)
            {
                throw new CustomException($"参数键 {driver.ParamKey} 已存在");
            }

            data.ParamKey = driver.ParamKey;
            data.ParamValue = driver.ParamValue;
            data.Description = driver.Description;

            this.UpdateData(data);
            return true;
        }

        [Function("delete", "删除驱动参数")]
        public bool Delete(string id)
        {
            using var db = this.GetDb();
            var data = db.LcIotDrivers.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                return false;
            }
            data.State = 0;
            this.UpdateData(data);
            return true;
        }

        [Function("batchAdd", "批量新增驱动参数")]
        public void BatchAdd(List<LcIotDriver> drivers)
        {
            if (drivers == null || drivers.Count == 0)
            {
                return;
            }

            using var db = this.GetDb();
            var driverCode = drivers[0].DriverCode;
            var equipmentId = drivers[0].EquipmentId;

            var existedParams = db.LcIotDrivers
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.DriverCode == driverCode &&
                           t.EquipmentId == equipmentId)
                .Select(t => t.ParamKey)
                .ToList();

            var duplicateParams = drivers
                .Where(t => existedParams.Contains(t.ParamKey))
                .Select(t => t.ParamKey)
                .ToList();

            if (duplicateParams.Any())
            {
                throw new CustomException($"以下参数键已存在: {string.Join(", ", duplicateParams)}");
            }

            foreach (var driver in drivers)
            {
                driver.SolutionId = this.SolutionId;
                driver.ProjectId = this.ProjectId;
                this.InsertData(driver, db);
            }
        }

        [Function("batchDelete", "批量删除驱动参数")]
        public bool BatchDelete(string[] ids)
        {
            using var db = this.GetDb();
            var data = db.LcIotDrivers
                .Where(t => ids.Contains(t.Id))
                .Set(t => t.State, (short)0);
            var rowsAffected = this.UpdateData(data);
            return rowsAffected > 0;
        }

        [Function("getGlobalParams", "获取驱动全局参数")]
        public List<LcIotDriver> GetGlobalParams(string driverCode)
        {
            using var db = this.GetDb();
            var data = db.LcIotDrivers
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.DriverCode == driverCode &&
                           t.EquipmentId == null)
                .ToList();
            return data;
        }

        [Function("copyGlobalParams", "复制全局参数到设备")]
        public void CopyGlobalParams(string driverCode, string equipmentId, string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new CustomException("请添加要复制的key");
            
            using var db = this.GetDb();
            var allParams = db.LcIotDrivers
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.DriverCode == driverCode &&
                           t.ParamKey == key &&
                           (t.EquipmentId == null || t.EquipmentId == equipmentId))
                .ToList();

            if (allParams.Any(t => t.EquipmentId != null))
            {
                return;
            }

            foreach (var param in allParams.Where(t => t.EquipmentId == null))
            {
                var newParam = new LcIotDriver
                {
                    SolutionId = this.SolutionId,
                    ProjectId = this.ProjectId,
                    DriverCode = driverCode,
                    EquipmentId = equipmentId,
                    ParamKey = param.ParamKey,
                    ParamValue = param.ParamValue,
                    Description = param.Description
                };
                this.InsertData(newParam, db);
            }
        }

        [Function("getDriverCodes", "获取驱动编码列表")]
        public List<string> GetDriverCodes()
        {
            using var db = this.GetDb();
            var data = db.LcIotDrivers
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId)
                .Select(t => t.DriverCode)
                .Distinct()
                .ToList();
            return data;
        }

        [Function("deleteEquipmentParams", "删除设备特定参数")]
        public bool DeleteEquipmentParams(string id, string driverCode, string equipmentId)
        {
            using var db = this.GetDb();
            db.LcIotDrivers
                .Where(t => t.Id == id &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.DriverCode == driverCode &&
                           t.EquipmentId == equipmentId)
                .Delete();

            return true;
        }
    }
}