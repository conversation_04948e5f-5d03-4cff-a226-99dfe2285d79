using GCP.DataAccess;
using GCP.Iot.Services;
using LinqToDB;
using EasyCaching.Core;
using GCP.Cache;
using GCP.Common;
using Serilog;
using Medallion.Threading;

namespace GCP.Eventbus.Infrastructure
{
    /// <summary>
    /// 消息事件初始化器，用于从数据库加载事件配置并初始化消息总线
    /// </summary>
    class EventBusInitializer : IDisposable
    {
        private readonly IMessageBusManager _messageBusManager;
        private readonly DistributedInstanceManager _instanceManager;
        private readonly IotEventIntegrationService _iotEventIntegration;
        private readonly IMessageBus _systemBus;
        private static string EventInitTopic => "event.init";
        
        public EventBusInitializer(
            IMessageBusManager messageBusManager,
            IEasyCachingProvider cachingProvider,
            IDistributedLockProvider distributedLockProvider,
            IotEventIntegrationService iotEventIntegration = null)
        {
            _messageBusManager = messageBusManager;
            _iotEventIntegration = iotEventIntegration;
            _instanceManager = new DistributedInstanceManager(
                "EventBus",
                cachingProvider,
                distributedLockProvider);
            
            // 获取系统消息总线
            _systemBus = messageBusManager.MessageBuses[EventBusHelper.SystemEventBusName]
                ?? throw new InvalidOperationException("系统消息总线未初始化");
            
            // 订阅资源变更事件
            _instanceManager.OnResourceChanged += HandleResourceChangedAsync;

            // 设置事件总线帮助类的委托
            EventBusHelper.InitializeEvent = InitializeEventAsync;
            EventBusHelper.StopEvent = StopEventAsync;

            // 注册事件初始化消息处理
            _systemBus.SubscribeAsync(EventInitTopic, HandleEventInitMessageAsync, new ConsumerOptions
            {
                Name = "EventInitHandler",
                Topic = EventInitTopic,
                BusName = _systemBus.Name,
            });
        }

        private async Task HandleEventInitMessageAsync(MessageEnvelope message, CancellationToken cancellationToken)
        {
            var eventId = message.Payload?.ToString();
            try
            {
                // 检查消息是否应该由当前实例处理
                if (await _instanceManager.ShouldHandleResourceAsync(eventId!))
                {
                    Log.Information("收到事件初始化消息: EventId={EventId}", eventId);
                    await InitializeEventAsync(eventId!);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理事件初始化消息失败: EventId={EventId}", eventId);
            }
        }

        private async Task HandleResourceChangedAsync(string eventId, bool isAssigned)
        {
            try
            {
                if (isAssigned)
                {
                    // 资源被分配给当前实例，需要初始化
                    await InitializeEventAsync(eventId);
                }
                else
                {
                    // 资源从当前实例移除，需要停止
                    await StopEventAsync(eventId);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理资源变更失败: EventId={EventId}, IsAssigned={IsAssigned}", eventId, isAssigned);
            }
        }

        /// <summary>
        /// 初始化所有启用的消息事件
        /// </summary>
        public async Task InitializeAllActiveEventsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                Log.Information("开始初始化所有启用的消息事件");

                // 注册实例
                await _instanceManager.RegisterInstanceAsync();

                await using var db = new GcpDb();
                // 1. 获取所有启用的消息事件
                var events = await db.LcMessageEvents
                    .Where(x => x.IsEnabled == 1 
                        && x.State == 1)
                    .ToListAsync(cancellationToken);

                if (events.Count == 0)
                {
                    return;
                }

                Log.Information("找到{EventsCount}个启用的消息事件", events.Count);

                // 2. 获取事件映射关系
                var mappings = await db.LcMessageEventMappings
                    .Where(x => x.State == 1)
                    .ToListAsync(cancellationToken);

                foreach (var evt in events)
                {
                    try
                    {
                        // 检查是否应该处理这个MQ
                        if (await _instanceManager.ShouldHandleResourceAsync(evt.Id))
                        {
                            await InitializeEventFromDatabaseAsync(evt, mappings.Where(m => m.EventId == evt.Id).ToList(), cancellationToken);
                        }
                        else
                        {
                            Log.Information("事件 {EvtEventName} 由其他实例处理", evt.EventName);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "初始化消息事件失败: {EvtId}", evt.Id);
                    }
                }

                if (events.Count > 0)
                {
                    Log.Information("所有消息事件初始化完成");
                }

                // 启动所有消息总线
                await _messageBusManager.StartAllAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化所有消息事件失败");
                throw;
            }
        }

        /// <summary>
        /// 初始化单个消息事件
        /// </summary>
        public async Task<bool> InitializeEventAsync(string eventId)
        {
            try
            {
                // 检查是否应该处理这个事件
                if (!await _instanceManager.ShouldHandleResourceAsync(eventId))
                {
                    // 获取负责该事件的实例
                    var targetInstance = await _instanceManager.GetResourceInstanceAsync(eventId);
                    if (targetInstance != null)
                    {
                        Log.Information("事件 {EventId} 由实例 {TargetInstance} 处理，发送初始化消息", eventId, targetInstance);
                        
                        // 发送初始化消息到目标实例
                        await _systemBus.PublishAsync(EventInitTopic, eventId);
                        return true;
                    }
                    
                    Log.Warning("事件 {EventId} 当前没有可用的处理实例", eventId);
                    return false;
                }

                await using var db = new GcpDb();
                
                // 查询事件信息
                var evt = await db.LcMessageEvents
                    .FirstOrDefaultAsync(e => e.Id == eventId);

                if (evt == null)
                {
                    throw new InvalidOperationException($"消息事件不存在: {eventId}");
                }

                // 查询事件映射
                var mappings = await db.LcMessageEventMappings
                    .Where(m => m.EventId == eventId)
                    .ToListAsync();

                return await InitializeEventFromDatabaseAsync(evt, mappings);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化消息事件失败: {EventId}", eventId);
                throw;
            }
        }

        /// <summary>
        /// 停止消息事件
        /// </summary>
        private async Task StopEventAsync(string eventId)
        {
            try
            {
                await using var db = new GcpDb();

                var evt = await db.LcMessageEvents
                    .FirstOrDefaultAsync(e => e.Id == eventId);

                if (evt.SourceType == 1 && _iotEventIntegration != null) // 1 表示设备类型
                {
                    // 使用 IoT 事件集成服务创建设备事件消费者
                    var mappings = await db.LcMessageEventMappings.Where(t => t.EventId == eventId).ToListAsync();
                    foreach (var mapping in mappings)
                    {
                        await _iotEventIntegration.UnsubscribeDeviceEventAsync(evt, mapping);
                    }

                    return;
                }

                // 停止并移除相关的消息总线
                var busName = $"{evt?.EventName}_{eventId}";
                if (_messageBusManager.MessageBuses.ContainsKey(busName))
                {
                    await _messageBusManager.RemoveMessageBusAsync(busName);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "停止消息事件失败: {EventId}", eventId);
                throw;
            }
            finally
            {
                Log.Information("消息事件已停止: {EventId}", eventId);
            }
        }

        /// <summary>
        /// 从数据库初始化消息事件的内部实现
        /// </summary>
        private async Task<bool> InitializeEventFromDatabaseAsync(LcMessageEvent evt, List<LcMessageEventMapping> mappings, CancellationToken cancellationToken = default)
        {
            if (!mappings.Any())
            {
                Log.Warning("消息事件 {EvtId} 没有配置映射关系", evt.Id);
                return false;
            }

            try
            {
                // 1. 创建消息总线实例
                var messageBus = await CreateMessageBusAsync(evt, cancellationToken);
                if (messageBus == null) return false;

                // 2. 为每个映射创建消费者
                foreach (var mapping in mappings)
                {
                    await CreateConsumerAsync(evt, mapping, cancellationToken);
                }

                Log.Information("消息事件 {EvtId} 初始化完成", evt.Id);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化消息事件失败: {EvtId}", evt.Id);
                return false;
            }
        }

        private async Task<IMessageBus> CreateMessageBusAsync(LcMessageEvent evt, CancellationToken cancellationToken)
        {
            try
            {
                // 检查是否是设备事件
                if (evt.SourceType == 1)
                {
                    return _messageBusManager.MessageBuses[EventBusHelper.LocalIotEventBusName];
                }

                var busType = evt.SourceType switch
                {
                    2 => MessageBusType.Mqtt,
                    3 => MessageBusType.RabbitMQ,
                    4 => MessageBusType.Kafka,
                    5 => MessageBusType.Redis,
                    6 => MessageBusType.Tcp,
                    _ => MessageBusType.InMemory
                };

                var settings = string.IsNullOrEmpty(evt.Settings) ? new Dictionary<string, string>() : JsonHelper.Deserialize<Dictionary<string, string>>(evt.Settings);
                settings = settings.Concat(new Dictionary<string, string>
                {
                    { "EventId", evt.Id },
                    { "EventName", evt.EventName },
                    { "FunctionId", evt.FunctionId }
                }).ToDictionary(t => t.Key, t => t.Value);

                var options = new MessageBusOptions
                {
                    Name = $"{evt.EventName}_{evt.Id}",
                    Type = busType,
                    IsEnabled = true,
                    Settings = settings
                };

                return await _messageBusManager.AddMessageBusAsync(options, cancellationToken);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "创建消息总线实例失败: {EventName}", evt.EventName);
                return null;
            }
        }

        private async Task CreateConsumerAsync(LcMessageEvent evt, LcMessageEventMapping mapping, CancellationToken cancellationToken)
        {
            try
            {
                // 检查是否是设备事件
                if (evt.SourceType == 1 && _iotEventIntegration != null) // 1 表示设备类型
                {
                    // 使用 IoT 事件集成服务创建设备事件消费者
                    if (await _iotEventIntegration.CreateDeviceEventConsumerAsync(evt, mapping, cancellationToken))
                    {
                        return;
                    }
                }
                var consumerGroupKey = $"{evt.Id}_{mapping.SourceId}";

                var settings = new Dictionary<string, string>
                {
                    { "SolutionId", mapping.SolutionId },
                    { "ProjectId", mapping.ProjectId },
                    { "SourceId", mapping.SourceId },
                    { "EventId", evt.Id },
                    { "ConsumerGroup", consumerGroupKey }
                };

                var options = new ConsumerOptions
                {
                    Name = $"{evt.EventName}",
                    Topic = mapping.SourceId,
                    ConsumerId = mapping.FunctionId ?? evt.FunctionId,
                    BusName = $"{evt.EventName}_{evt.Id}",
                    IsEnabled = evt.IsEnabled == 1,
                    IsFlow = true,
                    //ConcurrentCount = 1,
                    //RetryOptions = new RetryOptions
                    //{
                    //    MaxRetries = 3,
                    //    DelayMilliseconds = 1000,
                    //    ExponentialBackoff = true
                    //},
                    Settings = settings
                };

                var consumer = await _messageBusManager.AddConsumerAsync(options, cancellationToken);
                Log.Information("成功创建消费者: {ConsumerName}", options.Name);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "创建消费者失败: {EventName}, SourceId: {SourceId}", 
                    evt.EventName, mapping.SourceId);
            }
        }

        public void Dispose()
        {
            if (_instanceManager != null)
            {
                _instanceManager.OnResourceChanged -= HandleResourceChangedAsync;
                (_instanceManager as IDisposable)?.Dispose();
            }

            // 取消订阅消息处理
            _systemBus?.UnsubscribeAsync(EventInitTopic);
        }
    }
} 