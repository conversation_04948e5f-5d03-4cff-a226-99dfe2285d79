import type { ProjectListResult, PurchaseListResult } from '@/api/model/detailModel';
import { http } from '@/utils/request';

const Api = {
  PurchaseList: '/get-purchase-list',
  ProjectList: '/get-project-list',
};

export function getPurchaseList() {
  return http.get<PurchaseListResult>(Api.PurchaseList);
}

export function getProjectList() {
  return http.get<ProjectListResult>(Api.ProjectList);
}
