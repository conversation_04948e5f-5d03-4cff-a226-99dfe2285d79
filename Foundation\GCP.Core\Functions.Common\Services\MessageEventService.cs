
using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using LinqToDB;
using Serilog;

namespace GCP.Functions.Common.Services
{
    [Function("event", "消息事件管理服务")]
    class MessageEventService : BaseService
    {
        private readonly IMessageBusManager _messageBusManager = ServiceLocator.Current.GetService(typeof(IMessageBusManager)) as IMessageBusManager;

        [Function("getById", "获取事件详情")]
        public LcMessageEvent GetById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcMessageEvents.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                throw new CustomException("未找到事件");
            }
            return data;
        }

        [Function("getAll", "获取事件清单")]
        public PagingData<LcMessageEvent> GetAll(string keyword = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcMessageEvents
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(keyword) || (a.EventName.Contains(keyword) || a.Description.Contains(keyword)))
                        orderby a.TimeCreate descending
                        select a).ToPagingData(pageIndex, pageSize);
            return data;
        }

        [Function("getByDirCode", "根据目录代码获取事件清单")]
        public List<LcMessageEvent> GetByDirCode(string dirId)
        {
            using var db = this.GetDb();

            // 先获取目录信息
            var dir = db.LcDirTrees.FirstOrDefault(d =>
                d.Id == dirId &&
                d.State == 1 &&
                d.SolutionId == this.SolutionId &&
                d.DirType == "E");

            if (dir == null)
            {
                throw new Exception("目录不存在");
            }

            // 获取该目录下的所有事件
            var data = (from a in db.LcMessageEvents
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        a.DirCode == dirId
                        orderby a.TimeCreate descending
                        select a).ToList();

            return data;
        }

        [Function("add", "新增事件")]
        public string Add(LcMessageEvent evt)
        {
            evt.SolutionId = this.SolutionId;
            evt.ProjectId = this.ProjectId;

            using var db = this.GetDb();
            var existedEvent = db.LcMessageEvents
                .FirstOrDefault(t => t.SolutionId == evt.SolutionId && t.ProjectId == evt.ProjectId && t.EventName == evt.EventName);

            if (existedEvent != null)
            {
                if (existedEvent.State == 1)
                {
                    throw new CustomException($"事件名称 {evt.EventName} 已存在");
                }
                else
                {
                    db.LcMessageEvents.Delete(t => t.Id == existedEvent.Id);
                }
            }
            evt.FunctionId = AddFunction(evt.EventName, evt.Description, db);
            this.InsertData(evt);
            return evt.Id;
        }

        private string AddFunction(string functionName, string description, GcpDb db = null)
        {
            LcFunction function = new()
            {
                SolutionId = this.SolutionId,
                ProjectId = this.ProjectId,
                FunctionName = functionName,
                FunctionType = "FLOW",
                Description = description,
                UseVersion = 1
            };
            return this.InsertData(function);
        }

        [Function("update", "更新事件")]
        public async Task<bool> Update(LcMessageEvent evt)
        {
            await using var db = this.GetDb();
            var data = db.LcMessageEvents.FirstOrDefault(t => t.Id == evt.Id);
            if (data == null)
            {
                return false;
            }

            var existedEvent = db.LcMessageEvents
                .FirstOrDefault(t => t.Id != evt.Id && t.SolutionId == data.SolutionId && t.ProjectId == data.ProjectId && t.EventName == evt.EventName);

            if (existedEvent != null)
            {
                throw new CustomException($"事件名称 {evt.EventName} 已存在");
            }

            data.EventName = evt.EventName;
            data.EventType = evt.EventType;
            data.SourceType = evt.SourceType;
            data.FunctionId = evt.FunctionId;
            data.Description = evt.Description;
            if (data.IsEnabled != evt.IsEnabled || data.Settings != evt.Settings)
            {
                await StopEvent(evt.Id);
                if(evt.IsEnabled == 1)
                {
                    _ = InitializeEvent(evt.Id);
                }
            }

            data.Settings = evt.Settings;
            data.IsEnabled = evt.IsEnabled;

            await db.BeginTransactionAsync();
            if (string.IsNullOrEmpty(data.FunctionId))
            {
                data.FunctionId = AddFunction(evt.EventName, evt.Description, db);
            }

            this.UpdateData(data);
            await db.CommitTransactionAsync();
            return true;
        }

        [Function("delete", "删除事件")]
        public bool Delete(string id)
        {
            using var db = this.GetDb();
            var data = db.LcMessageEvents.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                return false;
            }
            data.State = 0;
            this.UpdateData(data);

            this.UpdateData(db.LcDirTrees.Where(t => t.LeafId == id).Set(t => t.State, (short)0));
            if (!string.IsNullOrEmpty(data.FunctionId))
            {
                this.UpdateData(db.LcFunctions.Where(t => t.Id == data.FunctionId).Set(t => t.State, (short)0));
            }

            return true;
        }

        [Function("updateState", "更新事件状态")]
        public async Task<bool> UpdateState(string[] ids, short isEnabled)
        {
            await using var db = this.GetDb();
            var data = db.LcMessageEvents
                .Where(t => ids.Contains(t.Id))
                .Set(t => t.IsEnabled, isEnabled);
            var rowsAffected = this.UpdateData(data);
            var result = rowsAffected > 0;

            if (result)
            {
                foreach (var id in ids)
                {
                    if (isEnabled == 1)
                    {
                        await InitializeEvent(id);
                    }
                    else
                    {
                        await StopEvent(id);
                    }
                }
            }
            return result;
        }

        [Function("getEquipmentInfoByEventId", "获取事件映射的设备数据")]
        public List<dynamic> GetEquipmentInfoByEventId(string eventId, int eventType)
        {
            using var db = this.GetDb();
            if (eventType == 1)
            {
                return (from maps in db.LcMessageEventMappings
                        join eq in db.LcIotEquipment on maps.SourceId equals eq.Id
                        join eqVar in db.LcIotEquipmentVariables on maps.SourceCode equals eqVar.VarName
                        where maps.State == 1 && maps.EventId == eventId
                        select new
                        {
                            id = maps.Id,
                            equipmentCode = eq.EquipmentCode,
                            equipmentName = eq.EquipmentName,
                            varName = eqVar.VarName,
                            address = eqVar.Address,
                        }).ToList<dynamic>();
            }
            else if (eventType == 2)
            {
                return (from maps in db.LcMessageEventMappings
                        join eq in db.LcIotEquipment on maps.SourceId equals eq.Id
                        where maps.State == 1 && maps.EventId == eventId
                        select new
                        {
                            id = maps.Id,
                            equipmentCode = eq.EquipmentCode,
                            equipmentName = eq.EquipmentName,
                            equipmentType = eq.EquipmentType,
                        }).ToList<dynamic>();
            }
            else if (eventType == 3)
            {
                return (from maps in db.LcMessageEventMappings
                        join eq in db.LcIotEquipment on maps.SourceId equals eq.EquipmentType
                        where maps.State == 1 && maps.EventId == eventId
                        select new
                        {
                            id = maps.Id,
                            equipmentType = eq.EquipmentType,
                        }).Distinct().ToList<dynamic>();
            }

            return [];
        }

        [Function("addMappingByEquipment", "新增事件映射根据设备信息")]
        public void AddMappingByEquipment(string text, string eventId, int eventType)
        {
            var list = text.Split(["\r\n", "\r", "\n"], StringSplitOptions.None)
                .Select(t => t.Trim())
                .Where(t => !string.IsNullOrWhiteSpace(t))
                .ToArray();

            using var db = this.GetDb();

            var equipmentTypes = new List<string>();
            if (eventType == 3)
            {
                equipmentTypes = db.LcIotEquipment.Select(t => t.EquipmentType).Distinct().ToList();
            }
            foreach (var item in list)
            {
                if (eventType == 1)
                {

                }
                else if (eventType == 2)
                {
                    var eq = db.LcIotEquipment.FirstOrDefault(t => t.EquipmentCode == item);
                    if (eq == null)
                    {
                        continue;
                    }

                    var mapping = new LcMessageEventMapping
                    {
                        EventId = eventId,
                        SourceId = eq.Id,
                        SourceCode = item
                    };
                    AddMapping(mapping);
                }
                else if (eventType == 3)
                {
                    if (!equipmentTypes.Contains(item))
                    {
                        continue;
                    }

                    var mapping = new LcMessageEventMapping
                    {
                        EventId = eventId,
                        SourceId = item,
                    };
                    AddMapping(mapping);
                }
            }

            var evt = db.LcMessageEvents.FirstOrDefault(t => t.Id == eventId);
            if (evt?.IsEnabled == 1)
            {
                _ = InitializeEvent(evt.Id);
            }
        }

        [Function("getMappings", "获取事件映射")]
        public List<LcMessageEventMapping> GetMappings(string eventId)
        {
            using var db = this.GetDb();
            return (from a in db.LcMessageEventMappings
                    where a.EventId == eventId
                        && a.State == 1
                    select a).ToList();
        }

        [Function("addMapping", "新增事件映射")]
        public void AddMapping(LcMessageEventMapping mapping, bool hasFunction = false)
        {
            mapping.SolutionId = this.SolutionId;
            mapping.ProjectId = this.ProjectId;

            using var db = this.GetDb();
            var existedMapping = db.LcMessageEventMappings
                .FirstOrDefault(t => t.EventId == mapping.EventId && t.SourceId == mapping.SourceId);

            if (existedMapping != null)
            {
                if (existedMapping.State == 1)
                {
                    throw new CustomException($"{mapping.SourceId} 该事件映射关系已存在");
                }
                else
                {
                    db.LcMessageEventMappings.Delete(t => t.Id == existedMapping.Id);
                }
            }

            if (hasFunction)
                mapping.FunctionId = AddFunction(mapping.SourceId, null, db);

            this.InsertData(mapping);
        }

        [Function("deleteMapping", "删除事件映射")]
        public bool DeleteMapping(string id)
        {
            using var db = this.GetDb();
            var data = db.LcMessageEventMappings.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                return false;
            }

            db.LcMessageEventMappings.Delete(t => t.Id == id);

            if (!string.IsNullOrEmpty(data.FunctionId))
            {
                this.UpdateData(db.LcFunctions.Where(t => t.Id == data.FunctionId).Set(t => t.State, (short)0));
            }

            var evt = db.LcMessageEvents.FirstOrDefault(t => t.Id == data.EventId);
            if (evt?.IsEnabled == 1)
            {
                _ = InitializeEvent(evt.Id);
            }

            return true;
        }

        [Function("getFunctionNames", "获取消息事件函数名称列表")]
        public List<OptionVO> GetFunctionNames()
        {
            using var db = this.GetDb();
            return (from a in db.LcMessageEvents
                    where a.SolutionId == this.SolutionId
                    && a.ProjectId == this.ProjectId
                    && a.State == 1
                    && !string.IsNullOrEmpty(a.FunctionId)
                    select new OptionVO
                    {
                        Id = a.Id,
                        Value = a.FunctionId,
                        Label = a.EventName,
                        Description = a.Description
                    }).ToList();
        }

        private async Task InitializeEvent(string eventId)
        {
            try
            {
                if (EventBusHelper.InitializeEvent != null)
                {
                    await EventBusHelper.InitializeEvent(eventId);
                }
                else
                {
                    Log.Error("事件初始化委托未设置");
                    throw new CustomException("事件初始化服务未就绪");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化事件失败: {eventId}", eventId);
                throw;
            }
        }

        private async Task StopEvent(string eventId)
        {
            try
            {
                if (EventBusHelper.StopEvent != null)
                {
                    await EventBusHelper.StopEvent(eventId);
                }
                else
                {
                    Log.Error("事件停止委托未设置");
                    throw new CustomException("事件停止服务未就绪");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "停止事件失败: {eventId}", eventId);
                throw;
            }
        }
    }
}