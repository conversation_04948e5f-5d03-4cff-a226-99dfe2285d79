﻿using System.Collections;
using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    /// <summary>
    /// SqlBuilder Extension（中级扩展, 动态sql语句和参数）
    /// </summary>
    public static class SqlBuilderExtension
    {
        public static SqlBuilder<dynamic> Sql(this DbConnection connection, string sqlString, params object[] args)
        {
            return connection.Sql<dynamic>(sqlString, args);
        }
        public static SqlBuilder<dynamic> Sql(this IDbContext db, string sqlString, params object[] args)
        {
            return db.Sql<dynamic>(sqlString, args);
        }
        public static SqlBuilder<T> Sql<T>(this DbConnection connection, string sqlString, params object[] args)
        {
            return new SqlBuilder<T>(connection).Append(sqlString, args);
        }
        public static SqlBuilder<T> Sql<T>(this IDbContext db, string sqlString, params object[] args)
        {
            return new SqlBuilder<T>(db).Append(sqlString, args);
        }

        public static int Execute(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text, DbTransaction tran = null)
        {
            IEnumerable multiExec = DbHelper.GetMultiExec(parms);
            if (multiExec != null)
            {
                var wasClosed = connection.State == ConnectionState.Closed;
                if (wasClosed)
                {
                    connection.Open();
                    tran = tran ?? connection.BeginTransaction();
                }
                int total = 0;
                try
                {
                    foreach (var parm in multiExec)
                    {
                        total += Execute(new SqlBuilder<object>(connection).Init(sqlString, parm), connection, tran, cmdType);
                    }
                    if (wasClosed) tran.Commit();
                }
                finally
                {
                    if (wasClosed) connection.Close();
                }
                return total;
            }

            return Execute(new SqlBuilder<object>(connection).Init(sqlString, parms), connection, tran, cmdType);
        }
        public static int Execute(this DbTransaction tran, string sqlString, object parms, CommandType cmdType = CommandType.Text)
        {
            return Execute(tran.Connection, sqlString, parms, cmdType, tran);
        }

        public static int Execute(this ISqlBuilder sqlBuilder, DbConnection connection, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return connection.Execute(tran, sqlBuilder.SqlString, cmdType, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }
        public static int Execute(this ISqlBuilder sqlBuilder, DbTransaction tran, CommandType cmdType = CommandType.Text)
        {
            return Execute(sqlBuilder, sqlBuilder.CreateConnection(), tran, cmdType);
        }
        public static int Execute(this ISqlBuilder sqlBuilder, CommandType cmdType = CommandType.Text)
        {
            return Execute(sqlBuilder, sqlBuilder.CreateConnection(), null, cmdType);
        }


        public static DbDataReader ExecuteReader(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text, CommandBehavior behavior = CommandBehavior.Default, DbTransaction tran = null)
        {
            return ExecuteReader(new SqlBuilder<object>(connection).Init(sqlString, parms), cmdType, behavior, connection, tran);
        }
        public static DbDataReader ExecuteReader(this ISqlBuilder sqlBuilder, CommandType cmdType = CommandType.Text, CommandBehavior behavior = CommandBehavior.Default, DbConnection connection = null, DbTransaction tran = null)
        {
            return (connection ?? sqlBuilder.CreateConnection()).ExecuteReader(tran, sqlBuilder.SqlString, cmdType, behavior, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }


        public static object ExecuteScalar(this DbConnection connection, string sqlString, object parms, DbTransaction tran = null)
        {
            return ExecuteScalar(new SqlBuilder<object>(connection).Init(sqlString, parms), connection, tran);
        }
        public static object ExecuteScalar(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            return (connection ?? sqlBuilder.CreateConnection()).ExecuteScalar(tran, sqlBuilder.SqlString, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }


        public static DataSet GetDataSet(this DbConnection connection, string sqlString, object parms, DbTransaction tran = null)
        {
            return GetDataSet(new SqlBuilder<object>(connection).Init(sqlString, parms), connection, tran);
        }
        public static DataSet GetDataSet(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            return (connection ?? sqlBuilder.CreateConnection()).GetDataSet(tran, sqlBuilder.SqlString, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }


        public static DataTable GetDataTable(this DbConnection connection, string sqlString, object parms, DbTransaction tran = null)
        {
            return GetDataTable(new SqlBuilder<object>(connection).Init(sqlString, parms), connection, tran);
        }
        public static DataTable GetDataTable(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            return (connection ?? sqlBuilder.CreateConnection()).GetDataTable(tran, sqlBuilder.SqlString, sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }


        public static DataTable GetDataTablePaged(this DbConnection connection, string sqlString, IDataParameter[] parms, int startIndex, int length, string orderBySql = "", DbTransaction tran = null, int commandTimeout = 30)
        {
            sqlString = DbProviderInfo.GetPagingSql(connection.GetDbProvider(), startIndex, length, sqlString, orderBySql);
            return connection.GetDataTable(tran, sqlString, commandTimeout, parms);
        }
        public static DataTable GetDataTablePaged(this DbConnection connection, string sqlString, object parms, int startIndex, int length, string orderBySql = "")
        {
            return GetDataTablePaged(new SqlBuilder<object>(connection).Init(sqlString, parms), startIndex, length, orderBySql);
        }
        public static DataTable GetDataTablePaged(this ISqlBuilder sqlBuilder, int startIndex, int length, string orderBySql = "")
        {
            return GetDataTablePaged(sqlBuilder, null, startIndex, length, orderBySql);
        }
        public static DataTable GetDataTablePaged(this ISqlBuilder sqlBuilder, DbConnection connection, int startIndex, int length, string orderBySql = "", DbTransaction tran = null)
        {
            var conn = connection ?? sqlBuilder.CreateConnection();
            return conn.GetDataTable(tran, DbProviderInfo.GetPagingSql(conn.GetDbProvider(), startIndex, length, sqlBuilder.SqlString, orderBySql), sqlBuilder.CommandTimeout, sqlBuilder.Parameters);
        }
    }
}
