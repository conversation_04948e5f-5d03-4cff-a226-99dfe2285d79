// import { core, Platform } from '../core';
import { Http, HttpOptions } from './http';
import { formatRequestData, gcpApi, jsonConvert, urlAppendTime } from './middlewares';

// const config: { serviceAddress: string } = await fetch('./config.json').then((response) => response.json());
const env = import.meta.env.MODE || 'development';

let baseUrl = localStorage.getItem('baseUrl') || import.meta.env.VITE_API_URL;
fetch(`./config.json?_t=${new Date().getTime()}`)
  .then((res) => {
    if (!res.ok) {
      throw new Error(`HTTP error! Status: ${res.status}`);
    }
    return res.json();
  })
  .then((config) => {
    baseUrl = config.baseUrl || window.location.origin;
    localStorage.setItem('baseUrl', baseUrl);
  })
  .catch((error) => {
    if (error.name === 'SyntaxError') return;
    console.error('Fetch failed:', error);
  });

const options = new HttpOptions();
options.baseURL = () => {
  return env === 'production' ? baseUrl : import.meta.env.VITE_API_URL;
};
options.middlewares = () => {
  return [gcpApi, jsonConvert, formatRequestData, urlAppendTime];
};

export const http = new Http(options);
