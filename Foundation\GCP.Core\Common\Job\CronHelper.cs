﻿namespace GCP.Common.Job
{
    public static class CronHelper
    {
        /// <summary>
        /// 获取corn表达式翻译描述
        /// </summary>
        /// <param name="corn">corn表达式</param>
        /// <param name="locale">翻译语言区域, 默认根据Thread.CurrentUICulture确认语言（en\zh-CH\zh-TW\...）</param>
        /// <returns></returns>
        public static string GetCronDescription(string cornExpression, string locale = null)
        {
            var options = new CronExpressionDescriptor.Options()
            {
                Use24HourTimeFormat = true,
            };

            if (!string.IsNullOrEmpty(locale))
            {
                options.Locale = locale;
            }

            return CronExpressionDescriptor.ExpressionDescriptor.GetDescription(cornExpression, options);
        }

        /// <summary>
        /// 获取corn表达式下一个发生时间
        /// </summary>
        /// <param name="cornExpression"></param>
        /// <param name="fromUtc"></param>
        /// <param name="zone"></param>
        /// <returns></returns>
        public static List<DateTime> GetCornNextOccurrence(string cornExpression, DateTime fromUtc, TimeZoneInfo zone, int count = 1)
        {
            if (count <= 0)
            {
                return new List<DateTime>();
            }
            var list = new List<DateTime>();
            DateTime lastTime = fromUtc;

            var expression = Cronos.CronExpression.Parse(cornExpression, Cronos.CronFormat.Standard);

            for (int i = 0; i < count; i++)
            {
                var nextTime = expression.GetNextOccurrence(lastTime, zone);
                if (!nextTime.HasValue) break;
                list.Add(nextTime.Value);
                lastTime = nextTime.Value;
            }
            return list;
        }

        /// <summary>
        /// 获取corn表达式下一个发生时间（默认中国时间）
        /// </summary>
        /// <param name="cornExpression"></param>
        /// <returns></returns>
        public static List<DateTime> GetCornNextOccurrence(string cornExpression, int count = 1)
        {
            var timeOffset = DateTime.UtcNow.Subtract(DateTime.Now);
            var list = GetCornNextOccurrence(cornExpression, DateTime.UtcNow, TimeZoneHelper.ChinaTimeZone, count);
            for (int i = 0; i < list.Count; i++)
            {
                list[i] -= timeOffset;
            }
            return list;
        }
    }
}
