﻿namespace GCP.Common
{
    public class SqlContextLogger
    {
        private FunctionContext ctx;
        public SqlContextLogger(FunctionContext context)
        {
            ctx = context;
        }

        public async Task Write(object obj, SqlType sqlType = SqlType.Insert)
        {
            await LoggerManager.SqlLogger.Write(() => new Tuple<SqlType, object>(sqlType, obj));
        }

        public async Task Write(Func<Tuple<SqlType, object>> objFunc)
        {
            await LoggerManager.SqlLogger.Write(objFunc);
        }

        public async Task Write(List<Func<Tuple<SqlType, object>>> list)
        {
            await LoggerManager.SqlLogger.Write(list);
        }

        public async Task Info(string message, bool isFlowLog = false)
        {
            await LoggerManager.SqlLogger.WriteLog(ctx, "INFO", message, isFlowLog: isFlowLog);
        }

        public async Task Warn(string message, bool isFlowLog = false)
        {
            await LoggerManager.SqlLogger.WriteLog(ctx, "WARN", message, isFlowLog: isFlowLog);
        }

        public async Task Error(Exception ex, string message, bool isFlowLog = false)
        {
            await LoggerManager.SqlLogger.WriteLog(ctx, "ERROR", message, ex, isFlowLog: isFlowLog);
        }

        public async Task Debug(string message, bool isFlowLog = false)
        {
            await LoggerManager.SqlLogger.WriteLog(ctx, "DEBUG", message, isFlowLog: isFlowLog);
        }

        public async Task WriteLog(string level, string message, bool isFlowLog = false)
        {
            await LoggerManager.SqlLogger.WriteLog(ctx, level, message, isFlowLog: isFlowLog);
        }

        public async Task AddFunctionOutputVariable(object output)
        {
            await LoggerManager.SqlLogger.AddFunctionOutputVariable(ctx, output);
        }

        public async Task AddFunctionContextVariable()
        {
            await LoggerManager.SqlLogger.AddFunctionContextVariable(ctx);
        }

        public async Task AddFunctionInputVariable()
        {
            await LoggerManager.SqlLogger.AddFunctionInputVariable(ctx);
        }
    }
}
