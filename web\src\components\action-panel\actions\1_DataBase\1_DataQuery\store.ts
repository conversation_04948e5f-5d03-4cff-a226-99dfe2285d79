import { cloneDeep, intersectionBy, merge } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowControl, FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { recursionMergeVariable } from '@/components/action-panel/utils';

import { ArgsInfo } from './model';

const control: FlowControl = {
  forEach: {
    async: false,
    item: [],
    list: '',
    nextId: '',
  },
};
const sqlParamsRegex = /[@:]((\w|[\u4e00-\u9fa5])+)/g;

export type forEachInfo = typeof control.forEach;

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};
  const isNewAction = !currentArgs.name; // 判断是否为新增动作

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      dataSource: '',
      autoPaged: true,
      isPaging: false,
      hasTotal: false,
      pageSize: {
        type: 'text',
        textValue: '500',
      },
      description: '',
      operateType: 'configure',
      sqlInfo: {
        sql: '',
        parameters: [],
      },
      configureInfo: [
        {
          id: 'root',
          tableData: {
            tableName: '',
            tableDescription: '',
            columns: [],
            sortColumns: [],
            conditions: null,
          },
        },
      ],
      useRoot: isNewAction ? true : (currentArgs.useRoot ?? false), // 新增动作默认true，历史配置默认false
    } as ArgsInfo),
    ...currentArgs,
  }) as ArgsInfo;

  state.forEachData = cloneDeep(actionFlowStore.currentStep.control?.forEach || control.forEach) as forEachInfo;
};

export const useDataAutoQueryStore = defineStore('DataAutoQuery', {
  state: () => {
    const state = { args: null, forEachData: null } as { args: ArgsInfo; forEachData: forEachInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      const itemKey = 'result';

      return [
        {
          id: itemKey,
          key: itemKey,
          description: '结果',
          type: 'array',
          children:
            this.args.configureInfo[0]?.tableData?.columns?.map((column) => {
              const itemPath = `${itemKey}.${column.columnName}`;
              return {
                id: column.columnName,
                key: column.columnName,
                description: column.description,
                type: column.dataType,
                value: {
                  type: 'variable',
                  dataType: column.dataType,
                  variableType: 'current',
                  variableValue: itemPath,
                },
              } as FlowData;
            }) || [],
        },
      ] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 如果不是自动分页，根据useRoot参数决定是否使用根节点
      if (!args.autoPaged) {
        const resultVariable = this.variables.find((item) => item.id === 'result');
        if (resultVariable) {
          if (!args.useRoot) {
            // 不使用根节点，直接设置原始变量结构
            actionFlowStore.setStepResultData(this.variables);
          } else {
            // 使用根节点，创建根节点绑定
            const rootNode = {
              ...resultVariable,
              id: 'result',
              key: 'result',
              description: '查询结果',
              value: {
                type: 'variable' as const,
                variableType: 'current' as const,
                variableValue: 'result',
              },
            };
            actionFlowStore.setStepResultData([rootNode]);
          }
        }
      }
    },
    setAutoPagedData() {
      const actionFlowStore = useActionFlowStore();
      const config = { ...actionFlowStore.currentStep.config };
      config.controlType = 'forEach';
      config.endStepName = '结束查询';
      actionFlowStore.setCurrentConfig(config);

      const itemParam: FlowData = {
        id: 'result',
        key: 'result',
        description: '查询结果',
        type: 'array',
        value: {
          type: 'variable',
          variableType: 'current',
          variableValue: 'result',
        },
      };

      if (this.args.operateType === 'configure') {
        itemParam.children = this.variables.find((item) => item.id === 'result')?.children;
      }

      if (this.forEachData?.item && this.forEachData.item.length > 0) {
        const tempItemData = cloneDeep(this.forEachData.item);
        recursionMergeVariable(tempItemData, [itemParam]);
        this.forEachData.item = tempItemData;
      } else {
        this.forEachData.item = [itemParam];
      }

      actionFlowStore.setCurrentControl({ forEach: this.forEachData });
    },
    cleanAutoPagedData() {
      const actionFlowStore = useActionFlowStore();
      const config = { ...actionFlowStore.currentStep.config };
      delete config.controlType;
      delete config.endStepName;

      actionFlowStore.setCurrentConfig(config);
      actionFlowStore.setCurrentControl(null);
    },
    setSqlParameters(sql: string) {
      const actionFlowStore = useActionFlowStore();

      let matches = [];
      let match;
      do {
        match = sqlParamsRegex.exec(sql);
        if (match && match.length > 1) {
          matches.push({
            paramName: match[1],
            paramCode: match[0],
            paramValue: '',
          });
        }
      } while (match);

      if (this.args.sqlInfo.parameters && this.args.sqlInfo.parameters.length > 0) {
        const intersection = intersectionBy(this.args.sqlInfo.parameters, matches, 'paramName');
        matches = merge(matches, intersection);
      }
      this.args.sqlInfo.parameters = matches;
      actionFlowStore.setCurrentArgs(this.args);
    },
  },
});
