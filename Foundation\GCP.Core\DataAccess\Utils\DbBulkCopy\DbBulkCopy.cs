﻿using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    public static class DbBulkCopy
    {
        public static void BulkCopy(this IDbConnection connection, DataTable table, IDbTransaction transaction = null)
        {
            if (string.IsNullOrEmpty(table.TableName))
            {
                throw new ArgumentException("表名不能为空！");
            }
            BulkCopy(connection, table.TableName, table, transaction);
        }

        public static void BulkCopy(this IDbConnection connection, string tableName, DataTable table, IDbTransaction transaction = null)
        {
            var type = connection.GetDbProvider().GetDbProviderType();
            switch (type)
            {
                case DbProviderType.Oracle:
                    connection.OracleBulkCopy(tableName, table, transaction);
                    return;
                case DbProviderType.MySql:
                    connection.MySqlBulkCopy(tableName, table, transaction);
                    return;
                case DbProviderType.SqlServer:
                    connection.SqlServerBulkCopy(tableName, table, transaction);
                    return;
                case DbProviderType.PostgreSQL:
                    connection.PostgreSqlBulkCopy(tableName, table, transaction);
                    return;
                case DbProviderType.DuckDB:
                    connection.DuckDbBulkCopy(tableName, table, transaction);
                    return;
            }
            throw new NotSupportedException("不支持数据库类型！");
        }

        public static void BulkCopy(this IDbConnection connection, string tableName, IDataReader reader, IDbTransaction transaction = null)
        {
            var type = connection.GetDbProvider().GetDbProviderType();
            switch (type)
            {
                case DbProviderType.Oracle:
                    connection.OracleBulkCopy(tableName, reader, transaction);
                    return;
                case DbProviderType.MySql:
                    connection.MySqlBulkCopy(tableName, reader, transaction);
                    return;
                case DbProviderType.SqlServer:
                    connection.SqlServerBulkCopy(tableName, reader, transaction);
                    return;
                case DbProviderType.PostgreSQL:
                    connection.PostgreSqlBulkCopy(tableName, reader, transaction);
                    return;
                case DbProviderType.DuckDB:
                    connection.DuckDbBulkCopy(tableName, reader, transaction);
                    return;
            }
            throw new NotSupportedException("不支持数据库类型！");
        }

        public static void BulkCopy<T>(this IDbConnection connection, string tableName, List<T> list, IDbTransaction transaction = null)
        {
            BulkCopy(connection, tableName, list.GetDataTable(), transaction);
        }

        public static async Task<int> BatchExecute(this DbConnection connection, SqlBuilder<int>[] sqlBuilders,
            DbTransaction transaction = null)
        {
            var type = connection.GetDbProvider().GetDbProviderType();
            switch (type)
            {
                case DbProviderType.Oracle:
                    return await connection.OracleBatchExecute(sqlBuilders, transaction).ConfigureAwait(false);
                case DbProviderType.MySql:
                case DbProviderType.SqlServer:
                case DbProviderType.PostgreSQL:
                    {
                        var rowsAffected = 0;
                        await using var dbBatch = connection.CreateBatch();
                        if (transaction != null)
                        {
                            dbBatch.Transaction = transaction;
                        }
                        
                        foreach (var sqlBuilder in sqlBuilders)
                        {
                            var batchCommand = dbBatch.CreateBatchCommand();
                            await batchCommand.PrepareBatchCommand(sqlBuilder.SqlString, sqlBuilder.Parameters).ConfigureAwait(false);

                            dbBatch.BatchCommands.Add(batchCommand);
                        }

                        rowsAffected += await dbBatch.ExecuteNonQueryAsync().ConfigureAwait(false);

                        return rowsAffected;
                    }
                default:
                    {
                        var rowsAffected = 0;
                        foreach (var sql in sqlBuilders)
                        {
                            rowsAffected += await sql.ExecuteAsync(connection, transaction).ConfigureAwait(false);
                        }

                        return rowsAffected;
                    }
            }
        }
    }
}
