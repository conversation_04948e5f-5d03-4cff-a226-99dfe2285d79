﻿namespace GCP.Iot
{
    [AttributeUsage(AttributeTargets.Class, Inherited = false, AllowMultiple = false)]
    public class DriverInfoAttribute: Attribute
    {
        public string Description { get; }
        public string Version { get; }
        public DriverInfoAttribute(string description, string version)
        {
            Description = description;
            Version = version;
        }

        public DriverInfoAttribute(string description)
        {
            Description = description;
        }
    }
}
