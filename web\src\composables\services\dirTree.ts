import { api, Services } from '@/api/system';

export interface DirTreeVO {
  id: string;
  label: string;
  value: string;
  children?: DirTreeVO[];
  isLeaf?: boolean;
  leafId?: string;
}

export interface DirTreeData {
  id?: string;
  parentId?: string;
  dirCode: string;
  dirType?: string;
  dirName: string;
  description?: string;
  seq?: number;
  icon?: string;
  isLeaf?: 'Y' | 'N';
  leafId?: string;
}

export const useDirTreeService = () => {
  return {
    // 获取目录树
    getTree: (dirType: string) => {
      return api.run(Services.dirTreeGetTree, { dirType });
    },

    // 获取叶子节点列表
    getLeafList: (dirType: string, keyword?: string) => {
      return api.run(Services.dirTreeGetLeafList, { dirType, keyword });
    },

    // 获取目录树节点
    getTreeItem: (id: string) => {
      return api.run(Services.dirTreeGetTreeItem, { id });
    },

    // 获取所有目录
    getAll: (dirType?: string) => {
      return api.run(Services.dirTreeGetAll, { dirType });
    },

    // 获取目录信息
    get: (id: string) => {
      return api.run(Services.dirTreeGet, { id });
    },

    // 根据编码获取目录信息
    getByCode: (dirCode: string) => {
      return api.run(Services.dirTreeGetByCode, { dirCode });
    },

    // 获取子目录列表
    getChildren: (parentId: string) => {
      return api.run(Services.dirTreeGetChildren, { parentId });
    },

    // 保存目录信息
    save: (data: DirTreeData) => {
      return api.run(Services.dirTreeSave, data);
    },

    // 删除目录
    delete: (id: string) => {
      return api.run(Services.dirTreeDelete, { id });
    },

    // 移动目录
    move: (id: string, targetParentId: string) => {
      return api.run(Services.dirTreeMove, { id, targetParentId });
    },
  };
};
