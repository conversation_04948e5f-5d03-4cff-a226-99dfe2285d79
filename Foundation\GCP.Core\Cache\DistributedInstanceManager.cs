using EasyCaching.Core;
using GCP.Common;
using Medallion.Threading;
using Serilog;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace GCP.Cache
{
    /// <summary>
    /// 分布式实例管理器，用于管理多实例环境下的资源分配
    /// </summary>
    class DistributedInstanceManager : IDisposable
    {
        private readonly IDistributedLockProvider _distributedLockProvider;
        private readonly IEasyCachingProvider _cachingProvider;
        private readonly string _instanceId;
        private readonly string _resourcePrefix;
        private const int CACHE_EXPIRE_HOURS = 24;
        private const int HEARTBEAT_INTERVAL_SECONDS = 30;
        private const int INSTANCE_TIMEOUT_SECONDS = 90;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private Task _heartbeatTask;
        private bool _isDisposed;

        // 资源变更事件
        public event Func<string, bool, Task> OnResourceChanged;

        public string InstanceId => _instanceId;

        public DistributedInstanceManager(
            string resourcePrefix,
            IEasyCachingProvider cachingProvider,
            IDistributedLockProvider distributedLockProvider)
        {
            _resourcePrefix = resourcePrefix;
            _cachingProvider = cachingProvider;
            _distributedLockProvider = distributedLockProvider;
            _instanceId = GenerateInstanceId();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        private string GenerateInstanceId()
        {
            var deviceId = TUID.DeviceID();
            var processId = Environment.ProcessId;
            var combined = $"{deviceId}_{processId}_{_resourcePrefix}";

            using var md5 = MD5.Create();
            var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(combined));
            return BitConverter.ToString(hash).Replace("-", "");
        }

        /// <summary>
        /// 注册实例并启动心跳检测
        /// </summary>
        public async Task RegisterInstanceAsync()
        {
            var lockKey = $"{_resourcePrefix}:Instances:lock";
            var instancesKey = $"{_resourcePrefix}:Instances";

            await using (await _distributedLockProvider.AcquireLockAsync(lockKey))
            {
                var instancesJson = await _cachingProvider.GetAsync<string>(instancesKey);
                var instances = instancesJson.HasValue
                    ? JsonSerializer.Deserialize<Dictionary<string, InstanceInfo>>(instancesJson.Value)
                    : new Dictionary<string, InstanceInfo>();

                instances ??= new Dictionary<string, InstanceInfo>();

                // 检查是否是新实例
                bool isNewInstance = !instances.ContainsKey(_instanceId);

                instances[_instanceId] = new InstanceInfo
                {
                    LastHeartbeat = DateTime.UtcNow,
                    Status = InstanceStatus.Active
                };

                await _cachingProvider.SetAsync(instancesKey,
                    JsonSerializer.Serialize(instances),
                    TimeSpan.FromHours(CACHE_EXPIRE_HOURS));

                Log.Information("实例 {S} 已注册到 {ResourcePrefix}", _instanceId, _resourcePrefix);

                // 如果是新实例，触发资源重分配
                if (isNewInstance)
                {
                    await RedistributeResourcesAsync();
                }

                // 启动心跳任务
                StartHeartbeat();
            }
        }

        /// <summary>
        /// 注销实例并停止心跳检测
        /// </summary>
        public async Task UnregisterInstanceAsync()
        {
            // 停止心跳任务
            await StopHeartbeat();

            var lockKey = $"{_resourcePrefix}:Instances:lock";
            var instancesKey = $"{_resourcePrefix}:Instances";

            await using (await _distributedLockProvider.AcquireLockAsync(lockKey))
            {
                var instancesJson = await _cachingProvider.GetAsync<string>(instancesKey);
                if (!instancesJson.HasValue) return;

                var instances = JsonSerializer.Deserialize<Dictionary<string, InstanceInfo>>(instancesJson.Value);
                if (instances?.Remove(_instanceId) == true)
                {
                    await _cachingProvider.SetAsync(instancesKey,
                        JsonSerializer.Serialize(instances),
                        TimeSpan.FromHours(CACHE_EXPIRE_HOURS));

                    Log.Information("实例 {S} 已从 {ResourcePrefix} 注销", _instanceId, _resourcePrefix);
                }

                // 触发资源重新分配
                await RedistributeResourcesAsync();
            }
        }

        /// <summary>
        /// 检查资源是否应该由当前实例处理
        /// </summary>
        /// <param name="resourceId">资源ID</param>
        /// <returns>是否应该由当前实例处理</returns>
        public async Task<bool> ShouldHandleResourceAsync(string resourceId)
        {
            var lockKey = $"{_resourcePrefix}:ResourceMap:lock";
            var instancesKey = $"{_resourcePrefix}:Instances";
            var resourceMapKey = $"{_resourcePrefix}:ResourceMap";

            await using (await _distributedLockProvider.AcquireLockAsync(lockKey))
            {
                // 获取所有活跃实例
                var instancesJson = await _cachingProvider.GetAsync<string>(instancesKey);
                var instances = instancesJson.HasValue
                    ? JsonSerializer.Deserialize<Dictionary<string, InstanceInfo>>(instancesJson.Value)
                    : new Dictionary<string, InstanceInfo>();

                // 过滤出活跃的实例
                var activeInstances = instances?
                    .Where(i => i.Value.Status == InstanceStatus.Active &&
                           (DateTime.UtcNow - i.Value.LastHeartbeat).TotalSeconds < INSTANCE_TIMEOUT_SECONDS)
                    .Select(i => i.Key)
                    .ToList() ?? new List<string>();

                if (!activeInstances.Any())
                {
                    return true; // 如果没有活跃实例，允许处理
                }

                // 获取资源分配映射
                var mapJson = await _cachingProvider.GetAsync<string>(resourceMapKey);
                var resourceMap = mapJson.HasValue
                    ? JsonSerializer.Deserialize<Dictionary<string, ResourceInfo>>(mapJson.Value)
                    : new Dictionary<string, ResourceInfo>();

                // 如果资源已经分配给某个实例
                if (resourceMap.TryGetValue(resourceId, out var resourceInfo))
                {
                    // 如果分配的实例不存在或已超时，重新分配
                    if (!activeInstances.Contains(resourceInfo.InstanceId) ||
                        (DateTime.UtcNow - resourceInfo.LastUpdate).TotalSeconds >= INSTANCE_TIMEOUT_SECONDS)
                    {
                        resourceMap.Remove(resourceId);
                    }
                    else
                    {
                        return resourceInfo.InstanceId == _instanceId;
                    }
                }

                // 使用一致性哈希分配
                var index = Math.Abs(resourceId.GetHashCode()) % activeInstances.Count;
                var selectedInstance = activeInstances[index];

                resourceMap[resourceId] = new ResourceInfo
                {
                    InstanceId = selectedInstance,
                    LastUpdate = DateTime.UtcNow
                };

                await _cachingProvider.SetAsync(resourceMapKey,
                    JsonSerializer.Serialize(resourceMap),
                    TimeSpan.FromHours(CACHE_EXPIRE_HOURS));

                var shouldHandle = selectedInstance == _instanceId;
                if (shouldHandle)
                {
                    Log.Information("资源 {ResourceId} 已分配给实例 {S}", resourceId, _instanceId);
                }

                return shouldHandle;

            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public async Task ReleaseResourceAsync(string resourceId)
        {
            var lockKey = $"{_resourcePrefix}:ResourceMap:lock";
            var resourceMapKey = $"{_resourcePrefix}:ResourceMap";

            await using (await _distributedLockProvider.AcquireLockAsync(lockKey))
            {
                var mapJson = await _cachingProvider.GetAsync<string>(resourceMapKey);
                if (!mapJson.HasValue) return;

                var resourceMap = JsonSerializer.Deserialize<Dictionary<string, ResourceInfo>>(mapJson.Value);
                if (resourceMap?.Remove(resourceId) == true)
                {
                    await _cachingProvider.SetAsync(resourceMapKey,
                        JsonSerializer.Serialize(resourceMap),
                        TimeSpan.FromHours(CACHE_EXPIRE_HOURS));

                    Log.Information("资源 {ResourceId} 已释放", resourceId);
                }
            }
        }

        /// <summary>
        /// 获取当前实例负责的所有资源
        /// </summary>
        public async Task<IEnumerable<string>> GetAssignedResourcesAsync()
        {
            var resourceMapKey = $"{_resourcePrefix}:ResourceMap";
            var mapJson = await _cachingProvider.GetAsync<string>(resourceMapKey);
            if (!mapJson.HasValue) return Enumerable.Empty<string>();

            var resourceMap = JsonSerializer.Deserialize<Dictionary<string, ResourceInfo>>(mapJson.Value);
            return resourceMap?
                .Where(kvp => kvp.Value.InstanceId == _instanceId)
                .Select(kvp => kvp.Key)
                ?? Enumerable.Empty<string>();
        }

        private void StartHeartbeat()
        {
            _heartbeatTask = Task.Run(async () =>
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        await UpdateHeartbeatAsync();
                        await Task.Delay(TimeSpan.FromSeconds(HEARTBEAT_INTERVAL_SECONDS), _cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "心跳更新失败");
                    }
                }
            }, _cancellationTokenSource.Token);
        }

        private async Task StopHeartbeat()
        {
            await _cancellationTokenSource.CancelAsync();
            if (_heartbeatTask != null) await _heartbeatTask;
        }

        private async Task UpdateHeartbeatAsync()
        {
            var lockKey = $"{_resourcePrefix}:Instances:lock";
            var instancesKey = $"{_resourcePrefix}:Instances";

            await using (await _distributedLockProvider.AcquireLockAsync(lockKey))
            {
                var instancesJson = await _cachingProvider.GetAsync<string>(instancesKey);
                if (!instancesJson.HasValue) return;

                var instances = JsonSerializer.Deserialize<Dictionary<string, InstanceInfo>>(instancesJson.Value);
                if (instances?.ContainsKey(_instanceId) == true)
                {
                    instances[_instanceId].LastHeartbeat = DateTime.UtcNow;
                    await _cachingProvider.SetAsync(instancesKey,
                        JsonSerializer.Serialize(instances),
                        TimeSpan.FromHours(CACHE_EXPIRE_HOURS));
                }

                // 检查是否需要进行资源重新分配
                var needRedistribute = instances?
                    .Any(i => i.Value.Status == InstanceStatus.Active &&
                         (DateTime.UtcNow - i.Value.LastHeartbeat).TotalSeconds >= INSTANCE_TIMEOUT_SECONDS)
                    ?? false;

                if (needRedistribute)
                {
                    await RedistributeResourcesAsync();
                }
            }
        }

        private async Task RedistributeResourcesAsync()
        {
            var lockKey = $"{_resourcePrefix}:ResourceMap:lock";
            var resourceMapKey = $"{_resourcePrefix}:ResourceMap";
            var instancesKey = $"{_resourcePrefix}:Instances";

            await using (await _distributedLockProvider.AcquireLockAsync(lockKey))
            {
                // 获取所有活跃实例
                var instancesJson = await _cachingProvider.GetAsync<string>(instancesKey);
                var instances = instancesJson.HasValue
                    ? JsonSerializer.Deserialize<Dictionary<string, InstanceInfo>>(instancesJson.Value)
                    : new Dictionary<string, InstanceInfo>();

                var activeInstances = instances?
                    .Where(i => i.Value.Status == InstanceStatus.Active &&
                           (DateTime.UtcNow - i.Value.LastHeartbeat).TotalSeconds < INSTANCE_TIMEOUT_SECONDS)
                    .Select(i => i.Key)
                    .ToList() ?? new List<string>();

                if (!activeInstances.Any()) return;

                // 获取所有资源
                var mapJson = await _cachingProvider.GetAsync<string>(resourceMapKey);
                var resourceMap = mapJson.HasValue
                    ? JsonSerializer.Deserialize<Dictionary<string, ResourceInfo>>(mapJson.Value)
                    : new Dictionary<string, ResourceInfo>();

                // 使用虚拟节点的一致性哈希进行资源重分配
                var virtualNodeCount = 10; // 每个实例的虚拟节点数
                var virtualNodes = new Dictionary<string, List<string>>();

                // 为每个实例创建虚拟节点
                foreach (var instance in activeInstances)
                {
                    virtualNodes[instance] = new List<string>();
                    for (int i = 0; i < virtualNodeCount; i++)
                    {
                        var virtualNodeId = $"{instance}##{i}";
                        virtualNodes[instance].Add(virtualNodeId);
                    }
                }

                var needsUpdate = false;
                var resourceChanges = new List<(string ResourceId, string OldInstance, string NewInstance)>();

                foreach (var resource in resourceMap.ToList())
                {
                    // 检查资源是否需要重新分配
                    if (!activeInstances.Contains(resource.Value.InstanceId))
                    {
                        var oldInstance = resource.Value.InstanceId;

                        // 使用虚拟节点的一致性哈希进行分配
                        var allVirtualNodes = virtualNodes.SelectMany(vn => vn.Value).ToList();
                        var resourceHash = resource.Key.GetHashCode();
                        var virtualNodeIndex = Math.Abs(resourceHash) % allVirtualNodes.Count;
                        var selectedVirtualNode = allVirtualNodes[virtualNodeIndex];
                        var newInstance = virtualNodes.First(vn => vn.Value.Contains(selectedVirtualNode)).Key;

                        resourceMap[resource.Key] = new ResourceInfo
                        {
                            InstanceId = newInstance,
                            LastUpdate = DateTime.UtcNow
                        };
                        needsUpdate = true;

                        resourceChanges.Add((resource.Key, oldInstance, newInstance));
                        Log.Information("资源 {ResourceKey} 已从实例 {OldInstance} 重新分配到实例 {NewInstance}", resource.Key, oldInstance, newInstance);
                    }
                }

                if (needsUpdate)
                {
                    // 先保存资源映射更新
                    await _cachingProvider.SetAsync(resourceMapKey,
                        JsonSerializer.Serialize(resourceMap),
                        TimeSpan.FromHours(CACHE_EXPIRE_HOURS));

                    // 通知资源变更
                    if (OnResourceChanged != null)
                    {
                        foreach (var change in resourceChanges)
                        {
                            // 如果是当前实例失去资源
                            if (change.OldInstance == _instanceId)
                            {
                                await OnResourceChanged(change.ResourceId, false);
                            }
                            // 如果是当前实例获得资源
                            else if (change.NewInstance == _instanceId)
                            {
                                await OnResourceChanged(change.ResourceId, true);
                            }
                        }
                    }
                }

            }
        }

        public void Dispose()
        {
            if (_isDisposed) return;

            _cancellationTokenSource.Cancel();
            _heartbeatTask?.Wait();
            _cancellationTokenSource.Dispose();
            _isDisposed = true;

            GC.SuppressFinalize(this);
        }

        private class InstanceInfo
        {
            public DateTime LastHeartbeat { get; set; }
            public InstanceStatus Status { get; set; }
        }

        private class ResourceInfo
        {
            public string InstanceId { get; set; } = string.Empty;
            public DateTime LastUpdate { get; set; }
        }

        private enum InstanceStatus
        {
            Active,
            Inactive
        }

        /// <summary>
        /// 获取资源当前所属的实例ID
        /// </summary>
        /// <param name="resourceId">资源ID</param>
        /// <returns>资源所属的实例ID，如果未分配则返回null</returns>
        public async Task<string> GetResourceInstanceAsync(string resourceId)
        {
            var resourceMapKey = $"{_resourcePrefix}:ResourceMap";
            var instancesKey = $"{_resourcePrefix}:Instances";

            // 获取资源映射
            var mapJson = await _cachingProvider.GetAsync<string>(resourceMapKey);
            if (!mapJson.HasValue) return null;

            var resourceMap = JsonSerializer.Deserialize<Dictionary<string, ResourceInfo>>(mapJson.Value);
            if (resourceMap?.TryGetValue(resourceId, out var resourceInfo) != true)
                return null;

            // 获取所有活跃实例
            var instancesJson = await _cachingProvider.GetAsync<string>(instancesKey);
            var instances = instancesJson.HasValue
                ? JsonSerializer.Deserialize<Dictionary<string, InstanceInfo>>(instancesJson.Value)
                : null;

            // 检查实例是否活跃
            if (instances?.TryGetValue(resourceInfo.InstanceId, out var instanceInfo) == true &&
                instanceInfo.Status == InstanceStatus.Active &&
                (DateTime.UtcNow - instanceInfo.LastHeartbeat).TotalSeconds < INSTANCE_TIMEOUT_SECONDS)
            {
                return resourceInfo.InstanceId;
            }

            return null;
        }
    }
}