﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20240802201000, "添加流程日志")]
    public class AddFlowLog : Migration
    {
        public override void Up()
        {
            // 流程运行中实例表
            Create.Table("LC_FRU_PROC").WithDescription("流程运行实例")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")

               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("函数ID")
               .WithColumn("VERSION").AsInt64().WithColumnDescription("函数版本")
               .WithColumn("FUNCTION_NAME").AsAnsiString(80).WithColumnDescription("函数名称")
               .WithColumn("TRIGGER_TYPE").AsAnsiString(36).Nullable().WithColumnDescription("触发流程类型 API/JOB/MQ")
               .WithColumn("STATUS").AsInt16().WithColumnDescription("状态 -1：失败, 0：运行中, 1：已运行")
               .WithColumn("BEGIN_TIME").AsDateTime().WithColumnDescription("开始时间")
               .WithColumn("END_TIME").AsDateTime().Nullable().WithColumnDescription("结束时间")
               .WithColumn("DURATION").AsInt32().Nullable().WithColumnDescription("运行时长")
               .WithColumn("TOTAL_TRAFFIC").AsDecimal().Nullable().WithColumnDescription("总流量(KB)")
               ;

            Create.Index("LC_FRU_PROC_IDX")
                .OnTable("LC_FRU_PROC")
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .OnColumn("FUNCTION_ID").Ascending()
                .OnColumn("VERSION").Ascending();

            Create.Table("LC_FRU_STEP").WithDescription("流程步骤实例")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")

               .WithColumn("PROC_ID").AsAnsiString(36).WithColumnDescription("流程运行实例 ID")
               .WithColumn("SEQ_NO").AsInt32().Nullable().WithColumnDescription("执行顺序")
               .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("流程动作函数ID")
               .WithColumn("ACTION_ID").AsAnsiString(36).Nullable().WithColumnDescription("流程动作ID")
               .WithColumn("STEP_NAME").AsAnsiString(80).Nullable().WithColumnDescription("流程步骤名称")
               .WithColumn("STATUS").AsInt16().WithColumnDescription("状态 -1：失败, 0：运行中, 1：已运行")
               .WithColumn("BEGIN_TIME").AsDateTime().WithColumnDescription("开始时间")
               .WithColumn("END_TIME").AsDateTime().Nullable().WithColumnDescription("结束时间")
               .WithColumn("DURATION").AsInt32().Nullable().WithColumnDescription("运行时长")
               ;

            Create.Index("LC_FRU_STEP_IDX")
                .OnTable("LC_FRU_STEP")
                .OnColumn("PROC_ID").Ascending()
                .OnColumn("SEQ_NO").Ascending()
                .OnColumn("FUNCTION_ID").Ascending();

            Create.Table("LC_FRU_LOG").WithDescription("流程运行日志")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")

               .WithColumn("PROC_ID").AsAnsiString(36).WithColumnDescription("流程运行实例 ID")
               .WithColumn("STEP_ID").AsAnsiString(36).Nullable().WithColumnDescription("流程步骤实例 ID")
               .WithColumn("LEVEL").AsAnsiString(36).WithColumnDescription("日志级别 INFO/ERROR/WARN/DEBUG")
               .WithColumn("CATEGORY").AsAnsiString(36).WithColumnDescription("日志类别 FLOW/ACTION")
               .WithColumn("TIMESTAMP").AsDateTime().WithColumnDescription("日志时间戳")
               .WithColumn("MESSAGE").AsAnsiString(int.MaxValue).WithColumnDescription("日志信息")
               .WithColumn("EXCEPTION").AsAnsiString(int.MaxValue).Nullable().WithColumnDescription("异常信息")
               ;

            Create.Index("LC_FRU_LOG_IDX")
                .OnTable("LC_FRU_LOG")
                .OnColumn("PROC_ID").Ascending()
                .OnColumn("STEP_ID").Ascending()
                .OnColumn("TIMESTAMP").Ascending();


            Create.Table("LC_FRU_VARIABLE").WithDescription("流程运行实例变量")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")

               .WithColumn("PROC_ID").AsAnsiString(36).WithColumnDescription("流程运行实例 ID")
               .WithColumn("STEP_ID").AsAnsiString(36).WithColumnDescription("流程运行步骤实例 ID")
               .WithColumn("VAR_TYPE").AsAnsiString(10).WithColumnDescription("参数类型 INPUT/OUTPUT/RESULT/CONTEXT")
               .WithColumn("VAR_NAME").AsAnsiString(80).WithColumnDescription("变量名称")
               .WithColumn("VAR_VALUE").AsAnsiString(int.MaxValue).WithColumnDescription("变量值")
               ;

            Create.Index("LC_FRU_VARIABLE_IDX")
                .OnTable("LC_FRU_VARIABLE")
                .OnColumn("PROC_ID").Ascending()
                .OnColumn("STEP_ID").Ascending()
                .OnColumn("VAR_NAME").Ascending()
                .WithOptions().Unique();

            // 流程结束后实例表, 删除运行中数据, 保留整合后的日志数据
            Create.Table("LC_FHI_PROC").WithDescription("历史流程实例")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")

               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("函数ID")
               .WithColumn("VERSION").AsInt64().WithColumnDescription("函数版本")
               .WithColumn("FUNCTION_NAME").AsAnsiString(80).WithColumnDescription("函数名称")
               .WithColumn("TRIGGER_TYPE").AsAnsiString(36).Nullable().WithColumnDescription("触发流程类型 API/JOB/MQ")
               .WithColumn("STATUS").AsInt16().WithColumnDescription("状态 -1：失败, 0：运行中, 1：已运行")
               .WithColumn("BEGIN_TIME").AsDateTime().WithColumnDescription("开始时间")
               .WithColumn("END_TIME").AsDateTime().Nullable().WithColumnDescription("结束时间")
               .WithColumn("DURATION").AsInt32().Nullable().WithColumnDescription("运行时长")
               .WithColumn("TOTAL_TRAFFIC").AsDecimal().Nullable().WithColumnDescription("总流量(KB)")
               .WithColumn("RUN_LOG").AsAnsiString(4000).Nullable().WithColumnDescription("运行流程日志")
               ;

            Create.Index("LC_FHI_PROC_IDX")
                .OnTable("LC_FHI_PROC")
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .OnColumn("BEGIN_TIME").Ascending()
                .OnColumn("FUNCTION_ID").Ascending()
                .OnColumn("VERSION").Ascending();

            Create.Table("LC_FHI_DETAIL").WithDescription("历史流程运行细节")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")

               .WithColumn("PROC_ID").AsAnsiString(36).WithColumnDescription("流程运行实例 ID")
               .WithColumn("SEQ_NO").AsInt32().Nullable().WithColumnDescription("执行顺序")
               .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("流程动作函数ID")
               .WithColumn("ACTION_ID").AsAnsiString(36).Nullable().WithColumnDescription("流程动作ID")
               .WithColumn("STEP_NAME").AsAnsiString(80).Nullable().WithColumnDescription("流程步骤名称")
               .WithColumn("STATUS").AsInt16().WithColumnDescription("状态 -1：失败, 0：运行中, 1：已运行")
               .WithColumn("BEGIN_TIME").AsDateTime().WithColumnDescription("开始时间")
               .WithColumn("END_TIME").AsDateTime().Nullable().WithColumnDescription("结束时间")
               .WithColumn("DURATION").AsInt32().Nullable().WithColumnDescription("运行时长")

               .WithColumn("RUN_DATA").AsAnsiString(int.MaxValue).WithColumnDescription("运行数据")
               ;

            Create.Index("LC_FHI_DETAIL_IDX")
                .OnTable("LC_FHI_DETAIL")
                .OnColumn("PROC_ID").Ascending()
                .OnColumn("SEQ_NO").Ascending()
                .OnColumn("FUNCTION_ID").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_FRU_PROC");
            Delete.Table("LC_FRU_STEP");
            Delete.Table("LC_FRU_LOG");
            Delete.Table("LC_FRU_VARIABLE");

            Delete.Table("LC_FHI_PROC");
            Delete.Table("LC_FHI_DETAIL");
        }
    }
}
