using System.Collections.Concurrent;
using Disruptor;
using Disruptor.Dsl;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using Serilog;

namespace GCP.Eventbus.Providers
{
    class InMemoryMessageBus : MessageBusBase
    {
        private readonly ConcurrentDictionary<string, DisruptorQueue> _disruptors;
        private readonly ConcurrentDictionary<string, ConcurrentBag<IMessageConsumer>> _consumers;
        private readonly int _ringSize;
        private bool _isConnected;

        public override bool IsConnected => _isConnected;

        public InMemoryMessageBus(MessageBusOptions options)
            : base(options)
        {
            _disruptors = new ConcurrentDictionary<string, DisruptorQueue>();
            _consumers = new ConcurrentDictionary<string, ConcurrentBag<IMessageConsumer>>();
            // 设置环形缓冲区大小，必须是2的幂
            _ringSize = GetRingBufferSize(options);
            _isConnected = false;
        }

        private int GetRingBufferSize(MessageBusOptions options)
        {
            var requestedSize = Convert.ToInt32(options.Settings.GetValueOrDefault("RingSize", "8192"));
            // 确保是2的幂
            return (int)Math.Pow(2, Math.Ceiling(Math.Log(requestedSize, 2)));
        }

        public override Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            _isConnected = true;
            return Task.CompletedTask;
        }

        public override Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            _isConnected = false;
            return Task.CompletedTask;
        }

        public override async Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var envelope = new MessageEnvelope
            {
                Payload = message,
            };
            if (headers is not null) envelope.Headers = headers;

            var disruptor = _disruptors.GetOrAdd(topic, _ => CreateDisruptorQueue());
            disruptor.Publish(envelope);
        }

        public override async Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var disruptor = _disruptors.GetOrAdd(topic, _ => CreateDisruptorQueue());

            var list = messages.ToList();
            disruptor.PublishBatch((i) =>
            {
                var envelope = new MessageEnvelope
                {
                    Payload = list[i],
                };
                if (headers is not null) envelope.Headers = headers;
                return envelope;
            }, list.Count);
        }

        public override async Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var disruptor = _disruptors.GetOrAdd(topic, _ => CreateDisruptorQueue());
            var consumer = new DisruptorMessageConsumer(options, disruptor, handler);
            var consumers = _consumers.GetOrAdd(topic, _ => new ConcurrentBag<IMessageConsumer>());
            consumers.Add(consumer);
            await consumer.StartAsync(cancellationToken);
        }

        public override async Task SubscribeBatchAsync(string topic, Func<List<MessageEnvelope>, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var disruptor = _disruptors.GetOrAdd(topic, _ => CreateDisruptorQueue());
            var consumer = new DisruptorMessageConsumer(options, disruptor, handler);
            var consumers = _consumers.GetOrAdd(topic, _ => new ConcurrentBag<IMessageConsumer>());
            consumers.Add(consumer);
            await consumer.StartAsync(cancellationToken);
        }

        public override async Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (_consumers.TryGetValue(topic, out var consumers))
            {
                var consumersToRemove = consumers.ToList();
                consumers.Clear();
                foreach (var consumer in consumersToRemove)
                {
                    await consumer.StopAsync(cancellationToken);
                    await consumer.DisposeAsync();
                }
            }
        }

        private DisruptorQueue CreateDisruptorQueue()
        {
            return new DisruptorQueue(_ringSize);
        }

        protected override async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            foreach (var consumers in _consumers.Values)
            {
                foreach (var consumer in consumers)
                {
                    await consumer.DisposeAsync();
                }
            }

            foreach (var disruptor in _disruptors.Values)
            {
                disruptor.Shutdown();
            }

            _disruptors.Clear();
            _consumers.Clear();

            await base.DisposeAsyncCore();
        }
    }

    internal class MessageEvent
    {
        public MessageEnvelope Envelope { get; set; }

        public void Clear()
        {
            Envelope = null;
        }
    }

    internal class DisruptorQueue
    {
        private readonly Disruptor<MessageEvent> _disruptor;
        private readonly RingBuffer<MessageEvent> _ringBuffer;
        private readonly ConcurrentDictionary<IBatchEventHandler<MessageEvent>, bool> _activeHandlers = new();

        public DisruptorQueue(int ringSize)
        {
            _disruptor = new Disruptor<MessageEvent>(() => new MessageEvent(), ringSize, TaskScheduler.Default, ProducerType.Single, new SpinWaitWaitStrategy());
            // 使用自定义的事件分发器处理所有事件
            _disruptor.HandleEventsWith(new EventDispatcher(_activeHandlers));
            _ringBuffer = _disruptor.RingBuffer;
            _disruptor.Start();
        }

        public void Publish(MessageEnvelope envelope)
        {
            var sequence = _ringBuffer.Next();
            try
            {
                var messageEvent = _ringBuffer[sequence];
                messageEvent.Envelope = envelope;
            }
            finally
            {
                _ringBuffer.Publish(sequence);
            }
        }

        public void PublishBatch(Func<int, MessageEnvelope> envelopeFactory, int batchSize)
        {
            var hi = _ringBuffer.Next(batchSize);
            var lo = hi - batchSize + 1;
            var i = 0;
            for (var l = lo; l <= hi; l++)
            {
                var messageEvent = _ringBuffer[l];
                messageEvent.Envelope = envelopeFactory(i);
                i++;
            }
            _ringBuffer.Publish(lo, hi);
        }

        public void AddHandler(IBatchEventHandler<MessageEvent> handler)
        {
            _activeHandlers.TryAdd(handler, true);
        }

        public void RemoveHandler(IBatchEventHandler<MessageEvent> handler)
        {
            _activeHandlers.TryRemove(handler, out _);
        }

        public void Shutdown()
        {
            _disruptor.Shutdown();
            _activeHandlers.Clear();
        }
    }

    // 自定义的事件分发器
    internal class EventDispatcher : IBatchEventHandler<MessageEvent>
    {
        private readonly ConcurrentDictionary<IBatchEventHandler<MessageEvent>, bool> _handlers;

        public EventDispatcher(ConcurrentDictionary<IBatchEventHandler<MessageEvent>, bool> handlers)
        {
            _handlers = handlers;
        }

        public void OnBatch(EventBatch<MessageEvent> batch, long sequence)
        {
            // 分发到所有活跃的处理器
            foreach (var handler in _handlers.Keys)
            {
                try
                {
                    handler.OnBatch(batch, sequence);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "在事件处理器中发生错误");
                }
            }
        }
    }

    internal class DisruptorMessageConsumer : IMessageConsumer, IBatchEventHandler<MessageEvent>
    {
        private readonly DisruptorQueue _disruptorQueue;
        private readonly Func<MessageEnvelope, CancellationToken, Task> _handler;
        private readonly Func<List<MessageEnvelope>, CancellationToken, Task> _batchHandler;
        private readonly CancellationTokenSource _cts;
        private bool _isRunning;

        public string Name { get; }
        public ConsumerOptions Options { get; }
        public bool IsRunning => _isRunning;

        public DisruptorMessageConsumer(
            ConsumerOptions options,
            DisruptorQueue disruptorQueue,
            Func<MessageEnvelope, CancellationToken, Task> handler)
        {
            Name = options.Name;
            Options = options;
            _disruptorQueue = disruptorQueue;
            _handler = handler;
            _cts = new CancellationTokenSource();
        }

        public DisruptorMessageConsumer(
            ConsumerOptions options,
            DisruptorQueue disruptorQueue,
            Func<List<MessageEnvelope>, CancellationToken, Task> batchHandler)
        {
            Name = options.Name;
            Options = options;
            _disruptorQueue = disruptorQueue;
            _batchHandler = batchHandler;
            _cts = new CancellationTokenSource();
        }

        public Task StartAsync(CancellationToken cancellationToken = default)
        {
            if (IsRunning) return Task.CompletedTask;

            _disruptorQueue.AddHandler(this);
            _isRunning = true;
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken = default)
        {
            if (!IsRunning) return Task.CompletedTask;

            _disruptorQueue.RemoveHandler(this);
            _isRunning = false;
            _cts.Cancel();
            return Task.CompletedTask;
        }

        public async ValueTask DisposeAsync()
        {
            await StopAsync();
            _cts.Dispose();
        }

        public void OnBatch(EventBatch<MessageEvent> batch, long sequence)
        {
            if (_cts.Token.IsCancellationRequested) return;
            if (Options.IsBatch)
            {
                var list = new List<MessageEnvelope>();
                foreach (var data in batch)
                {
                    list.Add(data.Envelope);
                }

                _ = _batchHandler(list, _cts.Token).ConfigureAwait(false);
            }
            else
            {
                var tasks = new List<Task>();
                foreach (var data in batch)
                {
                    if (data.Envelope == null) continue;

                    tasks.Add(_handler(data.Envelope, _cts.Token));
                }

#if DEBUG
                Task.WhenAll(tasks).ConfigureAwait(false).GetAwaiter().GetResult();
#else
                _ = Task.WhenAll(tasks).ConfigureAwait(false);
#endif
            }
        }
    }
}
