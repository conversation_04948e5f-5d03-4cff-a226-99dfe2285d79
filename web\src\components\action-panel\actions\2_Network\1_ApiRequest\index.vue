<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="12">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="请求API" prop="apiId">
              <t-select v-model="formData.apiId" placeholder="请选择请求API" :options="apiOptions"></t-select>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="请求头" prop="headers">
              <value-input
                v-model:data-value="formData.headers"
                :borderless="false"
                size="medium"
                only-variable
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="请求参数" prop="params">
              <value-input
                v-model:data-value="formData.params"
                :borderless="false"
                size="medium"
                only-variable
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="请求体" prop="body">
              <value-input
                v-model:data-value="formData.body"
                :borderless="false"
                size="medium"
                only-variable
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <action-form-title title="输入参数"> </action-form-title>
        <variable-list
          :data="formData.inputs"
          :on-change="onVariableChange"
          :show-root-node="formData.useRoot ?? false"
        ></variable-list>

        <action-form-title title="输出参数"> </action-form-title>
        <variable-list :data="currentStep.result" :show-root-node="formData.useRoot ?? false"></variable-list>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ApiRequestActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onMounted, ref, watch } from 'vue';

import { api, Services } from '@/api/system';
import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';

import { useApiRequestStore } from './store';

const actionFlowStore = useActionFlowStore();
const apiRequestStore = useApiRequestStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    apiRequestStore.updateState();
  },
  {
    immediate: true,
  },
);
const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData } = storeToRefs(apiRequestStore);

watch(
  () => formData.value,
  (newValue) => {
    apiRequestStore.setArgs(newValue);
  },
  {
    deep: true,
  },
);
const onVariableChange = (type, item) => {
  // apiRequestStore.setArgs(formData.value);
};

const apiOptions = ref([]);
const apiList = ref([]);
onMounted(() => {
  api
    .run(Services.apiGetAll, {
      apiType: 2,
    })
    .then((res) => {
      apiList.value = res;
      apiOptions.value = res.map((item) => ({ label: `${item.apiName}`, value: item.id }));
    });
});
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}
</style>
