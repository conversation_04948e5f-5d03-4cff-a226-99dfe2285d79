﻿using Polly;
using System.Collections.Concurrent;

namespace GCP.Common
{
    /// <summary>
    /// 一个静态的、线程安全的管理器，用于动态存储和执行 Polly 弹性策略管道。
    /// </summary>
    public static class ResiliencePipelineManager
    {
        // 使用 ConcurrentDictionary 来保证在高并发场景下的线程安全。
        // 字典的值存储非泛型的基类 ResiliencePipeline，以支持存储不同返回类型的管道。
        private static readonly ConcurrentDictionary<string, ResiliencePipeline<object>> Pipelines = new();

        public static ResiliencePipeline<object> TryAdd
            (string key, Func<ResiliencePipeline<object>> getPipeline)
        {
            if (Pipelines.TryGetValue(key, out var pipeline)) return pipeline;
            var newPipeline = getPipeline();
            Pipelines.TryAdd(key, newPipeline);
            return newPipeline;
        }

        /// <summary>
        /// 添加或更新一个策略管道。
        /// </summary>
        /// <param name="key">策略的唯一标识符</param>
        /// <param name="pipeline">要存储的策略管道（可以是泛型或非泛型）</param>
        public static void AddOrUpdate(string key, ResiliencePipeline<object> pipeline)
        {
            Pipelines.AddOrUpdate(key, pipeline, (existingKey, existingPipeline) => pipeline);
        }

        /// <summary>
        /// 尝试移除一个策略管道。
        /// </summary>
        /// <param name="key">策略的唯一标识符</param>
        /// <returns>如果成功移除则返回 true，否则返回 false</returns>
        public static bool TryRemove(string key)
        {
            return Pipelines.TryRemove(key, out _);
        }

        /// <summary>
        /// 根据前缀移除策略管道。
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        public static bool TryRemoveByPrefix(string prefix)
        {
             var keysToRemove = Pipelines.Keys.Where(k => k.StartsWith(prefix));
             foreach (var key in keysToRemove)
             {
                 Pipelines.TryRemove(key, out _);
             }
             return true;
        }

        /// <summary>
        /// 使用指定键的策略管道来执行一个带返回值的异步操作。
        /// </summary>
        public static async Task<TResult> ExecuteAsync<TResult>(string key, Func<CancellationToken, Task> action, CancellationToken cancellationToken = default)
        {
            if (!Pipelines.TryGetValue(key, out var pipeline))
            {
                throw new KeyNotFoundException($"策略管理器中未找到键为 '{key}' 的策略。");
            }

            return await pipeline.ExecuteAsync(async token =>
            {
                await action(token).ConfigureAwait(false);
                return default(TResult);
            }, cancellationToken).ConfigureAwait(false);
        }

        public static async Task<object> ExecuteAsync(string key, Func<CancellationToken, Task> action,
            CancellationToken cancellationToken = default)
        {
            return await ExecuteAsync<object>(key, action, cancellationToken);
        }
    }
}
