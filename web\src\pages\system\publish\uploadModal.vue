<template>
  <t-dialog
    v-model:visible="showUpload"
    header="导入当前环境"
    width="40%"
    :confirm-on-enter="true"
    :on-close="close"
    :on-confirm="onConfirmAnother"
  >
    <t-space direction="vertical" style="width: 100%">
      <t-upload
        ref="uploadRef1"
        v-model="files1"
        action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
        :headers="{ a: 'N1', b: 'N2' }"
        placeholder="要求文件大小在 1M 以内"
        :multiple="multiple"
        :auto-upload="autoUpload"
        :upload-all-files-in-one-request="uploadInOneRequest"
        :is-batch-upload="isBatchUpload"
        :size-limit="{ size: 1, unit: 'MB' }"
        :max="5"
        :disabled="disabled"
        :allow-upload-duplicate-file="true"
        @select-change="handleSelectChange"
        @fail="handleFail"
        @success="handleSuccess"
        @one-file-success="onOneFileSuccess"
        @validate="onValidate"
      />
    </t-space>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'UploadModal',
};
</script>
<script lang="ts" setup>
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, ref } from 'vue';

const props = defineProps<{
  visible: boolean;
}>();
const emits = defineEmits(['update:visible']);
const showUpload = computed({
  get() {
    return props.visible;
  },
  set(val) {
    emits('update:visible', val);
  },
});
const uploadRef1 = ref();

const files1 = ref([]);

const multiple = ref(false);
const uploadInOneRequest = ref(false);
const autoUpload = ref(true);
const isBatchUpload = ref(false);
const disabled = ref(false);
const onConfirmAnother = (context) => {
  console.log('点击了确认按钮', context);
  visible.value = false;
};
const close = (context) => {
  console.log('关闭弹窗，点击关闭按钮、按下ESC、点击蒙层等触发', context);
};
const handleFail = ({ file }) => {
  MessagePlugin.error(`文件 ${file.name} 上传失败`);
};

function handleSelectChange(files, context) {
  console.log('onSelectChange', files, context);
}

const handleSuccess = (params) => {
  console.log('success', params);
  MessagePlugin.success('上传成功');
};

// 多文件上传，一个文件一个请求场景，每个文件也会单独触发上传成功的事件
const onOneFileSuccess = (params) => {
  console.log('onOneFileSuccess', params);
};

// 有文件数量超出时会触发，文件大小超出限制、文件同名时会触发等场景。注意如果设置允许上传同名文件，则此事件不会触发
const onValidate = (params) => {
  const { files, type } = params;
  console.log('onValidate', type, files);
  const messageMap = {
    FILE_OVER_SIZE_LIMIT: '文件大小超出限制，已自动过滤',
    FILES_OVER_LENGTH_LIMIT: '文件数量超出限制，仅上传未超出数量的文件',
    // if you need same name files, setting allowUploadDuplicateFile={true} please
    FILTER_FILE_SAME_NAME: '不允许上传同名文件',
    BEFORE_ALL_FILES_UPLOAD: 'beforeAllFilesUpload 方法拦截了文件',
    CUSTOM_BEFORE_UPLOAD: 'beforeUpload 方法拦截了文件',
  };
  // you can also set Upload.tips and Upload.status to show warning message.
  messageMap[type] && MessagePlugin.warning(messageMap[type]);
};

// 仅自定义文件列表所需
// eslint-disable-next-line
// const outsideRemove = (index) => {
//   const tmpFiles = [...files3.value];
//   tmpFiles.splice(index, 1);
//   files3.value = tmpFiles;
// };

// 非自动上传文件，需要在父组件单独执行上传请求
// const uploadFiles = () => {
//   uploadRef1.value.uploadFiles();
// };

// const formatResponse = () => {
//   return { error: '上传失败，请重试' };
// };

// const handleProgress = (params) => {
//   console.log('progress', params);
// };
</script>
