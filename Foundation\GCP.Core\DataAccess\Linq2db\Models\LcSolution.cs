// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 解决方案
	/// </summary>
	[Table("lc_solution")]
	public class LcSolution : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"            , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                           )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"       , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                         )] public DateTime? TimeModified { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                              )] public string?   Modifier     { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                 )] public short     State        { get; set; } // smallint
		/// <summary>
		/// Description:解决方案名称
		/// </summary>
		[Column("SOLUTION_NAME" , CanBeNull = false                     )] public string    SolutionName { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                           )] public string?   Description  { get; set; } // varchar(200)
		/// <summary>
		/// Description:图标
		/// </summary>
		[Column("ICON"                                                  )] public string?   Icon         { get; set; } // varchar(200)
		/// <summary>
		/// Description:默认数据源 ID
		/// </summary>
		[Column("DATA_SOURCE_ID"                                        )] public string?   DataSourceId { get; set; } // varchar(36)
	}
}
