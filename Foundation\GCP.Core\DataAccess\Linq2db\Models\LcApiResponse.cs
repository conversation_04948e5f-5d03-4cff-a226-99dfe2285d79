// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// API返回体定义
	/// </summary>
	[Table("lc_api_response")]
	public class LcApiResponse : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"           , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                          )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"      , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                        )] public DateTime? TimeModified { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                             )] public string?   Modifier     { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                )] public short     State        { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"  , CanBeNull = false                     )] public string    SolutionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"   , CanBeNull = false                     )] public string    ProjectId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:API返回体编码
		/// </summary>
		[Column("RESPONSE_CODE", CanBeNull = false                     )] public string    ResponseCode { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:API返回体类型
		/// </summary>
		[Column("RESPONSE_TYPE", CanBeNull = false                     )] public string    ResponseType { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:API返回体名称
		/// </summary>
		[Column("RESPONSE_NAME", CanBeNull = false                     )] public string    ResponseName { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                          )] public string?   Description  { get; set; } // varchar(500)
		/// <summary>
		/// Description:API返回体
		/// </summary>
		[Column("RESPONSE_DATA", CanBeNull = false                     )] public string    ResponseData { get; set; } = null!; // varchar(4000)
		/// <summary>
		/// Description:成功标识
		/// </summary>
		[Column("SUCCESS_FLAG"                                         )] public string?   SuccessFlag  { get; set; } // varchar(2000)
		/// <summary>
		/// Description:错误码
		/// </summary>
		[Column("ERROR_CODE"                                           )] public string?   ErrorCode    { get; set; } // varchar(2000)
		/// <summary>
		/// Description:错误信息
		/// </summary>
		[Column("ERROR_MESSAGE"                                        )] public string?   ErrorMessage { get; set; } // varchar(2000)
		/// <summary>
		/// Description:结果
		/// </summary>
		[Column("RESULT"                                               )] public string?   Result       { get; set; } // varchar(2000)
	}
}
