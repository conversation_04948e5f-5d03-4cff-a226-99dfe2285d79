import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { createStringRootNode } from '@/components/action-panel/utils/rootNodeHelper';

import { ArgsInfo } from './model';

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      apiId: '',
      description: '',
      mode: 1,
      params: [],
      headers: [],
    } as ArgsInfo),
    ...(actionFlowStore.currentStep.args || {}),
  }) as ArgsInfo;
};

export const useApiForwarderStore = defineStore('ApiForwarder', {
  state: () => {
    const state = { args: null } as { args: ArgsInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [
        {
          id: 'result',
          key: 'result',
          description: '结果',
          type: 'string',
        },
      ] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 使用根节点绑定
      const rootNode = createStringRootNode('转发结果');
      actionFlowStore.setStepResultData([rootNode]);
    },
  },
});
