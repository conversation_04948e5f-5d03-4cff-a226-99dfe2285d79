using GCP.Functions.Common.Services;
using System.Text.Json;

namespace GCP.Tests
{
    /// <summary>
    /// Utils工具函数服务演示程序
    /// </summary>
    public class UtilsFunctionServiceDemo
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== Utils工具函数服务演示 ===");
            Console.WriteLine();

            try
            {
                var service = new UtilsFunctionService();

                // 测试获取所有函数
                Console.WriteLine("1. 获取所有函数列表:");
                var allFunctions = service.GetAll();
                Console.WriteLine($"总共获取到 {allFunctions.Count} 个函数");
                Console.WriteLine();

                // 显示前5个函数的详细信息
                Console.WriteLine("前5个函数的详细信息:");
                foreach (dynamic func in allFunctions.Take(5))
                {
                    Console.WriteLine($"  函数名: {func.value}");
                    Console.WriteLine($"  显示名: {func.label}");
                    Console.WriteLine($"  分类: {func.category}");
                    Console.WriteLine($"  脚本: {func.script}");
                    Console.WriteLine($"  描述: {func.remark}");
                    Console.WriteLine($"  返回类型: {func.returnType}");
                    Console.WriteLine("  ---");
                }
                Console.WriteLine();

                // 测试按分类获取函数
                Console.WriteLine("2. 按分类获取函数:");
                var categorizedFunctions = service.GetByCategory();
                Console.WriteLine($"总共获取到 {categorizedFunctions.Count} 个分类");
                Console.WriteLine();

                foreach (dynamic category in categorizedFunctions)
                {
                    var children = category.children as List<object>;
                    Console.WriteLine($"分类: {category.label} ({category.value}) - {children?.Count ?? 0} 个函数");
                    
                    if (children != null && children.Count > 0)
                    {
                        foreach (dynamic func in children.Take(3)) // 只显示前3个
                        {
                            Console.WriteLine($"  - {func.label} ({func.value})");
                        }
                        if (children.Count > 3)
                        {
                            Console.WriteLine($"  ... 还有 {children.Count - 3} 个函数");
                        }
                    }
                    Console.WriteLine();
                }

                // 验证英文标签
                Console.WriteLine("3. 验证函数标签语言:");
                int chineseLabelCount = 0;
                int englishLabelCount = 0;

                foreach (dynamic func in allFunctions)
                {
                    string label = func.label;
                    bool hasChineseChars = label.Any(c => c >= 0x4e00 && c <= 0x9fff);
                    
                    if (hasChineseChars)
                    {
                        chineseLabelCount++;
                        Console.WriteLine($"  发现中文标签: {func.value} - {label}");
                    }
                    else
                    {
                        englishLabelCount++;
                    }
                }

                Console.WriteLine($"英文标签函数: {englishLabelCount} 个");
                Console.WriteLine($"中文标签函数: {chineseLabelCount} 个");
                Console.WriteLine();

                // 按分类统计
                Console.WriteLine("4. 按分类统计函数数量:");
                var categoryStats = allFunctions
                    .GroupBy(f => ((dynamic)f).category)
                    .Select(g => new { Category = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count);

                foreach (var stat in categoryStats)
                {
                    Console.WriteLine($"  {stat.Category}: {stat.Count} 个函数");
                }

                Console.WriteLine();
                Console.WriteLine("=== 演示完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
