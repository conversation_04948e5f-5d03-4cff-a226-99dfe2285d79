using System.Collections.Concurrent;
using System.Reflection;
using GCP.Iot.Interfaces;
using GCP.Common;
using Serilog;

namespace GCP.Iot.Services
{
    class DriverManager
    {
        private readonly ConcurrentDictionary<string, Type> _driverTypes;
        private readonly ConcurrentDictionary<string, Dictionary<string, Dictionary<string, object>>> _driverConfigs;
        // 共享驱动实例缓存
        private readonly ConcurrentDictionary<string, IDriver> _sharedDrivers;

        public DriverManager()
        {
            _driverTypes = new ConcurrentDictionary<string, Type>();
            _driverConfigs = new ConcurrentDictionary<string, Dictionary<string, Dictionary<string, object>>>();
            _sharedDrivers = new ConcurrentDictionary<string, IDriver>();
        }

        /// <summary>
        /// 注册驱动类型
        /// </summary>
        /// <param name="driverCode">驱动名称</param>
        /// <param name="driverType">驱动类型</param>
        /// <returns>是否注册成功</returns>
        public bool RegisterDriver(string driverCode, Type driverType)
        {
            if (!typeof(IDriver).IsAssignableFrom(driverType))
            {
                Log.Error("驱动类型 {DriverTypeFullName} 未实现 IIotDriver 接口", driverType.FullName);
                return false;
            }

            return _driverTypes.TryAdd(driverCode, driverType);
        }

        /// <summary>
        /// 加载驱动程序集
        /// </summary>
        /// <param name="assemblyPath">程序集路径</param>
        /// <returns>加载的驱动数量</returns>
        public int LoadDriverAssembly(string assemblyPath)
        {
            try
            {
                var assembly = Assembly.LoadFrom(assemblyPath);
                var count = 0;

                foreach (var type in assembly.GetTypes())
                {
                    if (typeof(IDriver).IsAssignableFrom(type) && !type.IsInterface && !type.IsAbstract)
                    {
                        var driverInstance = Activator.CreateInstance(type);
                        if (driverInstance == null) continue;
                        var driver = (IDriver)driverInstance;
                        if (RegisterDriver(driver.DriverCode, type))
                        {
                            count++;
                            Log.Information("已加载Iot驱动: {DriverDriverCode}", driver.DriverCode);
                        }
                    }
                }

                return count;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "加载驱动程序集失败: {AssemblyPath}", assemblyPath);
                return 0;
            }
        }


        /// <summary>
        /// 获取所有已加载的驱动类型
        /// </summary>
        /// <returns>驱动类型列表</returns>
        public List<Type> GetAllDriverTypes()
        {
            return _driverTypes.Values.ToList();
        }

        /// <summary>
        /// 获取指定驱动代码的类型
        /// </summary>
        /// <param name="driverCode">驱动代码</param>
        /// <returns>驱动类型</returns>
        public Type GetDriverType(string driverCode)
        {
            if (_driverTypes.TryGetValue(driverCode, out var type))
            {
                return type;
            }
            return null;
        }

        /// <summary>
        /// 设置驱动全局配置
        /// </summary>
        /// <param name="driverCode">驱动名称</param>
        /// <param name="config">配置参数</param>
        public void SetDriverConfig(string driverCode, Dictionary<string, object> config)
        {
            var driverConfig = _driverConfigs.GetOrAdd(driverCode, _ => new Dictionary<string, Dictionary<string, object>>());
            driverConfig["global"] = config;
        }

        /// <summary>
        /// 设置设备级别的驱动配置
        /// </summary>
        /// <param name="driverCode">驱动名称</param>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="config">配置参数</param>
        public void SetEquipmentDriverConfig(string driverCode, string equipmentId, Dictionary<string, object> config)
        {
            var driverConfig = _driverConfigs.GetOrAdd(driverCode, _ => new Dictionary<string, Dictionary<string, object>>());
            driverConfig[equipmentId] = config;
        }

        /// <summary>
        /// 检查设备是否有专用配置
        /// </summary>
        /// <param name="driverCode">驱动编码</param>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>true-有专用配置，false-使用全局配置</returns>
        public bool HasEquipmentSpecificConfig(string driverCode, string equipmentId)
        {
            if (_driverConfigs.TryGetValue(driverCode, out var configs))
            {
                return configs.ContainsKey(equipmentId);
            }
            return false;
        }

        /// <summary>
        /// 创建驱动实例
        /// </summary>
        /// <param name="driverCode">驱动名称</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="equipmentId">设备ID，如果指定则应用设备级别配置</param>
        /// <returns>驱动实例</returns>
        public IDriver CreateDriver(string driverCode, string equipmentId = null)
        {
            if (!_driverTypes.TryGetValue(driverCode, out var driverType))
            {
                throw new ArgumentException($"未找到驱动: {driverCode}");
            }

            // 如果没有设备级别配置，并且不是空的equipmentId，则使用共享驱动
            if (!string.IsNullOrEmpty(equipmentId) && !HasEquipmentSpecificConfig(driverCode, equipmentId))
            {
                return GetOrCreateSharedDriver(driverCode);
            }

            var driver = (IDriver)Activator.CreateInstance(driverType);
            driver.Logger = Log.Logger;

            // 应用驱动配置
            if (_driverConfigs.TryGetValue(driverCode, out var configs))
            {
                // 应用全局配置
                if (configs.TryGetValue("global", out var globalConfig))
                {
                    ApplyDriverConfig(driver, globalConfig);
                }

                // 如果指定了设备ID，应用设备级别配置
                if (!string.IsNullOrEmpty(equipmentId) && configs.TryGetValue(equipmentId, out var equipmentConfig))
                {
                    ApplyDriverConfig(driver, equipmentConfig);
                }
            }

            return driver;
        }

        /// <summary>
        /// 获取或创建共享驱动实例
        /// </summary>
        /// <param name="driverCode">驱动代码</param>
        /// <returns>共享的驱动实例</returns>
        private IDriver GetOrCreateSharedDriver(string driverCode)
        {
            return _sharedDrivers.GetOrAdd(driverCode, code => {
                var driver = (IDriver)Activator.CreateInstance(_driverTypes[code]);
                driver.Logger = Log.Logger;

                // 应用全局配置
                if (_driverConfigs.TryGetValue(code, out var configs) && 
                    configs.TryGetValue("global", out var globalConfig))
                {
                    ApplyDriverConfig(driver, globalConfig);
                }

                Log.Information("创建共享驱动实例: {Code}", code);
                return driver;
            });
        }

        /// <summary>
        /// 释放共享驱动
        /// </summary>
        /// <param name="driverCode">驱动代码</param>
        public async Task ReleaseSharedDriverAsync(string driverCode)
        {
            if (_sharedDrivers.TryRemove(driverCode, out var driver))
            {
                try
                {
                    await driver.DisconnectAsync();
                    driver.Dispose();
                    Log.Information("释放共享驱动: {DriverCode}", driverCode);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "释放共享驱动异常: {DriverCode}", driverCode);
                }
            }
        }

        /// <summary>
        /// 获取已注册的驱动列表
        /// </summary>
        public IEnumerable<string> GetRegisteredDrivers()
        {
            return _driverTypes.Keys;
        }

        private void ApplyDriverConfig(IDriver driver, Dictionary<string, object> config)
        {
            if (config.Count == 0) return;

            var properties = driver.GetType().GetProperties();
            foreach (var property in properties)
            {
                if (config.TryGetValue(property.Name, out var value))
                {
                    try
                    {
                        var convertedValue = ConvertHelper.ChangeType(value, Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
                        property.SetValue(driver, convertedValue);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "设置驱动配置失败: {PropertyName}", property.Name);
                    }
                }
            }
        }
    }
}