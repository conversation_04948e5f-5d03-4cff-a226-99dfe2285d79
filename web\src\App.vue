<template>
  <t-config-provider :global-config="getComponentsLocale">
    <router-view :key="locale" :class="[mode]" />
  </t-config-provider>
</template>
<script setup lang="ts">
import { useClipboard } from '@vueuse/core';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';

import { useLocale } from '@/locales/useLocale';
import { useSettingStore } from '@/store';

import { core } from './utils/core';
import CustomError from './utils/exception/CustomError';

const router = useRouter();
const { isSupported } = useClipboard();

watch(
  isSupported,
  (val) => {
    if (val) {
      console.log('支持剪贴板API');
    } else {
      console.error('不支持剪贴板API，自定义右键复制粘贴功能将无法跨域使用');
    }
  },
  {
    immediate: true,
  },
);
const store = useSettingStore();

const mode = computed(() => {
  return store.displayMode;
});

onMounted(() => {
  if (window.top !== window) return;
  core.ipc.on('custom_error', (args: CustomError) => {
    if (args.code === 401) {
      if (router.currentRoute.value.path !== '/login') {
        router.push({
          path: '/login',
          query: { redirect: encodeURIComponent(router.currentRoute.value.fullPath) },
        });
      }
    }

    if (args.name === 'OperationError') {
      const alertDia = DialogPlugin.alert({
        theme: 'info',
        header: '提示',
        body: args.message,
        onConfirm: () => {
          alertDia.hide();
        },
      });
    } else {
      MessagePlugin.error(args.message);
    }
  });
});

const { getComponentsLocale, locale } = useLocale();
</script>
<style lang="less" scoped>
#nprogress .bar {
  background: var(--td-brand-color) !important;
}
</style>
