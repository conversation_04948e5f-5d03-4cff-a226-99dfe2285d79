import { defineStore } from 'pinia';

import { api, Services } from '@/api/system';
import { usePermissionStore } from '@/store';
import type { UserInfo } from '@/types/interface';

const InitUserInfo: UserInfo = {
  name: '', // 用户名，用于展示在页面右上角头像处
  roles: [], // 前端权限模型使用 如果使用请配置modules/permission-fe.ts使用
};

export const useUserStore = defineStore('user', {
  state: () => ({
    token: 'main_token', // 默认token不走权限
    userInfo: { ...InitUserInfo },
    projectId: '',
    projectName: '',
    solutionId: '',
  }),
  getters: {
    roles: (state) => {
      return state.userInfo?.roles;
    },
  },
  actions: {
    async login(userInfo: Record<string, string>) {
      const token = await api.run(Services.userLogin, { username: userInfo.account, password: userInfo.password });
      // core.setToken(token);
      this.token = token;
    },
    async getUserInfo() {
      // const mockRemoteUserInfo = async (token: string) => {
      //   if (token === 'main_token') {
      //     return {
      //       name: '管理员',
      //       roles: ['all'], // 前端权限模型使用 如果使用请配置modules/permission-fe.ts使用
      //     };
      //   }
      //   return {
      //     name: 'td_dev',
      //     roles: ['UserIndex', 'DashboardBase', 'login'], // 前端权限模型使用 如果使用请配置modules/permission-fe.ts使用
      //   };
      // };
      // const res = await mockRemoteUserInfo(this.token);
      const res = await api.run(Services.userCurrentInfo);

      this.userInfo = res;
    },
    async logout() {
      if (this.token === '') return;
      this.token = '';
      this.userInfo = { ...InitUserInfo };
      await api.run(Services.userLogout);
    },
    setProjectInfo(solutionId: string, projectId: string, projectName: string) {
      this.solutionId = solutionId;
      this.projectId = projectId;
      this.projectName = projectName;
    },
  },
  persist: {
    afterRestore: () => {
      const permissionStore = usePermissionStore();
      permissionStore.initRoutes();
    },
    key: 'user',
  },
});
