using GCP.Common;
using GCP.FunctionPool.Flow;
using Xunit;

namespace GCP.Tests.FunctionPool.Flow
{
    public class RootNodeVariableAccessTests
    {
        [Fact]
        public void GetVariableValue_WhenAccessingRootButOnlyResultExists_ShouldReturnResultValue()
        {
            // Arrange
            var localVariable = new Dictionary<string, object>
            {
                { "result", "test_value" }
            };

            var dataValue = new DataValue
            {
                Type = "variable",
                VariableType = "current",
                VariableValue = "ROOT"
            };

            // Act
            var result = FlowUtils.GetVariableValueByPath(dataValue, new Dictionary<string, object>(), localVariable);

            // Assert
            Assert.Equal("test_value", result);
        }

        [Fact]
        public void GetVariableValue_WhenAccessingRootAndRootExists_ShouldReturnRootValue()
        {
            // Arrange
            var localVariable = new Dictionary<string, object>
            {
                { "ROOT", "root_value" }
            };

            var dataValue = new DataValue
            {
                Type = "variable",
                VariableType = "current",
                VariableValue = "ROOT"
            };

            // Act
            var result = FlowUtils.GetVariableValueByPath(dataValue, new Dictionary<string, object>(), localVariable);

            // Assert
            Assert.Equal("root_value", result);
        }

        [Fact]
        public void GetVariableValue_WhenAccessingRootWithMultipleFields_ShouldThrowException()
        {
            // Arrange
            var localVariable = new Dictionary<string, object>
            {
                { "result", "test_value" },
                { "other", "other_value" }
            };

            var dataValue = new DataValue
            {
                Type = "variable",
                VariableType = "current",
                VariableValue = "ROOT"
            };

            // Act & Assert
            Assert.Throws<CustomException>(() =>
            {
                FlowUtils.GetVariableValueByPath(dataValue, new Dictionary<string, object>(), localVariable);
            });
        }

        [Fact]
        public void ProcessVariablePathResult_WhenPathPointsToRootAndOnlyResultExists_ShouldReturnResultValue()
        {
            // Arrange
            var dict = new Dictionary<string, object>
            {
                { "result", "test_value" }
            };

            // Act
            var result = RootNodeHelper.ProcessVariablePathResult(dict, "ROOT");

            // Assert
            Assert.Equal("test_value", result);
        }

        [Fact]
        public void ProcessVariablePathResult_WhenPathPointsToRootAndRootExists_ShouldReturnRootValue()
        {
            // Arrange
            var dict = new Dictionary<string, object>
            {
                { "ROOT", "root_value" }
            };

            // Act
            var result = RootNodeHelper.ProcessVariablePathResult(dict, "ROOT");

            // Assert
            Assert.Equal("root_value", result);
        }

        [Fact]
        public void ProcessVariablePathResult_WhenPathDoesNotPointToRoot_ShouldReturnOriginalResult()
        {
            // Arrange
            var dict = new Dictionary<string, object>
            {
                { "result", "test_value" }
            };

            // Act
            var result = RootNodeHelper.ProcessVariablePathResult(dict, "result");

            // Assert
            Assert.Equal(dict, result);
        }
    }
}
