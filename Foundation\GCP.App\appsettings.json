{"port": 6981, "dbSettings": {"DataSource": {"Default": {"ConnectionString": "Server=************;Database=scm_gcp;User=root;Password=********;Charset=utf8;Convert Zero Datetime=True;Port=3306;SslMode=None;Allow User Variables=True;AllowLoadLocalInfile=true;AllowPublicKeyRetrieval=True", "ProviderName": "MySql"}}, "Logging": {"EnableLocalDatabase": false, "ChannelCapacity": 8192, "Job": {"Flow": true, "Step": true}, "API": {"Flow": true, "Step": true}, "Message": {"Flow": true, "Step": true}}}, "mcpSettings": {"servers": {"GCP": {"command": "sse", "args": "http://localhost:6981/sse"}}}, "mqttSettings": {"enable": true, "port": 1883, "username": "", "password": ""}, "redis": {"ConnectionString": ""}, "email": {"Sender": "SMTP-xxx", "DefaultFromEmail": "<EMAIL>", "DefaultFromName": "mes", "SMTP": {"Host": "mail.qq.com", "Port": 465, "EnableSsl": true, "Username": "<EMAIL>", "Password": "qq@123"}}, "hangfire": {"storage": "memory", "workerCount": 50}, "functionPool": {"FileProvider": ""}, "FileServer": {"MimeType": [{"Key": ".vue", "Value": "application/octet-stream"}, {"Key": ".ipa", "Value": "application/octet-stream"}, {"Key": ".apk", "Value": "application/octet-stream"}, {"Key": ".plist", "Value": "text/xml"}], "VirtualPath": [{"IsAbsolutePath": false, "FileProvider": "wwwroot", "RequestPath": "/h5", "EnableDirectoryBrowsing": false}]}, "Cors": [{"Name": "<PERSON><PERSON><PERSON>", "Origins": "*", "Methods": "OPTIONS,GET,POST,PUT,DELETE", "AllowHeaders": "Content-Type,Token,X-TZ-Offset,pid,sid", "ExposedHeaders": "Token"}], "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "Yarp": "Warning", "Microsoft.AspNetCore": "Warning"}}, "WriteTo": [{"Name": "Debug"}, {"Name": "File", "Args": {"path": "./logs/log_.txt", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "retainedFileCountLimit": 90}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Debug"]}, "Authentication": {}, "AllowedHosts": "*"}