using GCP.Common;

namespace GCP.FunctionPool.Flow.Models
{
    /// <summary>
    /// 缓存写入动作
    /// </summary>
    public class DataCacheWrite
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 缓存键
        /// </summary>
        public DataValue CacheKey { get; set; }
        /// <summary>
        /// 缓存值
        /// </summary>
        public DataValue CacheValue { get; set; }
        /// <summary>
        /// 过期时间（秒），为空则永不过期
        /// </summary>
        public DataValue ExpirationSeconds { get; set; }
    }

    /// <summary>
    /// 缓存读取动作
    /// </summary>
    public class DataCacheRead
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 缓存键
        /// </summary>
        public DataValue CacheKey { get; set; }
        /// <summary>
        /// 默认值（当缓存不存在时返回）
        /// </summary>
        public DataValue DefaultValue { get; set; }
    }

    /// <summary>
    /// 缓存删除动作
    /// </summary>
    public class DataCacheRemove
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 缓存键
        /// </summary>
        public DataValue CacheKey { get; set; }
    }

    /// <summary>
    /// 缓存检查存在动作
    /// </summary>
    public class DataCacheExists
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 缓存键
        /// </summary>
        public DataValue CacheKey { get; set; }
    }
}
