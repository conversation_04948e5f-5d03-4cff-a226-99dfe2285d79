<template>
  <div style="width: 100%" @mouseover.stop="showEdit = true" @blur="showEdit = false">
    <t-input v-if="showEdit" v-bind="targetAttrs"></t-input>
    <div v-else :class="'value-input-text' + (disabled ? ' is-disabled' : '')">
      <div v-if="modelValue">{{ modelValue }}</div>
      <div v-else class="value-input-text-placeholder">{{ placeholder }}</div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'MouseInput',
};
</script>
<script setup lang="ts">
import { InputProps } from 'tdesign-vue-next';
import { computed, ref, useAttrs } from 'vue';

interface MouseInputProps extends Omit<InputProps, 'options'> {}

const props = withDefaults(defineProps<MouseInputProps>(), {});
const attrs: Partial<MouseInputProps> = useAttrs();
const targetAttrs = computed<MouseInputProps>(() => {
  return { ...attrs, ...props };
});

const showEdit = ref(false);
</script>
<style lang="less" scoped>
.value-input-text {
  font-size: 12px;
  height: 24px;
  padding: 1px 8px;
  border: 1px solid transparent;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: var(--td-text-color-primary);
  &.is-disabled {
    border: none;
    background-color: var(--td-bg-color-component-disabled);
    color: var(--td-text-color-disabled);
  }
  .value-input-text-placeholder {
    color: #999;
  }
}
</style>
