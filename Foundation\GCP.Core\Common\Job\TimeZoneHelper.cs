﻿namespace GCP.Common
{
    public static class TimeZoneHelper
    {
        /// <summary>
        /// 根据时区ID获取时区信息
        /// </summary>
        /// <param name="windowsOrIanaTimeZoneId"></param>
        /// <returns></returns>
        public static TimeZoneInfo FindTimeZoneInfoById(string windowsOrIanaTimeZoneId = "Asia/Shanghai")
        {
            //windows和linux系统时区信息不一致, .net6以下需要第三方库TimeZoneConverter自动转换处理
            return TimeZoneConverter.TZConvert.GetTimeZoneInfo(windowsOrIanaTimeZoneId);
            //.net6使用自带的即可TimeZoneInfo.FindSystemTimeZoneById（自动时区转换和手动时区转换的 API 都包含其中）
            //return TimeZoneInfo.FindSystemTimeZoneById(windowsOrIanaTimeZoneId);
        }

        /// <summary>
        /// 将时间通过来源时区偏移量转换成目标时区的时间（常用于通过客户时区时间转换成统一时区时间保存到数据库, 以保证数据库时区一致）
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="sourceTimeZoneOffset"></param>
        /// <param name="destinationTimeZone"></param>
        /// <returns></returns>
        public static DateTime ConvertTimeZone(this DateTime dateTime, int sourceTimeZoneOffset, TimeZoneInfo destinationTimeZone)
        {
            var timeOffset = new TimeSpan(sourceTimeZoneOffset / 60, sourceTimeZoneOffset % 60, 0);
            var utcDt = DateTime.SpecifyKind(dateTime + timeOffset, DateTimeKind.Utc);
            return TimeZoneInfo.ConvertTime(utcDt, TimeZoneInfo.Utc, destinationTimeZone);
        }

        /// <summary>
        /// 将时间通过来源时区和目标偏移量转换成来源时区的时间（常用于取出统一时区时间转换到目标客户端时区时间, 以保证全球不同用户显示当地时间）
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="sourceTimeZone"></param>
        /// <param name="destinationTimeZoneOffset"></param>
        /// <returns></returns>
        public static DateTime ConvertTimeZone(this DateTime dateTime, TimeZoneInfo sourceTimeZone, int destinationTimeZoneOffset)
        {
            var timeOffset = new TimeSpan(destinationTimeZoneOffset / 60, destinationTimeZoneOffset % 60, 0);
            var utcDt = TimeZoneInfo.ConvertTime(DateTime.SpecifyKind(dateTime, DateTimeKind.Unspecified), sourceTimeZone, TimeZoneInfo.Utc);
            return utcDt - timeOffset;
        }

        public static readonly TimeZoneInfo ChinaTimeZone = FindTimeZoneInfoById("Asia/Shanghai");
        /// <summary>
        /// 将本地时间转换为中国时间
        /// </summary>
        /// <param name="localTime"></param>
        /// <param name="localTimezoneOffset"></param>
        /// <returns></returns>
        public static DateTime ConvertLocalToChina(this DateTime localTime, int localTimezoneOffset)
        {
            return ConvertTimeZone(localTime, localTimezoneOffset, ChinaTimeZone);
        }

        /// <summary>
        /// 将中国时间转换为用户本地时间
        /// </summary>
        /// <param name="chinaTime"></param>
        /// <param name="localTimezoneOffset"></param>
        /// <returns></returns>
        public static DateTime ConvertChinaToLocal(this DateTime chinaTime, int localTimezoneOffset)
        {
            return ConvertTimeZone(chinaTime, ChinaTimeZone, localTimezoneOffset);
        }
    }
}
