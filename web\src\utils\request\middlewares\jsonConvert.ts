// nodejs依赖
// import FormData = require('form-data');
// import { URLSearchParams } from "url";

import { isEmpty } from '../../base';
import { ContentTypeEnum, HttpContext } from '../http';

export default async (ctx: HttpContext, next: () => any) => {
  const req = ctx.requestOptions;
  const { body } = req;

  if (!(body instanceof FormData) && !(body instanceof URLSearchParams)) {
    let isJsonType: boolean = true;
    const headers = req.headers as any;
    const reqContextType: string | null = isEmpty(headers) ? null : headers?.['Content-Type'];
    if (reqContextType) {
      isJsonType = reqContextType.startsWith('application/json');
    } else {
      headers['Content-Type'] = ContentTypeEnum.Json;
    }

    if (isJsonType) {
      if (typeof body === 'object') {
        req.body = JSON.stringify(body);
      }
    }
  }

  // 下个中间件执行前
  await next();
  // 下个中间件执行后

  if (ctx.result || ctx.response?.bodyUsed) return;
  const resContextType: string = ctx.response?.headers.get('Content-Type') || '';
  if (ctx.response?.ok && resContextType?.startsWith('application/json')) {
    ctx.result = await ctx.response?.json();
  }
};
