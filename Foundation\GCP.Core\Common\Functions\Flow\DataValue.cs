﻿using GCP.Functions.Common.VisualFunction;

namespace GCP.Common
{
    public class FlowData
    {
        public string Id { get; set; }
        public string Key { get; set; }
        public string path { get; set; }
        public string pathDescription { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool? Required { get; set; }
        /// <summary>
        /// 是否自定义值
        /// </summary>
        public bool? IsCustomize { get; set; }
        public DataValue Value { get; set; }
        public List<FlowData> Children { get; set; }
    }

    public class DataValue
    {
        public string Type { get; set; }
        public string DataType { get; set; }
        public string TextValue { get; set; }
        public string VariableType { get; set; }
        public string VariableValue { get; set; }
        public string VariableName { get; set; }
        public string ScriptValue { get; set; }
        public string ScriptName { get; set; }
        /// <summary>
        /// 可视化函数步骤列表（JSON序列化字符串）
        /// </summary>
        public List<VisualFunctionStep> VisualSteps { get; set; }
        /// <summary>
        /// 可视化函数名称
        /// </summary>
        public string VisualName { get; set; }
    }
}
