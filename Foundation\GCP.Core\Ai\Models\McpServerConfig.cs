﻿using ModelContextProtocol.Client;
using ModelContextProtocol.Protocol.Transport;
using System.Text.Json.Serialization;

namespace GCP.Core.Ai.Models
{
    public class ServerConfig
    {
        [JsonPropertyName("command")]
        public string Command { get; set; }

        [JsonPropertyName("args")]
        public string Args { get; set; }
    }

    public class McpServerDictionary
    {
        public Dictionary<string, ServerConfig> Servers { get; set; } = new Dictionary<string, ServerConfig>();
    }

    public class MCPServerConfig
    {
        public string Name { get; set; }
        public string Command { get; set; }
        public string Args { get; set; }
        public string TransportType { get; set; } = TransportTypes.StdIo;
        internal IList<McpClientTool> Tools { get; set; }
    }
}
