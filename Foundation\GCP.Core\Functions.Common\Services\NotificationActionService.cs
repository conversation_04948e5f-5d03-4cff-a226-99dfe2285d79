using GCP.Common;
using GCP.Models;

namespace GCP.Functions.Common.Services
{
    [Function("notificationAction", "通知动作服务")]
    internal class NotificationActionService : BaseService
    {
        [Function("sendNotification", "发送通知")]
        public async Task<bool> SendNotification(string channelId, string title, string content)
        {
            if (string.IsNullOrEmpty(channelId))
            {
                throw new CustomException("通知通道ID不能为空");
            }

            if (string.IsNullOrEmpty(title))
            {
                throw new CustomException("通知标题不能为空");
            }

            if (string.IsNullOrEmpty(content))
            {
                throw new CustomException("通知内容不能为空");
            }

            var notificationService = ServiceLocator.GetScopedService<NotificationService>();

            var request = new NotificationRequest
            {
                Title = title,
                Content = content
            };

            return await notificationService.SendNotificationAsync(channelId, request);
        }

        [Function("sendNotificationWithException", "发送异常通知")]
        public async Task<bool> SendNotificationWithException(string channelId, string title, string message, string stackTrace = null)
        {
            if (string.IsNullOrEmpty(channelId))
            {
                throw new CustomException("通知通道ID不能为空");
            }

            if (string.IsNullOrEmpty(title))
            {
                throw new CustomException("通知标题不能为空");
            }

            if (string.IsNullOrEmpty(message))
            {
                throw new CustomException("异常消息不能为空");
            }

            var notificationService = ServiceLocator.GetScopedService<NotificationService>();

            var exception = new Exception(message);
            if (!string.IsNullOrEmpty(stackTrace))
            {
                // 创建一个包含堆栈跟踪的异常
                var exceptionWithStack = new Exception(message);
                // 注意：这里无法直接设置StackTrace，但可以在消息中包含堆栈信息
                exception = new Exception($"{message}\n\n堆栈跟踪:\n{stackTrace}");
            }

            var request = new NotificationRequest
            {
                Title = title,
                Content = message,
                Exception = exception
            };

            return await notificationService.SendNotificationAsync(channelId, request);
        }

        [Function("getAvailableChannels", "获取可用的通知通道")]
        public List<object> GetAvailableChannels()
        {
            using var db = this.GetDb();
            var channels = (from c in db.LcNotificationChannels
                           where c.State == 1 &&
                           c.SolutionId == this.SolutionId &&
                           c.ProjectId == this.ProjectId &&
                           c.IsEnabled == true
                           orderby c.ChannelName
                           select new
                           {
                               Id = c.Id,
                               Name = c.ChannelName,
                               Type = c.ChannelType,
                               Description = c.Description
                           }).ToList<object>();

            return channels;
        }
    }
}
