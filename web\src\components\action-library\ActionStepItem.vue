<template>
  <div 
    class="action-step-item" 
    :class="{ active }"
    @click="$emit('select', step)"
  >
    <div class="step-header">
      <div class="step-index">{{ index + 1 }}</div>
      <div class="step-info">
        <div class="step-name">{{ step.name || step.function }}</div>
        <div class="step-function">{{ step.function }}</div>
      </div>
      <div class="step-actions">
        <t-dropdown :options="actionOptions" @click="handleAction">
          <t-button size="small" variant="text" shape="square">
            <template #icon>
              <more-icon />
            </template>
          </t-button>
        </t-dropdown>
      </div>
    </div>
    <div v-if="step.description" class="step-description">
      {{ step.description }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { MoreIcon } from 'tdesign-icons-vue-next';

interface FlowStep {
  id: string;
  name?: string;
  function: string;
  description?: string;
  args?: any;
  result?: any[];
}

interface Props {
  step: FlowStep;
  index: number;
  active?: boolean;
}

const props = defineProps<Props>();

const emits = defineEmits<{
  select: [step: FlowStep];
  delete: [stepId: string];
  moveUp: [stepId: string];
  moveDown: [stepId: string];
}>();

const actionOptions = computed(() => [
  {
    content: '上移',
    value: 'move-up',
    disabled: props.index === 0
  },
  {
    content: '下移', 
    value: 'move-down'
  },
  {
    content: '删除',
    value: 'delete',
    theme: 'error'
  }
]);

const handleAction = (data: any) => {
  switch (data.value) {
    case 'move-up':
      emits('moveUp', props.step.id);
      break;
    case 'move-down':
      emits('moveDown', props.step.id);
      break;
    case 'delete':
      emits('delete', props.step.id);
      break;
  }
};
</script>

<style lang="less" scoped>
.action-step-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--td-brand-color-light);
    background: var(--td-bg-color-container-hover);
  }

  &.active {
    border-color: var(--td-brand-color);
    background: var(--td-brand-color-light);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .step-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .step-index {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: var(--td-brand-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      flex-shrink: 0;
    }

    .step-info {
      flex: 1;
      min-width: 0;

      .step-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-primary);
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .step-function {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .step-actions {
      flex-shrink: 0;
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }

  .step-description {
    margin-top: 8px;
    padding-left: 36px;
    font-size: 12px;
    color: var(--td-text-color-secondary);
    line-height: 1.4;
  }

  &:hover .step-actions {
    opacity: 1;
  }

  &.active {
    .step-index {
      background: var(--td-brand-color);
    }

    .step-actions {
      opacity: 1;
    }
  }
}
</style>
