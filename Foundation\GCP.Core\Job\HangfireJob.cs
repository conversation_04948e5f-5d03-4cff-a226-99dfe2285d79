﻿using Hangfire;
using Hangfire.Storage;
using GCP.Common;
using Serilog;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace GCP.Job
{
    class HangfireJob : IJob
    {
        static ConcurrentDictionary<string, JobInfo> dicMethod = new ConcurrentDictionary<string, JobInfo>();
        static RecurringJobOptions options = new RecurringJobOptions();

        static HangfireJob()
        {
            options.TimeZone = TimeZoneHelper.ChinaTimeZone;
        }

        public void AddOrUpdate(string id, Func<Task> methodCall, string cron, string name = "", string desc = "")
        {
            dicMethod.AddOrUpdate(id, new JobInfo
            {
                Id = id,
                Name = name,
                Method = methodCall
            }, (key, oldValue) =>
            {
                oldValue.Method = methodCall;
                oldValue.Name = name;
                return oldValue;
            });

            //cron = FormatCronExpression(cron);

            RecurringJob.AddOrUpdate(id, () => HangfireMethodCall(id), cron, options);
        }

        [AutomaticRetry(Attempts = 0)]
        public async Task HangfireMethodCall(string id)
        {
            JobInfo info;
            if (dicMethod.TryGetValue(id, out info))
            {
                if (!LicenseSevice.CheckLicense())
                {
                    Log.Warning("未激活, 禁止执行JOB {jobId} {jobName}", id, info.Name);
                    return;
                }

                Stopwatch sw = new Stopwatch();
                Log.Information("JOB {jobName} 开始执行", info.Name);
                try
                {
                    sw.Start();
                    await info.Method();
                }
                catch (CustomException)
                {
                    return;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "JOB {jobId} {jobName} 执行失败", id, info.Name);
                    return;
                }
                finally
                {
                    sw.Stop();
                    Log.Information("JOB {jobName} 执行完毕, 耗时:{time}", info.Name, sw.Elapsed.FormatString());
                }
            }
        }


        public List<string> GetAllJobIds()
        {
            //JobStorage.Current.JobExpirationTimeout = TimeSpan.FromDays(1);
            List<string> ids = new List<string>();
            using (var connection = JobStorage.Current.GetConnection())
            {
                foreach (var recurringJob in connection.GetRecurringJobs())
                {
                    ids.Add(recurringJob.Id);
                }
            }
            return ids;
        }

        public void RemoveIfExists(string id)
        {
            dicMethod.TryRemove(id, out _);
            RecurringJob.RemoveIfExists(id);
        }

        public void Trigger(string id)
        {
            RecurringJob.TriggerJob(id);
        }

        private string FormatCronExpression(string cron)
        {
            if (string.IsNullOrEmpty(cron))
            {
                throw new CustomException("Cron表达式不能为空");
            }

            // 如果cron表达式缺少秒, 补上0
            if (cron.Split(' ').Length == 5)
            {
                cron = "0 " + cron;
            }

            return cron;
        }
    }
}
