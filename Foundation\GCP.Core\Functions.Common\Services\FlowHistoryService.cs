﻿using GCP.Common;
using GCP.DataAccess;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("flowHistory", "流程历史实例服务")]
    internal class FlowHistoryService : BaseService
    {
        [Function("getAll", "获取流程实例清单")]
        public List<LcFhiProc> GetAll(string keyword = null, string triggerType = null, string functionId = null,
            DateTime? beginTime = null, DateTime? endTime = null, string runLog = null, bool? hasRunLog = null, int? duration = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetFlowDb();
            var query = from a in db.LcFhiProcs
                        where a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(triggerType) || a.TriggerType == triggerType) &&
                        (string.IsNullOrEmpty(functionId) || a.FunctionId == functionId) &&
                        (string.IsNullOrEmpty(runLog) || a.RunLog.Contains(runLog)) &&
                        (!beginTime.HasValue || a.BeginTime >= beginTime.Value) &&
                        (!endTime.HasValue || a.BeginTime <= endTime.Value) &&
                        (!duration.HasValue || a.Duration >= duration.Value) &&
                        (!hasRunLog.HasValue || !string.IsNullOrEmpty(a.RunLog))
                        select a;


            if (!string.IsNullOrEmpty(keyword))
            {
                if (string.IsNullOrEmpty(triggerType) || string.IsNullOrEmpty(functionId))
                {
                    throw new CustomException("步骤日志搜索时，必须同时指定触发类型和任务名称");
                }

                query = from a in query
                        from b in db.LcFhiDetails.InnerJoin(t => t.ProcId == a.Id)
                        where (string.IsNullOrEmpty(keyword) || (a.RunLog.Contains(keyword) || b.RunData.Contains(keyword)))
                        select a;
            }

            var data = query.OrderByDescending(t => t.BeginTime).Distinct().Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();

            return data;
        }

        [Function("getSteps", "获取流程实例步骤信息")]
        public List<LcFhiDetail> GetSteps(string procId)
        {
            using var db = this.GetFlowDb();
            var data = (from a in db.LcFhiDetails
                        where a.ProcId == procId
                        orderby a.SeqNo
                        select new LcFhiDetail
                        {
                            Id = a.Id,
                            ProcId = a.ProcId,
                            SeqNo = a.SeqNo,
                            FunctionId = a.FunctionId,
                            ActionId = a.ActionId,
                            StepName = a.StepName,
                            Status = a.Status,
                            BeginTime = a.BeginTime,
                            EndTime = a.EndTime,
                            Duration = a.Duration
                        }).ToList();
            return data;
        }

        [Function("getStepData", "获取流程实例步骤数据")]
        public string GetStepData(string procId, string stepId)
        {
            using var db = this.GetFlowDb();
            var data = (from a in db.LcFhiDetails
                        where a.ProcId == procId
                        && a.Id == stepId
                        select a.RunData).FirstOrDefault();
            return data;
        }

        [Function("cleanupFlowLogs", "清理流程日志")]
        [Job(JobId = "CleanupFlowLogs", JobCron = "0 0 1 1/1 * ?", JobName = "清理流程日志")]
        public async Task CleanupFlowLogs()
        {
            var dicService = new DataDictionaryService(this.Context, "0", "0");
            var logRetentionDaysInfo = dicService.GetByCode("SYS_PARAM", "FlowLogRetentionDays");
            int logRetentionDays = logRetentionDaysInfo == null ? 7 : logRetentionDaysInfo.DictValue.Parse(7);
            await using var db = this.GetDb();
            var jobs = await db.LcJobs.Where(t => t.LogRetentionDays != null && t.LogRetentionDays > 0).ToListAsync();

            await using var flowDb = this.GetFlowDb();
            var cleanupList = await flowDb.LcFhiProcs.ToListAsync();

            var ids = new List<string>();
            var now = flowDb.GetDbTime();
            var logTime = now.AddDays(-logRetentionDays);
            foreach (var item in cleanupList)
            {
                if (item.TriggerType == "JOB")
                {
                    var job = jobs.FirstOrDefault(t => t.FunctionId == item.FunctionId);
                    if (job != null && job.LogRetentionDays > 0)
                    {
                        var jobLogTime = now.AddDays(-job.LogRetentionDays.Value);
                        if (item.BeginTime < jobLogTime)
                        {
                            ids.Add(item.Id);
                            continue;
                        }
                    }
                }

                if (item.BeginTime < logTime)
                {
                    ids.Add(item.Id);
                }
            }

            await flowDb.BeginTransactionAsync();
            if (ids.Count > 0)
            {
                await flowDb.LcFhiProcs.Where(t => ids.Contains(t.Id)).DeleteAsync();
                await flowDb.LcFhiDetails.Where(t => ids.Contains(t.ProcId)).DeleteAsync();
            }

            // 标记1天前[运行中]的流程为[失败]
            var runLogTime = now.AddDays(-1);
            await flowDb.LcFruProcs.Where(t => t.Status == 0 && t.BeginTime < runLogTime).UpdateAsync(t => new LcFruProc { Status = -1 });

            await flowDb.CommitTransactionAsync();

        }

        [Function("calculateDailyStats", "计算每日统计数据")]
        [Job(JobId = "CalculateDailyFlowStats", JobCron = "0 0 1 * * ?", JobName = "计算每日流程统计数据")]
        public async Task CalculateDailyStats(DateTime? beginTime)
        {
            var now = DateTime.Now.Date;
            if (!beginTime.HasValue)
            {
                beginTime = now.Date.AddDays(-1);
            }

            var days = (now - beginTime.Value).Days;
            for (int i = 1; i <= days; i++)
            {
                var date = beginTime.Value.AddDays(i - 1);
                await CalculateStats(date, date.AddDays(1), 1);
            }
        }

        [Function("calculateMinuteStats", "计算每10分钟统计数据")]
        [Job(JobId = "CalculateMinuteFlowStats", JobCron = "0 */10 * * * ?", JobName = "计算每10分钟流程统计数据")]
        public async Task CalculateMinuteStats()
        {
            var now = DateTime.Now;
            var weekStart = now.Date.AddDays(-(int)now.DayOfWeek + 1); // 本周一
            await using var db = this.GetFlowDb();

            // 1. 查询本周LcFhiProcs最早EndTime
            var minProcTime = db.LcFhiProcs
                .Where(p => p.EndTime >= weekStart)
                .OrderBy(p => p.EndTime)
                .Select(p => p.EndTime)
                .FirstOrDefault();

            // 2. 查询本周LcFhiStat最早StatDate（10分钟统计）
            var minStatTimes = db.LcFhiStats
                .Where(p => p.StatDate >= weekStart && p.ArchivePeriod == 2)
                .OrderBy(p => p.StatDate)
                .Select(p => p.StatDate)
                .ToList();

            DateTime? minStatTime = minStatTimes.Count == 0 ? null : minStatTimes.First();

            if (minProcTime == null)
                return; // 本周无数据

            // 3. 取最小时间
            var minTime = new DateTime(minProcTime.Value.Year, minProcTime.Value.Month, minProcTime.Value.Day, minProcTime.Value.Hour, minProcTime.Value.Minute, 0, 0);
            if (minStatTime != null)
                minTime = minTime < minStatTime ? minTime : minStatTimes.Last();

            // 4. 补全到当前10分钟
            var start = minTime;
            var end = now.Date.AddHours(now.Hour).AddMinutes(now.Minute / 10 * 10);

            for (var t = start; t < end; t = t.AddMinutes(10))
            {
                await CalculateStats(t, t.AddMinutes(10), 2);
            }
        }

        /// <summary>
        /// 计算统计数据
        /// </summary>
        /// <param name="beginTime"> 开始时间 </param>
        /// <param name="endTime"> 结束时间 </param>
        /// <param name="archivePeriod"> 统计周期：1:日统计；2:10分钟统计 </param>
        /// <returns></returns>
        private async Task CalculateStats(DateTime beginTime, DateTime endTime, short archivePeriod)
        {
            await using var db = this.GetFlowDb();

            // 计算昨天的成功统计数据 (LcFhiProcs表)
            var successStats = db.LcFhiProcs
                .Where(p => p.EndTime >= beginTime && p.EndTime < endTime)
                .GroupBy(p => new { p.SolutionId, p.ProjectId, TriggerType = p.TriggerType ?? "UNKNOWN", p.FunctionId, p.Version })
                .Select(g => new LcFhiStat
                {
                    SolutionId = g.Key.SolutionId,
                    ProjectId = g.Key.ProjectId,
                    TriggerType = g.Key.TriggerType,
                    FunctionId = g.Key.FunctionId,
                    Version = g.Key.Version,
                    TotalCount = g.Count(),
                    SuccessCount = g.Count(p => p.Status == 1),
                    FailCount = g.Count(p => p.Status != 1),
                    TotalDuration = g.Sum(p => p.Duration ?? 0),
                    AvgDuration = (int)g.Average(p => p.Duration ?? 0),
                    TotalTraffic = g.Sum(p => p.TotalTraffic ?? 0),
                })
                .ToList();

            // 计算昨天的失败统计数据 (LcFruProcs表)
            var failStats = db.LcFruProcs
                .Where(p => p.BeginTime >= beginTime && p.BeginTime < endTime)
                .GroupBy(p => new { p.SolutionId, p.ProjectId, TriggerType = p.TriggerType ?? "UNKNOWN", p.FunctionId, p.Version })
                .Select(g => new LcFhiStat
                {
                    SolutionId = g.Key.SolutionId,
                    ProjectId = g.Key.ProjectId,
                    TriggerType = g.Key.TriggerType,
                    FunctionId = g.Key.FunctionId,
                    Version = g.Key.Version,
                    TotalCount = g.Count(),
                    SuccessCount = g.Count(p => p.Status == 1),
                    FailCount = g.Count(p => p.Status != 1),
                    TotalTraffic = g.Sum(p => p.TotalTraffic ?? 0),
                })
                .ToList();

            // 合并统计结果
            var combinedStats = successStats;
            foreach (var item in failStats)
            {
                var match = combinedStats.FirstOrDefault(t => t.SolutionId == item.SolutionId && t.ProjectId == item.ProjectId && t.TriggerType == item.TriggerType && t.FunctionId == item.FunctionId && t.Version == item.Version);
                if (match != null)
                {
                    match.TotalCount += item.TotalCount;
                    match.SuccessCount += item.SuccessCount;
                    match.FailCount += item.FailCount;
                    match.TotalTraffic += item.TotalTraffic;
                }
                else
                {
                    combinedStats.Add(item);
                }
            }

            if(combinedStats.Count == 0)
            {
                return;
            }

            // 保存统计数据
            await db.BeginTransactionAsync();

            await db.LcFhiStats.DeleteAsync(t => t.StatDate >= beginTime && t.StatDate < endTime && t.ArchivePeriod == archivePeriod);

            foreach (var item in combinedStats)
            {
                item.ArchivePeriod = archivePeriod;
                item.StatDate = beginTime;

                item.Id = TUID.NewTUID().ToString();
                item.Creator = "sys";
                item.TimeCreate = DateTime.Now;

                db.Insert(item);
            }

            await db.CommitTransactionAsync();
        }
    }
}
