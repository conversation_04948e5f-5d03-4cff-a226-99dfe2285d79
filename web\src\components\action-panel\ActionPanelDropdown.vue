<template>
  <t-popup
    :visible="visible"
    placement="bottom-left"
    trigger="click"
    :overlay-style="{ padding: 0 }"
    @visible-change="onVisibleChange"
  >
    <div>
      <slot />
    </div>
    <template #content>
      <div class="action-panel-dropdown" @click.stop>
        <!-- 搜索框 -->
        <div class="search-container">
          <t-input
            ref="searchInputRef"
            v-model="searchKeyword"
            placeholder="搜索动作(支持拼音)..."
            clearable
            @input="onSearch"
            @keydown="onKeyDown"
            @enter="onEnterKey"
          >
            <template #prefix-icon>
              <t-icon name="search" />
            </template>
          </t-input>
        </div>

        <!-- 分组标签 -->
        <div class="group-tabs">
          <div
            v-for="group in groups"
            :key="group.key"
            :class="['group-tab', { active: activeGroup === group.key }]"
            @click="setActiveGroup(group.key)"
          >
            {{ group.name }}
          </div>
        </div>

        <!-- 动作列表 -->
        <div ref="actionListRef" class="action-list-container" @scroll="onScroll">
          <div v-for="group in displayGroups" :key="group.key" class="action-group">
            <div class="group-title sticky-header">{{ group.name }}</div>
            <div class="action-items">
              <div
                v-for="(action, actionIndex) in group.actions"
                :key="action.value"
                :class="[
                  'action-item',
                  {
                    'action-item-selected': getGlobalActionIndex(group, actionIndex) === selectedIndex,
                  },
                ]"
                @click="onSelectAction(action)"
              >
                <t-icon :name="action.icon" class="action-icon" />
                <span class="action-name">{{ action.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </t-popup>
</template>

<script lang="ts">
export default {
  name: 'ActionPanelDropdown',
};
</script>

<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';
import PinyinMatch from 'pinyin-match';

import { getComponentConfig, getOptions } from './actions';
import { useActionFlowStore } from './store/index';

interface ActionItem {
  name: string;
  value: string;
  icon: string;
  category: string;
  categoryName: string;
}

interface ActionGroup {
  key: string;
  name: string;
  actions: ActionItem[];
}

const emit = defineEmits<{
  addAction: [action: { value: string }];
}>();

const actionFlowStore = useActionFlowStore();
const visible = ref(false);
const searchKeyword = ref('');
const activeGroup = ref('all');
const actionListRef = ref<HTMLElement>();
const searchInputRef = ref();
const selectedIndex = ref(-1);

// 分组映射
const categoryMap = {
  '1_DataBase': '数据库',
  '2_Network': '网络',
  '3_Variable': '变量',
  '4_Control': '控制',
  '5_Log': '日志',
  '6_File': '文件',
  '9_Other': '其他',
};

// 获取所有动作
const allActions = computed(() => {
  const options = getOptions(actionFlowStore.flowType);
  return options.map((option) => {
    const config = getComponentConfig(option.value);
    const category = option.value.split('/')[1];
    return {
      name: config.name,
      value: option.value,
      icon: config.icon,
      category,
      categoryName: categoryMap[category] || category,
    };
  });
});

// 分组数据
const groups = computed(() => {
  const groupMap = new Map<string, ActionItem[]>();

  allActions.value.forEach((action) => {
    if (!groupMap.has(action.category)) {
      groupMap.set(action.category, []);
    }
    groupMap.get(action.category)!.push(action);
  });

  const result: ActionGroup[] = [
    {
      key: 'all',
      name: '全部',
      actions: allActions.value,
    },
  ];

  // 按分组顺序添加
  const sortedCategories = Array.from(groupMap.keys()).sort();
  sortedCategories.forEach((category) => {
    result.push({
      key: category,
      name: categoryMap[category] || category,
      actions: groupMap.get(category)!,
    });
  });

  return result;
});

// 显示的分组（搜索时过滤，切换分组时显示对应分组）
const displayGroups = computed(() => {
  if (searchKeyword.value) {
    // 搜索模式下显示所有匹配的动作
    const keyword = searchKeyword.value.trim();
    const filteredActions = allActions.value.filter((action) => {
      // 支持普通文本搜索
      if (action.name.toLowerCase().includes(keyword.toLowerCase())) {
        return true;
      }
      // 支持拼音搜索
      const pinyinMatch = PinyinMatch.match(action.name, keyword);
      return pinyinMatch !== false;
    });

    if (filteredActions.length === 0) return [];

    return [
      {
        key: 'search',
        name: '搜索结果',
        actions: filteredActions,
      },
    ];
  }

  // 非搜索模式下根据当前选中的分组显示
  if (activeGroup.value === 'all') {
    // 显示所有分组
    return groups.value.slice(1); // 排除"全部"分组
  } else {
    // 只显示当前选中的分组
    const currentGroup = groups.value.find((group) => group.key === activeGroup.value);
    return currentGroup ? [currentGroup] : [];
  }
});

// 获取当前显示的所有动作（用于键盘导航）
const displayActions = computed(() => {
  const actions: ActionItem[] = [];
  displayGroups.value.forEach((group) => {
    actions.push(...group.actions);
  });
  return actions;
});

// 获取动作在全局列表中的索引
const getGlobalActionIndex = (group: ActionGroup, actionIndex: number) => {
  let globalIndex = 0;
  for (const g of displayGroups.value) {
    if (g.key === group.key) {
      return globalIndex + actionIndex;
    }
    globalIndex += g.actions.length;
  }
  return -1;
};

const onVisibleChange = (val: boolean) => {
  visible.value = val;
  if (val) {
    searchKeyword.value = '';
    activeGroup.value = 'all';
    selectedIndex.value = -1;
    // 自动获取焦点，使用多次尝试确保成功
    focusSearchInput();
  }
};

// 获取搜索框焦点的方法
const focusSearchInput = () => {
  nextTick(() => {
    // 第一次尝试
    setTimeout(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus();
        // 如果第一次失败，再尝试一次
        setTimeout(() => {
          if (document.activeElement !== searchInputRef.value?.$el?.querySelector('input')) {
            searchInputRef.value?.focus();
          }
        }, 50);
      }
    }, 150);
  });
};

const onSearch = () => {
  // 搜索时自动切换到全部分组
  if (searchKeyword.value) {
    activeGroup.value = 'all';
  }
  // 重置选中索引
  selectedIndex.value = -1;
};

const setActiveGroup = (groupKey: string) => {
  activeGroup.value = groupKey;
  searchKeyword.value = '';
  selectedIndex.value = -1;

  // 滚动到顶部
  nextTick(() => {
    if (actionListRef.value) {
      actionListRef.value.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
    // 重新获取焦点（切换类型时直接获取即可）
    setTimeout(() => {
      searchInputRef.value?.focus();
    }, 50);
  });
};

const onSelectAction = (action: ActionItem) => {
  emit('addAction', { value: action.value });
  visible.value = false;
};

// 键盘事件处理
const onKeyDown = (_value: any, context: { e: KeyboardEvent }) => {
  const event = context.e;
  const actions = displayActions.value;
  if (actions.length === 0) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      selectedIndex.value = Math.min(selectedIndex.value + 1, actions.length - 1);
      scrollToSelectedItem();
      break;
    case 'ArrowUp':
      event.preventDefault();
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1);
      scrollToSelectedItem();
      break;
    case 'Escape':
      event.preventDefault();
      visible.value = false;
      break;
  }
};

// 单独处理 Enter 键事件
const onEnterKey = () => {
  const actions = displayActions.value;
  if (actions.length === 0) return;

  // 如果有选中的项目，选择它
  if (selectedIndex.value >= 0 && selectedIndex.value < actions.length) {
    onSelectAction(actions[selectedIndex.value]);
  } else if (actions.length > 0) {
    // 如果没有选中项目但有可用动作，选择第一个
    onSelectAction(actions[0]);
  }
};

// 滚动到选中的项目
const scrollToSelectedItem = () => {
  if (selectedIndex.value >= 0 && actionListRef.value) {
    const actionItems = actionListRef.value.querySelectorAll('.action-item');
    const selectedItem = actionItems[selectedIndex.value];
    if (selectedItem) {
      selectedItem.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }
  }
};

// 简化的滚动事件处理（不再自动切换分组）
const onScroll = () => {
  // 保留滚动事件处理器，但不执行任何自动切换逻辑
};
</script>

<style lang="less" scoped>
.action-panel-dropdown {
  width: 400px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  background: var(--td-bg-color-container);
  overflow: hidden;
}

.search-container {
  padding: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
  background: var(--td-bg-color-container);
}

.group-tabs {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid var(--td-border-level-2-color);
  background-color: var(--td-bg-color-container);
  gap: 4px;
  min-height: 40px;
  flex-wrap: wrap;
}

.group-tab {
  padding: 8px 12px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  font-size: 12px;
  color: var(--td-text-color-secondary);
  background-color: var(--td-bg-color-component);
  transition: all 0.2s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin-bottom: 4px;

  &:hover {
    background-color: var(--td-brand-color-light);
    color: var(--td-brand-color);
  }

  &.active {
    background-color: var(--td-brand-color);
    color: var(--td-text-color-anti);
  }
}

.action-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 0 8px 0;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--td-border-level-2-color);
    border-radius: 3px;

    &:hover {
      background: var(--td-text-color-placeholder);
    }
  }
}

.action-group {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 8px;
  }
}

.group-title {
  padding: 8px 16px 4px;
  font-size: 12px;
  font-weight: 500;
  color: var(--td-text-color-secondary);
  border-bottom: 1px solid var(--td-border-level-2-color);
  margin-bottom: 4px;
  background: var(--td-bg-color-container);
}

/* 粘性分组头 */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--td-bg-color-container);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-items {
  padding: 0 8px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--td-brand-color-light);
  }

  &.action-item-selected {
    background-color: var(--td-brand-color);
    color: var(--td-text-color-anti);

    .action-icon {
      color: var(--td-text-color-anti);
    }

    .action-name {
      color: var(--td-text-color-anti);
    }
  }
}

.action-icon {
  margin-right: 8px;
  color: var(--td-text-color-secondary);
}

.action-name {
  font-size: 14px;
  color: var(--td-text-color-primary);
}
</style>
