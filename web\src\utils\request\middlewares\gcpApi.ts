import { isEmpty } from 'lodash-es';

import { useUserStore } from '@/store';

import CustomError from '../../exception/CustomError';
import { HttpContext } from '../http';

interface ResponseData {
  /**
   * 返回编码
   */
  code: number;
  /**
   * 返回消息
   */
  message?: string;
  /**
   * 返回数据
   */
  data: any;
}

export default async (ctx: HttpContext, next: () => any) => {
  const userStore = useUserStore();
  const req = ctx.requestOptions;
  const headers = req.headers as any;
  const { token, projectId, solutionId } = userStore;
  if (token && isEmpty(headers.Authorization)) {
    headers.Authorization = `Bearer ${token}`;
  }
  headers.sid = solutionId;
  headers.pid = projectId;
  // headers['Accept-Language'] = core.getLanguage();
  headers['X-TZ-Offset'] = new Date().getTimezoneOffset().toString();

  try {
    await next();
  } catch (error) {
    let message = error.message || 'unknown';
    if (message === 'Failed to fetch') {
      message = `${ctx.baseURL} 无法连接，请检查服务器或者网络连接`;
    }
    throw new CustomError(message, -1, -1);
  }

  if (!ctx.response?.ok) {
    const error = new Error('An error occurred while fetching the data.');
    error.message = `${ctx.response?.status}|${(await ctx.response?.text()) || ''}`;
    throw error;
  }

  if (ctx.response?.status !== 200) {
    throw new CustomError(JSON.stringify(ctx.result), 500, ctx.response.status);
  }

  if (!ctx.requestOptions.returnNativeResponse && ctx.result) {
    const { result }: { result: ResponseData } = ctx;
    if (result.code === 0 || result.code === 200) {
      ctx.result = result.data;
    } else {
      throw new CustomError(result.message || 'unknown', result.code, ctx.response.status, JSON.stringify(result));
    }
  }
};
