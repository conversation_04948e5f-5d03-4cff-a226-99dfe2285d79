﻿using Microsoft.Extensions.DependencyInjection;
using FluentMigrator.Runner;
using FluentMigrator.Runner.Initialization;
using FluentMigrator.Runner.Logging;
using FluentMigrator.Runner.Processors;
using System.Reflection;
using Microsoft.Extensions.Logging;
using GCP.Core.DataAccess.Migrations.DuckDb;

namespace GCP.DataAccess.Migrations
{
    class MigrationsService
    {
        public MigrationsService() { }

        public MigrationsService(string dbProvider, string connectionString)
        {
            ProcessorType = dbProvider;
            ConnectionString = connectionString;
        }

        public string ProcessorType { get; set; } = "";
        public string ConnectionString { get; set; } = "";
        public string TargetAssembly { get; set; } = "";
        public string Task { get; set; } = "migrate";
        public long Version { get; set; } = 0;
        public bool Verbose { get; set; } = true;
        public List<Assembly> Assemblies { get; private set; } = new List<Assembly>();
        public List<string> Tags { get; private set; } = new List<string>();

        private IServiceCollection CreateCoreServices()
        {
            var services = new ServiceCollection()
                .AddFluentMigratorCore()
                .ConfigureRunner(
                    builder => builder
                        //.AddDb2()
                        //.AddDb2ISeries()
                        //.AddDotConnectOracle()
                        //.AddDotConnectOracle12C()
                        //.AddFirebird()
                        //.AddHana()
                        .AddMySql5()
                        .AddMySql8()
                        //.AddOracle()
                        //.AddOracle12C()
                        .AddOracleManaged()
                        .AddOracle12CManaged()
                        .AddPostgres()
                        //.AddPostgres92()
                        //.AddRedshift()
                        //.AddSqlAnywhere()
                        .AddDuckDb()
                        .AddSQLite()
                        .AddSqlServer()
                        //.AddSqlServer2000()
                        //.AddSqlServer2005()
                        //.AddSqlServer2008()
                        //.AddSqlServer2012()
                        //.AddSqlServer2014()
                        //.AddSqlServer2016()
                        //.AddSqlServerCe()
                        );
            return services;
        }

        private void ExecuteMigrations()
        {
            var services = CreateCoreServices()
                .Configure<FluentMigratorLoggerOptions>(
                    opt =>
                    {
                        opt.ShowElapsedTime = Verbose;
                        opt.ShowSql = Verbose;
                    })
                .Configure<SelectingProcessorAccessorOptions>(opt => opt.ProcessorId = ProcessorType)
                .Configure<RunnerOptions>(
                    opt =>
                    {
                        opt.Task = Task;
                        opt.Version = Version;
                        opt.Tags = Tags.ToArray();
                        //opt.TransactionPerSession = true;
                    })
                .Configure<ProcessorOptions>(
                    opt =>
                    {
                        opt.ConnectionString = ConnectionString;
                    });
            
            services.AddSingleton<ILoggerProvider, FluentMigratorConsoleLoggerProvider>();

            if (!string.IsNullOrEmpty(TargetAssembly))
            {
                services.Configure<AssemblySourceOptions>(opt => opt.AssemblyNames = new[] { TargetAssembly });
            }

            if (Assemblies.Count > 0)
            {
                services.ConfigureRunner(rb => rb.ScanIn(Assemblies.ToArray()).For.Migrations());
            }

            using (var serviceProvider = services.BuildServiceProvider(validateScopes: false))
            {
                var executor = serviceProvider.GetRequiredService<TaskExecutor>();
                executor.Execute();

                //var runner = (MigrationRunner)serviceProvider.GetRequiredService<IMigrationRunner>();
                //runner.MigrateDown(0, false);
            }
        }

        /// <summary>
        /// 迁移到最新版本
        /// </summary>
        /// <param name="assemblies"></param>
        public void Run(params Assembly[] assemblies)
        {
            RunByVersion(0, assemblies);
        }

        /// <summary>
        /// 迁移到指定版本
        /// </summary>
        /// <param name="version"></param>
        /// <param name="assemblies"></param>
        public void RunByVersion(long version, params Assembly[] assemblies)
        {
            Task = "migrate:up";
            Version = version;
            Assemblies = assemblies.ToList();
            ExecuteMigrations();
        }

        /// <summary>
        /// 回滚到上个版本
        /// </summary>
        /// <param name="assemblies"></param>
        public void RunRollback(params Assembly[] assemblies)
        {
            Task = "rollback";
            Assemblies = assemblies.ToList();
            ExecuteMigrations();
        }

        /// <summary>
        /// 回滚到指定版本
        /// </summary>
        /// <param name="version"></param>
        /// <param name="assemblies"></param>
        public void RunRollbackByVersion(long version, params Assembly[] assemblies)
        {
            Task = "rollback:toversion";
            Version = version;
            Assemblies = assemblies.ToList();
            ExecuteMigrations();
        }
    }
}