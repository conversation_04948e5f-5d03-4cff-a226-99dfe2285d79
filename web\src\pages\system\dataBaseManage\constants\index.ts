/**
 * 数据库提供者类型
 */
export const DataProviders = [
  {
    label: 'MySql',
    value: 'MySql',
  },
  {
    label: 'Oracle',
    value: 'Oracle',
  },
  {
    label: 'SQL Server',
    value: 'SqlServer',
  },
  {
    label: 'PostgreSQL',
    value: 'PostgreSQL',
  },
] as const;

export type DataProviderType = (typeof DataProviders)[number]['value'];

/**
 * SQL Server身份验证类型
 */
export const SqlServerAuthTypes = [
  {
    label: 'SQL Server验证',
    value: 1,
  },
  {
    label: 'Windows验证',
    value: 2,
  },
] as const;

/**
 * Oracle连接类型
 */
export const OracleConnectTypes = [
  {
    label: '服务名',
    value: 1,
  },
  {
    label: 'SID',
    value: 2,
  },
] as const;

/**
 * 默认端口映射
 */
export const DefaultPorts: Record<DataProviderType, number> = {
  MySql: 3306,
  Oracle: 1521,
  SqlServer: 1433,
  PostgreSQL: 5432,
};
