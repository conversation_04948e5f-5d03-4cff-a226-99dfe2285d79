import { core } from '../core';

class CustomError extends Error {
  code: number = -1;

  status: string | number = '';

  type: string = '';

  description: string = '';

  constructor(message: string, code: number, status: string | number, description: string = '') {
    super();
    this.message = message;
    this.name = 'CustomError';
    this.code = code;
    this.status = status;
    this.type = 'error';
    this.description = description;
    core.ipc.targets.get('_self')?.send('custom_error', this);
  }
}

export default CustomError;
