﻿using System.Data;

namespace GCP.DataAccess
{
    /// <summary>
    /// DbContext Extension（初级扩展）
    /// </summary>
    public static class DbParametersExtension
    {
        public static DbParameters CreateParameters(this IDbConnection connection, object parms = null)
        {
            return new DbParameters(connection, parms);
        }

        public static DbParameters CreateParameters(this IDbContext dbContext, object parms = null)
        {
            return new DbParameters(dbContext, parms);
        }
    }
}
