﻿using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("api", "API服务")]
    class ApiService : BaseService
    {
        [Function("getAll", "获取api清单")]
        public List<LcApi> GetAll(short? apiType = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcApis
                        where a.SolutionId == this.SolutionId
                        && a.ProjectId == this.ProjectId
                        && a.State == 1
                        && (apiType == null || a.ApiType == apiType)
                        select a).ToList();
            return data;
        }

        [Function("getListByFuncId", "根据函数id获取api清单")]
        public List<LcApi> GetListByFuncId(string funcId)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcApis
                        where (string.IsNullOrEmpty(funcId) || a.FunctionId == funcId)
                        select a).ToList();
            return data;
        }

        [Function("getApiById", "根据id获取api")]
        public LcApi GetApiById(string id)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcApis
                        where a.Id == id && a.State == 1
                        select a).FirstOrDefault();
            return data;
        }

        [Function("add", "新增API")]
        public void AddJob(LcApi api)
        {
            api.SolutionId = this.SolutionId;
            api.ProjectId = this.ProjectId;

            using var db = this.GetDb();
            var existedApi = db.LcApis
                .FirstOrDefault(t => t.SolutionId == api.SolutionId && t.ProjectId == api.ProjectId && t.ApiType == api.ApiType && t.HttpUrl == api.HttpUrl && t.RequestType == api.RequestType);

            db.BeginTransaction();
            if (existedApi != null)
            {
                throw new CustomException($"存在相同的Url地址配置[{existedApi.ApiName}]，请检查");
                //db.LcApis.Where(t => t.Id == existedApi.Id).Delete();
            }

            if(api.ApiType != 2)
            {
                api.FunctionId = AddFunction(api.ApiName, api.Description, db);
            }

            this.InsertData(api, db);
            db.CommitTransaction();
        }

        private string AddFunction(string functionName, string description, GcpDb db = null)
        {

            LcFunction function = new()
            {
                SolutionId = this.SolutionId,
                ProjectId = this.ProjectId,
                FunctionName = functionName,
                FunctionType = "FLOW",
                Description = description,
                UseVersion = 1
            };
            return this.InsertData(function);
        }

        [Function("update", "更新api")]
        public bool Update(LcApi api)
        {
            using var db = this.GetDb();
            var data = db.LcApis.FirstOrDefault(t => t.Id == api.Id);
            if (data == null)
            {
                return false;
            }
            data.State = api.State;
            data.ApiName = api.ApiName;
            data.ApiType = api.ApiType;
            data.HttpUrl = api.HttpUrl;
            data.RequestType = api.RequestType;
            data.QueryParameters = api.QueryParameters;
            data.Headers = api.Headers;
            data.Body = api.Body;
            data.ClusterId = api.ClusterId;
            data.Hosts = api.Hosts;
            data.Description = api.Description;
            data.Response = api.Response;
            data.BaseUrl = api.BaseUrl;
            data.ResponseId = api.ResponseId;

            db.BeginTransaction();
            if (api.ApiType != 2 && string.IsNullOrEmpty(data.FunctionId))
            {
                data.FunctionId = AddFunction(api.ApiName, api.Description, db);
            }
            else if (data.ApiName != api.ApiName || data.Description != api.Description)
            {
                var updateFunction = db.LcFunctions
                    .Where(t => t.Id == data.FunctionId)
                    .Set(t => t.FunctionName, api.ApiName)
                    .Set(t => t.Description, api.Description);

                this.UpdateData(updateFunction);
            }

            this.UpdateData(data, db);
            db.CommitTransaction();
            
            this.Context.Current.Target = data.FunctionId;
            return true;
        }

        [Function("delete", "删除Api")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            var data = db.LcApis.FirstOrDefault(a => a.Id == id);
            if (data == null)
            {
                return;
            }
            data.State = 0;
            this.UpdateData(data, db);
            
            if (!string.IsNullOrEmpty(data.FunctionId))
            {
                this.UpdateData(db.LcFunctions.Where(t => t.Id == data.FunctionId).Set(t => t.State, (short)0));
            }
        }

        [Function("getFunctionNames", "获取API函数名称列表")]
        public List<OptionVO> GetFunctionNames()
        {
            using var db = this.GetDb();
            return (from a in db.LcApis
                            where a.SolutionId == this.SolutionId
                            && a.ProjectId == this.ProjectId
                            && a.State == 1
                            && a.ApiType != 2
                            && !string.IsNullOrEmpty(a.FunctionId)
                            select new OptionVO {
                                Id = a.Id,
                                Value = a.FunctionId,
                                Label = a.ApiName,
                                Description = a.Description
                            }).ToList();
        }

        [Function("getResponseList", "获取API响应体列表")]
        public List<LcApiResponse> GetResponseList()
        {
            using var db = this.GetDb();
            return (from r in db.LcApiResponses
                    where r.SolutionId == this.SolutionId
                    && r.ProjectId == this.ProjectId
                    && r.State == 1
                    orderby r.TimeCreate descending
                    select r).ToList();
        }

        [Function("getResponseById", "根据ID获取API响应体")]
        public LcApiResponse GetResponseById(string id)
        {
            using var db = this.GetDb();
            return db.LcApiResponses.FirstOrDefault(r => r.Id == id && r.State == 1);
        }

        [Function("saveResponse", "保存API响应体")]
        public void SaveResponse(LcApiResponse response)
        {
            response.SolutionId = this.SolutionId;
            response.ProjectId = this.ProjectId;

            using var db = this.GetDb();

            if (string.IsNullOrEmpty(response.Id))
            {
                // 新增
                response.Id = TUID.NewTUID().ToString();
                response.State = 1;
                this.InsertData(response, db);
            }
            else
            {
                // 更新
                var existingResponse = db.LcApiResponses.FirstOrDefault(r => r.Id == response.Id);
                if (existingResponse != null)
                {
                    existingResponse.ResponseCode = response.ResponseCode;
                    existingResponse.ResponseType = response.ResponseType;
                    existingResponse.ResponseName = response.ResponseName;
                    existingResponse.Description = response.Description;
                    existingResponse.ResponseData = response.ResponseData;
                    existingResponse.SuccessFlag = response.SuccessFlag;
                    existingResponse.ErrorCode = response.ErrorCode;
                    existingResponse.ErrorMessage = response.ErrorMessage;
                    existingResponse.Result = response.Result;
                    this.UpdateData(existingResponse, db);
                }
            }
        }

        [Function("deleteResponse", "删除API响应体")]
        public void DeleteResponse(string id)
        {
            using var db = this.GetDb();
            var response = db.LcApiResponses.FirstOrDefault(r => r.Id == id);
            if (response != null)
            {
                response.State = 0;
                this.UpdateData(response, db);
            }
        }

        [Function("validateApiResponse", "验证API响应是否成功")]
        public ApiValidationResult ValidateApiResponse(string responseId, object responseData)
        {
            using var db = this.GetDb();
            var responseConfig = db.LcApiResponses.FirstOrDefault(r => r.Id == responseId && r.State == 1);

            if (responseConfig == null)
            {
                return new ApiValidationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "未找到响应体配置"
                };
            }

            try
            {
                var responseJson = JsonHelper.Serialize(responseData);
                var responseObj = JsonHelper.Deserialize<Dictionary<string, object>>(responseJson);

                // 验证成功标识
                if (!string.IsNullOrEmpty(responseConfig.SuccessFlag))
                {
                    var successConfig = JsonHelper.Deserialize<Dictionary<string, object>>(responseConfig.SuccessFlag);
                    foreach (var config in successConfig)
                    {
                        if (responseObj.ContainsKey(config.Key))
                        {
                            var actualValue = responseObj[config.Key]?.ToString();
                            var expectedValue = config.Value?.ToString();
                            if (actualValue != expectedValue)
                            {
                                return new ApiValidationResult
                                {
                                    IsSuccess = false,
                                    ErrorMessage = GetErrorMessage(responseObj, responseConfig.ErrorMessage),
                                    Data = GetResultData(responseObj, responseConfig.Result)
                                };
                            }
                        }
                        else
                        {
                            return new ApiValidationResult
                            {
                                IsSuccess = false,
                                ErrorMessage = $"响应中缺少成功标识字段: {config.Key}"
                            };
                        }
                    }
                }

                return new ApiValidationResult
                {
                    IsSuccess = true,
                    Data = GetResultData(responseObj, responseConfig.Result)
                };
            }
            catch (Exception ex)
            {
                return new ApiValidationResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"验证响应时发生错误: {ex.Message}"
                };
            }
        }

        private string GetErrorMessage(Dictionary<string, object> responseObj, string errorMessageConfig)
        {
            if (string.IsNullOrEmpty(errorMessageConfig))
                return "API调用失败";

            try
            {
                var errorConfig = JsonHelper.Deserialize<Dictionary<string, string>>(errorMessageConfig);
                foreach (var config in errorConfig)
                {
                    if (responseObj.ContainsKey(config.Value))
                    {
                        return responseObj[config.Value]?.ToString() ?? "未知错误";
                    }
                }
            }
            catch
            {
                // 如果配置格式错误，直接作为字段名使用
                if (responseObj.ContainsKey(errorMessageConfig))
                {
                    return responseObj[errorMessageConfig]?.ToString() ?? "未知错误";
                }
            }

            return "API调用失败";
        }

        private object GetResultData(Dictionary<string, object> responseObj, string resultConfig)
        {
            if (string.IsNullOrEmpty(resultConfig))
                return responseObj;

            try
            {
                var dataConfig = JsonHelper.Deserialize<Dictionary<string, string>>(resultConfig);
                foreach (var config in dataConfig)
                {
                    if (responseObj.ContainsKey(config.Value))
                    {
                        return responseObj[config.Value];
                    }
                }
            }
            catch
            {
                // 如果配置格式错误，直接作为字段名使用
                if (responseObj.ContainsKey(resultConfig))
                {
                    return responseObj[resultConfig];
                }
            }

            return responseObj;
        }
    }
}
