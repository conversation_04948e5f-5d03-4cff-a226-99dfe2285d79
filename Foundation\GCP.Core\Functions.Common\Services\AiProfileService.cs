using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// AI对话配置服务
    /// </summary>
    [Function("aiProfile", "AI对话配置服务")]
    internal class AiProfileService : BaseService
    {
        private readonly AiModelConfigService _modelConfigService;

        public AiProfileService()
        {
            _modelConfigService = new AiModelConfigService();
        }

        [Function("createChatProfile", "创建对话配置文件")]
        public ChatProfile CreateChatProfile(ChatProfile profile)
        {
            using var db = this.GetDb();
            
            var entity = new LcChatProfile
            {
                Id = profile.Id,
                Name = profile.Name,
                Description = profile.Description,
                SystemPrompt = profile.SystemPrompt,
                DatasourceId = profile.DatasourceId,
                ModelConfigId = profile.ModelConfigId,
                State = 1
            };

            this.InsertData(entity);
            
            return profile;
        }

        [Function("getChatProfile", "获取对话配置文件")]
        public async Task<ChatProfile> GetChatProfile(string profileId)
        {
            await using var db = this.GetDb();
            var entity = await db.LcChatProfiles
                .FirstOrDefaultAsync(p => p.Id == profileId);

            if (entity == null)
            {
                throw new CustomException($"找不到ID为{profileId}的对话配置文件");
            }

            return MapToModel(entity);
        }

        [Function("getDefaultChatProfile", "获取默认对话配置文件")]
        public async Task<ChatProfile> GetDefaultChatProfile()
        {
            await using var db = this.GetDb();
            // 返回第一个配置文件作为默认值
            var entity = await db.LcChatProfiles
                .FirstOrDefaultAsync();

            if (entity == null)
            {
                // 如果没有配置文件，创建一个默认的
                var profile = new ChatProfile
                {
                    Name = "默认配置",
                    Description = "系统默认配置",
                    SystemPrompt = "You are a helpful assistant."
                };

                // 获取默认模型配置
                try
                {
                    var modelConfig = await _modelConfigService.GetDefaultModelConfiguration();
                    profile.ModelConfigId = modelConfig.Id;
                }
                catch (CustomException)
                {
                    // 如果没有默认模型配置，添加一个
                    var config = new ChatModelConfiguration
                    {
                        Name = "默认模型",
                        Type = "OpenAI",
                        ApiKey = "sk-demo",
                        ChatModelID = "gpt-3.5-turbo",
                        EmbeddingModelID = "text-embedding-ada-002",
                        BaseURL = "https://api.openai.com/v1",
                        IsDefault = true
                    };
                    
                    config = await _modelConfigService.CreateModelConfiguration(config);
                    profile.ModelConfigId = config.Id;
                }

                // 保存到数据库
                var newEntity = new LcChatProfile
                {
                    Id = profile.Id,
                    Name = profile.Name,
                    Description = profile.Description,
                    SystemPrompt = profile.SystemPrompt,
                    ModelConfigId = profile.ModelConfigId,
                    State = 1
                };

                this.InsertData(newEntity);
                
                return profile;
            }

            return MapToModel(entity);
        }

        [Function("getAllChatProfiles", "获取所有对话配置文件")]
        public async Task<List<ChatProfile>> GetAllChatProfiles()
        {
            await using var db = this.GetDb();
            var entities = await db.LcChatProfiles
                .Where(p => p.State == 1)
                .ToListAsync();

            return entities.Select(MapToModel).ToList();
        }

        [Function("updateChatProfile", "更新对话配置文件")]
        public async Task<ChatProfile> UpdateChatProfile(ChatProfile profile)
        {
            await using var db = this.GetDb();
            
            var entity = await db.LcChatProfiles
                .FirstOrDefaultAsync(p => p.Id == profile.Id);

            if (entity == null)
            {
                throw new CustomException($"找不到ID为{profile.Id}的对话配置文件");
            }

            entity.Name = profile.Name;
            entity.Description = profile.Description;
            entity.SystemPrompt = profile.SystemPrompt;
            entity.DatasourceId = profile.DatasourceId;
            entity.ModelConfigId = profile.ModelConfigId;

            this.UpdateData(entity);
            
            return profile;
        }

        [Function("deleteChatProfile", "删除对话配置文件")]
        public async Task DeleteChatProfile(string profileId)
        {
            await using var db = this.GetDb();
            
            var entity = await db.LcChatProfiles
                .FirstOrDefaultAsync(p => p.Id == profileId);

            if (entity == null)
            {
                throw new CustomException($"找不到ID为{profileId}的对话配置文件");
            }

            entity.State = 0;

            this.UpdateData(entity);
        }
        
        /// <summary>
        /// 将数据库实体转换为业务模型
        /// </summary>
        private ChatProfile MapToModel(LcChatProfile entity)
        {
            return new ChatProfile
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description,
                SystemPrompt = entity.SystemPrompt,
                DatasourceId = entity.DatasourceId,
                ModelConfigId = entity.ModelConfigId
            };
        }
    }
} 