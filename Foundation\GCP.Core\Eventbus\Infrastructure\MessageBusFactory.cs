using GCP.Common;
using GCP.Eventbus.Providers;
using Serilog;

namespace GCP.Eventbus.Infrastructure
{
    class MessageBusFactory : IMessageBusFactory
    {
        public MessageBusFactory()
        {
        }

        public Task<IMessageBus> CreateAsync(MessageBusOptions options)
        {
            try
            {
                IMessageBus messageBus = options.Type switch
                {
                    MessageBusType.RabbitMQ => new RabbitMqMessageBus(options),
                    MessageBusType.Redis => new RedisMessageBus(options),
                    MessageBusType.Kafka => new KafkaMessageBus(options),
                    MessageBusType.Mqtt => new MqttMessageBus(options),
                    MessageBusType.InMemory => new InMemoryMessageBus(options),
                    MessageBusType.Tcp => new TcpMessageBus(options),
                    _ => throw new MessageBusException(options.Name, options.Type, $"Message bus type not supported: {options.Type}")
                };

                return Task.FromResult(messageBus);
            }
            catch (Exception ex) when (ex is not MessageBusException)
            {
                var error = new MessageBusException(options.Name, options.Type, "Failed to create message bus", ex);
                Log.Error(ex, "Failed to create message bus {Name} of type {Type}", options.Name, options.Type);
                throw error;
            }
        }
    }
}