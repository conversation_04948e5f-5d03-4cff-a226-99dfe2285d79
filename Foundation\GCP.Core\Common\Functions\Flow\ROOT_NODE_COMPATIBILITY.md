# ROOT节点处理兼容性说明

## 概述

为了支持ROOT节点的类型转换（object/array），我们对后端进行了重构，同时确保与现有Jint脚本的兼容性。

## 1. 通用方法封装

### RootNodeHelper 工具类

所有ROOT节点相关的处理都封装在 `RootNodeHelper` 类中：

```csharp
// 根据ROOT节点类型转换数据
object ConvertDataByRootNodeType(object data, string rootNodeType)

// 处理系统API请求体
Task<(object data, FlowData rootNode)> ProcessSystemApiRequestBody(Stream requestStream, List<FlowData> bodyFlowData)

// 处理第三方API请求数据
object ProcessRequestBodyByRootNode(object bodyData, List<FlowData> bodyFlowData)

// 为脚本引擎准备兼容的_data变量
Dictionary<string, object> PrepareScriptCompatibleData(Dictionary<string, object> globalData)
```

### 使用示例

**系统API处理：**
```csharp
// 原来的代码
var arrayBody = await JsonHelper.DeserializeAsync<object[]>(ms);
args[rootNode.Key] = rootNode.ParseDefaultValue(arrayBody);

// 现在的代码
var (processedData, rootNode) = await RootNodeHelper.ProcessSystemApiRequestBody(ms, bodyFlowData);
if (rootNode != null)
{
    args[rootNode.Key] = rootNode.ParseDefaultValue(processedData);
}
```

**第三方API处理：**
```csharp
// 原来的代码
if (rootNode.Type == "array") { /* 复杂的转换逻辑 */ }

// 现在的代码
var processedBody = RootNodeHelper.ProcessRequestBodyByRootNode(bodyObj, bodyFlowData);
args = JsonHelper.Serialize(processedBody);
```

## 2. Jint脚本兼容性

### 问题描述

原来的脚本使用 `_data.stepId.fieldName` 的方式访问数据。引入ROOT节点后，数据结构可能发生变化：

**原来的数据结构：**
```json
{
  "step1": {
    "field1": "value1",
    "field2": "value2"
  }
}
```

**ROOT节点（object类型）后的数据结构：**
```json
{
  "step1": {
    "ROOT": {
      "field1": "value1",
      "field2": "value2"
    }
  }
}
```

**ROOT节点（array类型）后的数据结构：**
```json
{
  "step1": {
    "ROOT": [
      {"field1": "value1"},
      {"field2": "value2"}
    ]
  }
}
```

### 兼容性解决方案

#### A. 脚本引擎_data变量处理

使用 `PrepareScriptCompatibleData` 方法处理 `_data` 变量：

```csharp
// 原来的代码
engine.SetValue("_data", context.globalData);

// 现在的代码
var compatibleData = RootNodeHelper.PrepareScriptCompatibleData(context.globalData);
engine.SetValue("_data", compatibleData);
```

#### B. 变量路径访问处理

使用 `ProcessVariablePathResult` 方法处理变量路径访问：

```csharp
// 在 GetVariableValueByPath 方法中
var result = GetVariableValue(varDic, parts, 0);

// 使用ROOT节点兼容性处理
result = RootNodeHelper.ProcessVariablePathResult(result, value.VariableValue);
```

### 兼容性策略

#### 1. 对象类型ROOT节点
当ROOT节点是对象类型时，将ROOT节点的内容展开到父级：

**数据转换：**
```csharp
// 原始数据
{
  "step1": {
    "ROOT": {
      "field1": "value1",
      "field2": "value2"
    }
  }
}

// 脚本中的_data
{
  "step1": {
    "field1": "value1",  // ROOT内容展开
    "field2": "value2"
  }
}
```

**脚本访问：**
```javascript
// 仍然可以使用原来的方式
_data.step1.field1  // 返回 "value1"
_data.step1.field2  // 返回 "value2"
```

#### 2. 数组类型ROOT节点
当ROOT节点是数组类型时，保持ROOT节点结构：

**数据转换：**
```csharp
// 原始数据和脚本中的_data保持一致
{
  "step1": {
    "ROOT": [
      {"field1": "value1"},
      {"field2": "value2"}
    ]
  }
}
```

**脚本访问：**
```javascript
// 需要通过ROOT访问
_data.step1.ROOT[0].field1  // 返回 "value1"
_data.step1.ROOT.length     // 返回 2
```

## 3. 更新的文件列表

### 核心工具类
- `Foundation/GCP.Core/Common/Functions/Flow/RootNodeHelper.cs` (新增)

### 系统API处理
- `Foundation/GCP.Core/FunctionPool/FunctionHttpHandler.cs`

### 第三方API处理
- `Foundation/GCP.Core/FunctionPool/Flow/Services/ApiRequest.cs`

### 数据绑定处理
- `Foundation/GCP.Core/FunctionPool/Flow/FlowUtils.cs`

### 脚本引擎兼容性
- `Foundation/GCP.Core/FunctionPool/Flow/Services/DataBaseService.cs`
- `Foundation/GCP.Core/FunctionPool/Flow/Services/DataControl.cs`
- `Foundation/GCP.Core/FunctionPool/Flow/FlowRunner.cs`
- `Foundation/GCP.Core/FunctionPool/Flow/Services/JobControl.cs`

## 4. 测试建议

### 兼容性测试用例

#### 测试用例1：对象类型ROOT节点
```javascript
// 脚本内容
var result = _data.apiStep.field1;
return result;

// 预期：能够正常访问，无需修改脚本
```

#### 测试用例2：数组类型ROOT节点
```javascript
// 脚本内容
var items = _data.apiStep.ROOT;
var firstItem = items[0];
return firstItem.field1;

// 预期：能够正常访问数组数据
```

#### 测试用例3：混合数据结构
```javascript
// 脚本内容
var objectData = _data.step1.field1;  // 对象类型ROOT节点
var arrayData = _data.step2.ROOT;     // 数组类型ROOT节点
return { object: objectData, array: arrayData };

// 预期：两种类型都能正常访问
```

## 5. 迁移指南

### 现有脚本迁移

**大部分脚本无需修改：**
- 对象类型ROOT节点：脚本访问方式不变
- 基本数据类型：脚本访问方式不变

**需要修改的脚本：**
- 数组类型ROOT节点：需要通过 `_data.stepId.ROOT` 访问

### 新脚本编写建议

1. **优先使用对象类型ROOT节点**，保持最佳兼容性
2. **数组类型ROOT节点**适用于列表数据处理
3. **在脚本编辑器中**，变量提示会显示正确的访问路径

## 6. 性能影响

### 优化措施
- 兼容性处理只在脚本引擎初始化时执行一次
- 变量路径访问的兼容性处理开销很小
- 通用方法减少了重复代码，提高了维护性

### 内存使用
- `PrepareScriptCompatibleData` 会创建新的字典，但只包含引用
- 对于大数据量，内存开销可控
- 建议在必要时使用，避免不必要的转换

## 7. 总结

通过封装通用方法和兼容性处理，我们实现了：

✅ **功能完整性**：支持ROOT节点的object/array类型转换
✅ **向后兼容性**：现有脚本无需修改即可正常工作
✅ **代码可维护性**：统一的处理逻辑，减少重复代码
✅ **性能优化**：最小化兼容性处理的性能开销

这个方案确保了系统的平滑升级，同时为未来的扩展提供了良好的基础。
