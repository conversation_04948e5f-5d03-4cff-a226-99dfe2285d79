﻿using FluentEmail.Core;
using System.Reflection;

namespace GCP.FunctionPool.Builder
{
    class DllFileResolver
    {
        static ICollection<Assembly> GetAssemblies(string directoryPath = "")
        {
            List<Assembly> assemblies = AppDomain.CurrentDomain.GetAssemblies().ToList();
            if (FunctionRunner.IsAOT) return assemblies;

            //List<Assembly> assemblies = new List<Assembly>();
            if (string.IsNullOrEmpty(directoryPath))
            {
                directoryPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "functions");
            }

            if (Directory.Exists(directoryPath))
            {
                string[] files = Directory.GetFiles(directoryPath, "GCP.*.dll");
                //Parallel.ForEach(files, fileName =>
                files.ForEach(fileName =>
                {
                    assemblies.Add(Assembly.Load(File.ReadAllBytes(Path.Combine(directoryPath, fileName))));
                });
            }
            return assemblies;
        }

        /// <summary>
        /// 编译dll, 获取多个函数
        /// </summary>
        internal static void Handler(string directoryPath = "")
        {
            Parallel.ForEach(GetAssemblies(directoryPath).Distinct(), assembly =>
            //GetAssemblies(directoryPath).Distinct().ForEach(assembly =>
            {
                AssemblyResolver.Handler(FunctionRunner.IsAOT ? assembly.FullName : assembly.Location, assembly.GetName().Name, assembly, null, FunctionResolveType.ASSEMBLY);
            });
        }
    }
}
