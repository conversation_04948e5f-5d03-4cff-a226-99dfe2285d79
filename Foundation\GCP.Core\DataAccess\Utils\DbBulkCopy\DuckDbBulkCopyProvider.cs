﻿using DuckDB.NET.Data;
using System.Data;
using System.Numerics;
using System.Text;

namespace GCP.DataAccess
{
    internal static class DuckDbBulkCopyProvider
    {
        internal static void DuckDbBulkCopy(this IDbConnection connection, string tableName, IDataReader reader, IDbTransaction transaction = null)
        {
            var conn = (DuckDBConnection) connection;
            
            // 检查表是否存在，如果不存在则自动创建
            if (!TableExists(conn, tableName))
            {
                CreateTableFromReader(conn, tableName, reader);
            }
            
            using var appender = conn.CreateAppender(tableName);
            reader.GetList<IDuckDBAppenderRow>(null, null, -1, appender.CreateRow, false);
        }

        private static bool TableExists(DuckDBConnection connection, string tableName)
        {
            using var cmd = connection.CreateCommand();
            cmd.CommandText = $"SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{tableName}'";
            var result = Convert.ToInt32(cmd.ExecuteScalar());
            return result > 0;
        }

        private static void CreateTableFromReader(DuckDBConnection connection, string tableName, IDataReader reader)
        {
            var schemaTable = reader.GetSchemaTable();
            if (schemaTable == null)
                throw new InvalidOperationException("无法获取IDataReader的架构信息");

            var createTableSql = new StringBuilder();
            createTableSql.Append($"CREATE TABLE {tableName} (");

            var columnDefinitions = new List<string>();
            foreach (DataRow row in schemaTable.Rows)
            {
                string columnName = row["ColumnName"].ToString();
                Type columnType = (Type)row["DataType"];
                bool allowNull = (bool)row["AllowDBNull"];

                string duckDbType = MapToDuckDbType(columnType);
                columnDefinitions.Add($"{columnName} {duckDbType}{(allowNull ? "" : " NOT NULL")}");
            }

            createTableSql.Append(string.Join(", ", columnDefinitions));
            createTableSql.Append(")");

            using var cmd = connection.CreateCommand();
            cmd.CommandText = createTableSql.ToString();
            cmd.ExecuteNonQuery();
        }

        private static string MapToDuckDbType(Type clrType)
        {
            if (clrType == typeof(bool)) return "BOOLEAN";
            if (clrType == typeof(byte)) return "UTINYINT";
            if (clrType == typeof(sbyte)) return "TINYINT";
            if (clrType == typeof(short)) return "SMALLINT";
            if (clrType == typeof(ushort)) return "USMALLINT";
            if (clrType == typeof(int)) return "INTEGER";
            if (clrType == typeof(uint)) return "UINTEGER";
            if (clrType == typeof(long)) return "BIGINT";
            if (clrType == typeof(ulong)) return "UBIGINT";
            if (clrType == typeof(float)) return "REAL";
            if (clrType == typeof(double)) return "DOUBLE";
            if (clrType == typeof(decimal)) return "DECIMAL";
            if (clrType == typeof(string)) return "VARCHAR";
            if (clrType == typeof(Guid)) return "UUID";
            if (clrType == typeof(DateTime)) return "TIMESTAMP";
            if (clrType == typeof(DateTimeOffset)) return "TIMETZ";
            if (clrType == typeof(TimeSpan)) return "INTERVAL";
            if (clrType == typeof(byte[])) return "BLOB";
            if (clrType == typeof(BigInteger)) return "HUGEINT";

            return "VARCHAR"; // 默认类型
        }

        internal static void DuckDbBulkCopy(this IDbConnection connection, string tableName, DataTable dt, IDbTransaction transaction = null)
        {
            var conn = (DuckDBConnection) connection;
            
            // 检查表是否存在，如果不存在则自动创建
            if (!TableExists(conn, tableName))
            {
                CreateTableFromDataTable(conn, tableName, dt);
            }
            
            using var appender = conn.CreateAppender(tableName);
            foreach (DataRow row in dt.Rows)
            {
                var newRow = appender.CreateRow();
                foreach (var col in dt.Columns)
                {
                }
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    newRow.AppendObject(row[i]);
                }
                newRow.EndRow();
            }
        }

        private static void CreateTableFromDataTable(DuckDBConnection connection, string tableName, DataTable dt)
        {
            var createTableSql = new StringBuilder();
            createTableSql.Append($"CREATE TABLE {tableName} (");

            var columnDefinitions = new List<string>();
            foreach (DataColumn column in dt.Columns)
            {
                string columnName = column.ColumnName;
                Type columnType = column.DataType;
                bool allowNull = column.AllowDBNull;

                string duckDbType = MapToDuckDbType(columnType);
                columnDefinitions.Add($"{columnName} {duckDbType}{(allowNull ? "" : " NOT NULL")}");
            }

            createTableSql.Append(string.Join(", ", columnDefinitions));
            createTableSql.Append(")");

            using var cmd = connection.CreateCommand();
            cmd.CommandText = createTableSql.ToString();
            cmd.ExecuteNonQuery();
        }

        internal static void AppendObject(this IDuckDBAppenderRow row, object value)
        {
            if (value is null or DBNull)
            {
                row.AppendNullValue();
            }
            else if (value is string stringValue)
            {
                row.AppendValue(stringValue);
            }
            else if (value is bool boolValue)
            {
                row.AppendValue(boolValue);
            }
            else if (value is byte byteValue)
            {
                row.AppendValue(byteValue);
            }
            else if (value is sbyte sbyteValue)
            {
                row.AppendValue(sbyteValue);
            }
            else if (value is short shortValue)
            {
                row.AppendValue(shortValue);
            }
            else if (value is ushort ushortValue)
            {
                row.AppendValue(ushortValue);
            }
            else if (value is int intValue)
            {
                row.AppendValue(intValue);
            }
            else if (value is uint uintValue)
            {
                row.AppendValue(uintValue);
            }
            else if (value is long longValue)
            {
                row.AppendValue(longValue);
            }
            else if (value is ulong ulongValue)
            {
                row.AppendValue(ulongValue);
            }
            else if (value is float floatValue)
            {
                row.AppendValue(floatValue);
            }
            else if (value is double doubleValue)
            {
                row.AppendValue(doubleValue);
            }
            else if (value is decimal decimalValue)
            {
                row.AppendValue(decimalValue);
            }
            else if (value is DateTime dateTimeValue)
            {
                row.AppendValue(dateTimeValue);
            }
            else if (value is DateTimeOffset dateTimeOffsetValue)
            {
                row.AppendValue(dateTimeOffsetValue);
            }
            else if (value is TimeSpan timeSpanValue)
            {
                row.AppendValue(timeSpanValue);
            }
            else if (value is Enum enumValue)
            {
                row.AppendValue(enumValue);
            }
            else if (value is Guid guidValue)
            {
                row.AppendValue(guidValue);
            }
            else if (value is byte[] byteArrayValue)
            {
                row.AppendValue(byteArrayValue);
            }
            else if (value is BigInteger bigIntegerValue)
            {
                row.AppendValue(bigIntegerValue);
            }
            else
            {
                throw new NotSupportedException($"Unsupported type: {value.GetType().FullName}");
            }
        }
    }
}
