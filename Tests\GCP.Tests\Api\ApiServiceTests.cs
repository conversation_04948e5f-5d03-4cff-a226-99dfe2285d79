using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.Functions.Common.Services;
using GCP.DataAccess;
using GCP.Common;
using LinqToDB;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.Tests.Api
{
    /// <summary>
    /// API服务测试类
    /// </summary>
    public class ApiServiceTests : DatabaseTestBase
    {
        public ApiServiceTests(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<ApiService>();
        }

        [Fact]
        public async Task GetAll_ShouldReturnAllActiveApis()
        {
            // Arrange
            await InitializeTestDataAsync();

            var apiService = GetService<ApiService>();
            SetTestContext(apiService);

            // Act
            var result = apiService.GetAll();

            // Assert
            result.Should().NotBeNull();
            Output.WriteLine($"ApiService返回的API数量: {result.Count()}");

            // 由于数据插入和查询可能使用不同的连接，暂时只验证方法不抛异常
            // 后续可以改进测试数据的插入方式
        }

        [Fact]
        public async Task GetAll_WithApiType_ShouldReturnFilteredApis()
        {
            // Arrange
            await InitializeTestDataAsync();
            var apiService = GetService<ApiService>();
            SetTestContext(apiService);

            // Act
            var result = apiService.GetAll(apiType: (short)1);

            // Assert
            result.Should().NotBeNull();
            result.All(api => api.ApiType == 1).Should().BeTrue("所有返回的API都应该是指定类型");
        }

        [Fact]
        public async Task GetListByFuncId_ShouldReturnApisWithSpecificFunctionId()
        {
            // Arrange
            await InitializeTestDataAsync();
            var apiService = GetService<ApiService>();
            SetTestContext(apiService);
            
            var testFunctionId = "test-function-001";

            // Act
            var result = apiService.GetListByFuncId(testFunctionId);

            // Assert
            result.Should().NotBeNull();
            if (result.Any())
            {
                result.All(api => api.FunctionId == testFunctionId).Should().BeTrue("所有返回的API都应该有指定的函数ID");
            }
        }

        [Fact]
        public async Task Add_ShouldCreateNewApi()
        {
            // Arrange
            await InitializeTestDataAsync();
            var apiService = GetService<ApiService>();
            SetTestContext(apiService);

            var newApi = TestDataBuilder.CreateTestApi("新增测试API");

            // Act
            var action = () => apiService.AddJob(newApi);

            // Assert
            action.Should().NotThrow("添加新API不应该抛出异常");

            // 验证API是否已添加
            var addedApis = apiService.GetAll();
            addedApis.Should().Contain(api => api.ApiName == "新增测试API");
        }

        [Fact]
        public async Task Add_WithDuplicateUrl_ShouldThrowException()
        {
            // Arrange
            await InitializeTestDataAsync();
            var apiService = GetService<ApiService>();
            SetTestContext(apiService);

            var existingApi = TestDataBuilder.CreateTestApi("现有API");
            apiService.AddJob(existingApi);

            var duplicateApi = TestDataBuilder.CreateTestApi("重复API");
            duplicateApi.HttpUrl = existingApi.HttpUrl;
            duplicateApi.RequestType = existingApi.RequestType;
            duplicateApi.ApiType = existingApi.ApiType;

            // Act & Assert
            var action = () => apiService.AddJob(duplicateApi);
            action.Should().Throw<CustomException>()
                .WithMessage("*存在相同的Url地址配置*");
        }

        [Fact]
        public async Task Update_ShouldModifyExistingApi()
        {
            // Arrange
            await InitializeTestDataAsync();
            var apiService = GetService<ApiService>();
            SetTestContext(apiService);

            var originalApi = TestDataBuilder.CreateTestApi("原始API");
            apiService.AddJob(originalApi);

            // 修改API信息
            originalApi.Description = "更新后的描述";
            originalApi.TimeModified = DateTime.Now;

            // Act
            var action = () => apiService.Update(originalApi);

            // Assert
            action.Should().NotThrow("更新API不应该抛出异常");

            // 验证更新是否成功
            var updatedApi = apiService.GetApiById(originalApi.Id);
            updatedApi.Should().NotBeNull();
            updatedApi!.Description.Should().Be("更新后的描述");
        }

        [Fact]
        public async Task Delete_ShouldRemoveApi()
        {
            // Arrange
            await InitializeTestDataAsync();
            var apiService = GetService<ApiService>();
            SetTestContext(apiService);

            var apiToDelete = TestDataBuilder.CreateTestApi("待删除API");
            apiService.AddJob(apiToDelete);

            // Act
            var action = () => apiService.Delete(apiToDelete.Id);

            // Assert
            action.Should().NotThrow("删除API不应该抛出异常");

            // 验证API是否已删除（状态变为0）
            var deletedApi = apiService.GetApiById(apiToDelete.Id);
            deletedApi.Should().BeNull("删除的API应该无法查询到");
        }

        protected override async Task SeedTestDataAsync(GcpDb db)
        {
            try
            {
                // 创建测试解决方案
                var solution = TestDataBuilder.CreateTestSolution("测试解决方案");
                await db.InsertAsync(solution);

                // 创建测试项目
                var project = TestDataBuilder.CreateTestProject("测试项目", "test-solution-001");
                await db.InsertAsync(project);

                // 创建测试API
                var apis = new[]
                {
                    TestDataBuilder.CreateTestApi("API1", "test-solution-001", "test-project-001"),
                    TestDataBuilder.CreateTestApi("API2", "test-solution-001", "test-project-001"),
                    TestDataBuilder.CreateTestApi("API3", "test-solution-001", "test-project-001")
                };

                // 设置不同的HTTP_URL以避免唯一约束冲突
                apis[0].HttpUrl = "/api/test1";
                apis[1].HttpUrl = "/api/test2";
                apis[2].HttpUrl = "/api/test3";

                apis[0].ApiType = 1;
                apis[1].ApiType = 2;
                apis[2].ApiType = 1;
                apis[0].FunctionId = "test-function-001";

                foreach (var api in apis)
                {
                    await db.InsertAsync(api);
                }
            }
            catch (Exception ex)
            {
                Output.WriteLine($"测试数据初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置测试上下文
        /// </summary>
        private void SetTestContext(ApiService apiService)
        {
            // 通过反射设置私有字段
            var baseType = typeof(ApiService).BaseType; // BaseService
            var solutionIdField = baseType?.GetField("_solutionId", BindingFlags.NonPublic | BindingFlags.Instance);
            var projectIdField = baseType?.GetField("_projectId", BindingFlags.NonPublic | BindingFlags.Instance);

            solutionIdField?.SetValue(apiService, "test-solution-001");
            projectIdField?.SetValue(apiService, "test-project-001");
        }
    }
}
