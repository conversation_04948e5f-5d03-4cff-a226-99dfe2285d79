// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 历史流程运行细节
	/// </summary>
	[Table("lc_fhi_detail")]
	public class LcFhiDetail
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"         , CanBeNull = false, IsPrimaryKey = true)] public string    Id         { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:流程运行实例 ID
		/// </summary>
		[Column("PROC_ID"    , CanBeNull = false                     )] public string    ProcId     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:执行顺序
		/// </summary>
		[Column("SEQ_NO"                                             )] public int?      SeqNo      { get; set; } // int
		/// <summary>
		/// Description:流程动作函数ID
		/// </summary>
		[Column("FUNCTION_ID", CanBeNull = false                     )] public string    FunctionId { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:流程动作ID
		/// </summary>
		[Column("ACTION_ID"                                          )] public string?   ActionId   { get; set; } // varchar(36)
		/// <summary>
		/// Description:流程步骤名称
		/// </summary>
		[Column("STEP_NAME"                                          )] public string?   StepName   { get; set; } // varchar(80)
		/// <summary>
		/// Description:状态 -1：失败, 0：运行中, 1：已运行
		/// </summary>
		[Column("STATUS"                                             )] public short     Status     { get; set; } // smallint
		/// <summary>
		/// Description:开始时间
		/// </summary>
		[Column("BEGIN_TIME"                                         )] public DateTime  BeginTime  { get; set; } // datetime
		/// <summary>
		/// Description:结束时间
		/// </summary>
		[Column("END_TIME"                                           )] public DateTime? EndTime    { get; set; } // datetime
		/// <summary>
		/// Description:运行时长
		/// </summary>
		[Column("DURATION"                                           )] public int?      Duration   { get; set; } // int
		/// <summary>
		/// Description:运行数据
		/// </summary>
		[Column("RUN_DATA"   , CanBeNull = false                     )] public string    RunData    { get; set; } = null!; // longtext
	}
}
