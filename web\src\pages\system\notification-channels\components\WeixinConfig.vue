<template>
  <div class="weixin-config">
    <t-form-item label="Webhook地址" name="webHook">
      <t-input v-model="config.webHook" placeholder="请输入企业微信机器人Webhook地址" />
    </t-form-item>

    <t-form-item label="提及用户" name="mentionedList">
      <div class="mention-list">
        <div v-for="(user, index) in config.mentionedList" :key="index" class="mention-item">
          <t-input v-model="config.mentionedList[index]" placeholder="请输入用户ID" />
          <t-button theme="danger" variant="text" @click="removeUser(index)">
            删除
          </t-button>
        </div>
        <t-button theme="primary" variant="text" @click="addUser">
          添加用户
        </t-button>
      </div>
    </t-form-item>

    <t-form-item label="提及手机号" name="mentionedMobileList">
      <div class="mobile-list">
        <div v-for="(mobile, index) in config.mentionedMobileList" :key="index" class="mobile-item">
          <t-input v-model="config.mentionedMobileList[index]" placeholder="请输入手机号" />
          <t-button theme="danger" variant="text" @click="removeMobile(index)">
            删除
          </t-button>
        </div>
        <t-button theme="primary" variant="text" @click="addMobile">
          添加手机号
        </t-button>
      </div>
    </t-form-item>

    <t-alert theme="info" title="配置说明">
      <p>1. Webhook地址：在企业微信群中添加群机器人后获得</p>
      <p>2. 提及用户：可以@指定的用户ID</p>
      <p>3. 提及手机号：可以@指定手机号的用户</p>
      <p>4. 企业微信机器人支持文本、markdown等消息类型</p>
    </t-alert>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
const props = defineProps<{
  modelValue: {
    webHook: string;
    mentionedList: string[];
    mentionedMobileList: string[];
  };
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
}>();

// 计算属性
const config = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const addUser = () => {
  config.value.mentionedList.push('');
};

const removeUser = (index: number) => {
  config.value.mentionedList.splice(index, 1);
};

const addMobile = () => {
  config.value.mentionedMobileList.push('');
};

const removeMobile = (index: number) => {
  config.value.mentionedMobileList.splice(index, 1);
};
</script>

<style scoped>
.weixin-config {
  padding: 10px 0;
}

.mention-list,
.mobile-list {
  width: 100%;
}

.mention-item,
.mobile-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.mention-item .t-input,
.mobile-item .t-input {
  flex: 1;
}

.t-alert {
  margin-top: 20px;
}
</style>
