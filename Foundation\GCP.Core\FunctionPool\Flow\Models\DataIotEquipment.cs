using GCP.Common;

namespace GCP.FunctionPool.Flow.Models
{
    /// <summary>
    /// 设备测点赋值动作
    /// </summary>
    public class DataIotVariableWrite
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public DataValue EquipmentId { get; set; }
        /// <summary>
        /// 变量地址
        /// </summary>
        public DataValue Address { get; set; }
        /// <summary>
        /// 写入值
        /// </summary>
        public DataValue Value { get; set; }
    }

    /// <summary>
    /// 设备测点读取动作
    /// </summary>
    public class DataIotVariableRead
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public DataValue EquipmentId { get; set; }
        /// <summary>
        /// 变量名称
        /// </summary>
        public DataValue VariableName { get; set; }
    }

    /// <summary>
    /// 设备所有参数读取动作
    /// </summary>
    public class DataIotEquipmentRead
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public DataValue EquipmentId { get; set; }
    }

    /// <summary>
    /// 批量设备参数读取动作
    /// </summary>
    public class DataIotMultiEquipmentRead
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 设备ID数组
        /// </summary>
        public DataValue EquipmentIds { get; set; }
    }
}
