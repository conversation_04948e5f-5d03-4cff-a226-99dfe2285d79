<template>
  <t-dialog
    v-model:visible="showMoveActionDialog"
    :header="'移动【' + currentStep?.name + '】到'"
    :on-confirm="onMoveAction"
  >
    <t-form style="margin-top: 16px">
      <t-form-item label="目标动作">
        <t-select v-model="target" filterable placeholder="请选择文件夹" :options="actionOptions"></t-select>
      </t-form-item>
      <t-form-item label="顺序">
        <t-radio-group v-model="targetPosition" name="city" :options="positionOptions"></t-radio-group>
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'MoveActionDialog',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, ref } from 'vue';

import { FlowStep } from './model';
import { useActionFlowStore } from './store/index';

const actionFlowStore = useActionFlowStore();
const { showMoveActionDialog, currentStep, flowInfo } = storeToRefs(actionFlowStore);

const positionOptions = computed(() => {
  const hasControlType = targetStep.value?.controlType;
  return [
    {
      value: 'before',
      label: '前面',
    },
    {
      value: 'inside',
      label: '内部',
      disabled: !hasControlType,
    },
    {
      value: 'after',
      label: '后面',
    },
  ];
});
const target = ref('');

const targetStep = computed<FlowStep>(() => {
  return flowInfo.value.body.find((item) => item.id === target.value);
});
const targetPosition = ref<'before' | 'inside' | 'after'>('before');

const actionOptions = computed(() => {
  const list = [];
  flowInfo.value.body.forEach((item) => {
    if (item.id !== currentStep.value?.id) {
      list.push({
        value: item.id,
        label: item.name,
      });
    }
  });
  return list;
});

const onMoveAction = () => {
  if (!target.value) {
    MessagePlugin.error('请选择目标动作');
    return;
  }
  const hasControlType = targetStep.value?.controlType;
  if (!hasControlType && targetPosition.value === 'inside') {
    MessagePlugin.error('请选择正确的目标位置');
    return;
  }

  actionFlowStore.moveStep(currentStep.value, targetStep.value, targetPosition.value);
  showMoveActionDialog.value = false;
};
</script>
<style lang="less" scoped></style>
