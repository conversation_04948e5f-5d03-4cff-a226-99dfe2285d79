<template>
  <div class="action-step-form">
    <t-form ref="formRef" :data="formData" layout="vertical">
      <!-- 基础信息 -->
      <div class="form-section">
        <h4>基础信息</h4>
        <t-row :gutter="16">
          <t-col :span="6">
            <t-form-item label="步骤名称" name="name">
              <t-input v-model="formData.name" placeholder="请输入步骤名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="函数类型" name="function">
              <t-select v-model="formData.function" :options="functionTypeOptions" />
            </t-form-item>
          </t-col>
        </t-row>
        <t-form-item label="描述" name="description">
          <t-textarea 
            v-model="formData.description" 
            placeholder="请输入步骤描述"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </t-form-item>
      </div>

      <!-- 参数配置 -->
      <div class="form-section">
        <h4>参数配置</h4>
        <div class="args-config">
          <template v-if="formData.function === 'javascript'">
            <t-form-item label="JavaScript代码" name="args.code">
              <t-textarea 
                v-model="formData.args.code"
                placeholder="请输入JavaScript代码"
                :autosize="{ minRows: 6, maxRows: 12 }"
              />
            </t-form-item>
          </template>
          
          <template v-else-if="formData.function === 'http'">
            <t-row :gutter="16">
              <t-col :span="4">
                <t-form-item label="请求方法" name="args.method">
                  <t-select 
                    v-model="formData.args.method"
                    :options="httpMethods"
                    placeholder="GET"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="8">
                <t-form-item label="请求URL" name="args.url">
                  <t-input v-model="formData.args.url" placeholder="https://api.example.com/data" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-form-item label="请求头" name="args.headers">
              <t-textarea 
                v-model="formData.args.headers"
                placeholder='{"Content-Type": "application/json"}'
                :autosize="{ minRows: 3, maxRows: 6 }"
              />
            </t-form-item>
            <t-form-item label="请求体" name="args.body">
              <t-textarea 
                v-model="formData.args.body"
                placeholder="请求体内容（JSON格式）"
                :autosize="{ minRows: 4, maxRows: 8 }"
              />
            </t-form-item>
          </template>

          <template v-else-if="formData.function === 'database'">
            <t-form-item label="SQL查询" name="args.sql">
              <t-textarea 
                v-model="formData.args.sql"
                placeholder="SELECT * FROM table_name WHERE condition"
                :autosize="{ minRows: 4, maxRows: 8 }"
              />
            </t-form-item>
            <t-form-item label="参数" name="args.params">
              <t-textarea 
                v-model="formData.args.params"
                placeholder='{"param1": "value1", "param2": "value2"}'
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </t-form-item>
          </template>

          <template v-else-if="formData.function === 'condition'">
            <t-form-item label="条件表达式" name="args.condition">
              <t-input v-model="formData.args.condition" placeholder="_data.value > 100" />
            </t-form-item>
            <t-form-item label="真值分支" name="args.trueBranch">
              <t-textarea 
                v-model="formData.args.trueBranch"
                placeholder="条件为真时执行的代码"
                :autosize="{ minRows: 3, maxRows: 6 }"
              />
            </t-form-item>
            <t-form-item label="假值分支" name="args.falseBranch">
              <t-textarea 
                v-model="formData.args.falseBranch"
                placeholder="条件为假时执行的代码"
                :autosize="{ minRows: 3, maxRows: 6 }"
              />
            </t-form-item>
          </template>

          <template v-else-if="formData.function === 'delay'">
            <t-row :gutter="16">
              <t-col :span="6">
                <t-form-item label="延时时间" name="args.duration">
                  <t-input-number v-model="formData.args.duration" :min="1" placeholder="1000" />
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="时间单位" name="args.unit">
                  <t-select 
                    v-model="formData.args.unit"
                    :options="timeUnits"
                    placeholder="毫秒"
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </template>

          <template v-else>
            <t-form-item label="参数配置" name="args">
              <t-textarea 
                v-model="argsJson"
                placeholder="请输入JSON格式的参数配置"
                :autosize="{ minRows: 4, maxRows: 8 }"
              />
            </t-form-item>
          </template>
        </div>
      </div>

      <!-- 输出配置 -->
      <div class="form-section">
        <h4>输出配置</h4>
        <div class="result-config">
          <div v-if="formData.result && formData.result.length > 0" class="result-list">
            <div 
              v-for="(result, index) in formData.result" 
              :key="index"
              class="result-item"
            >
              <t-row :gutter="16">
                <t-col :span="4">
                  <t-input v-model="result.key" placeholder="变量名" />
                </t-col>
                <t-col :span="3">
                  <t-select 
                    v-model="result.value.type"
                    :options="resultTypes"
                    placeholder="类型"
                  />
                </t-col>
                <t-col :span="4">
                  <t-input v-model="result.description" placeholder="描述" />
                </t-col>
                <t-col :span="1">
                  <t-button 
                    size="small" 
                    theme="danger" 
                    variant="text"
                    @click="removeResult(index)"
                  >
                    <template #icon>
                      <delete-icon />
                    </template>
                  </t-button>
                </t-col>
              </t-row>
            </div>
          </div>
          <t-button variant="dashed" @click="addResult">
            <template #icon>
              <add-icon />
            </template>
            添加输出变量
          </t-button>
        </div>
      </div>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { AddIcon, DeleteIcon } from 'tdesign-icons-vue-next';

interface FlowStep {
  id: string;
  name?: string;
  function: string;
  description?: string;
  args?: any;
  result?: any[];
}

interface Props {
  modelValue: FlowStep;
}

const props = defineProps<Props>();

const emits = defineEmits<{
  'update:modelValue': [value: FlowStep];
  update: [step: FlowStep];
}>();

const formRef = ref();
const formData = ref<FlowStep>({ ...props.modelValue });

// 确保args是对象
if (!formData.value.args) {
  formData.value.args = {};
}

// 确保result是数组
if (!formData.value.result) {
  formData.value.result = [];
}

const functionTypeOptions = [
  { label: 'JavaScript代码', value: 'javascript' },
  { label: 'HTTP请求', value: 'http' },
  { label: '数据库查询', value: 'database' },
  { label: '条件判断', value: 'condition' },
  { label: '循环处理', value: 'loop' },
  { label: '数据转换', value: 'transform' },
  { label: '延时等待', value: 'delay' },
  { label: '发送邮件', value: 'email' },
  { label: '文件操作', value: 'file' },
  { label: '调用函数', value: 'function' }
];

const httpMethods = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' }
];

const timeUnits = [
  { label: '毫秒', value: 'ms' },
  { label: '秒', value: 's' },
  { label: '分钟', value: 'm' }
];

const resultTypes = [
  { label: '文本', value: 'text' },
  { label: '数字', value: 'number' },
  { label: '布尔', value: 'boolean' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' }
];

// 通用参数JSON编辑
const argsJson = computed({
  get: () => {
    try {
      return JSON.stringify(formData.value.args, null, 2);
    } catch {
      return '{}';
    }
  },
  set: (value: string) => {
    try {
      formData.value.args = JSON.parse(value);
    } catch {
      // 忽略JSON解析错误
    }
  }
});

// 添加输出变量
const addResult = () => {
  if (!formData.value.result) {
    formData.value.result = [];
  }
  formData.value.result.push({
    key: `result_${formData.value.result.length + 1}`,
    value: { type: 'object' },
    description: ''
  });
};

// 删除输出变量
const removeResult = (index: number) => {
  if (formData.value.result) {
    formData.value.result.splice(index, 1);
  }
};

// 监听表单数据变化
watch(
  () => formData.value,
  (newValue) => {
    emits('update:modelValue', { ...newValue });
    emits('update', { ...newValue });
  },
  { deep: true }
);

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...newValue };
    if (!formData.value.args) {
      formData.value.args = {};
    }
    if (!formData.value.result) {
      formData.value.result = [];
    }
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
.action-step-form {
  .form-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }
  }

  .result-list {
    margin-bottom: 16px;

    .result-item {
      margin-bottom: 12px;
      padding: 12px;
      background: var(--td-bg-color-container);
      border-radius: 6px;
      border: 1px solid var(--td-border-level-1-color);
    }
  }
}
</style>
