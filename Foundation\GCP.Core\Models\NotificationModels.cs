namespace GCP.Models
{
    /// <summary>
    /// 通知通道类型
    /// </summary>
    public enum NotificationChannelType
    {
        Email,
        Dingtalk,
        Feishu,
        Weixin
    }

    /// <summary>
    /// 通知通道配置基类
    /// </summary>
    public abstract class NotificationChannelConfig
    {
        /// <summary>
        /// 通道类型
        /// </summary>
        public abstract NotificationChannelType ChannelType { get; }
    }

    /// <summary>
    /// 邮件通知配置
    /// </summary>
    public class EmailNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Email;
        
        /// <summary>
        /// SMTP服务器地址
        /// </summary>
        public string Host { get; set; } = null!;
        
        /// <summary>
        /// SMTP端口
        /// </summary>
        public int Port { get; set; }
        
        /// <summary>
        /// 发送人名称
        /// </summary>
        public string FromName { get; set; } = null!;
        
        /// <summary>
        /// 发送人邮箱
        /// </summary>
        public string FromAddress { get; set; } = null!;
        
        /// <summary>
        /// 邮箱密码或授权码
        /// </summary>
        public string Password { get; set; } = null!;
        
        /// <summary>
        /// 收件人列表
        /// </summary>
        public List<string> ToAddress { get; set; } = new();
        
        /// <summary>
        /// 是否启用SSL
        /// </summary>
        public bool EnableSsl { get; set; } = true;
    }

    /// <summary>
    /// 钉钉通知配置
    /// </summary>
    public class DingtalkNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Dingtalk;
        
        /// <summary>
        /// Webhook地址
        /// </summary>
        public string WebHook { get; set; } = null!;
        
        /// <summary>
        /// 签名密钥
        /// </summary>
        public string? Secret { get; set; }
        
        /// <summary>
        /// @所有人
        /// </summary>
        public bool IsAtAll { get; set; }
        
        /// <summary>
        /// @指定人员手机号列表
        /// </summary>
        public List<string> AtMobiles { get; set; } = new();
    }

    /// <summary>
    /// 飞书通知配置
    /// </summary>
    public class FeishuNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Feishu;
        
        /// <summary>
        /// Webhook地址
        /// </summary>
        public string WebHook { get; set; } = null!;
        
        /// <summary>
        /// 签名密钥
        /// </summary>
        public string? Secret { get; set; }
    }

    /// <summary>
    /// 企业微信通知配置
    /// </summary>
    public class WeixinNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Weixin;
        
        /// <summary>
        /// Webhook地址
        /// </summary>
        public string WebHook { get; set; } = null!;
        
        /// <summary>
        /// 提及用户列表
        /// </summary>
        public List<string> MentionedList { get; set; } = new();
        
        /// <summary>
        /// 提及手机号列表
        /// </summary>
        public List<string> MentionedMobileList { get; set; } = new();
    }

    /// <summary>
    /// 通知请求
    /// </summary>
    public class NotificationRequest
    {
        /// <summary>
        /// 通知标题
        /// </summary>
        public string Title { get; set; } = null!;
        
        /// <summary>
        /// 通知内容
        /// </summary>
        public string Content { get; set; } = null!;
        
        /// <summary>
        /// 异常信息（可选）
        /// </summary>
        public Exception? Exception { get; set; }
    }
}
