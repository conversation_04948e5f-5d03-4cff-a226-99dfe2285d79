import { pathToRegexp } from 'path-to-regexp';

const MOCK_KEY = '_$mockHttp';

const isBrowser = typeof window !== 'undefined';

interface MockInfo {
  isMock?: boolean;
  mockData?: Array<MockMethod>;
}

export declare interface MockMethod {
  url: string;
  method?: string;
  timeout?: number;
  statusCode?: number;
  response?: Function | object;
}

const topWindow = isBrowser ? window.top || window : {};
export const getMockInfo = (): MockInfo => {
  return ((topWindow as any)[MOCK_KEY] || {
    isMock: false,
    mockData: [],
  }) as MockInfo;
};

export const setMockInfo = (data: MockInfo) => {
  const mockInfo = getMockInfo();
  (topWindow as any)[MOCK_KEY] = Object.assign(mockInfo, data) as MockInfo;
};

export const setMockData = (data: MockMethod[]) => {
  setMockInfo({ mockData: data });
};

export const openMock = () => {
  setMockInfo({ isMock: true });
};

export const closeMock = () => {
  setMockInfo({ isMock: true });
};

if (isBrowser) {
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    const mockInfo = getMockInfo();
    if (mockInfo.isMock && args[0] !== null) {
      let req: Request;
      let reqUrl: string = '';
      if (typeof args[0] === 'object') {
        [req] = args as [Request];
        reqUrl = req.url;
      } else {
        [reqUrl, req] = args as [string, Request];
      }

      const queryParams = new URL(reqUrl, 'http://localhost');
      const matchRequest = mockInfo.mockData?.find((item: MockMethod) => {
        if (!item || !item.url) {
          return false;
        }

        if (item.method && item.method.toUpperCase() !== req?.method) {
          return false;
        }

        // 解析 mock item 的 URL
        const itemUrl = new URL(item.url, 'http://localhost');
        const itemSearchParams = new URLSearchParams(itemUrl.search);

        // 解析请求的 URL
        const reqSearchParams = new URLSearchParams(queryParams.search);

        // 检查每一个 mock item 的查询参数是否在请求的查询参数中
        for (const [key, value] of itemSearchParams) {
          if (reqSearchParams.get(key) !== value) {
            return false;
          }
        }

        return pathToRegexp(itemUrl.pathname).test(queryParams.pathname);
      });

      if (matchRequest) {
        const { response, timeout, statusCode } = matchRequest;
        if (timeout) {
          await new Promise((resolve) => {
            setTimeout(resolve, timeout);
          });
        }
        const fakeData = typeof response === 'function' ? response(req) : response;
        console.debug(`mock success: ${reqUrl}`);
        console.debug(fakeData);
        if (fakeData instanceof Response) return Promise.resolve(fakeData);
        return Promise.resolve(
          new Response(JSON.stringify(fakeData), {
            status: statusCode || 200,
            headers: { 'Content-Type': 'application/json' },
          }),
        );
      }
    }

    // @ts-ignore
    return originalFetch.apply(this, args);
  };
}
