# 自定义查询输出参数结构

## 概述

根据不同的输出配置类型，自定义查询的输出参数结构会自动调整，以便后续步骤能够正确访问数据。

## 输出参数结构

### 1. 列表类型（List）- 默认

**配置：**
```json
{
  "outputConfig": {
    "type": "list"
  }
}
```

**输出参数结构：**
```
result (array)
├── id (int)
├── name (string)
├── email (string)
└── ...其他字段
```

**访问方式：**
- `result` - 完整数组
- `result.id` - 数组中每项的 id 字段
- `result.name` - 数组中每项的 name 字段

### 2. 单条记录类型（Single）

**配置：**
```json
{
  "outputConfig": {
    "type": "single"
  }
}
```

**输出参数结构：**
```
result (object)
├── id (int)
├── name (string)
├── email (string)
└── ...其他字段
```

**访问方式：**
- `result` - 完整对象
- `result.id` - 对象的 id 字段
- `result.name` - 对象的 name 字段

### 3. 字典类型（Dictionary）

**配置：**
```json
{
  "outputConfig": {
    "type": "dictionary",
    "dictionaryConfig": {
      "keyColumns": ["eid", "oid"],
      "keySeparator": "|",
      "valueColumn": "id"
    }
  }
}
```

**输出参数结构：**
```
result (object)
```

**访问方式：**
- `result` - 完整字典对象
- `result["E001|O001"]` - 通过键访问值
- 无预定义子字段，因为键是动态的

### 4. 记录数量类型（Count）

**配置：**
```json
{
  "outputConfig": {
    "type": "count"
  }
}
```

**输出参数结构：**
```
result (int)
```

**访问方式：**
- `result` - 记录数量（整数）

### 5. 分页类型（Paging + HasTotal）

**配置：**
```json
{
  "isPaging": true,
  "hasTotal": true,
  "outputConfig": {
    "type": "any" // 此配置会被忽略
  }
}
```

**输出参数结构：**
```
result (object)
├── total (int)
└── list (array)
    ├── id (int)
    ├── name (string)
    ├── email (string)
    └── ...其他字段
```

**访问方式：**
- `result.total` - 总记录数
- `result.list` - 当前页数据数组
- `result.list.id` - 数组中每项的 id 字段

**重要说明：** 当启用分页且需要总数时，后端总是返回固定的 `{list: [], total: number}` 结构，会忽略 `outputConfig` 配置。

## 前端实现细节

### 变量生成逻辑

```typescript
// 根据输出类型生成不同的变量结构
const outputType = this.args.outputConfig?.type || 'list';

switch (outputType) {
  case 'count':
    // 返回 int 类型
    return [{
      id: 'result',
      type: 'int',
      description: '记录数量'
    }];
    
  case 'single':
    // 返回 object 类型，包含所有字段
    return [{
      id: 'result',
      type: 'object',
      description: '单条记录',
      children: columnChildren
    }];
    
  case 'dictionary':
    // 返回 object 类型，无预定义字段
    return [{
      id: 'result',
      type: 'object',
      description: '字典结果'
    }];
    
  case 'list':
  default:
    // 返回 array 类型，包含所有字段
    return [{
      id: 'result',
      type: 'array',
      description: '查询结果',
      children: columnChildren
    }];
}
```

### 动态更新机制

当用户在前端界面中切换输出类型时，系统会：

1. **监听配置变化**：
   ```typescript
   watch(
     () => formData.value.outputConfig?.type,
     () => {
       dataQueryStore.setArgs(formData.value);
     },
   );
   ```

2. **重新生成变量结构**：
   - 调用 `setArgs` 方法
   - 重新计算 `variables` getter
   - 更新输出参数面板

3. **更新根节点描述**：
   ```typescript
   switch (outputType) {
     case 'count': description = '记录数量'; break;
     case 'single': description = '单条记录'; break;
     case 'dictionary': description = '字典结果'; break;
     case 'list': description = '查询结果'; break;
   }
   ```

## 使用示例

### 在后续步骤中访问数据

#### 列表类型
```javascript
// 遍历所有记录
result.forEach(item => {
  console.log(item.name);
});

// 获取第一条记录
const firstItem = result[0];
```

#### 单条记录类型
```javascript
// 直接访问字段
console.log(result.name);
console.log(result.email);
```

#### 字典类型
```javascript
// 通过键访问值
const value = result["E001|O001"];

// 遍历所有键值对
Object.keys(result).forEach(key => {
  console.log(key, result[key]);
});
```

#### 记录数量类型
```javascript
// 直接使用数字
if (result > 0) {
  console.log(`找到 ${result} 条记录`);
}
```

## 重要说明

### 后端处理逻辑

1. **分页 + HasTotal**：总是返回 `{list: [], total: number}` 结构，**忽略** `outputConfig`
2. **分页 + 无Total**：根据 `outputConfig` 处理结果
3. **非分页**：根据 `outputConfig` 处理结果，但 `Single` 和 `Count` 有特殊优化

### 前端适配

前端输出参数结构完全匹配后端返回结构：

```typescript
// 分页且有总数时，忽略输出配置
if (hasTotal) {
  return [{
    id: 'result',
    type: 'object', // 固定为对象
    children: [
      { id: 'total', type: 'int' },
      { id: 'list', type: 'array', children: columnChildren }
    ]
  }];
}

// 其他情况根据输出配置生成结构
switch (outputType) {
  case 'count': return [{ type: 'int' }];
  case 'single': return [{ type: 'object', children: columnChildren }];
  case 'dictionary': return [{ type: 'object' }];
  case 'list': return [{ type: 'array', children: columnChildren }];
}
```

## 注意事项

1. **分页优先级**：分页且有总数时，输出配置会被忽略
2. **字段访问**：字典类型无法预定义字段访问路径，需要动态访问
3. **类型安全**：前端会根据后端实际返回结构设置正确的数据类型
4. **向后兼容**：未配置输出类型时默认使用列表类型

## 测试验证

项目包含完整的单元测试来验证不同输出类型的变量结构：

```bash
npm test -- output-variables.test.ts
```

测试覆盖：
- ✅ 列表类型变量结构
- ✅ 单条记录类型变量结构  
- ✅ 字典类型变量结构
- ✅ 记录数量类型变量结构
- ✅ 分页列表类型变量结构
- ✅ 默认行为验证
