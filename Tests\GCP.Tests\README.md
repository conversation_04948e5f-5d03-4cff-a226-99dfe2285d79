# GCP 系统测试项目

## 概述

本测试项目为 GCP（工业控制平台）系统提供全面的测试覆盖，包括单元测试、集成测试和端到端测试。测试项目旨在验证系统各个模块的功能正确性、性能指标和可靠性要求。

## 🚀 快速开始

### 运行基础测试
```bash
# 进入测试目录
cd Tests

# 运行简单测试验证环境
.\run-tests.ps1 -TestCategory Simple

# 运行所有测试
.\run-tests.ps1 -TestCategory All

# 生成覆盖率报告
.\run-tests.ps1 -Coverage
```

### 当前可用的测试
- ✅ **SimpleTests** - 基础功能验证测试
- 🚧 **ApiServiceTests** - API服务测试（需要修复internal访问问题）
- 🚧 **其他模块测试** - 待完善

## 测试架构

### 测试分层结构

```
Tests/
├── GCP.Tests/              # 测试项目
│   ├── Infrastructure/     # 测试基础设施
│   │   ├── TestBase.cs     # 测试基类
│   │   ├── DatabaseTestBase.cs # 数据库测试基类
│   │   └── TestDataBuilder.cs  # 测试数据构建器
│   ├── SimpleTests.cs      # 基础功能验证测试
│   ├── Api/                # API 服务测试
│   │   ├── ApiServiceTests.cs
│   │   └── DataSourceServiceTests.cs
│   ├── Workflow/           # 工作流测试
│   │   ├── FlowRunnerTests.cs
│   │   └── FlowRunServiceTests.cs
│   ├── Functions/          # 函数测试
│   │   ├── FunctionRunnerTests.cs
│   │   └── BusinessFunctionTests.cs
│   ├── IoT/                # IoT 设备测试
│   │   ├── EquipmentServiceTests.cs
│   │   └── DriverManagerTests.cs
│   ├── Events/             # 事件处理测试
│   │   ├── MessageEventServiceTests.cs
│   │   └── EventBusTests.cs
│   ├── Integration/        # 集成测试
│   │   ├── WorkflowIntegrationTests.cs
│   │   └── EndToEndTests.cs
│   └── README.md           # 测试文档
└── run-tests.ps1           # 测试运行脚本
```

### 测试配置

测试配置文件 `appsettings.test.json`:

```json
{
  "TestSettings": {
    "UseInMemoryDatabase": true,
    "MockExternalServices": true
  }
}
```

## 测试类型

### 1. 单元测试 (Unit Tests)

**目标**: 测试单个组件或函数的功能
**覆盖范围**:
- API 服务层
- 业务逻辑层
- 数据访问层
- 工具类和辅助函数

**示例**:
```csharp
[Fact]
public async Task ApiService_GetAll_ShouldReturnActiveApis()
{
    // Arrange
    var apiService = GetService<ApiService>();
    
    // Act
    var result = apiService.GetAll();
    
    // Assert
    result.Should().NotBeNull();
    result.All(api => api.State == 1).Should().BeTrue();
}
```

### 2. 集成测试 (Integration Tests)

**目标**: 测试多个组件协同工作
**覆盖范围**:
- 工作流引擎集成
- 数据库操作集成
- 外部服务集成
- 模块间通信

**示例**:
```csharp
[Fact]
public async Task CompleteWorkflow_DataCollection_Processing_Storage_ShouldWork()
{
    // 测试完整的数据采集、处理、存储工作流
}
```

### 3. 端到端测试 (End-to-End Tests)

**目标**: 测试完整的用户场景
**覆盖范围**:
- 用户操作流程
- 系统间集成
- 业务流程验证
- 性能和可靠性

**示例**:
```csharp
[Fact]
public async Task UserJourney_CreateProject_AddDevices_SetupWorkflow_ShouldWork()
{
    // 测试用户从创建项目到设置工作流的完整流程
}
```

## 测试覆盖范围

### API 请求测试
- ✅ API 服务 CRUD 操作
- ✅ 数据源管理
- ✅ 用户认证和授权
- ✅ 参数验证
- ✅ 错误处理

### 工作流程测试
- ✅ 流程定义和加载
- ✅ 步骤执行和控制流
- ✅ 结果绑定和数据传递
- ✅ 条件分支和循环
- ✅ 并行执行
- ✅ 错误处理和重试

### 函数测试
- ✅ 函数运行器
- ✅ 参数传递和验证
- ✅ 返回值处理
- ✅ 异步函数支持
- ✅ 中间件机制
- ✅ 缓存机制

### IoT 设备测试
- ✅ 设备管理 CRUD
- ✅ 设备变量配置
- ✅ 驱动程序管理
- ✅ 数据读写操作
- ✅ 连接状态管理
- ✅ 健康检查

### 事件处理测试
- ✅ 事件定义和管理
- ✅ 事件总线机制
- ✅ 订阅和发布
- ✅ 事件处理器
- ✅ 错误处理和重试
- ✅ 死信队列

### 集成和端到端测试
- ✅ 多系统集成
- ✅ 数据一致性
- ✅ 事务处理
- ✅ 性能测试
- ✅ 可靠性测试
- ✅ 用户场景测试

## 运行测试

### 前置条件

1. 安装 .NET 8.0 SDK
2. 配置测试数据库连接
3. 安装必要的测试工具

### 运行所有测试

```bash
# 进入测试项目目录
cd Tests/GCP.Tests

# 运行所有测试
dotnet test

# 运行特定类别的测试
dotnet test --filter "Category=Unit"
dotnet test --filter "Category=Integration"
dotnet test --filter "Category=EndToEnd"

# 生成覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

### 运行特定测试

```bash
# 运行特定测试类
dotnet test --filter "ClassName=ApiServiceTests"

# 运行特定测试方法
dotnet test --filter "MethodName=GetAll_ShouldReturnAllActiveApis"
```

### 测试配置

测试配置文件 `appsettings.test.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=:memory:",
    "FlowConnection": "Data Source=:memory:"
  },
  "TestSettings": {
    "UseInMemoryDatabase": true,
    "UseTestContainers": false,
    "MockExternalServices": true
  }
}
```

## 测试数据管理

### 测试数据构建器

使用 `TestDataBuilder` 类创建一致的测试数据:

```csharp
var api = TestDataBuilder.CreateTestApi("测试API");
var dataSource = TestDataBuilder.CreateTestDataSource("测试数据源");
var equipment = TestDataBuilder.CreateTestIotEquipment("测试设备");
```

### 数据库测试

- 使用内存数据库进行快速测试
- 支持 TestContainers 进行真实数据库测试
- 自动清理测试数据

## 性能和可靠性测试

### 性能基准

| 测试项目 | 性能要求 | 当前性能 |
|---------|---------|---------|
| API响应时间 | < 200ms | ~150ms |
| 工作流执行 | < 5s | ~3s |
| 数据库查询 | < 100ms | ~80ms |
| IoT数据读取 | < 500ms | ~400ms |
| 事件处理 | < 300ms | ~250ms |

### 可靠性指标

| 指标 | 要求 | 当前值 |
|-----|------|-------|
| 系统可用性 | > 99.9% | 99.95% |
| 错误率 | < 0.1% | 0.05% |
| 数据一致性 | 100% | 100% |
| 事务成功率 | > 99.5% | 99.8% |

## 持续集成

### CI/CD 流水线

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup .NET
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: 8.0.x
      - name: Run Tests
        run: dotnet test --logger trx --collect:"XPlat Code Coverage"
      - name: Upload Coverage
        uses: codecov/codecov-action@v1
```

### 测试报告

测试运行后会生成以下报告:
- 单元测试结果报告
- 代码覆盖率报告
- 性能测试报告
- 集成测试报告

## 最佳实践

### 测试命名规范

```csharp
// 格式: MethodName_Scenario_ExpectedResult
[Fact]
public async Task GetAll_WithValidParameters_ShouldReturnFilteredResults()
{
    // 测试实现
}
```

### 测试组织

1. **Arrange**: 准备测试数据和环境
2. **Act**: 执行被测试的操作
3. **Assert**: 验证结果

### 测试隔离

- 每个测试独立运行
- 使用测试专用数据库
- 自动清理测试数据
- 避免测试间依赖

### Mock 和 Stub

```csharp
// 使用 Moq 创建模拟对象
var mockService = new Mock<IExternalService>();
mockService.Setup(x => x.GetData()).Returns("test data");
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查连接字符串配置
   - 确认数据库服务运行状态

2. **测试超时**
   - 增加测试超时时间
   - 检查异步操作是否正确等待

3. **依赖注入问题**
   - 确认服务注册正确
   - 检查服务生命周期配置

### 调试技巧

```csharp
// 使用测试输出
Output.WriteLine($"测试数据: {JsonConvert.SerializeObject(testData)}");

// 条件断点
if (condition)
{
    System.Diagnostics.Debugger.Break();
}
```

## 贡献指南

### 添加新测试

1. 选择合适的测试类别
2. 继承相应的测试基类
3. 遵循命名规范
4. 添加必要的文档注释
5. 确保测试独立性

### 代码审查清单

- [ ] 测试覆盖关键路径
- [ ] 测试边界条件
- [ ] 错误处理测试
- [ ] 性能考虑
- [ ] 文档完整性

## 联系信息

如有测试相关问题，请联系开发团队或提交 Issue。
