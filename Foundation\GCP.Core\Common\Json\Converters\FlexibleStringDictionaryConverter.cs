using System.Text.Json;
using System.Text.Json.Serialization;

namespace GCP.Common
{
    /// <summary>
    /// 灵活的字符串字典转换器，能够将任何类型的值转换为字符串
    /// 用于处理前端传来的 JSON 中可能包含数字、布尔值等类型的情况
    /// </summary>
    public class FlexibleStringDictionaryConverter : JsonConverter<Dictionary<string, string>>
    {
        public override Dictionary<string, string> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException("Expected StartObject token");
            }

            var dictionary = new Dictionary<string, string>();

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return dictionary;
                }

                if (reader.TokenType != JsonTokenType.PropertyName)
                {
                    throw new JsonException("Expected PropertyName token");
                }

                string propertyName = reader.GetString();
                reader.Read();

                // 将任何类型的值转换为字符串
                string value = ConvertValueToString(ref reader);
                dictionary[propertyName] = value;
            }

            throw new JsonException("Expected EndObject token");
        }

        public override void Write(Utf8JsonWriter writer, Dictionary<string, string> value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();

            foreach (var kvp in value)
            {
                writer.WritePropertyName(kvp.Key);
                writer.WriteStringValue(kvp.Value);
            }

            writer.WriteEndObject();
        }

        private string ConvertValueToString(ref Utf8JsonReader reader)
        {
            return reader.TokenType switch
            {
                JsonTokenType.String => reader.GetString(),
                JsonTokenType.Number => reader.GetDecimal().ToString(),
                JsonTokenType.True => "true",
                JsonTokenType.False => "false",
                JsonTokenType.Null => null,
                JsonTokenType.StartObject => ReadObjectAsString(ref reader),
                JsonTokenType.StartArray => ReadArrayAsString(ref reader),
                _ => throw new JsonException($"Unexpected token type: {reader.TokenType}")
            };
        }

        private string ReadObjectAsString(ref Utf8JsonReader reader)
        {
            // 对于复杂对象，将其序列化为 JSON 字符串
            using var document = JsonDocument.ParseValue(ref reader);
            return document.RootElement.GetRawText();
        }

        private string ReadArrayAsString(ref Utf8JsonReader reader)
        {
            // 对于数组，将其序列化为 JSON 字符串
            using var document = JsonDocument.ParseValue(ref reader);
            return document.RootElement.GetRawText();
        }
    }
}
