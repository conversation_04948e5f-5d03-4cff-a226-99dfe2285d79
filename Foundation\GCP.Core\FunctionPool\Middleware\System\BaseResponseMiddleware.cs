﻿using GCP.Common;

namespace GCP.FunctionPool
{
    internal class BaseResponseMiddleware : IFunctionMiddleware
    {
        internal static async Task Handler(FunctionContext ctx, Func<Task> next)
        {
            var res = new ResponseBase();
            try
            {
                await next.Invoke();
                res.Data = ctx.Current.Result;
            }
            catch (CustomException ex)
            {
                res.Code = ex.Code ?? 500;
                res.Message = ex.Message;
            }
            catch (Exception ex)
            {
                res.Code = 500;
                res.Message = ex.Message;
            }

            ctx.Result = res;
        }
    }
}
