using GCP.Common;
using GCP.Functions.Common.ScriptExtensions;
using GCP.Functions.Common.Attributes;
using System.Reflection;
using System.Text.Json;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// Utils工具函数服务
    /// </summary>
    [Function("utilsFunction", "Utils工具函数服务")]
    internal class UtilsFunctionService : BaseService
    {
        /// <summary>
        /// 获取所有Utils工具函数列表
        /// </summary>
        /// <returns>函数列表</returns>
        [Function("getAll", "获取所有Utils工具函数")]
        public List<object> GetAll()
        {
            try
            {
                var functions = new List<object>();
                var utilsType = typeof(JavascriptUtils);
                var methods = utilsType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Where(m => !m.IsSpecialName && m.DeclaringType == utilsType);

                foreach (var method in methods)
                {
                    // 获取UtilsFunction属性
                    var utilsFunctionAttr = method.GetCustomAttribute<UtilsFunctionAttribute>();

                    // 如果没有属性，跳过该方法
                    if (utilsFunctionAttr == null) continue;

                    var parameters = method.GetParameters().Select(p =>
                    {
                        // 获取参数描述属性
                        var paramDescAttr = p.GetCustomAttribute<ParameterDescriptionAttribute>();
                        var description = paramDescAttr?.Description ?? GetParameterDescription(p.Name);

                        // 检查是否是可变长度参数 (params)
                        var isVariadic = p.GetCustomAttribute<ParamArrayAttribute>() != null;

                        return new
                        {
                            name = p.Name,
                            type = GetParameterTypeName(p.ParameterType),
                            required = !p.HasDefaultValue,
                            defaultValue = p.HasDefaultValue ? p.DefaultValue : null,
                            description = description,
                            isVariadic = isVariadic
                        };
                    }).ToList();

                    // 获取使用示例
                    var examples = GetFunctionExamples(method, utilsFunctionAttr);

                    functions.Add(new
                    {
                        value = method.Name,
                        label = utilsFunctionAttr.DisplayName,
                        script = GenerateFunctionScript(method.Name, method.GetParameters()),
                        remark = utilsFunctionAttr.Description,
                        category = utilsFunctionAttr.Category,
                        categoryDisplayName = utilsFunctionAttr.CategoryDisplayName,
                        parameters = parameters,
                        returnType = GetParameterTypeName(method.ReturnType),
                        outputType = utilsFunctionAttr.OutputType ?? GetParameterTypeName(method.ReturnType),
                        examples = examples
                    });
                }

                return functions.OrderBy(f => ((dynamic)f).category).ThenBy(f => ((dynamic)f).label).ToList();
            }
            catch (Exception ex)
            {
                throw new CustomException($"获取Utils工具函数列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 按分类获取Utils工具函数
        /// </summary>
        /// <returns>按分类分组的函数列表</returns>
        [Function("getByCategory", "按分类获取Utils工具函数")]
        public List<object> GetByCategory()
        {
            try
            {
                var allFunctions = GetAll();

                // 从函数中动态获取分类信息
                var categoryGroups = allFunctions
                    .GroupBy(f => new {
                        Category = ((dynamic)f).category,
                        CategoryDisplayName = ((dynamic)f).categoryDisplayName
                    })
                    .ToList();

                var result = new List<object>();
                foreach (var group in categoryGroups)
                {
                    result.Add(new
                    {
                        value = group.Key.Category,
                        label = group.Key.CategoryDisplayName,
                        children = group.ToList()
                    });
                }

                // 按分类名称排序
                return result.OrderBy(r => ((dynamic)r).label).ToList();
            }
            catch (Exception ex)
            {
                throw new CustomException($"按分类获取Utils工具函数失败: {ex.Message}", ex);
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取参数类型名称
        /// </summary>
        private string GetParameterTypeName(Type type)
        {
            if (type == typeof(string)) return "string";
            if (type == typeof(int) || type == typeof(int?)) return "number";
            if (type == typeof(double) || type == typeof(double?)) return "number";
            if (type == typeof(bool) || type == typeof(bool?)) return "boolean";
            if (type == typeof(DateTime) || type == typeof(DateTime?)) return "DateTime";
            if (type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>))) return "array";
            if (type == typeof(object) || type.Name.Contains("Dictionary")) return "object";
            return "any";
        }

        /// <summary>
        /// 获取参数描述
        /// </summary>
        private string GetParameterDescription(string paramName)
        {
            return paramName switch
            {
                "input" => "Input string",
                "array" => "Input array",
                "dict" => "Dictionary object",
                "date" => "Date object",
                "date1" => "First date",
                "date2" => "Second date",
                "value" => "Input value",
                "value1" => "First value",
                "value2" => "Second value",
                "condition" => "Boolean condition",
                "trueValue" => "Value when condition is true",
                "falseValue" => "Value when condition is false",
                "key" => "Dictionary key",
                "time" => "Time object",
                "jsonString" => "JSON string",
                "dateString" => "Date string",
                "timestamp" => "Unix timestamp",
                "separator" => "Separator character",
                "format" => "Format string",
                "startIndex" => "Start position",
                "length" => "Length",
                "oldValue" => "Value to replace",
                "newValue" => "New value",
                "defaultValue" => "Default value",
                "digits" => "Decimal places",
                "unit" => "Time unit",
                "groupFields" => "Group fields array",
                "fields" => "Other fields array",
                "childField" => "Child field name",
                "dict1" => "First dictionary",
                "dict2" => "Second dictionary",
                "operator_" => "Math operator",
                "sqlString" => "SQL query string",
                "args" => "Query parameters",
                _ => $"{paramName} parameter"
            };
        }

        /// <summary>
        /// 生成函数脚本
        /// </summary>
        private string GenerateFunctionScript(string functionName, ParameterInfo[] parameters)
        {
            var paramList = parameters.Select(p =>
            {
                if (p.HasDefaultValue)
                {
                    var defaultValue = p.DefaultValue?.ToString() ?? "null";
                    if (p.ParameterType == typeof(string) && p.DefaultValue != null)
                        defaultValue = $"\"{defaultValue}\"";
                    return p.Name;
                }
                return p.Name;
            });

            return $"Utils.{functionName}({string.Join(", ", paramList)})";
        }

        /// <summary>
        /// 获取函数使用示例
        /// </summary>
        private List<object> GetFunctionExamples(MethodInfo method, UtilsFunctionAttribute utilsFunctionAttr)
        {
            var examples = new List<object>();

            // 1. 从 FunctionExample 属性获取示例
            var exampleAttrs = method.GetCustomAttributes<FunctionExampleAttribute>();
            foreach (var exampleAttr in exampleAttrs)
            {
                examples.Add(new
                {
                    title = exampleAttr.Title,
                    code = exampleAttr.Code,
                    result = exampleAttr.Result,
                    description = exampleAttr.Description
                });
            }

            // 2. 从 UtilsFunction 属性的 Examples 字段获取示例
            if (!string.IsNullOrEmpty(utilsFunctionAttr.Examples))
            {
                try
                {
                    var jsonExamples = JsonSerializer.Deserialize<List<object>>(utilsFunctionAttr.Examples);
                    if (jsonExamples != null)
                    {
                        examples.AddRange(jsonExamples);
                    }
                }
                catch (JsonException ex)
                {
                    // 如果JSON解析失败，记录错误但不影响其他功能
                    Console.WriteLine($"解析函数 {method.Name} 的示例JSON失败: {ex.Message}");
                }
            }

            // 3. 如果没有定义示例，生成默认示例
            if (examples.Count == 0)
            {
                examples.AddRange(GenerateDefaultExamples(method));
            }

            return examples;
        }

        /// <summary>
        /// 生成默认使用示例
        /// </summary>
        private List<object> GenerateDefaultExamples(MethodInfo method)
        {
            var examples = new List<object>();
            var parameters = method.GetParameters();

            // 基本用法示例
            examples.Add(new
            {
                title = "基本用法",
                code = GenerateFunctionScript(method.Name, parameters),
                result = (string)null,
                description = "基本调用方式"
            });

            // 如果有参数，生成详细示例
            if (parameters.Length > 0)
            {
                var exampleParams = parameters.Select(p => GenerateExampleValue(p)).ToArray();
                var detailedCode = $"Utils.{method.Name}({string.Join(", ", exampleParams)})";

                examples.Add(new
                {
                    title = "详细示例",
                    code = detailedCode,
                    result = (string)null,
                    description = "带具体参数的调用示例"
                });
            }

            return examples;
        }

        /// <summary>
        /// 为参数生成示例值
        /// </summary>
        private string GenerateExampleValue(ParameterInfo parameter)
        {
            var type = parameter.ParameterType;
            var name = parameter.Name.ToLower();

            // 根据参数名称生成特定示例
            if (name.Contains("format"))
                return "\"yyyy-MM-dd HH:mm:ss\"";
            if (name.Contains("separator"))
                return "\",\"";
            if (name.Contains("unit"))
                return "\"days\"";

            // 根据类型生成示例
            if (type == typeof(string))
                return $"\"{parameter.Name}示例\"";
            if (type == typeof(int) || type == typeof(int?))
                return "123";
            if (type == typeof(double) || type == typeof(double?) || type == typeof(decimal) || type == typeof(decimal?))
                return "123.45";
            if (type == typeof(bool) || type == typeof(bool?))
                return "true";
            if (type == typeof(DateTime) || type == typeof(DateTime?))
                return "new Date()";
            if (type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>)))
                return "[1, 2, 3]";
            if (type == typeof(object) || type.Name.Contains("Dictionary"))
                return "{ key: \"value\" }";

            // 如果有默认值，使用默认值
            if (parameter.HasDefaultValue)
            {
                var defaultValue = parameter.DefaultValue;
                if (defaultValue == null)
                    return "null";
                if (type == typeof(string))
                    return $"\"{defaultValue}\"";
                return defaultValue.ToString();
            }

            return parameter.Name;
        }

        #endregion
    }
}
