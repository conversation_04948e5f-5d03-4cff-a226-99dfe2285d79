using System.Buffers;
using System.Text.Json;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using MQTTnet;
using MQTTnet.Protocol;
using Serilog;

namespace GCP.Eventbus.Providers
{
    class MqttMessageBus : MessageBusBase
    {
        private IMqttClient _client;
        private readonly Dictionary<string, List<Func<MqttApplicationMessageReceivedEventArgs, Task>>> _handlers;
        private readonly SemaphoreSlim _connectionLock = new SemaphoreSlim(1, 1);

        public override bool IsConnected => _client?.IsConnected == true;

        public MqttMessageBus(MessageBusOptions options)
            : base(options)
        {
            _handlers = new Dictionary<string, List<Func<MqttApplicationMessageReceivedEventArgs, Task>>>();
        }

        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (IsConnected) return;

                var factory = new MqttClientFactory();
                _client = factory.CreateMqttClient();

                _client.ApplicationMessageReceivedAsync += HandleMessageReceived;
                _client.DisconnectedAsync += HandleDisconnected;
            
                var host = Options.Settings.GetValueOrDefault("Host", "localhost");
                var port = int.Parse(Options.Settings.GetValueOrDefault("Port", "1883"));

                var options = new MqttClientOptionsBuilder()
                        .WithTcpServer(host, port)
                        .WithClientId(Options.Settings.GetValueOrDefault("ClientId", $"GCP.Eventbus_{TUID.DeviceID()}"))
                        .WithCredentials(
                            Options.Settings.GetValueOrDefault("Username", ""),
                            Options.Settings.GetValueOrDefault("Password", ""))
                        .WithCleanSession()
                        .Build();


                await _client!.ConnectAsync(options, cancellationToken);
                Log.Information("Connected to MQTT broker: {Host}:{Port}", host, port);
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected) return;

                _handlers.Clear();
                
                await _client!.DisconnectAsync(cancellationToken: cancellationToken);
                Log.Information("Disconnected from MQTT broker");
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var envelope = new MessageEnvelope
            {
                Payload = message,
            };
            if (headers is not null) envelope.Headers = headers;

            var json = JsonHelper.Serialize(envelope);
            var mqttMessage = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(json)
                .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                .WithRetainFlag(false)
                .Build();

            await _client!.PublishAsync(mqttMessage, cancellationToken);
            Log.Debug("Published message {MessageId} to {Topic}", envelope.MessageId, topic);
        }

        public override async Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var msgArr = messages.ToArray();
            foreach (var message in msgArr)
            {
                var envelope = new MessageEnvelope
                {
                    Payload = message,
                };
                if (headers is not null) envelope.Headers = headers;

                var json = JsonSerializer.Serialize(envelope);
                var mqttMessage = new MqttApplicationMessageBuilder()
                    .WithTopic(topic)
                    .WithPayload(json)
                    .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                    .WithRetainFlag(false)
                    .Build();

                await _client!.PublishAsync(mqttMessage, cancellationToken);
            }

            Log.Debug("Published {Count} messages to {Topic}", msgArr.Count(), topic);
        }

        public override async Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                if (!_handlers.ContainsKey(topic))
                {
                    _handlers[topic] = new List<Func<MqttApplicationMessageReceivedEventArgs, Task>>();
                }

                _handlers[topic].Add(async args =>
                {
                    try
                    {
                        var payload = args.ApplicationMessage.Payload;
                        if (payload.IsEmpty) return;
                        var json = System.Text.Encoding.UTF8.GetString(payload.ToArray());
                        var envelope = JsonSerializer.Deserialize<MessageEnvelope>(json);
                        if (envelope != null)
                        {
                            await handler(envelope, cancellationToken).ConfigureAwait(false);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error processing message from {Topic}", topic);
                    }
                });
            
                var mqttOptions = new MqttTopicFilterBuilder()
                    .WithTopic(topic)
                    .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                    .Build();

                await _client!.SubscribeAsync(mqttOptions, cancellationToken: cancellationToken);
                Log.Information("Subscribed to {Topic}", topic);
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public override async Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            await _connectionLock.WaitAsync(cancellationToken);
            try
            {
                _handlers.Remove(topic);
                await _client!.UnsubscribeAsync(topic, cancellationToken: cancellationToken);
                Log.Information("Unsubscribed from {Topic}", topic);
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        private Task HandleMessageReceived(MqttApplicationMessageReceivedEventArgs args)
        {
            var topic = args.ApplicationMessage.Topic;
            if (_handlers.TryGetValue(topic, out var handlers))
            {
                var handlingTasks = handlers.Select(t => t(args));

                _ = Task.WhenAll(handlingTasks).ContinueWith(task =>
                {
                    if (task.IsFaulted)
                    {
                        Log.Error(task.Exception, "One or more handlers failed while processing a message for topic {Topic}.", topic);
                    }
                }, TaskContinuationOptions.OnlyOnFaulted);
            }
            return Task.CompletedTask;
        }

        private Task HandleDisconnected(MqttClientDisconnectedEventArgs args)
        {
            Log.Warning("Disconnected from MQTT broker: {Reason}", args.Reason);
            return Task.CompletedTask;
        }

        protected override async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            try
            {
                await _connectionLock.WaitAsync();
                try
                {
                    _handlers.Clear();
                    if (_client?.IsConnected == true)
                    {
                        await _client.DisconnectAsync();
                    }
                    _client?.Dispose();
                }
                finally
                {
                    _connectionLock.Release();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error disposing MQTT client");
            }

            _connectionLock.Dispose();
            await base.DisposeAsyncCore();
        }
    }
}