using LinqToDB.Mapping;

namespace GCP.DataAccess
{
    /// <summary>
    /// 在线函数管理
    /// </summary>
    [Table("lc_online_function")]
    public class LcOnlineFunction : IBaseEntity
    {
        /// <summary>
        /// Description:数据行 ID 号
        /// </summary>
        [Column("ID", CanBeNull = false, IsPrimaryKey = true)] public string Id { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:创建时间
        /// </summary>
        [Column("TIME_CREATE")] public DateTime TimeCreate { get; set; } // datetime
        /// <summary>
        /// Description:创建人
        /// </summary>
        [Column("CREATOR", CanBeNull = false)] public string Creator { get; set; } = null!; // varchar(80)
        /// <summary>
        /// Description:更新时间
        /// </summary>
        [Column("TIME_MODIFIED")] public DateTime? TimeModified { get; set; } // datetime
        /// <summary>
        /// Description:更新数据行的用户
        /// </summary>
        [Column("MODIFIER")] public string? Modifier { get; set; } // varchar(80)
        /// <summary>
        /// Description:可用状态
        /// </summary>
        [Column("STATE", CanBeNull = false)] public short State { get; set; } // smallint
        /// <summary>
        /// Description:解决方案 ID
        /// </summary>
        [Column("SOLUTION_ID", CanBeNull = false)] public string SolutionId { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:项目 ID
        /// </summary>
        [Column("PROJECT_ID", CanBeNull = false)] public string ProjectId { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:函数名称
        /// </summary>
        [Column("NAME", CanBeNull = false)] public string Name { get; set; } = null!; // varchar(200)
        /// <summary>
        /// Description:函数类型 javascript、csharp
        /// </summary>
        [Column("FUNCTION_TYPE", CanBeNull = false)] public string FunctionType { get; set; } = null!; // varchar(50)
        /// <summary>
        /// Description:函数描述
        /// </summary>
        [Column("DESCRIPTION")] public string? Description { get; set; } // varchar(500)
        /// <summary>
        /// Description:函数代码
        /// </summary>
        [Column("CODE", CanBeNull = false)] public string Code { get; set; } = null!; // text
        /// <summary>
        /// Description:函数状态 active、inactive
        /// </summary>
        [Column("STATUS", CanBeNull = false)] public string Status { get; set; } = "active"; // varchar(20)
        /// <summary>
        /// Description:函数版本
        /// </summary>
        [Column("VERSION", CanBeNull = false)] public int Version { get; set; } = 1; // int
        /// <summary>
        /// Description:输入参数JSON定义
        /// </summary>
        [Column("INPUT_PARAMETERS_JSON")] public string? InputParametersJson { get; set; } // text
        /// <summary>
        /// Description:输出格式JSON定义
        /// </summary>
        [Column("OUTPUT_FORMAT_JSON")] public string? OutputFormatJson { get; set; } // text
    }
}
