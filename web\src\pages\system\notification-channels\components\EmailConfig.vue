<template>
  <div class="email-config">
    <t-form-item label="SMTP服务器" name="host">
      <t-input v-model="config.host" placeholder="请输入SMTP服务器地址" />
    </t-form-item>

    <t-form-item label="端口" name="port">
      <t-input-number v-model="config.port" :min="1" :max="65535" />
    </t-form-item>

    <t-form-item label="发送人名称" name="fromName">
      <t-input v-model="config.fromName" placeholder="请输入发送人名称" />
    </t-form-item>

    <t-form-item label="发送人邮箱" name="fromAddress">
      <t-input v-model="config.fromAddress" placeholder="请输入发送人邮箱地址" />
    </t-form-item>

    <t-form-item label="邮箱密码" name="password">
      <t-input v-model="config.password" type="password" placeholder="请输入邮箱密码或授权码" />
    </t-form-item>

    <t-form-item label="启用SSL" name="enableSsl">
      <t-switch v-model="config.enableSsl" />
    </t-form-item>

    <t-form-item label="收件人列表" name="toAddress">
      <div class="email-list">
        <div v-for="(email, index) in config.toAddress" :key="index" class="email-item">
          <t-input v-model="config.toAddress[index]" placeholder="请输入邮箱地址" />
          <t-button theme="danger" variant="text" @click="removeEmail(index)">
            删除
          </t-button>
        </div>
        <t-button theme="primary" variant="text" @click="addEmail">
          添加邮箱
        </t-button>
      </div>
    </t-form-item>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
const props = defineProps<{
  modelValue: {
    host: string;
    port: number;
    fromName: string;
    fromAddress: string;
    password: string;
    toAddress: string[];
    enableSsl: boolean;
  };
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
}>();

// 计算属性
const config = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const addEmail = () => {
  config.value.toAddress.push('');
};

const removeEmail = (index: number) => {
  config.value.toAddress.splice(index, 1);
};
</script>

<style scoped>
.email-config {
  padding: 10px 0;
}

.email-list {
  width: 100%;
}

.email-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.email-item .t-input {
  flex: 1;
}
</style>
