﻿using GCP.Common;
using GCP.DataAccess;
using Microsoft.Extensions.Configuration;
using Serilog;
using System.Diagnostics;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class DbExtensions
    {
        public static IServiceCollection AddDbContext(this IServiceCollection services, IConfiguration configuration)
        {
            var logLevel = configuration["Serilog:MinimumLevel:Default"];
            if (logLevel == "Debug" || Debugger.IsAttached)
            {
                DbDebug.IsOpen = true;
                DbDebug.SqlExecuteCompleteAction = (info, result) =>
                {
                    var infoStr = info.ToString();
                    //Debug.WriteLine(infoStr);
                    Log.Logger.Debug(infoStr);
                    return Task.CompletedTask;
                };
            }

            DbConfigListener configListener = new DbConfigListener(configuration);
            configListener.UpdateDbSettings();

            // V2 版本才行
            //var configSvc = services.BuildServiceProvider().GetService<INacosConfigService>();
            //configuration.GetSection("nacos:Listeners").GetChildren().ToList().ForEach(t =>
            //{
            //    configSvc.AddListener(t.GetValue<string>("DataId"), t.GetValue<string>("Group"), configListener);
            //});

            services.AddSingleton<IDbContext, DbContext>();


            //var db = new DbContext("Data Source=scm_gcp.db", DbProviderType.DuckDB);

            //var oldDb = new DbContext("Server=172.20.215.109;Database=scm_gcp;User=root;Password=********;Charset=utf8;Convert Zero Datetime=True;Port=3306;SslMode=None;Allow User Variables=True;AllowLoadLocalInfile=true;AllowPublicKeyRetrieval=True", DbProviderType.MySql);

            //var tables = oldDb.Sql("select TABLE_NAME from information_schema.tables where TABLE_SCHEMA ='scm_gcp'").GetList<string>();

            //Stopwatch sw = new Stopwatch();
            //sw.Start();
            //Parallel.ForEach(tables, table =>
            //{
            //    using var oldConn = oldDb.CreateOpenConnection();
            //    using var conn = db.CreateOpenConnection();

            //    var reader = oldConn.Sql("select * from " + table).ExecuteReader();
            //    conn.BulkCopy(table, reader);
            //    Log.Information("Copy table {Table} to new db elapsed time: {ElapsedTime}", table, sw.Elapsed);
            //});
            //sw.Stop();
            //Log.Information("Copy data from old db to new db, elapsed time: {ElapsedTime}", sw.Elapsed);

            return services;
        }
    }
}
