import EventEmitter from 'eventemitter3';

import Dictionary from './dictionary';

class TargetEmitter extends EventEmitter {}

const BUS_KEY = '_ipc';

/**
 * 目标事件实例
 */
export class EventTarget {
  /**
   * 事件触发器
   */
  emitter: TargetEmitter;

  /**
   * iframe窗口对象
   */
  contentWindow: Window | null = null;

  /**
   * 是否跨域
   */
  isCrossOrigin: boolean = false;

  constructor(win: Window | null = null) {
    this.emitter = new TargetEmitter();
    if (win) {
      try {
        // eslint-disable-next-line no-unused-expressions
        win.document;
      } catch {
        this.isCrossOrigin = true;
      }
      this.contentWindow = win;
    }
  }

  /**
   * 执行监听器
   * @param event 事件名
   * @param args 参数
   */
  send(event: string | symbol, args: any) {
    if (this.isCrossOrigin && this.contentWindow) {
      this.contentWindow.postMessage(
        {
          event,
          args,
        },
        '*',
      );
    } else {
      if (this.contentWindow) {
        const ipc = (this.contentWindow as any)[BUS_KEY];
        if (ipc) {
          ipc.send(event, args);
          return;
        }
      }

      this.emitter.emit(event, args);
    }
  }
}

/**
 * 事件总线（支持Nodejs和浏览器）
 */
export default class EventBus {
  /**
   * 目标监听器清单
   */
  targets: Dictionary<EventTarget>;

  sendingEvents: Set<string | symbol> = new Set();

  constructor() {
    this.targets = new Dictionary<EventTarget>();
  }

  static init(): EventBus {
    const ipc = new EventBus();
    if (typeof window !== 'undefined') {
      const winIpc = (window as any)[BUS_KEY];
      if (winIpc) return winIpc;
      (window as any)[BUS_KEY] = ipc;
    }
    return ipc;
  }

  /**
   * 添加目标监听器
   * @param key 目标名称
   */
  addTarget(key: string, win: Window | null = null) {
    const target = this.targets.items[key];
    if (target) {
      target.contentWindow = win;
      return;
    }
    this.targets.add(key, new EventTarget(win));
  }

  /**
   * 删除目标监听器
   * @param key 目标名称
   */
  removeTarget(key: string) {
    this.targets.remove(key);
  }

  /**
   * 删除指定事件所有监听器
   * @param event 事件名
   */
  remove(event: string): this;

  /**
   * 删除指定事件注册监听器
   * @param event 事件名
   * @param listener 监听器函数
   */
  remove(event: string, listener: (args: any) => void): this;

  /**
   * 删除指定事件注册监听器
   * @param event 事件名
   * @param sourceName 来源名
   * @param listener 监听器函数
   */
  remove(event: string, sourceName: string, listener: (args: any) => void): this;

  remove(event: string, sourceName?: string | ((args: any) => void), listener?: (args: any) => void): this {
    if (typeof sourceName === 'function') {
      listener = sourceName;
      sourceName = '*';
    }
    if (sourceName && listener) {
      this.targets.get(sourceName)?.emitter.removeListener(event, listener);
    } else {
      this.targets.map((_key, target) => {
        target.emitter.removeAllListeners(event);
        return target;
      });
    }

    return this;
  }

  /**
   * 指定事件注册监听器
   * @param event 事件名
   * @param listener 监听器函数
   */
  on(event: string, listener: (args: any) => void): this;

  /**
   * 指定事件注册监听器
   * @param event 事件名
   * @param sourceName 来源名
   * @param listener 监听器函数
   */
  on(event: string, sourceName: string, listener: (args: any) => void): this;

  on(event: string, sourceName: string | ((args: any) => void), listener?: (args: any) => void): this {
    if (typeof sourceName === 'function') {
      listener = sourceName;
      sourceName = '*';
    }

    if (listener) {
      this.addTarget(sourceName);
      this.targets.get(sourceName)?.emitter.on(event, listener);
    }

    return this;
  }

  /**
   * 指定事件注册一个单次监听器，触发后立即解除
   * @param event 事件名
   * @param listener 监听器函数
   */
  once(event: string, listener: (args: any) => void): this;

  /**
   * 指定事件注册一个单次监听器，触发后立即解除
   * @param event 事件名
   * @param sourceName 来源名
   * @param listener 监听器函数
   */
  once(event: string, sourceName: string, listener: (args: any) => void): this;

  once(event: string, sourceName: string | ((args: any) => void), listener?: (args: any) => void): this {
    if (typeof sourceName === 'function') {
      listener = sourceName;
      sourceName = '*';
    }

    if (listener) {
      this.addTarget(sourceName);
      this.targets.get(sourceName)?.emitter.once(event, listener);
    }

    return this;
  }

  /**
   * 执行所有监听器
   * @param event 事件名
   * @param args 参数
   */
  send(event: string | symbol, args: any | null) {
    if (this.sendingEvents.has(event)) {
      // 如果事件已经在发送中，直接返回以防止循环
      return;
    }

    this.sendingEvents.add(event);
    this.targets.map((_key, target) => {
      target.send(event, args);
      return target;
    });
    this.sendingEvents.delete(event);
  }
}
