import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowControl, FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { recursionMergeVariable } from '@/components/action-panel/utils';

import { ArgsInfo } from './model';

const control: FlowControl = {
  forEach: {
    async: false,
    item: [],
    list: '',
    nextId: '',
  },
};
export type forEachInfo = typeof control.forEach;

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      description: '',
      dataSource: null,
    } as ArgsInfo),
    ...(actionFlowStore.currentStep.args || {}),
  }) as ArgsInfo;

  state.forEachData = cloneDeep(actionFlowStore.currentStep.control?.forEach || control.forEach) as forEachInfo;
};

export const useForEachStore = defineStore('ForEach', {
  state: () => {
    const state = { args: null, forEachData: null } as { args: ArgsInfo; forEachData: forEachInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      const actionFlowStore = useActionFlowStore();
      let sourceDataChildren = [];
      if (this.args.dataSource && this.args.dataSource.variableValue) {
        sourceDataChildren = cloneDeep(actionFlowStore.getVariableData(this.args.dataSource.variableValue)?.children);
      }
      const itemKey = 'result';

      return [
        // {
        //   id: 'result',
        //   key: 'result',
        //   description: '结果',
        //   type: 'object',
        // },
        {
          id: 'result',
          key: 'result',
          description: '数据项',
          type: 'object',
          children:
            sourceDataChildren?.map((item: FlowData) => {
              const itemPath = `${itemKey}.${item.key}`;
              item.isCustomize = false;
              item.value = {
                type: 'variable',
                dataType: item.type,
                variableType: 'current',
                variableValue: itemPath,
              };
              return item;
            }) || [],
        },
      ] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      this.setForEachData();
    },
    setForEachData() {
      const actionFlowStore = useActionFlowStore();
      const itemParam: FlowData = {
        id: 'item',
        key: 'item',
        description: '数据项',
        type: 'object',
        value: {
          type: 'variable',
          variableType: 'current',
          variableValue: 'result',
        },
      };

      itemParam.children = this.variables.find((item) => item.id === 'result')?.children;

      if (this.forEachData?.item && this.forEachData.item.length > 0) {
        const tempItemData = cloneDeep(this.forEachData.item);
        recursionMergeVariable(tempItemData, [itemParam]);
        this.forEachData.item = tempItemData;
      } else {
        this.forEachData.item = [itemParam];
      }

      actionFlowStore.setCurrentControl({ forEach: this.forEachData });
    },
  },
});
