﻿namespace GCP.Common
{
    [AttributeUsage(AttributeTargets.Assembly | AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class JobAttribute : Attribute
    {
        /// <summary>
        /// 定时任务Id
        /// </summary>
        public string JobId { get; set; }
        /// <summary>
        /// 定时任务cron表达式
        /// </summary>
        public string JobCron { get; set; }
        /// <summary>
        /// 任务名称
        /// </summary>
        public string JobName { get; set; }
        /// <summary>
        /// 任务描述
        /// </summary>
        public string Description { get; set; }

        public string Path { get; set; }

        public JobAttribute() { }

        public JobAttribute(string jobId, string jobCron, string jobName = "", string description = "")
        {
            JobId = jobId;
            JobCron = jobCron;
            JobName = jobName;
            Description = description;
        }
    }
}
