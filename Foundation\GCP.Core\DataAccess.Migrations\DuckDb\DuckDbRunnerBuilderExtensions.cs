﻿using FluentMigrator.Runner;
using FluentMigrator;
using Microsoft.Extensions.DependencyInjection;
using GCP.Core.DataAccess.Migrations.Processors.DuckDb;
using GCP.Core.DataAccess.Migrations.Generators.DuckDb;
using FluentMigrator.Runner.Processors;
using Microsoft.Extensions.Options;

namespace GCP.Core.DataAccess.Migrations.DuckDb
{
    public static class DuckDbRunnerBuilderExtensions
    {
        public static IMigrationRunnerBuilder AddDuckDb(this IMigrationRunnerBuilder builder)
        {
            builder.Services
                .AddScoped<DuckDbProcessor>()
                .AddScoped<IMigrationProcessor>(sp => sp.GetRequiredService<DuckDbProcessor>())
                .AddScoped<IDuckDbTypeMap>(sp => new DuckDbTypeMap())
                .AddScoped<DuckDbGenerator>()
                .AddScoped<IMigrationGenerator>(sp => sp.GetRequiredService<DuckDbGenerator>());

            return builder.AddCommonDuckDbServices();
        }

        private static IMigrationRunnerBuilder AddCommonDuckDbServices(this IMigrationRunnerBuilder builder)
        {
            builder.Services
                .AddScoped(
                    sp =>
                    {
                        var processorOptions = sp.GetRequiredService<IOptionsSnapshot<ProcessorOptions>>();
                        return DuckDbOptions.ParseProviderSwitches(processorOptions.Value.ProviderSwitches);
                    })
                .AddScoped<DuckDbFactory>()
                .AddScoped<DuckDbQuoter>();
            return builder;
        }
    }
}
