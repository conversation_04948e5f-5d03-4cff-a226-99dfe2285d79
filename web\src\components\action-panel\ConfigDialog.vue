<template>
  <t-dialog v-model:visible="showFlowConfigDialog" header="动作配置" width="80%" top="72px" :footer="false">
    <t-tabs default-value="online">
      <t-tab-panel value="online" label="在线编辑">
        <t-space style="margin: 8px 0">
          <t-button @click="onConfirmConfig">加载配置</t-button>
        </t-space>
        <editor ref="editorRef" v-model:value="flowDataJson" language="json" style="height: 480px"></editor>
      </t-tab-panel>
      <t-tab-panel value="offline" label="导入/导出">
        <div class="config-upload-export">
          <t-space size="large">
            <t-button @click="onExportConfig">
              <template #icon><cloud-download-icon /></template>
              导出配置</t-button
            >

            <t-upload :request-method="onImportConfig" theme="custom" accept=".gcp">
              <t-button theme="default">
                <template #icon><cloud-upload-icon /></template>

                导入配置</t-button
              >
            </t-upload>
          </t-space>
        </div>
      </t-tab-panel>
    </t-tabs>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'ConfigDialog',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { CloudDownloadIcon, CloudUploadIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin, UploadProps } from 'tdesign-vue-next';
import { ref, watch } from 'vue';

import Editor from '@/components/editor/index.vue';

import { FlowInfo } from './model';
import { useActionFlowStore } from './store/index';

const actionFlowStore = useActionFlowStore();
const { showFlowConfigDialog, flowInfo } = storeToRefs(actionFlowStore);

const flowDataJson = ref('');
const setFlowDataJson = () => {
  const data: FlowInfo = JSON.parse(JSON.stringify(flowInfo.value));
  delete data.id;
  delete data.version;
  flowDataJson.value = JSON.stringify(data, null, 2);
};

watch(
  () => showFlowConfigDialog.value,
  (newValue) => {
    if (newValue) {
      setFlowDataJson();
    }
  },
);

const onConfirmConfig = () => {
  showFlowConfigDialog.value = false;
  saveConfig(flowDataJson.value);
};

const onExportConfig = () => {
  const jsonData = flowDataJson.value;
  exportConfig(jsonData);
};

const onImportConfig: UploadProps['requestMethod'] = (file) => {
  return new Promise((resolve) => {
    const fileToImport = Array.isArray(file) ? file[0] : file;
    if (fileToImport) importConfig(fileToImport.raw);
    resolve({ status: 'success', response: {} });
  });
};

const saveConfig = (json: string) => {
  const flow: FlowInfo = {
    data: [],
    body: [],
    ...JSON.parse(json),
  };
  flowInfo.value.data = flow.data;
  flowInfo.value.body = flow.body;
  actionFlowStore.setCurrentStep(flow.body[0] || null);
};

const exportConfig = (jsonData, filename = null) => {
  if (!jsonData) {
    MessagePlugin.error('导出配置失败，请重新打开配置面板，加载配置。');
    return;
  }
  const a = document.createElement('a');
  const blob = new Blob([jsonData], { type: 'application/json' });
  a.href = URL.createObjectURL(blob);
  a.download = filename || `${flowInfo.value.id}.config.gcp`;
  a.click();
};

const importConfig = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    const json = e.target?.result as string;
    if (!json) {
      MessagePlugin.error('导入配置失败，请检查文件格式和内容是否正确。');
      return;
    }
    saveConfig(json);
    MessagePlugin.success('导入配置成功');
    showFlowConfigDialog.value = false;
  };
  reader.readAsText(file);
};
</script>
<style lang="less" scoped>
.config-upload-export {
  padding: 32px;
  text-align: center;
  border: 1px dashed var(--td-component-border);
  margin-top: var(--td-comp-margin-xxl);
  transition: border 0.2s linear;
  &:hover {
    border-color: var(--td-brand-color);
    transition: border-color 0.2s linear;
  }
}
</style>
