﻿using System.Text;

namespace GCP.Common
{
    /// <summary>
    /// 常用扩展方法
    /// </summary>
    static class ExtensionHelper
    {
        private static Type stringType = typeof(string);
        /// <summary>
        /// 是否是C#基础类型
        /// </summary>
        public static bool IsPrimitiveType(this Type t)
        {
            return t.IsPrimitive || t.IsValueType || t == stringType;
        }

        /// <summary>
        /// 枚举器成员, 根据分隔符合并成字符串
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <param name="separator"></param>
        /// <returns></returns>
        public static string Join<T>(this IEnumerable<T> value, string separator = "")
        {
            StringBuilder sb = new StringBuilder();
            bool isFirst = true;
            foreach (T s in value)
            {
                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sb.Append(separator);
                }
                sb.Append(s);
            }

            return sb.ToString();
        }

        /// <summary>
        /// 枚举器成员运行Func委托处理后, 根据分隔符合并成字符串
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <param name="separator"></param>
        /// <param name="method"></param>
        /// <returns></returns>
        public static string Join<T>(this IEnumerable<T> value, string separator, Func<T, string> method)
        {
            StringBuilder sb = new StringBuilder();
            bool isFirst = true;
            foreach (T s in value)
            {
                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sb.Append(separator);
                }
                sb.Append(method.Invoke(s));
            }

            return sb.ToString();
        }

        /// <summary>
        /// 移除此字符串结尾相同的字符串
        /// </summary>
        /// <param name="value"></param>
        /// <param name="toRemove"></param>
        /// <returns></returns>
        public static string TrimEnd(this string value, string toRemove)
        {
            if (!value.EndsWith(toRemove, StringComparison.Ordinal)) return value;
            return TrimEnd(value.Substring(0, value.Length - toRemove.Length), toRemove);
        }

        /// <summary>
        /// 移除此字符串开头相同的字符串
        /// </summary>
        /// <param name="value"></param>
        /// <param name="toRemove"></param>
        /// <returns></returns>
        public static string TrimStart(this string value, string toRemove)
        {
            if (!value.StartsWith(toRemove, StringComparison.Ordinal)) return value;
            return TrimStart(value.Substring(toRemove.Length), toRemove);
        }


        /// <summary>
        /// (Substring不报异常版)从此实例检索子字符串。子字符串从指定的字符位置开始且具有指定的长度。
        /// </summary>
        /// <param name="value"></param>
        /// <param name="startIndex"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static string SubStr(this string value, int startIndex, int length)
        {
            int valueLength = value.Length;
            if (length <= 0 || startIndex > valueLength) return "";
            if (startIndex <= 0 && length >= valueLength) return value;

            int surplusLength = valueLength - startIndex;
            if (length > surplusLength) length = surplusLength;
            return value.Substring(startIndex, length);
        }

        ///// <summary>
        ///// 去掉重复值 people.DistinctBy(p => new { p.Id, p.Name }); people.DistinctBy(p => p.Id);
        ///// </summary>
        ///// <typeparam name="TSource"></typeparam>
        ///// <typeparam name="TKey"></typeparam>
        ///// <param name="source"></param>
        ///// <param name="keySelector"></param>
        ///// <returns></returns>
        //public static IEnumerable<TSource> DistinctBy<TSource, TKey>(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector)
        //{
        //    HashSet<TKey> seenKeys = new HashSet<TKey>();
        //    foreach (TSource element in source)
        //    {
        //        if (seenKeys.Add(keySelector(element)))
        //        {
        //            yield return element;
        //        }
        //    }
        //}
    }
}
