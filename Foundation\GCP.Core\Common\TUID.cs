﻿using DeviceId;
using IdGen;

namespace GCP.Common
{
    /// <summary>
    /// Twitter 唯一 ID 生成器
    /// </summary>
    public static class TUID
    {
        /// <summary>
        /// 工作 ID, 整个系统中唯一[0~1024]
        /// </summary>
        public static int WorkerID { get; private set; }

        // 使用Lazy<T>替代手动实现的双重检查锁
        private static readonly Lazy<IdGenerator> lazyGenerator = new Lazy<IdGenerator>(() =>
        {
            WorkerID = Random.Shared.Next(0, 1024);
            return CreateGenerator(WorkerID);
        }, isThreadSafe: true);

        internal static IdGenerator Generator
        {
            get => lazyGenerator.Value;
            set
            {
                // 由于已经使用Lazy<T>，这个setter可能不再需要
                // 但保留以兼容现有代码，需要调整ChangeWorkerID方法相应逻辑
                throw new NotSupportedException("使用Lazy<T>后不支持直接设置Generator属性");
            }
        }

        static IdGenerator CreateGenerator(int workerId)
        {
            var epoch = new DateTime(2022, 11, 11, 0, 0, 0, DateTimeKind.Utc);
            // 10 bits, max worker 1024
            var structure = new IdStructure(41, 10, 12);
            var options = new IdGeneratorOptions(structure, new DefaultTimeSource(epoch));
            return new IdGenerator(workerId, options);
        }

        /// <summary>
        /// 修改工作 ID
        /// </summary>
        /// <param name="workerId"></param>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        public static void ChangeWorkerID(int workerId)
        {
            if (workerId < 0 || workerId >= 1024)
                throw new ArgumentOutOfRangeException("workerId", "生成器ID不能小于0 或大于等于1024");

            WorkerID = workerId;

            // 当需要改变WorkerID时，需要重新创建Lazy<T>实例
            // 使用LazyInitializer来修改全局静态变量
            LazyInitializer.EnsureInitialized(ref generator, () => CreateGenerator(WorkerID));
        }

        // 保留一个私有变量供ChangeWorkerID使用
        private static IdGenerator generator;

        /// <summary>
        /// 获取一个TUID
        /// </summary>
        /// <returns></returns>
        public static long NewTUID()
        {
            return Generator.CreateId();
        }

        /// <summary>
        /// 获取指定数目的TUID
        /// </summary>
        /// <param name="count"></param>
        /// <returns></returns>
        public static IEnumerable<long> TakeNewTUID(int count)
        {
            return Generator.Take(count);
        }


        public static string DeviceID()
        {
            return new DeviceIdBuilder()
                .AddMacAddress(excludeWireless: true)
                .AddOsVersion()
                .OnWindows(windows => windows
                    .AddMacAddressFromWmi(excludeWireless: true, excludeNonPhysical: true)
                    .AddSystemDriveSerialNumber()
                    .AddMotherboardSerialNumber())
                .OnLinux(linux => linux
                    //.AddSystemDriveSerialNumber() //centos 命令报错 lsblk -b -J，取消获取硬盘序列号
                    .AddDockerContainerId()
                    .AddProductUuid()
                    .AddMachineId()
                    .AddMotherboardSerialNumber())
                .OnMac(mac => mac
                    .AddSystemDriveSerialNumber()
                    .AddPlatformSerialNumber())
                .ToString();
        }
    }
}
