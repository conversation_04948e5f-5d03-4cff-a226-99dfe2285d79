// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// AI模型配置
	/// </summary>
	[Table("lc_chat_model_config")]
	public class LcChatModelConfig : IBaseEntity
	{
		/// <summary>
		/// Description:数据行ID号
		/// </summary>
		[Column("ID"                , CanBeNull = false, IsPrimaryKey = true)] public string    Id               { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                               )] public DateTime  TimeCreate       { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"           , CanBeNull = false                     )] public string    Creator          { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                             )] public DateTime? TimeModified     { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                                  )] public string?   Modifier         { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                     )] public short     State            { get; set; } // smallint
		/// <summary>
		/// Description:配置名称
		/// </summary>
		[Column("NAME"              , CanBeNull = false                     )] public string    Name             { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:模型类型
		/// </summary>
		[Column("TYPE"              , CanBeNull = false                     )] public string    Type             { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:API密钥
		/// </summary>
		[Column("API_KEY"           , CanBeNull = false                     )] public string    ApiKey           { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:聊天模型ID
		/// </summary>
		[Column("CHAT_MODEL_ID"     , CanBeNull = false                     )] public string    ChatModelId      { get; set; } = null!; // varchar(100)
		/// <summary>
		/// Description:嵌入模型ID
		/// </summary>
		[Column("EMBEDDING_MODEL_ID", CanBeNull = false                     )] public string    EmbeddingModelId { get; set; } = null!; // varchar(100)
		/// <summary>
		/// Description:基础URL
		/// </summary>
		[Column("BASE_URL"          , CanBeNull = false                     )] public string    BaseUrl          { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:是否默认
		/// </summary>
		[Column("IS_DEFAULT"                                                )] public bool      IsDefault        { get; set; } // tinyint(1)
	}
}
