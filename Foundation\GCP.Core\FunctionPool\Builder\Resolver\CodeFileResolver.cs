﻿using GCP.Common;
using System.Text;

namespace GCP.FunctionPool.Builder
{
    class CodeFileResolver
    {
        internal static Dictionary<string, string> GetCodes(string directoryPath = "")
        {
            Dictionary<string, string> dicCodes = new Dictionary<string, string>(128);
            if (string.IsNullOrEmpty(directoryPath))
            {
                directoryPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "functions");
            }

            if (Directory.Exists(directoryPath))
            {
                string[] files = Directory.EnumerateFiles(directoryPath, "*.*", SearchOption.AllDirectories)
                    .Select(t => t.ToLower().Replace('\\', '/'))
                    .Where(t => t.EndsWith(CSharpExt) || t.EndsWith(JsExt))
                    .ToArray();
                foreach (var fileName in files)
                {
                    var code = File.ReadAllText(fileName, Encoding.UTF8);
                    dicCodes.Add(fileName, code);
                }
            }
            else
            {
                Directory.CreateDirectory(directoryPath);
            }
            CodeDirectoryPath = directoryPath;
            return dicCodes;
        }

        private static string CodeDirectoryPath { get; set; } = "";
        private static string CSharpExt => ".cs";
        //private static string CsharpMiddlewareExt => "middleware.cs";
        private static string JsExt => "/main.js";

        /// <summary>
        /// 编译dll或js, 从目录获取多个函数
        /// </summary>
        internal static void Handler(string directoryPath = "")
        {
            var dicCodes = GetCodes(directoryPath);
            var lowerDirectoryPath = CodeDirectoryPath.ToLower().Replace('\\', '/');
            Parallel.ForEach(dicCodes, item =>
            {
                var path = item.Key.TrimStart(lowerDirectoryPath);
                if(!path.StartsWith('/')) path = "/" + path;
                //if (path.EndsWith(CSharpExt))
                //{
                //    FunctionResolveType resolverType = path.EndsWith(CsharpMiddlewareExt) ? FunctionResolveType.MIDDLEWARE : FunctionResolveType.PURE;
                //    path = path.TrimEnd(resolverType == FunctionResolveType.PURE ? CSharpExt : CsharpMiddlewareExt);
                //    CSharpResolver.Handler(path, item.Key, item.Value, resolverType);
                //}
                if (path.EndsWith(JsExt))
                {
                    JavaScriptResolver.Handler(item.Key.TrimEnd(JsExt), path.TrimEnd(JsExt), item.Value);
                }
            });
        }
    }
}
