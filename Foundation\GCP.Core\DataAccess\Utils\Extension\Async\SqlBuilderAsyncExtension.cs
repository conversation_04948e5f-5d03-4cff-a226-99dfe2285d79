﻿using System.Collections;
using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    /// <summary>
    /// SqlBuilder Extension（中级扩展, 动态sql语句和参数）
    /// </summary>
    public static class SqlBuilderAsyncExtension
    {
        public static async Task<int> ExecuteAsync(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text, DbTransaction tran = null)
        {
            IEnumerable multiExec = DbHelper.GetMultiExec(parms);
            if (multiExec != null)
            {
                var wasClosed = connection.State == ConnectionState.Closed;
                if (wasClosed)
                {
                    await connection.OpenAsync();
                    tran = tran ?? await connection.BeginTransactionAsync();
                }
                int total = 0;
                try
                {
                    foreach (var parm in multiExec)
                    {
                        total += await ExecuteAsync(new SqlBuilder<object>(connection).Init(sqlString, parm), connection, tran, cmdType).ConfigureAwait(false);
                    }
                    if (wasClosed) await tran.CommitAsync();
                }
                finally
                {
                    if (wasClosed) await connection.CloseAsync();
                }
                return total;
            }

            return await ExecuteAsync(new SqlBuilder<object>(connection).Init(sqlString, parms), connection, tran, cmdType);
        }
        public static async Task<int> ExecuteAsync(this DbTransaction tran, string sqlString, object parms, CommandType cmdType = CommandType.Text)
        {
            return await ExecuteAsync(tran.Connection, sqlString, parms, cmdType, tran).ConfigureAwait(false);
        }
        public static async Task<int> ExecuteAsync(this ISqlBuilder sqlBuilder, DbConnection connection, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return await connection.ExecuteAsync(tran, sqlBuilder.SqlString, cmdType, sqlBuilder.CommandTimeout, sqlBuilder.Parameters).ConfigureAwait(false);
        }
        public static async Task<int> ExecuteAsync(this ISqlBuilder sqlBuilder, DbTransaction tran, CommandType cmdType = CommandType.Text)
        {
            return await ExecuteAsync(sqlBuilder, sqlBuilder.CreateConnection(), tran, cmdType).ConfigureAwait(false);
        }
        public static async Task<int> ExecuteAsync(this ISqlBuilder sqlBuilder, CommandType cmdType = CommandType.Text)
        {
            return await ExecuteAsync(sqlBuilder, sqlBuilder.CreateConnection(), null, cmdType).ConfigureAwait(false);
        }

        public static async Task<DbDataReader> ExecuteReaderAsync(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text, CommandBehavior behavior = CommandBehavior.Default, DbTransaction tran = null)
        {
            return await ExecuteReaderAsync(new SqlBuilder<object>(connection).Init(sqlString, parms), cmdType, behavior, connection, tran).ConfigureAwait(false);
        }
        public static async Task<DbDataReader> ExecuteReaderAsync(this ISqlBuilder sqlBuilder, CommandType cmdType = CommandType.Text, CommandBehavior behavior = CommandBehavior.Default, DbConnection connection = null, DbTransaction tran = null)
        {
            return await (connection ?? sqlBuilder.CreateConnection()).ExecuteReaderAsync(tran, sqlBuilder.SqlString, cmdType, behavior, sqlBuilder.Parameters).ConfigureAwait(false);
        }


        public static async Task<object> ExecuteScalarAsync(this DbConnection connection, string sqlString, object parms, DbTransaction tran = null)
        {
            return await ExecuteScalarAsync(new SqlBuilder<object>(connection).Init(sqlString, parms), connection, tran).ConfigureAwait(false);
        }
        public static async Task<object> ExecuteScalarAsync(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            return await (connection ?? sqlBuilder.CreateConnection()).ExecuteScalarAsync(tran, sqlBuilder.SqlString, sqlBuilder.CommandTimeout, sqlBuilder.Parameters).ConfigureAwait(false);
        }
    }
}
