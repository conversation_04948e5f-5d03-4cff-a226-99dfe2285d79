<template>
  <t-select
    v-model="tableName"
    v-model:popupVisible="showPopup"
    filterable
    placeholder="请选择数据表"
    @search="onSearch"
    @enter="onEnter"
    @focus="onFocus"
  >
    <template #suffix>
      <!-- <icon name="browse" style="margin-right: 8px" /> -->
      <t-tooltip class="refresh-btn" content="刷新字段">
        <t-button theme="default" variant="text" size="small" @click.stop.prevent="onClickRefresh">
          <template #icon><refresh-icon></refresh-icon></template>
        </t-button>
      </t-tooltip>
    </template>
    <t-option v-for="option in tableSchemas" :key="option.tableName" :value="option.tableName">
      <t-space direction="horizontal">
        <t-avatar size="small">{{ option.isView ? 'V' : 'T' }}</t-avatar>
        <span>{{ option.tableName }}</span>
        <t-tag v-if="option.description" size="small">{{ option.description }}</t-tag>
      </t-space>
    </t-option>
  </t-select>
</template>
<script lang="ts">
export default {
  name: 'DataSourceTableSelect',
};
</script>
<script setup lang="ts">
import { isEmpty } from 'lodash-es';
import { RefreshIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref, watch, watchEffect } from 'vue';

import { api, Services } from '@/api/system';

const props = defineProps<{
  dataSourceId: string;
  table: string | undefined;
}>();

// onMounted(() => {
//   onSearch('');
// });

watch(
  () => props.dataSourceId,
  (newVal, oldVal) => {
    if (newVal !== oldVal) onSearch('');
  },
);

const emits = defineEmits(['select-table', 'update:table', 'refresh-columns']);
const tableName = ref(props.table);
const tableSchemas = ref([]);
const options = ref([]);
const showPopup = ref(false);

watchEffect(() => {
  tableName.value = props.table;
});

watch(tableName, async (value) => {
  if (isEmpty(value)) return;
  const item = tableSchemas.value.find((item) => item.tableName === value);
  if (!item) {
    return;
  }
  emits('update:table', value);
  emits('select-table', {
    tableName: value,
    description: item.description,
    columns: item.columns,
  });
});

const onClickRefresh = async () => {
  if (!tableName.value) {
    MessagePlugin.error('请先选择数据表');
    return;
  }

  await onSearch(tableName.value);
  const item = tableSchemas.value.find((item) => item.tableName === tableName.value);
  if (!item) {
    MessagePlugin.error('数据表不存在');
    return;
  }
  emits('refresh-columns', {
    columns: item.columns,
  });
};

const onSearch = async (value: string) => {
  if (isEmpty(props.dataSourceId)) return;
  const key = `${props.dataSourceId}_${value}`;
  tableSchemas.value = JSON.parse(sessionStorage.getItem(key) ?? '[]');
  await api
    .run(Services.dataSourceSearchTableSchemas, { dataSourceId: props.dataSourceId, keyword: value })
    .then((data) => {
      tableSchemas.value = data;
      sessionStorage.setItem(key, JSON.stringify(data));

      options.value = data.map((item) => ({ label: `${item.tableName} ${item.description}`, value: item.tableName }));
    });
};

const onFocus = () => {
  if (tableSchemas.value && tableSchemas.value.length === 0) onSearch(tableName.value);
};

const onEnter = async ({ inputValue }) => {
  let item =
    tableSchemas.value.length === 1
      ? tableSchemas.value[0]
      : tableSchemas.value.find((item) => item.tableName.toLowerCase() === inputValue.toLowerCase());
  if (!item) {
    await onSearch(inputValue);
    item = tableSchemas.value.find((item) => item.tableName.toLowerCase() === inputValue.toLowerCase());
  }

  if (item) {
    tableName.value = item.tableName;
    showPopup.value = false;
  }
};
</script>
<style lang="less" scoped></style>
