﻿using System.Text;

namespace GCP.Common
{
    static class TimerHelper
    {
        /// <summary>
        /// 时间格式化成字符串
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public static string FormatString(this TimeSpan time)
        {
            StringBuilder sb = new StringBuilder();
            if (time.Days > 0)
            {
                sb.Append(' ');
                sb.Append(time.Days);
                sb.Append(" days");
            }

            if (time.Hours > 0)
            {
                sb.Append(' ');
                sb.Append(time.Hours);
                sb.Append(" hours");
            }

            if (time.Minutes > 0)
            {
                sb.Append(' ');
                sb.Append(time.Minutes);
                sb.Append(" minutes");
            }

            if (time.Days == 0 && time.Seconds > 0)
            {
                sb.Append(' ');
                sb.Append(time.Seconds);
                sb.Append(" s");
            }

            if (time.Hours == 0 && time.Milliseconds > 0)
            {
                sb.Append(' ');
                sb.Append(time.Milliseconds);
                sb.Append(" ms");
            }

            string result = sb.ToString().Trim();
            if (string.IsNullOrEmpty(result))
            {
                result = "0 ms";
            }

            return result;
        }
    }
}
