// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 流程统计数据
	/// </summary>
	[Table("lc_fhi_stats")]
	public class LcFhiStat
	{
		/// <summary>
		/// Description:数据行ID号
		/// </summary>
		[Column("ID"            , CanBeNull = false, IsPrimaryKey = true)] public string   Id            { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                           )] public DateTime TimeCreate    { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"       , CanBeNull = false                     )] public string   Creator       { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"   , CanBeNull = false                     )] public string   SolutionId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"    , CanBeNull = false                     )] public string   ProjectId     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:函数ID
		/// </summary>
		[Column("FUNCTION_ID"   , CanBeNull = false                     )] public string   FunctionId    { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:函数版本
        /// </summary>
        [Column("VERSION")] public long Version { get; set; } // bigint
        /// <summary>
        /// Description:统计日期
        /// </summary>
        [Column("STAT_DATE"                                             )] public DateTime StatDate      { get; set; } // datetime
		/// <summary>
		/// Description:归档周期(1:日,2:10分钟)
		/// </summary>
		[Column("ARCHIVE_PERIOD"                                        )] public short    ArchivePeriod { get; set; } // smallint
		/// <summary>
		/// Description:触发类型
		/// </summary>
		[Column("TRIGGER_TYPE"  , CanBeNull = false                     )] public string   TriggerType   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:总调用次数
		/// </summary>
		[Column("TOTAL_COUNT"                                           )] public int      TotalCount    { get; set; } // int
		/// <summary>
		/// Description:成功次数
		/// </summary>
		[Column("SUCCESS_COUNT"                                         )] public int      SuccessCount  { get; set; } // int
		/// <summary>
		/// Description:失败次数
		/// </summary>
		[Column("FAIL_COUNT"                                            )] public int      FailCount     { get; set; } // int
		/// <summary>
		/// Description:总耗时(ms)
		/// </summary>
		[Column("TOTAL_DURATION"                                        )] public long     TotalDuration { get; set; } // bigint
		/// <summary>
		/// Description:平均耗时(ms)
		/// </summary>
		[Column("AVG_DURATION"                                          )] public int      AvgDuration   { get; set; } // int
		/// <summary>
		/// Description:总流量(MB)
		/// </summary>
		[Column("TOTAL_TRAFFIC"                                         )] public decimal? TotalTraffic  { get; set; } // decimal(19,5)
	}
}
