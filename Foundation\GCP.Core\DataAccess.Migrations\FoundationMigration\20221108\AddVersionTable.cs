﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20221108121804, "初始化版本表")]
    public class AddVersionTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_VERSION").WithDescription("版本")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")

               .WithColumn("VERSION").AsInt64().WithColumnDescription("版本")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               ;

            Create.UniqueConstraint("VERSOIN_IDX")
                .OnTable("LC_VERSION")
                .Columns("VERSION", "SOLUTION_ID");
        }

        public override void Down()
        {
            Delete.Table("LC_VERSION");
        }
    }
}
