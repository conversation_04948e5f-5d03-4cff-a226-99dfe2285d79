using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using LinqToDB;
using Microsoft.Extensions.AI;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// AI对话服务
    /// </summary>
    [Function("aiConversation", "AI对话服务")]
    internal class AiConversationService : BaseService
    {
        private readonly AiClientService _clientService;
        private readonly AiProfileService _profileService;

        public AiConversationService()
        {
            _clientService = new AiClientService();
            _profileService = new AiProfileService();
        }

        [Function("createConversation", "创建新的对话")]
        public async Task<ChatConversation> CreateConversation(string userId, string title, string profileId = null)
        {
            // 获取配置文件
            var profile = string.IsNullOrEmpty(profileId) 
                ? await _profileService.GetDefaultChatProfile()
                : await _profileService.GetChatProfile(profileId);

            // 创建对话
            var conversation = new ChatConversation
            {
                UserId = userId,
                Title = title,
                ModelConfigId = profile.ModelConfigId
            };

            // 添加系统消息
            conversation.Messages.Add(new ChatMessage(Enum.Parse<ChatRole>("System"), profile.SystemPrompt));
            
            // 保存到数据库
            await SaveConversation(conversation);
            
            return conversation;
        }

        [Function("getConversation", "获取对话")]
        public async Task<ChatConversation> GetConversation(string conversationId)
        {
            await using var db = this.GetDb();

            // 从数据库加载对话
            var dbConversation = await db.LcChatConversations
                .FirstOrDefaultAsync(c => c.Id == conversationId);

            if (dbConversation == null)
            {
                throw new CustomException($"找不到ID为{conversationId}的对话");
            }

            // 加载消息历史
            var dbMessages = await db.LcChatMessages
                .Where(m => m.ConversationId == conversationId)
                .OrderBy(m => m.Sequence)
                .ToListAsync();

            // 转换为ChatMessage对象
            var conversation = new ChatConversation
            {
                Id = dbConversation.Id,
                UserId = dbConversation.UserId,
                Title = dbConversation.Title,
                CreatedAt = dbConversation.TimeCreate,
                LastUpdatedAt = dbConversation.TimeModified ?? dbConversation.TimeCreate,
                ModelConfigId = dbConversation.ModelConfigId,
                Messages = dbMessages.Select(m => new ChatMessage(Enum.Parse<ChatRole>(m.Role), m.Content)).ToList(),
                MessageInfos = [.. dbMessages.Select(m => new MessageInfo(m.Role, m.Content))]
            };
            
            return conversation;
        }

        [Function("getUserConversations", "获取用户所有对话")]
        public async Task<List<ChatConversation>> GetUserConversations(string userId)
        {
            await using var db = this.GetDb();

            // 获取用户的所有对话概要信息
            var dbConversations = await db.LcChatConversations
                .Where(c => c.UserId == userId)
                .OrderByDescending(c => c.TimeModified ?? c.TimeCreate)
                .ToListAsync();

            var conversations = new List<ChatConversation>();
            foreach (var dbConversation in dbConversations)
            {
                conversations.Add(new ChatConversation
                {
                    Id = dbConversation.Id,
                    UserId = dbConversation.UserId,
                    Title = dbConversation.Title,
                    CreatedAt = dbConversation.TimeCreate,
                    LastUpdatedAt = dbConversation.TimeModified ?? dbConversation.TimeCreate,
                    ModelConfigId = dbConversation.ModelConfigId
                });
            }

            return conversations;
        }

        [Function("deleteConversation", "删除对话")]
        public async Task DeleteConversation(string conversationId)
        {
            await using var db = this.GetDb();
            db.BeginTransaction();

            try
            {
                // 删除消息
                await db.LcChatMessages
                    .Where(m => m.ConversationId == conversationId)
                    .DeleteAsync();

                // 删除对话
                await db.LcChatConversations
                    .Where(c => c.Id == conversationId)
                    .DeleteAsync();

                db.CommitTransaction();
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("sendMessage", "发送消息")]
        public async Task<ChatConversation> SendMessage(string conversationId, string message)
        {
            var conversation = await GetConversation(conversationId);
            
            // 获取对应的客户端上下文
            var (client, tools) = await _clientService.GetClientContext(conversation.ModelConfigId);
            
            if (tools == null)
            {
                throw new CustomException("正在获取MCP工具列表，请稍后再试。");
            }
            
            // 添加用户消息
            conversation.MessageInfos.Add(new MessageInfo("User", message));
            conversation.Messages.Add(new ChatMessage(Enum.Parse<ChatRole>("User"), message));

            var options = new ChatOptions
            {
                Tools = [..tools]
            };

            // 发送消息并获取响应
            var response = await client.GetResponseAsync(conversation.Messages, options);
            conversation.Messages.AddMessages(response);

            var toolUseMessages = response.Messages.Where(m => m.Role == Enum.Parse<ChatRole>("Tool"));

            if (response.Messages[0].Contents.Count > 1)
            {
                var functionCall = (FunctionCallContent)response.Messages[0].Contents[1];
                string arguments = "";
                MessageInfo messageInfo = new MessageInfo();
                if (functionCall.Arguments != null)
                {
                    foreach (var arg in functionCall.Arguments)
                    {
                        arguments += $"{arg.Key}:{arg.Value};";
                    }
                    messageInfo.FunctionCallInfo = $"调用函数名:{functionCall.Name};参数信息：{arguments}";
                    foreach (var toolUseMessage in toolUseMessages)
                    {
                        var functionResultContent = (FunctionResultContent)toolUseMessage.Contents[0];
                        messageInfo.FunctionCallResult = $"调用工具结果：{functionResultContent.Result}";
                    }
                }
                conversation.MessageInfos.Add(messageInfo);
            }
            
            conversation.MessageInfos.Add(new MessageInfo("Assistant", response.Text));
            
            // 更新对话标题（如果是第一条消息）
            if (conversation.Messages.Count <= 3 && string.IsNullOrEmpty(conversation.Title))
            {
                conversation.Title = message.Length > 20 ? message.Substring(0, 20) + "..." : message;
            }
            
            // 保存对话到数据库
            await SaveConversation(conversation);
            
            return conversation;
        }

        [Function("clearConversation", "清空对话历史")]
        public async Task ClearConversation(string conversationId)
        {
            var conversation = await GetConversation(conversationId);
            
            // 保留系统消息
            var systemMessage = conversation.Messages.FirstOrDefault(m => m.Role == Enum.Parse<ChatRole>("System"));
            
            conversation.Messages.Clear();
            conversation.MessageInfos.Clear();
            
            if (systemMessage != null)
            {
                conversation.Messages.Add(systemMessage);
            }
            
            // 保存更改
            await SaveConversation(conversation);
        }

        private async Task SaveConversation(ChatConversation conversation)
        {
            DateTime now = DateTime.Now;
            await using var db = this.GetDb();
            db.BeginTransaction();
            
            try
            {
                // 更新或插入对话
                var existingConversation = await db.LcChatConversations
                    .FirstOrDefaultAsync(c => c.Id == conversation.Id);

                if (existingConversation == null)
                {
                    // 插入新对话
                    var entity = new LcChatConversation
                    {
                        Id = conversation.Id,
                        UserId = conversation.UserId,
                        Title = conversation.Title,
                        State = 1,
                        ModelConfigId = conversation.ModelConfigId
                    };
                    
                    this.InsertData(entity, db);
                }
                else
                {
                    // 更新现有对话
                    existingConversation.Title = conversation.Title;
                    existingConversation.ModelConfigId = conversation.ModelConfigId;
                    
                    this.UpdateData(existingConversation, db);
                }

                // 清除旧消息
                await db.LcChatMessages
                    .Where(m => m.ConversationId == conversation.Id)
                    .DeleteAsync();

                // 添加新消息
                int sequence = 0;
                foreach (var message in conversation.Messages)
                {
                    string content = "";
                    
                    // 这里我们需要获取消息内容，但ChatMessage的结构可能不同，所以需要适应不同情况
                    if (message.Contents != null && message.Contents.Count > 0)
                    {
                        var textContent = message.Contents[0] as TextContent;
                        if (textContent != null)
                        {
                            content = textContent.Text;
                        }
                        else
                        {
                            // 尝试使用其他方式获取内容
                            content = message.Contents[0].ToString();
                        }
                    }
                    else
                    {
                        // 可能是旧版本的ChatMessage
                        content = message.ToString();
                    }
                    
                    var messageEntity = new LcChatMessage
                    {
                        Id = Guid.NewGuid().ToString(),
                        ConversationId = conversation.Id,
                        Role = message.Role.ToString(),
                        Content = content,
                        Sequence = sequence++,
                        TimeCreate = now,
                        Creator = conversation.UserId,
                        State = 1
                    };
                    
                    this.InsertData(messageEntity, db);
                }

                db.CommitTransaction();
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }
    }
} 