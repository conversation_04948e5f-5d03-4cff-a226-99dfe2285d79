<template>
  <div style="height: 100%">
    <job-form
      v-if="isShowForm"
      :data="currentRowData"
      :is-edit="isEditForm"
      @back="onClickBackForm"
      @submit="onClickSubmitForm"
    />
    <action-panel
      ref="actionPanelRef"
      v-model:visible="isShowActionPanel"
      :action-id="actionId"
      :data="actionData"
      @back="onClickBackForm"
      @save="onClickSaveAction"
    ></action-panel>
    <CmpContainer v-show="!(isShowForm || isShowActionPanel)" full>
      <CmpCard>
        <t-row justify="space-between">
          <div class="left-operation-container">
            <t-button @click="onClickAdd"> 新建任务 </t-button>
            <t-tooltip content="在定时任务开启后会自动刷新任务，或数据库更新job后手动刷新">
              <t-button variant="base" theme="default" @click="onClickRefresh"> 刷新任务 </t-button>
            </t-tooltip>
            <t-button
              variant="base"
              theme="default"
              :disabled="!selectedRowKeys.length"
              @click="onClickUpdateStatus(1)"
            >
              开启任务
            </t-button>
            <t-button
              variant="base"
              theme="default"
              :disabled="!selectedRowKeys.length"
              @click="onClickUpdateStatus(0)"
            >
              暂停任务
            </t-button>
            <p v-if="!!selectedRowKeys.length" class="selected-count">
              {{ t('pages.listBase.select') }} {{ selectedRowKeys.length }} {{ t('pages.listBase.items') }}
            </p>
          </div>
          <div class="search-input">
            <t-input v-model="searchValue" :placeholder="t('pages.listBase.placeholder')" clearable @enter="fetchData">
              <template #suffix-icon>
                <search-icon size="16px" />
              </template>
            </t-input>
          </div>
        </t-row>
        <t-table
          :data="data"
          :columns="COLUMNS"
          size="small"
          height="calc(-233px + 100vh)"
          row-key="id"
          :hover="true"
          :pagination="pagination"
          :selected-row-keys="selectedRowKeys"
          :loading="dataLoading"
          @page-change="onPageChange"
          @select-change="onSelectChange"
        >
          <template #status="{ row }">
            <t-tag v-if="row.status === 0" theme="warning" variant="light-outline">
              <pause-circle-filled-icon />
              暂停
            </t-tag>
            <t-tag v-if="row.status === 1" theme="success" variant="light-outline">
              <play-circle-filled-icon />
              运行中
            </t-tag>
          </template>

          <template #operate="{ row }">
            <t-space size="small">
              <t-link theme="primary" @click="onClickBindAction(row)"> 动作 </t-link>
              <t-link theme="primary" @click="onClickLog(row)"> 日志 </t-link>
              <t-link theme="primary" @click="onClickExecute(row)"> 执行一次 </t-link>
              <t-link theme="primary" @click="onClickEdit(row)"> 编辑 </t-link>

              <t-popconfirm content="确认删除吗" @confirm="onConfirmDelete(row)">
                <t-link theme="danger"> 删除 </t-link>
              </t-popconfirm>
            </t-space>
          </template>
        </t-table>

        <t-dialog v-model:visible="isShowTriggerPanel" header="执行一次" @confirm="triggerJob">
          <t-form>
            <t-form-item label="上次执行时间">
              <t-date-picker v-model="triggerLastTime" enable-time-picker />
            </t-form-item>
          </t-form>
        </t-dialog>
      </CmpCard>
    </CmpContainer>
  </div>
</template>
<script lang="ts">
export default {
  name: 'CronJob',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { PauseCircleFilledIcon, PlayCircleFilledIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin, PrimaryTableCol, TableProps, TableRowData } from 'tdesign-vue-next';
import { onActivated, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { api, Services } from '@/api/system';
import ActionPanel from '@/components/action-panel/ActionPanel.vue';
import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { t } from '@/locales';

import JobForm from './JobForm.vue';

const actionFlowStore = useActionFlowStore();
const { flowTitle } = storeToRefs(actionFlowStore);

onMounted(() => {
  fetchData();
});

onActivated(() => {
  isShowActionPanel.value = false;
  actionFlowStore.flowType = 'JOB';
  fetchData();
});

const isShowForm = ref(false);
const selectedRowKeys = ref<Array<string | number>>([]);
const searchValue = ref('');

const COLUMNS: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'row-select', type: 'multiple', width: 64, fixed: 'left' },
  {
    title: '任务名称',
    align: 'left',
    colKey: 'jobName',
    fixed: 'left',
    minWidth: 180,
    ellipsis: true,
  },
  {
    title: '任务组',
    ellipsis: true,
    width: 130,
    colKey: 'jobGroup',
  },
  { title: '状态', colKey: 'status', width: 100 },
  {
    title: '执行间隔',
    colKey: 'cronDescription',
    width: 130,
    ellipsis: true,
  },
  {
    title: '下次执行时间',
    width: 180,
    colKey: 'nextTime',
  },
  {
    title: '开始时间',
    width: 180,
    colKey: 'jobBeginTime',
  },
  {
    title: '结束时间',
    width: 180,
    colKey: 'jobEndTime',
  },
  {
    title: '下次开始时间',
    width: 180,
    colKey: 'nextBeginTime',
  },
  {
    title: '操作',
    align: 'left',
    fixed: 'right',
    width: 240,
    colKey: 'operate',
  },
];
const data = ref([]);
const pagination = ref({
  pageSize: 20,
  total: 0,
  current: 1,
});

const dataLoading = ref(false);
const fetchData = async () => {
  dataLoading.value = true;
  try {
    const { list, total } = await api.run(Services.jobGetAll, {
      keyword: searchValue.value,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });
    data.value = list;
    pagination.value = {
      ...pagination.value,
      total,
    };
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};
const onSelectChange: TableProps['onSelectChange'] = (value, params) => {
  selectedRowKeys.value = value;
};
const onPageChange: TableProps['onPageChange'] = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchData();
};

const onConfirmDelete = (row) => {
  api.run(Services.jobDelete, { id: row.id }).then(() => {
    fetchData();
    MessagePlugin.success('删除成功');
  });
};

const executeArgs = ref<{
  JOB_LAST_TIME: string;
}>(null);
const triggerId = ref('');
const isShowTriggerPanel = ref(false);
const triggerLastTime = ref('');

const onClickExecute = (row) => {
  triggerId.value = row.id;
  api.run(Services.jobGetById, { id: row.id }).then((res) => {
    const { jobBeginTime, nextBeginTime } = res;
    isShowTriggerPanel.value = true;
    triggerLastTime.value = nextBeginTime ?? jobBeginTime;
  });
};

const triggerJob = () => {
  if (!triggerId.value) return;
  executeArgs.value = {
    JOB_LAST_TIME: triggerLastTime.value,
  };
  isShowTriggerPanel.value = false;
  api
    .run(Services.jobTrigger, {
      ids: [triggerId.value],
      args: executeArgs.value,
    })
    .then(() => {
      MessagePlugin.success('执行成功');
      fetchData();
    });
};

const currentRowData = ref({});
const isEditForm = ref(false);
const onClickEdit = (row) => {
  isEditForm.value = true;
  currentRowData.value = row;
  isShowForm.value = true;
};

const onClickAdd = () => {
  isEditForm.value = false;
  isShowForm.value = true;
  currentRowData.value = null;
};

const onClickUpdateStatus = (status: number) => {
  api.run(Services.jobUpdateState, { ids: selectedRowKeys.value, status }).then(() => {
    fetchData();
    MessagePlugin.success('操作成功');
  });
};

const onClickRefresh = async () => {
  fetchData();
  await api.run(Services.jobRefresh).then(() => {
    MessagePlugin.success('刷新任务最新状态成功');
  });
};

const onClickBackForm = () => {
  isShowForm.value = false;
  fetchData();
};

const onClickSubmitForm = (data: any) => {
  isShowForm.value = false;
  fetchData();
};

const actionData: FlowData[] = [
  {
    id: 'JOB_LAST_TIME',
    key: 'JOB_LAST_TIME',
    description: 'JOB上次执行时间',
    type: 'DateTime',
    value: {
      type: 'variable',
      variableType: 'current',
      variableName: '获取JOB上次执行时间',
      variableValue: '_JOB_LAST_TIME',
    },
  },
];

const isShowActionPanel = ref(false);
const actionId = ref('');
const actionPanelRef = ref<InstanceType<typeof ActionPanel>>();
const onClickBindAction = (row) => {
  isShowActionPanel.value = true;
  actionId.value = row.functionId;
  flowTitle.value = row.jobName;
  actionPanelRef.value.reload(row.functionId);
};

const router = useRouter();
const onClickLog = (row) => {
  router.push({
    path: '/system/log',
    query: {
      triggerType: 'JOB',
      functionId: row.functionId,
    },
  });
};

const onClickSaveAction = () => {
  // isShowActionPanel.value = false;
};
</script>
<style lang="less" scoped>
.left-operation-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-comp-margin-xxl);

  .selected-count {
    display: inline-block;
    margin-left: var(--td-comp-margin-l);
    color: var(--td-text-color-secondary);
  }
}

.search-input {
  width: 360px;
}
</style>
