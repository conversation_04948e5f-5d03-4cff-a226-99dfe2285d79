<template>
  <div style="height: 100%">
    <action-panel
      ref="actionPanelRef"
      v-model:visible="isShowActionPanel"
      :action-id="actionId"
      :data="actionData"
    ></action-panel>
    <cmp-container v-show="!isShowActionPanel" full>
      <cmp-card no-fill>
        <cmp-table-tree-container
          style="height: calc(-90px + 100vh)"
          name="消息事件"
          dir-type="E"
          :columns="columns"
          :get-default-data="getTableDefaultData"
          :get-by-dir-code="getEventsByDirCode"
          :save="saveEvent"
          :remove="removeEvent"
          :get-by-id="getEventById"
          :get-current-data="getCurrentData"
          @validate="onValidate"
        >
          <template #row-operator="{ row }">
            <t-link theme="primary" @click="onClickBindAction(row)"> 动作 </t-link>
            <t-link theme="primary" @click="onClickLog(row)"> 日志 </t-link>
          </template>
          <template #edit-operator="{ currentData }">
            <t-button
              v-if="currentData.id && currentData.sourceType == 1"
              theme="default"
              @click="onClickBindAction(currentData)"
            >
              动作
            </t-button>
          </template>
          <template #item="{ item }">
            <event-item
              :name="getEvent(item.leafId)?.eventName || item.label"
              :desc="getEvent(item.leafId)?.description || ''"
              :tag="getSourceDisplayName(getEvent(item.leafId)?.sourceType)"
              :enabled="getEvent(item.leafId)?.isEnabled === 1"
            />
          </template>
          <template #form="{ currentData }">
            <event-form v-if="!isEmpty(currentData)" ref="eventFormRef" :data="currentData as LcMessageEvent" />
          </template>
        </cmp-table-tree-container>
      </cmp-card>
    </cmp-container>
  </div>
</template>

<script lang="tsx">
export default {
  name: 'Events',
};
</script>

<script setup lang="tsx">
import { type PrimaryTableCol } from 'tdesign-vue-next';
import { computed, ref, onActivated, onMounted } from 'vue';

import CmpTableTreeContainer from '@/components/cmp-table-tree-container/CmpTableTreeContainer.vue';
import { LcMessageEvent, useMessageEventService } from '@/composables/services/messageEvent';

import ActionPanel from '@/components/action-panel/ActionPanel.vue';
import EventForm from './EventForm.vue';
import EventItem from './EventItem.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { storeToRefs } from 'pinia';
import { FlowData } from '@/components/action-panel/model';
import { isEmpty } from 'lodash-es';
import { useRouter } from 'vue-router';
import { core } from '@/utils/core';

const actionFlowStore = useActionFlowStore();
const { flowTitle } = storeToRefs(actionFlowStore);

onMounted(() => {
  core.ipc.on('open-action-panel', ({ functionId, title }) => {
    isShowActionPanel.value = true;
    actionId.value = functionId;
    flowTitle.value = title;
    actionPanelRef.value.reload(functionId);
  });
});

onActivated(() => {
  isShowActionPanel.value = false;
  actionFlowStore.flowType = 'EVENT';
});

const actionData = computed<FlowData[]>(() => {
  if (currentEvent.value?.sourceType !== 1) {
    return [
      {
        id: 'EVENT_DATA',
        key: 'EVENT_DATA',
        description: '事件数据',
        type: 'object',
        isCustomize: true,
        value: {
          type: 'variable',
          variableType: 'current',
          variableName: '获取事件数据',
          variableValue: '_EVENT_DATA',
        },
      },
    ] as FlowData[];
  }

  // 设备参数
  return [
    {
      id: 'EquipmentId',
      key: 'EquipmentId',
      description: '设备ID',
      type: 'string',
      value: {
        type: 'variable',
        variableType: 'current',
        variableName: '获取设备ID',
        variableValue: 'EquipmentId',
      },
    },
    {
      id: 'EquipmentType',
      key: 'EquipmentType',
      description: '设备类型',
      type: 'string',
      value: {
        type: 'variable',
        variableType: 'current',
        variableName: '获取设备类型',
        variableValue: 'EquipmentType',
      },
    },
    {
      id: 'Timestamp',
      key: 'Timestamp',
      description: '时间戳',
      type: 'DateTime',
      value: {
        type: 'variable',
        variableType: 'current',
        variableName: '获取时间戳',
        variableValue: 'Timestamp',
      },
    },
    {
      id: 'Values',
      key: 'Values',
      description: '值',
      type: 'object',
      value: {
        type: 'variable',
        variableType: 'current',
        variableName: '获取值',
        variableValue: 'Values',
      },
    },
  ] as FlowData[];
});

const isShowActionPanel = ref(false);
const actionId = ref('');
const actionPanelRef = ref<InstanceType<typeof ActionPanel>>();
const onClickBindAction = (row) => {
  currentEvent.value = row;
  isShowActionPanel.value = true;
  actionId.value = row.functionId;
  flowTitle.value = row.eventName;
  actionPanelRef.value.reload(row.functionId);
};

const router = useRouter();
const onClickLog = (row) => {
  router.push({
    path: '/system/log',
    query: {
      triggerType: 'EVENT',
      functionId: row.functionId,
    },
  });
};

// 表格列定义
const columns: PrimaryTableCol[] = [
  { colKey: 'eventName', title: '事件名称' },
  {
    colKey: 'sourceType',
    title: '事件源类型',
    cell: (h, { row }) => {
      return <div>{getSourceDisplayName(row.sourceType)}</div>;
    },
  },
  {
    colKey: 'eventType',
    title: '事件类型',
    cell: (h, { row }) => {
      return <div>{getEventTypeTag(row.eventType)}</div>;
    },
  },
  {
    colKey: 'isEnabled',
    title: '状态',
    cell: (h, { row }) => {
      return <div>{row.isEnabled === 1 ? '已启用' : '未启用'}</div>;
    },
  },
  { colKey: 'description', title: '事件描述', ellipsis: true },
  { colKey: 'op', title: '操作', width: 160, fixed: 'right' },
];

// 消息事件服务
const messageEventService = useMessageEventService();

// 事件列表状态
const tableData = ref<LcMessageEvent[]>([]);
const eventFormRef = ref();

// 当前选中的事件
const currentEvent = ref<LcMessageEvent | null>(null);

// 获取事件
const getEvent = (id: string) => {
  return tableData.value.find((v) => v.id === id);
};

// 获取事件类型标签
const getEventTypeTag = (type?: number) => {
  if (!type) return '';
  switch (type) {
    case 1:
      return '测点数据变化';
    case 2:
      return '设备数据变化';
    case 3:
      return '设备类型数据变化';
    default:
      return '';
  }
};

// 获取源显示名称
const getSourceDisplayName = (sourceType: number) => {
  if (!sourceType) return '';
  switch (sourceType) {
    case 1:
      return '设备';
    case 2:
      return 'MQTT';
    case 3:
      return 'RabbitMQ';
    case 4:
      return 'Kafka';
    case 5:
      return 'Redis';
    case 6:
      return 'Tcp';
    case 0:
      return 'InMemory';
    default:
      return '';
  }
};

// 获取事件默认数据
const getTableDefaultData = (currentNode: any): LcMessageEvent => {
  return {
    id: '',
    eventName: '',
    eventType: 1,
    sourceType: 1,
    isEnabled: 0,
    description: '',
    dirCode: currentNode.id,
  };
};

// 获取目录下的事件列表
const getEventsByDirCode = async (dirId: string) => {
  const items = await messageEventService.getByDirCode(dirId);
  tableData.value = items;
  return items;
};

// 获取单个事件
const getEventById = async (id: string): Promise<LcMessageEvent> => {
  const data = await messageEventService.getById(id);
  if (data) {
    currentEvent.value = data;
    return data;
  }
  return null;
};

// 保存事件
const saveEvent = async (data: LcMessageEvent) => {
  if (data.id) {
    await messageEventService.update(data);
    return data.id;
  }
  return messageEventService.add(data);
};

// 删除事件
const removeEvent = async (id: string) => {
  await messageEventService.delete(id);
};

// 保存事件回调
const onValidate = () => {
  return eventFormRef.value?.validate();
};

// 获取当前数据
const getCurrentData = () => {
  const data = eventFormRef.value?.getData();
  return {
    data: {
      isEnabled: 1,
      ...data,
    },
    dirData: {
      dirCode: data.eventName,
      dirName: data.eventName,
      description: data.description,
    },
  };
};
</script>

<style lang="less" scoped>
.mb-2 {
  margin-bottom: 8px;
}
</style>
