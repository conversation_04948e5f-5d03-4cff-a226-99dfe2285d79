﻿using MySqlConnector;
using System.Data;

namespace GCP.DataAccess
{
    internal static class MySqlBulkCopyProvider
    {
        private static MySqlBulkCopy PrepareOptions(IDbConnection connection, string tableName, IDbTransaction transaction = null)
        {
            MySqlBulkCopy bulkCopy = new MySqlBulkCopy((MySqlConnection)connection, (MySqlTransaction)transaction);
            bulkCopy.BulkCopyTimeout = 0;
            bulkCopy.DestinationTableName = tableName;

            return bulkCopy;
        }

        internal static void MySqlBulkCopy(this IDbConnection connection, string tableName, IDataReader reader, IDbTransaction transaction = null)
        {
            MySqlBulkCopy bulkCopy = PrepareOptions(connection, tableName, transaction);
            bulkCopy.WriteToServer(reader);
        }

        internal static void MySqlBulkCopy(this IDbConnection connection, string tableName, DataTable dt, IDbTransaction transaction = null)
        {
            MySqlBulkCopy bulkCopy = PrepareOptions(connection, tableName, transaction);
            bulkCopy.WriteToServer(dt);
        }
    }
}
