﻿using System.Runtime.Serialization;

namespace GCP.DataAccess
{
    /// <summary>
    /// 分页数据
    /// </summary>
    /// <typeparam name="T"></typeparam>
    [DataContract]
    [Serializable]
    public sealed class PagingData<T>
    {
        /// <summary>
        /// 数据清单
        /// </summary>
        [DataMember(Name = "list")]
        public List<T> List { get; set; }
        /// <summary>
        /// 分页信息
        /// </summary>
        [DataMember(Name = "paging")]
        public PagingInfo Paging { get; set; }
        /// <summary>
        /// 总数
        /// </summary>
        [DataMember(Name = "total")]
        public long Total { get { return this.Paging.Total; } }

        public PagingData()
        {
        }

        public PagingData(List<T> dataList, int pageIndex, int pageSize = 20)
        {
            this.Paging = new PagingInfo(dataList.Count, pageSize, pageIndex);
            this.List = dataList.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
        }

        public PagingData(IQueryable<T> query, int pageIndex, int pageSize = 20)
        {
            this.Paging = new PagingInfo(query.Count(), pageSize, pageIndex);
            this.List = query.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
        }
    }

    public static class PagingExtension
    {
        public static PagingData<T> ToPagingData<T>(this List<T> dataList, int pageIndex, int pageSize = 20)
        {
            return new PagingData<T>(dataList, pageIndex, pageSize);
        }

        public static PagingData<T> ToPagingData<T>(this IQueryable<T> query, int pageIndex, int pageSize = 20)
        {
            return new PagingData<T>(query, pageIndex, pageSize);
        }
    }

    /// <summary>
    /// 分页信息
    /// </summary>
    [DataContract]
    [Serializable]
    public sealed class PagingInfo
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        [DataMember(Name = "total")]
        public long Total { get; set; }
        /// <summary>
        /// 当前页码
        /// </summary>
        [DataMember(Name = "current")]
        public int Current { get; set; }
        /// <summary>
        /// 每页显示记录数
        /// </summary>
        [DataMember(Name = "pageSize")]
        public int PageSize { get; set; }
        /// <summary>
        /// 总页数
        /// </summary>
        [DataMember(Name = "pageCount")]
        public int PageCount
        {
            get
            {
                return (int)Math.Ceiling((decimal)this.Total / this.PageSize);
            }
        }
        /// <summary>
        /// 是否有上一页
        /// </summary>
        [DataMember(Name = "hasPreviousPage")]
        public bool HasPreviousPage
        {
            get { return this.Current > 1; }
        }
        /// <summary>
        /// 是否有下一页
        /// </summary>
        [DataMember(Name = "hasNextPage")]
        public bool HasNextPage
        {
            get { return this.Current < this.PageCount; }
        }
        /// <summary>
        /// 数据行Index
        /// </summary>
        [DataMember(Name = "dataIndex")]
        public int DataIndex
        {
            get { return (this.Current - 1) * this.PageSize + 1; }
        }

        public PagingInfo()
        {
        }

        public PagingInfo(long total, int pageSize, int current)
        {
            this.Total = total;
            this.PageSize = pageSize;
            this.Current = current;
        }
    }
}
