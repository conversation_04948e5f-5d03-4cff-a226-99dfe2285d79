<template>
  <t-form ref="formRef" :data="formData" :rules="rules" class="mb-4">
    <t-form-item name="dictCode" label="变量代码">
      <t-input v-model="formData.dictCode" placeholder="请输入变量代码" />
    </t-form-item>

    <t-form-item name="dictName" label="变量名称">
      <t-input v-model="formData.dictName" placeholder="请输入变量名称" />
    </t-form-item>

    <t-form-item name="dictType" label="值类型">
      <t-radio-group v-model="formData.dictType">
        <t-radio-button value="TEXT">TEXT</t-radio-button>
        <t-radio-button value="JSON">JSON</t-radio-button>
      </t-radio-group>
    </t-form-item>

    <t-form-item name="dictValue" label="变量值">
      <editor
        v-model:value="formData.dictValue"
        :language="editorLanguage"
        :show-line-numbers="false"
        style="height: 280px"
      ></editor>
    </t-form-item>

    <t-form-item name="description" label="变量描述">
      <t-textarea v-model="formData.description" placeholder="请输入变量描述" :autosize="{ minRows: 2, maxRows: 5 }" />
    </t-form-item>
  </t-form>
</template>

<script lang="ts">
export default {
  name: 'VariableForm',
};
</script>

<script setup lang="ts">
import { FormRule } from 'tdesign-vue-next';
import { computed, ref, toRef } from 'vue';

import Editor from '@/components/editor/index.vue';

export interface VariableData {
  id: string;
  dictCode: string;
  dictName: string;
  dictValue: string;
  dictType: 'TEXT' | 'JSON';
  description?: string;
  dirCode?: string;
  groupCode: string;
  seq: number;
  parentId?: string;
}

const props = defineProps<{
  data: VariableData;
}>();

const formData = toRef(props, 'data');

const rules = {
  dictCode: [{ required: true, message: '请输入变量代码', trigger: 'blur' }],
  dictName: [{ required: true, message: '请输入变量名称', trigger: 'blur' }],
  dictValue: [{ required: true, message: '请输入变量值', trigger: 'blur' }],
} as Record<string, FormRule[]>;

const formRef = ref();

const editorLanguage = computed(() => {
  return formData.value.dictType === 'JSON' ? 'json' : 'plaintext';
});

// 暴露方法
defineExpose({
  validate: async () => formRef.value?.validate(),
  formRef,
  getData: () => formData.value,
});
</script>

<style lang="less" scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
