import { CatalogIcon, DataBaseIcon, LockOnIcon, NotificationIcon, SystemSettingIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/system',
    component: Layout,
    redirect: '/system/setting',
    name: 'System',
    meta: {
      title: {
        zh_CN: '系统',
        en_US: 'System',
      },
      icon: shallowRef(SystemSettingIcon),
      orderNo: 1,
    },
    children: [
      // {
      //   path: 'setting',
      //   name: 'SystemSetting',
      //   component: () => import('@/pages/system/setting/index.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '系统设置',
      //       en_US: 'Setting',
      //     },
      //   },
      // },
      // {
      //   path: 'user',
      //   name: 'SystemUser',
      //   component: () => import('@/pages/system/user/index.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '用户管理',
      //       en_US: 'User',
      //     },
      //   },
      // },
      {
        path: 'dataBaseManage',
        name: 'DataBaseManage',
        component: () => import('@/pages/system/dataBaseManage/index.vue'),
        meta: {
          title: {
            zh_CN: '数据源管理',
            en_US: 'DataBase Manage',
          },
          icon: shallowRef(DataBaseIcon),
        },
      },
      {
        path: 'notification-channels',
        name: 'NotificationChannels',
        component: () => import('@/pages/system/notification-channels/index.vue'),
        meta: {
          title: {
            zh_CN: '通知渠道',
            en_US: 'Notification Channels',
          },
          icon: shallowRef(NotificationIcon),
        },
      },
      // {
      //   path: 'publish',
      //   name: 'PublishConfig',
      //   component: () => import('@/pages/system/publish/index.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '发布配置',
      //       en_US: 'Publish Config',
      //     },
      //   },
      // },
      {
        path: 'log',
        name: 'SystemLog',
        component: () => import('@/pages/system/log/index.vue'),
        props: (route) => ({
          keyword: route.query.keyword,
          triggerType: route.query.triggerType,
          functionId: route.query.functionId,
        }),
        meta: {
          title: {
            zh_CN: '系统日志',
            en_US: 'Log',
          },
          icon: shallowRef(CatalogIcon),
        },
      },
      {
        path: 'license',
        name: 'LicenseManager',
        component: () => import('@/pages/system/license/index.vue'),
        meta: {
          title: {
            zh_CN: '许可证管理',
            en_US: 'License Manager',
          },
          icon: shallowRef(LockOnIcon),
        },
      },
      // {
      //   path: 'actions',
      //   name: 'Actions',
      //   component: () => import('@/pages/system/actions/index.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '动作库',
      //       en_US: 'Actions',
      //     },
      //   },
      // },
      // {
      //   path: 'plugins',
      //   name: 'Plugins',
      //   component: () => import('@/pages/system/plugins/index.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '插件',
      //       en_US: 'Plugins',
      //     },
      //   },
      // },
    ],
  },
];
