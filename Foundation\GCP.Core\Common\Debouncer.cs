﻿namespace GCP.Common
{
    public sealed class Debouncer : IDisposable
    {
        private readonly TimeSpan _debounceTime;
        private CancellationTokenSource _cts = new CancellationTokenSource();

        public Debouncer(TimeSpan debounceTime)
        {
            _debounceTime = debounceTime;
        }

        public Task DebounceAsync(Func<Task> func)
        {
            var newCts = new CancellationTokenSource();
            var oldCts = Interlocked.Exchange(ref _cts, newCts);

            oldCts.Cancel();
            oldCts.Dispose();

            return InternalDebounceAsync(func, newCts.Token);
        }

        private async Task InternalDebounceAsync(Func<Task> func, CancellationToken token)
        {
            try
            {
                await Task.Delay(_debounceTime, token);
                if (!token.IsCancellationRequested)
                    await func().ConfigureAwait(false);
            }
            catch (TaskCanceledException)
            {
                // 忽略取消异常
            }
        }

        public void Dispose()
        {
            _cts.Cancel();
            _cts.Dispose();
        }
    }
}
