<template>
  <t-select
    v-model="selectedValue"
    :options="fieldOptions"
    :placeholder="fieldOptions.length > 0 ? placeholder : '请先定义响应体结构'"
    :disabled="fieldOptions.length === 0"
    filterable
    clearable
    @change="onFieldChange"
  >
    <template #valueDisplay="{ value }">
      <div v-if="value" class="selected-field">
        <t-icon name="layers" class="selected-field-icon" />
        <span class="selected-field-path">{{ getSelectedFieldLabel(value) }}</span>
        <span v-if="getSelectedFieldDescription(value)" class="selected-field-description">
          {{ getSelectedFieldDescription(value) }}
        </span>
        <t-tag
          v-if="getSelectedFieldType(value)"
          size="small"
          :theme="getTypeTheme(getSelectedFieldType(value))"
          variant="light"
          class="selected-field-type"
        >
          {{ getTypeLabel(getSelectedFieldType(value)) }}
        </t-tag>
      </div>
    </template>
    <template #option="{ option }">
      <div class="field-option" :style="{ paddingLeft: `${getFieldLevel(option.label) * 16 + 8}px` }">
        <div class="field-info">
          <div class="field-path-container">
            <t-icon name="layers" class="field-icon" />
            <span class="field-path">{{ getDisplayPath(option.label) }}</span>
          </div>
          <span v-if="option.description" class="field-description">{{ option.description }}</span>
        </div>
        <t-tag
          v-if="option.type"
          size="small"
          :theme="getTypeTheme(option.type)"
          variant="light"
          class="field-type-tag"
        >
          {{ getTypeLabel(option.type) }}
        </t-tag>
      </div>
    </template>
    <template #empty>
      <div class="empty-state">
        <t-icon name="info-circle" class="empty-icon" />
        <div class="empty-text">请先在响应体结构中定义字段</div>
      </div>
    </template>
  </t-select>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FlowData } from '@/components/action-panel/model';
import { VALUE_TYPE_MAP } from '@/components/action-panel/constants';

interface FieldOption {
  label: string;
  value: string;
  type?: string;
  description?: string;
}

const props = defineProps<{
  modelValue: string;
  treeData: FlowData[];
  placeholder?: string;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value || ''),
});

// 将树形数据转换为选项列表
const fieldOptions = computed(() => {
  const options: FieldOption[] = [];

  const traverseTree = (nodes: FlowData[], parentPath = '') => {
    nodes.forEach((node) => {
      // 跳过ROOT节点本身，但处理其子节点
      if (node.key === 'ROOT') {
        if (node.children && node.children.length > 0) {
          traverseTree(node.children, '');
        }
        return;
      }

      const currentPath = parentPath ? `${parentPath}.${node.key}` : node.key;

      // 添加当前节点
      options.push({
        label: currentPath,
        value: currentPath,
        type: node.type,
        description: node.description,
      });

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        traverseTree(node.children, currentPath);
      }
    });
  };

  // 处理所有节点，包括可能存在的ROOT节点
  if (props.treeData && props.treeData.length > 0) {
    traverseTree(props.treeData);
  }

  return options;
});

const getTypeLabel = (type: string) => {
  return VALUE_TYPE_MAP[type] || type;
};

const getSelectedFieldLabel = (value: string) => {
  const option = fieldOptions.value.find((opt) => opt.value === value);
  return option?.label || value;
};

const getSelectedFieldDescription = (value: string) => {
  const option = fieldOptions.value.find((opt) => opt.value === value);
  return option?.description || '';
};

const getSelectedFieldType = (value: string) => {
  const option = fieldOptions.value.find((opt) => opt.value === value);
  return option?.type || '';
};

// 获取字段层级深度
const getFieldLevel = (fieldPath: string) => {
  return fieldPath.split('.').length - 1;
};

// 获取显示路径（只显示最后一级）
const getDisplayPath = (fieldPath: string) => {
  const parts = fieldPath.split('.');
  return parts[parts.length - 1];
};

// 根据类型获取主题色
const getTypeTheme = (type: string): 'primary' | 'warning' | 'success' | 'default' | 'danger' => {
  const themeMap: Record<string, 'primary' | 'warning' | 'success' | 'default' | 'danger'> = {
    string: 'primary',
    number: 'warning',
    boolean: 'success',
    object: 'default',
    array: 'primary',
    date: 'danger',
    datetime: 'danger',
  };
  return themeMap[type] || 'default';
};

const onFieldChange = (value: string) => {
  emit('update:modelValue', value || '');
};
</script>

<style lang="less" scoped>
.field-option {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
  padding: 8px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--td-bg-color-container-hover);
  }

  .field-info {
    flex: 1;
    min-width: 0;

    .field-path-container {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 2px;

      .field-icon {
        font-size: 14px;
        color: var(--td-text-color-placeholder);
        flex-shrink: 0;
      }

      .field-path {
        font-weight: 500;
        color: var(--td-text-color-primary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .field-description {
      font-size: 12px;
      color: var(--td-text-color-secondary);
      margin-left: 20px; // 与图标对齐
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.4;
    }
  }

  .field-type-tag {
    flex-shrink: 0;
    margin-top: 2px;
  }
}

.selected-field {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 6px;

  .selected-field-icon {
    font-size: 14px;
    color: var(--td-text-color-placeholder);
    flex-shrink: 0;
  }

  .selected-field-path {
    font-weight: 500;
    color: var(--td-text-color-primary);
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .selected-field-description {
    font-size: 12px;
    color: var(--td-text-color-secondary);
    flex-shrink: 0;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .selected-field-type {
    flex-shrink: 0;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 12px;
  color: var(--td-text-color-placeholder);

  .empty-icon {
    font-size: 24px;
    margin-bottom: 8px;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 14px;
    text-align: center;
    line-height: 1.4;
  }
}
</style>
