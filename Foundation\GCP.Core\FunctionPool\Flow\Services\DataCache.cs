using GCP.Common;
using GCP.FunctionPool.Flow.Models;
using System.Text;

namespace GCP.FunctionPool.Flow.Services
{
    class DataCache : DataBaseService
    {
        /// <summary>
        /// 生成带ProjectId前缀的缓存键
        /// </summary>
        /// <param name="originalKey">原始缓存键</param>
        /// <returns>带前缀的缓存键</returns>
        private string GenerateCacheKey(string originalKey)
        {
            // 尝试从多个来源获取ProjectId
            var projectId = GetProjectId();
            return $"Project:{projectId}:Cache:{originalKey}";
        }

        /// <summary>
        /// 获取当前项目ID
        /// </summary>
        /// <returns>项目ID</returns>
        private string GetProjectId()
        {
            // 1. 尝试从globalData中获取
            if (this.Context?.globalData?.ContainsKey("ProjectId") == true)
            {
                var projectIdFromGlobal = this.Context.globalData["ProjectId"]?.ToString();
                if (!string.IsNullOrEmpty(projectIdFromGlobal))
                {
                    return projectIdFromGlobal;
                }
            }

            // 2. 尝试从Args中获取
            if (this.Context?.Args?.ContainsKey("ProjectId") == true)
            {
                var projectIdFromArgs = this.Context.Args["ProjectId"]?.ToString();
                if (!string.IsNullOrEmpty(projectIdFromArgs))
                {
                    return projectIdFromArgs;
                }
            }

            // 3. 尝试从trackId中解析（如果trackId包含项目信息）
            if (!string.IsNullOrEmpty(this.Context?.trackId))
            {
                // 如果trackId是从数据库记录中生成的，可能包含项目信息
                // 这里可以根据实际的trackId格式进行解析
                // 暂时使用trackId的前8位作为项目标识
                var trackId = this.Context.trackId;
                if (trackId.Length >= 8)
                {
                    return $"track_{trackId.Substring(0, 8)}";
                }
            }

            // 4. 默认值
            return "default";
        }

        [Function("cacheWrite", "缓存写入")]
        public async Task WriteCache(DataCacheWrite data)
        {
            var engine = this.GetEngine();

            // 获取参数值
            var originalCacheKey = GetDataValue(data.CacheKey, engine)?.ToString();
            var cacheValue = GetDataValue(data.CacheValue, engine);
            var expirationSecondsObj = GetDataValue(data.ExpirationSeconds, engine);

            // 参数验证
            if (string.IsNullOrEmpty(originalCacheKey))
            {
                throw new CustomException("缓存键不能为空");
            }

            // 生成带ProjectId前缀的缓存键
            var cacheKey = GenerateCacheKey(originalCacheKey);

            // 解析过期时间
            TimeSpan? expiration = null;
            if (expirationSecondsObj != null)
            {
                if (int.TryParse(expirationSecondsObj.ToString(), out var seconds) && seconds > 0)
                {
                    expiration = TimeSpan.FromSeconds(seconds);
                }
            }

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"原始缓存键: {originalCacheKey}");
                this.Context.Current.ArgsBuilder.AppendLine($"实际缓存键: {cacheKey}");
                this.Context.Current.ArgsBuilder.AppendLine($"缓存值: {cacheValue}");
                this.Context.Current.ArgsBuilder.AppendLine($"过期时间: {(expiration?.TotalSeconds.ToString() ?? "永不过期")}秒");
            }

            try
            {
                // 执行缓存写入操作
                if (expiration.HasValue)
                {
                    await Cache.SetAsync(cacheKey, cacheValue, expiration.Value);
                }
                else
                {
                    await Cache.SetAsync(cacheKey, cacheValue, TimeSpan.FromDays(365)); // 设置一个很长的过期时间
                }

                await this.Context.SqlLog.Info($"缓存写入成功: 原始键={originalCacheKey}, 实际键={cacheKey}", true);
            }
            catch (Exception ex)
            {
                await this.Context.SqlLog.Error(ex, $"缓存写入失败: {ex.Message}", true);
                throw new CustomException($"缓存写入失败: {ex.Message}");
            }
        }

        [Function("cacheRead", "缓存读取")]
        public async Task<object> ReadCache(DataCacheRead data)
        {
            var engine = this.GetEngine();

            // 获取参数值
            var originalCacheKey = GetDataValue(data.CacheKey, engine)?.ToString();
            var defaultValue = GetDataValue(data.DefaultValue, engine);

            // 参数验证
            if (string.IsNullOrEmpty(originalCacheKey))
            {
                throw new CustomException("缓存键不能为空");
            }

            // 生成带ProjectId前缀的缓存键
            var cacheKey = GenerateCacheKey(originalCacheKey);

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"原始缓存键: {originalCacheKey}");
                this.Context.Current.ArgsBuilder.AppendLine($"实际缓存键: {cacheKey}");
                this.Context.Current.ArgsBuilder.AppendLine($"默认值: {defaultValue}");
            }

            try
            {
                // 执行缓存读取操作
                var cachedValue = await Cache.GetAsync<object>(cacheKey);

                if (cachedValue.HasValue)
                {
                    await this.Context.SqlLog.Info($"缓存读取成功: 原始键={originalCacheKey}, 实际键={cacheKey}", true);
                    return cachedValue.Value;
                }
                else
                {
                    await this.Context.SqlLog.Info($"缓存未命中，返回默认值: 原始键={originalCacheKey}, 实际键={cacheKey}", true);
                    return defaultValue;
                }
            }
            catch (Exception ex)
            {
                await this.Context.SqlLog.Error(ex, $"缓存读取失败: {ex.Message}", true);
                throw new CustomException($"缓存读取失败: {ex.Message}");
            }
        }

        [Function("cacheRemove", "缓存删除")]
        public async Task RemoveCache(DataCacheRemove data)
        {
            var engine = this.GetEngine();

            // 获取参数值
            var originalCacheKey = GetDataValue(data.CacheKey, engine)?.ToString();

            // 参数验证
            if (string.IsNullOrEmpty(originalCacheKey))
            {
                throw new CustomException("缓存键不能为空");
            }

            // 生成带ProjectId前缀的缓存键
            var cacheKey = GenerateCacheKey(originalCacheKey);

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"原始缓存键: {originalCacheKey}");
                this.Context.Current.ArgsBuilder.AppendLine($"实际缓存键: {cacheKey}");
            }

            try
            {
                // 执行缓存删除操作
                await Cache.RemoveAsync(cacheKey);

                await this.Context.SqlLog.Info($"缓存删除成功: 原始键={originalCacheKey}, 实际键={cacheKey}", true);
            }
            catch (Exception ex)
            {
                await this.Context.SqlLog.Error(ex, $"缓存删除失败: {ex.Message}", true);
                throw new CustomException($"缓存删除失败: {ex.Message}");
            }
        }

        [Function("cacheExists", "缓存检查存在")]
        public async Task<bool> ExistsCache(DataCacheExists data)
        {
            var engine = this.GetEngine();

            // 获取参数值
            var originalCacheKey = GetDataValue(data.CacheKey, engine)?.ToString();

            // 参数验证
            if (string.IsNullOrEmpty(originalCacheKey))
            {
                throw new CustomException("缓存键不能为空");
            }

            // 生成带ProjectId前缀的缓存键
            var cacheKey = GenerateCacheKey(originalCacheKey);

            // 记录持久化信息
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"原始缓存键: {originalCacheKey}");
                this.Context.Current.ArgsBuilder.AppendLine($"实际缓存键: {cacheKey}");
            }

            try
            {
                // 执行缓存存在检查操作
                var exists = await Cache.ExistsAsync(cacheKey);

                await this.Context.SqlLog.Info($"缓存存在检查: 原始键={originalCacheKey}, 实际键={cacheKey}, 存在={exists}", true);
                return exists;
            }
            catch (Exception ex)
            {
                await this.Context.SqlLog.Error(ex, $"缓存存在检查失败: {ex.Message}", true);
                throw new CustomException($"缓存存在检查失败: {ex.Message}");
            }
        }
    }
}
