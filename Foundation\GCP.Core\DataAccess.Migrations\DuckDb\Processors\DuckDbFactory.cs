using FluentMigrator.Runner.Processors;

namespace GCP.Core.DataAccess.Migrations.Processors.DuckDb
{
    public class DuckDbFactory : ReflectionBasedDbFactory
    {
        private static readonly TestEntry[] _entries =
        {
            new TestEntry("DuckDB.NET.Data", "DuckDB.NET.Data.DuckDBClientFactory"),
        };

        public DuckDbFactory(IServiceProvider serviceProvider)
            : base(serviceProvider, _entries)
        {
        }
    }
}
