﻿using System.Data.Common;

namespace GCP.DataAccess
{
    public interface IDbProvider
    {
        bool NotUseParameter { get; }
        string ProviderName { get; }
        DbProviderType ProviderType { get; }
        DbProviderFactory Factory { get; }
        string GetParameterName(string parameterName);
        string GetTimeFormatStr(DateTime time);
        string GetTimeSql();
        string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "");
        string GetTableSizeSql(string tableName, string schemaName = null);
    }
}
