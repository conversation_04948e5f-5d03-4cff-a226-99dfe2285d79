import { Icon } from 'tdesign-icons-vue-next';

import { ActionConfig } from '../model';

const components = import.meta.glob('./*/*/index.vue', { eager: true }) as Record<string, any>;
const configs = import.meta.glob('./*/*/config.json', { eager: true }) as Record<string, any>;

/**
 * 获取组件
 * @param {string} path 组件路径
 * @returns {any}
 */
export const getComponent = (path) => {
  if (!path) return null;
  return components[`${path}/index.vue`].default;
};
/**
 * 获取组件配置
 * @param {string} path 组件路径
 * @returns {ActionConfig}
 */
export const getComponentConfig = (path) => {
  return configs[`${path}/config.json`].default as ActionConfig;
};

/**
 * 获取下拉选项
 * @returns 动作选项数组
 */
export const getOptions = (flowType: string | undefined) => {
  const options = [];
  let lastCategory = '';
  let lastOption;

  // 定义配置项接口
  interface ConfigItem {
    key: string;
    config: any;
    value: string;
    componentName: string;
    version: number | null | undefined;
    priority: number;
  }

  // 收集所有配置并按版本优先级排序
  const allConfigs: ConfigItem[] = [];
  for (const key in configs) {
    const config = configs[key].default;
    const value = key.substring(0, key.lastIndexOf('/'));
    const componentName = value.split('/').pop() || '';

    // 从config中读取版本号，null为最小优先级，数字越大优先级越高
    const version = config.version;
    let priority: number;
    if (version === null || version === undefined) {
      priority = 0; // null/undefined 最小优先级
    } else if (typeof version === 'number') {
      priority = version; // 数字版本号，越大优先级越高
    } else {
      priority = 0; // 其他类型当作最小优先级
    }

    allConfigs.push({
      key,
      config,
      value,
      componentName,
      version,
      priority
    });
  }

  // 按功能分组，每组只显示最高版本的组件
  const groupedConfigs: Record<string, ConfigItem> = {};
  allConfigs.forEach(item => {
    // 提取功能名称（去掉版本后缀）
    const functionName = item.config.function || item.componentName;
    const baseFunctionName = functionName.replace(/V\d+$/, ''); // 移除V2, V3等版本后缀

    if (!groupedConfigs[baseFunctionName] || groupedConfigs[baseFunctionName].priority < item.priority) {
      groupedConfigs[baseFunctionName] = item;
    }
  });

  // 获取每组的最高版本组件
  const filteredConfigs: ConfigItem[] = Object.values(groupedConfigs);

  for (const item of filteredConfigs) {
    const { config, value, key } = item;

    if(config.flowType && config.flowType !== flowType){
      continue;
    }

    const option = {
      content: config.name,
      value: '',
      divider: false,
      prefixIcon: () => <Icon name={config.icon} />,
    };
    option.value = value;

    const category = key.split('/')[1];
    if (lastCategory && category !== lastCategory) {
      lastOption.divider = true;
    }
    options.push(option);
    lastOption = option;
    lastCategory = category;
  }
  return options;
};

/**
 * 获取所有选项（包括旧版本）
 * @returns 所有动作选项数组
 */
export const getAllOptions = (flowType: string | undefined) => {
  interface Option {
    content: string;
    value: string;
    divider: boolean;
    prefixIcon: () => any;
  }

  const options: Option[] = [];
  let lastCategory = '';
  let lastOption: Option | undefined;

  for (const key in configs) {
    const config = configs[key].default;

    if(config.flowType && config.flowType !== flowType){
      continue;
    }

    const option: Option = {
      content: config.name,
      value: '',
      divider: false,
      prefixIcon: () => <Icon name={config.icon} />,
    };
    const value = key.substring(0, key.lastIndexOf('/'));
    option.value = value;

    const category = key.split('/')[1];
    if (lastCategory && category !== lastCategory && lastOption) {
      lastOption.divider = true;
    }
    options.push(option);
    lastOption = option;
    lastCategory = category;
  }
  return options;
};
