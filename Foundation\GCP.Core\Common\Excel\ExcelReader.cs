﻿using ExcelDataReader;
using GCP.DataAccess;
using System.Data;

namespace GCP.Common.Excel
{
    public static class ExcelReader
    {
        public static void ImportExcelToDb(this IDbContext db, string filePath, bool useTransaction = false, params string[] tableNames)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                ImportExcelToDb(db, stearm, useTransaction, tableNames);
            }
        }

        public static void ImportExcelToDb(this IDbContext db, Stream fileStream, bool useTransaction = false, params string[] tableNames)
        {
            var ds = GetDataSet(fileStream);
            var useCustomTableName = tableNames.Length > 0;

            using (var conn = db.CreateOpenConnection())
            {
                IDbTransaction tran = null;
                if (useTransaction) tran = conn.BeginTransaction();
                for (int i = 0; i < ds.Tables.Count; i++)
                {
                    var dt = ds.Tables[i];
                    var tableName = dt.TableName;
                    if (useCustomTableName)
                    {
                        if (i >= tableNames.Length) break;
                        tableName = tableNames[i];
                    }
                    conn.BulkCopy(tableName, dt, useTransaction ? tran : null);
                }
                if (useTransaction) tran.Commit();
            }
        }

        public static List<T> GetList<T>(string filePath)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                return GetList<T>(stearm);
            }
        }
        public static List<T> GetList<T>(Stream fileStream)
        {
            using (var reader = OpenReader(fileStream))
            {
                reader.Read();
                return reader.GetList<T>(null, (r, i) =>
                {
                    return Convert.ToString(reader.GetValue(i));
                });
            }
        }

        public static DataTable DataForEach(string filePath, Action<DataRow, int> action, bool isSave = false, int total = -1)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                return DataForEach(stearm, action, isSave, total);
            }
        }

        public static DataTable DataForEach(Stream fileStream, Action<DataRow, int> action, bool isSave = false, int total = -1)
        {
            return DataForEach(fileStream, (t, i) =>
            {
                action(t, i);
                return t;
            }, isSave, total);
        }

        public static DataTable DataForEach(string filePath, Func<DataRow, int, DataRow> action, bool isSave = false, int total = -1)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                return DataForEach(stearm, action, isSave, total);
            }
        }

        public static DataTable DataForEach(Stream fileStream, Func<DataRow, int, DataRow> action, bool isSave = false, int total = -1)
        {
            using (var reader = OpenReader(fileStream))
            {
                reader.Read();

                return reader.GetDataTable(action, (r, i) =>
                {
                    return Convert.ToString(reader.GetValue(i));
                }, total, isSave);
            }
        }
        public static List<T> DataForEach<T>(string filePath, Action<T, int> action, bool isSave = false, int total = -1)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                return DataForEach(stearm, action, isSave, total);
            }
        }

        public static List<T> DataForEach<T>(Stream fileStream, Action<T, int> action, bool isSave = false, int total = -1)
        {
            return DataForEach<T>(fileStream, (t, i) =>
            {
                action(t, i);
                return t;
            }, isSave, total);
        }

        public static List<T> DataForEach<T>(string filePath, Func<T, int, T> action, bool isSave = false, int total = -1)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                return DataForEach(stearm, action, isSave, total);
            }
        }

        public static List<T> DataForEach<T>(Stream fileStream, Func<T, int, T> action, bool isSave = false, int total = -1)
        {
            using (var reader = OpenReader(fileStream))
            {
                reader.Read();
                return reader.GetList<T>(action, (r, i) =>
                {
                    return Convert.ToString(reader.GetValue(i));
                }, total, null, isSave);
            }
        }

        public static DataTable GetDataTable(string filePath)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                return GetDataTable(stearm);
            }
        }

        public static DataTable GetDataTable(Stream fileStream)
        {
            var tables = GetDataSet(fileStream).Tables;
            return tables.Count >= 1 ? tables[0] : null;
        }

        public static DataSet GetDataSet(string filePath)
        {
            using (var stearm = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                return GetDataSet(stearm);
            }
        }

        public static DataSet GetDataSet(Stream fileStream)
        {
            using (var reader = OpenReader(fileStream))
            {
                return reader.AsDataSet(new ExcelDataSetConfiguration
                {
                    UseColumnDataType = true,
                    ConfigureDataTable = t => new ExcelDataTableConfiguration
                    {
                        UseHeaderRow = true
                    }
                });
            }
        }

        internal static IExcelDataReader OpenReader(Stream fileStream)
        {
            return ExcelReaderFactory.CreateReader(fileStream);
        }
    }
}
