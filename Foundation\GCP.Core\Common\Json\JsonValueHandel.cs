﻿using System.Text.Json;

namespace GCP.Common.Json
{
    public static class JsonValueHandel
    {
        public static object ParseObject(this JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.Object => element.EnumerateObject().ToDictionary(x => x.Name, x => ParseObject(x.Value)),
                JsonValueKind.Array => element.EnumerateArray().Select(ParseObject),
                JsonValueKind.String => element.GetString(),
                JsonValueKind.Number => ParseNumber(element),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null,
                _ => throw new JsonException($"Unsupported JSON value kind: {element.ValueKind}"),
            };
        }

        private static object ParseNumber(JsonElement element)
        {
            if (element.TryGetDecimal(out decimal number))
            {
                if (number == (int)number)
                    return (int)number;
                else if (number == (long)number)
                    return (long)number;
                else
                    return number;
            }
            else
            {
                return element.GetRawText();
            }
        }
    }
}
