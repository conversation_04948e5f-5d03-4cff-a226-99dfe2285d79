﻿using GCP.Common;
using GCP.Core.Ai;
using GCP.Core.Ai.Models;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using Microsoft.Extensions.AI;
using ModelContextProtocol.Client;
using OpenAI;
using Serilog;
using System.ClientModel;
using System.Collections.ObjectModel;
using System.Text;

namespace GCP.Functions.Common.Services
{
    [Function("ai", "AI实例服务")]
    internal class AiService : BaseService
    {
        public static ChatModelConfig ChatModelConfig { get; set; }
        public static ObservableCollection<MessageInfo> MessageInfos { get; set; }
        public static List<ChatMessage> Messages { get; set; }
        public static IChatClient ChatClient { get; set; }
        public static IEmbeddingGenerator<string, Embedding<float>> EmbeddingGenerator { get; set; }
        public static IList<McpClientTool> Tools { get; set; }

        static AiService()
        {
            ChatModelConfig = new ChatModelConfig
            {
                Type = "OpenAI",
                ApiKey = "sk-vyqnjvnnnaqlshrznbaixbaxilztvwwifkpgiregwwnkqpog",
                ChatModelID = "Qwen/Qwen2.5-72B-Instruct", // "Qwen/QwQ-32B",
                BaseURL = "https://api.siliconflow.cn/v1",
                EmbeddingModelID = "BAAI/bge-m3"
            };

            //ChatModelConfig = new ChatModelConfig
            //{
            //    Type = "Ollama",
            //    BaseURL = "http://10.88.32.2:11434/v1",
            //    ModelID = "deepseek-r1:70b",
            //};
            InitializeAsync();
        }

        private static async void InitializeAsync()
        {
            if (ChatModelConfig.Type == "OpenAI")
            {
                var apiKeyCredential = new ApiKeyCredential(ChatModelConfig.ApiKey);

                var openAIClientOptions = new OpenAIClientOptions();
                openAIClientOptions.Endpoint = new Uri(ChatModelConfig.BaseURL);

                var openaiClient = new OpenAIClient(apiKeyCredential, openAIClientOptions);
                ChatClient = openaiClient
                    .AsChatClient(ChatModelConfig.ChatModelID)
                    .AsBuilder()
                    .UseFunctionInvocation()
                    .Build();

                EmbeddingGenerator = openaiClient.AsEmbeddingGenerator(ChatModelConfig.EmbeddingModelID);
            }
            else
            {
                ChatClient = new OllamaChatClient(ChatModelConfig.BaseURL, ChatModelConfig.ChatModelID);

                EmbeddingGenerator = new OllamaEmbeddingGenerator(ChatModelConfig.BaseURL, ChatModelConfig.EmbeddingModelID);
            }

            Messages =
               [
               // Add a system message
               new(ChatRole.System, "You are a helpful assistant, helping us test MCP server functionality."),
                ];

            MessageInfos = new ObservableCollection<MessageInfo>();
            Tools = await GetMcpTools();
        }

        [Function("getMcpTools", "获取MCP工具")]
        public static async Task<IList<McpClientTool>> GetMcpTools()
        {
            var tools = await McpService.GetToolsAsync();
            return tools;
        }

        [Function("trainDatabaseSchema", "训练数据库Schema")]
        public async Task TrainDatabaseSchemaAsync(string dataSourceId)
        {
            await using var gcpdb = new GcpDb();
            var dataSource = gcpdb.LcDataSources
                .Where(t => t.Id == dataSourceId)
                .FirstOrDefault();

            var schemas = new List<string>();
            if (!string.IsNullOrEmpty(dataSource.Database))
            {
                schemas.Add(dataSource.Database);
            }

            await using var db = new DbBase(dataSource.DataProvider, dataSource.ConnectionString);
            var tables = db.GetSchema(includedSchemas: schemas.ToArray()).Tables;
            foreach (var table in tables)
            {
                try
                {
                    // 构建包含所有列信息的表描述文本
                    StringBuilder tableDescription = new StringBuilder();
                    tableDescription.AppendLine($"表名: {table.TableName}");
                    tableDescription.AppendLine($"描述: {table.Description ?? "无描述"}");

                    // 添加外键关系信息
                    if (table.ForeignKeys != null && table.ForeignKeys.Count > 0)
                    {
                        tableDescription.AppendLine("外键关系:");
                        foreach (var fk in table.ForeignKeys)
                        {
                            tableDescription.AppendLine($"  - {fk.MemberName}");
                        }
                    }

                    tableDescription.AppendLine("列信息:");

                    foreach (var column in table.Columns)
                    {
                        tableDescription.AppendLine($"  - 列名: {column.ColumnName}, 类型: {column.DataType}, 主键: {(column.IsPrimaryKey ? "是" : "否")}, 可空: {(column.IsNullable ? "是" : "否")}, 描述: {column.Description.TrimStart("Description:") ?? "无描述"}");
                    }

                    var tableEmbedding = new
                    {
                        ConnectionId = dataSourceId,
                        table.TableName,
                        Description = tableDescription.ToString(),
                    };

                    var text = JsonHelper.Serialize(tableEmbedding);

                    var vector = await EmbeddingGenerator.GenerateEmbeddingAsync(text); // 返回 float[]
                }
                catch (Exception ex)
                {
                    Log.Error("训练表{TableTableName}失败：{ExMessage}", table.TableName, ex.Message);
                }
            }
            
        }

        [Function("sendMessage", "发送消息")]
        public static async Task<object> SendMessage(string message)
        {
            if (Tools == null)
            {
                throw new CustomException("正在获取MCP工具列表，请稍后再试。");
            }
            if (Messages.Count == 0)
            {
                Messages =
                   [
                   // Add a system message
                   new(ChatRole.System, "You are a helpful assistant, helping us test MCP server functionality."),
                ];
            }

            MessageInfos.Add(new("User", message));
            Messages.Add(new(ChatRole.User, message));

            var options = new ChatOptions
            {
                Tools = [.. Tools]
            };

            //List<ChatResponseUpdate> updates = [];
            //await foreach (var update in ChatClient.GetStreamingResponseAsync(Messages, options))
            //{
            //    updates.Add(update);
            //}
            //Messages.AddMessages(updates);
            var response = await ChatClient.GetResponseAsync(Messages, options);

            Messages.AddMessages(response);

            var toolUseMessages = response.Messages.Where(m => m.Role == ChatRole.Tool);

            if (response.Messages[0].Contents.Count > 1)
            {
                var functionCall = (FunctionCallContent)response.Messages[0].Contents[1];
                string arguments = "";
                MessageInfo messageInfo = new MessageInfo();
                if (functionCall.Arguments != null)
                {
                    foreach (var arg in functionCall.Arguments)
                    {
                        arguments += $"{arg.Key}:{arg.Value};";
                    }
                    messageInfo.FunctionCallInfo = $"调用函数名:{functionCall.Name};参数信息：{arguments}";
                    foreach (var toolUseMessage in toolUseMessages)
                    {
                        var functionResultContent = (FunctionResultContent)toolUseMessage.Contents[0];

                        messageInfo.FunctionCallResult = $"调用工具结果：{functionResultContent.Result}";
                    }
                }
                MessageInfos.Add(messageInfo);
            }
            MessageInfos.Add(new("Assistant", response.Text));
            return MessageInfos;
        }

        public void ClearMessages()
        {
            MessageInfos.Clear();
            Messages.Clear();
        }
    }
}
