<template>
  <t-select :options="options" placeholder="请选择数据源"></t-select>
</template>
<script lang="ts">
export default {
  name: 'DataSourceSelect',
};
</script>
<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { api, Services } from '@/api/system';

const options = ref([]);
const dataSources = ref();
onMounted(() => {
  fetchDataSources();
});

const fetchDataSources = async () => {
  await api.run(Services.dataSourceGetAll).then((data) => {
    dataSources.value = data;
    options.value = data.map((item) => ({ label: item.dataSourceName, value: item.id }));
  });
};
</script>
<style lang="less" scoped></style>
