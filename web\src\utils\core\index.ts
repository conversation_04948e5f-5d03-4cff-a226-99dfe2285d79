import { isEmpty } from '../base';
import EventBus from '../eventbus';

const baseCore = {
  ipc: null,
  config: {
    baseUrl: '',
    languageCodes: ['zh-CN', 'en-US'],
  },
  // getToken() {
  //   return localStorage.getItem('_token');
  // },
  // setToken(token: string) {
  //   localStorage.setItem('_token', token);
  // },
  // getPId() {
  //   return localStorage.getItem('_pid');
  // },
  // setPId(projectId: string) {
  //   localStorage.setItem('_pid', projectId);
  // },
  // getPName() {
  //   return localStorage.getItem('_pname');
  // },
  // setPName(name: string) {
  //   localStorage.setItem('_pname', name);
  // },
  // getSId() {
  //   return localStorage.getItem('_sid');
  // },
  // setSId(solutionId: string) {
  //   localStorage.setItem('_sid', solutionId);
  // },
  utils: {
    isEmpty,
  },
};

const core = baseCore;

core.ipc = EventBus.init();
core.ipc.addTarget('_self', window);

export { core };
