// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// AI对话消息
	/// </summary>
	[Table("lc_chat_message")]
	public class LcChatMessage : IBaseEntity
	{
		/// <summary>
		/// Description:数据行ID号
		/// </summary>
		[Column("ID"             , CanBeNull = false, IsPrimaryKey = true)] public string    Id             { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                            )] public DateTime  TimeCreate     { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"        , CanBeNull = false                     )] public string    Creator        { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                          )] public DateTime? TimeModified   { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                               )] public string?   Modifier       { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                  )] public short     State          { get; set; } // smallint
		/// <summary>
		/// Description:会话ID
		/// </summary>
		[Column("CONVERSATION_ID", CanBeNull = false                     )] public string    ConversationId { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:角色
		/// </summary>
		[Column("ROLE"           , CanBeNull = false                     )] public string    Role           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:内容
		/// </summary>
		[Column("CONTENT"        , CanBeNull = false                     )] public string    Content        { get; set; } = null!; // text
		/// <summary>
		/// Description:序号
		/// </summary>
		[Column("SEQUENCE"                                               )] public int       Sequence       { get; set; } // int
	}
}
