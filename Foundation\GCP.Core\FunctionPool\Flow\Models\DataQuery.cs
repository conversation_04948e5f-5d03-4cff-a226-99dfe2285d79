﻿using GCP.Common;
using GCP.DataAccess;

namespace GCP.FunctionPool.Flow.Models
{
    public class DataQueryData
    {
        public string Name { get; set; }
        public string DataSource { get; set; }
        public bool AutoPaged { get; set; }
        public bool IsPaging { get; set; }
        public bool HasTotal { get; set; }
        public DataValue PageSize { get; set; } = new DataValue { Type = "text", TextValue = "500" };
        public DataValue PageIndex { get; set; } = new DataValue { Type = "text", TextValue = "1" };
        public string Description { get; set; }
        public string OperateType { get; set; }
        public SqlInfo SqlInfo { get; set; }
        public List<ConfigureInfo> ConfigureInfo { get; set; }

        /// <summary>
        /// 输出方式配置
        /// </summary>
        public OutputConfig OutputConfig { get; set; }
    }

    public class SqlInfo
    {
        public string Sql { get; set; }
        public List<SqlParamInfo> Parameters { get; set; }
    }

    public class ConfigureInfo
    {
        public string Id { get; set; }
        public DataSourceTableData TableData { get; set; }
    }

    public class SqlParamInfo
    {
        public string ParamName { get; set; }
        public string ParamCode { get; set; }
        public DataValue ParamValue { get; set; }
    }

    public enum ConditionInfoType
    {
        Node,
        Leaf
    }

    public class ConditionInfo
    {
        public string Id { get; set; }
        public ConditionInfoType Type { get; set; }
        public ConditionOperatorType Operator { get; set; }
        public DataValue ColumnValue { get; set; }
        public string Column { get; set; }
        public DataValue Value { get; set; }
        public List<ConditionInfo> Children { get; set; }
        public bool IsFilter { get; set; }
    }

    public class ColumnInfo
    {
        public string ColumnName { get; set; }
        public DataValue ColumnValue { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool Required { get; set; }
        /// <summary>
        /// 是否是自定义列
        /// </summary>
        public bool IsCustomize { get; set; }
        /// <summary>
        /// 是否是查询列
        /// </summary>
        public bool IsQuery { get; set; }
        /// <summary>
        /// 是否更新
        /// </summary>
        public bool IsUpdate { get; set; }
        /// <summary>
        /// 是否插入
        /// </summary>
        public bool IsInsert { get; set; } = true;
        /// <summary>
        /// 是否是条件
        /// </summary>
        public bool IsCondition { get; set; }
        /// <summary>
        /// 是否是主键
        /// </summary>
        public bool IsPrimaryKey { get; set; }
    }

    public class SortColumnInfo
    {
        public string Id { get; set; }
        public string ColumnName { get; set; }
        public string Order { get; set; }
    }

    public class DataSourceTableData
    {
        public string TableName { get; set; }
        public string TableDescription { get; set; }
        public List<ColumnInfo> Columns { get; set; }
        public List<SortColumnInfo> SortColumns { get; set; }
        public ConditionInfo Conditions { get; set; }

        public JoinType JoinType { get; set; } = JoinType.None;
        public string JoinTableName { get; set; }
        public ConditionInfo JoinConditions { get; set; }
    }

    public enum JoinType
    {
        None,
        Inner,
        Left,
        Right,
        Full
    }

    /// <summary>
    /// 输出方式枚举
    /// </summary>
    public enum OutputType
    {
        /// <summary>
        /// 列表（默认）
        /// </summary>
        List,
        /// <summary>
        /// 字典
        /// </summary>
        Dictionary,
        /// <summary>
        /// 单条记录
        /// </summary>
        Single,
        /// <summary>
        /// 记录数量
        /// </summary>
        Count
    }

    /// <summary>
    /// 输出配置
    /// </summary>
    public class OutputConfig
    {
        /// <summary>
        /// 输出类型
        /// </summary>
        public OutputType Type { get; set; } = OutputType.List;

        /// <summary>
        /// 字典配置（当 Type = Dictionary 时使用）
        /// </summary>
        public DictionaryConfig DictionaryConfig { get; set; }
    }

    /// <summary>
    /// 字典配置
    /// </summary>
    public class DictionaryConfig
    {
        /// <summary>
        /// 键字段配置（支持多列组合）
        /// </summary>
        public List<string> KeyColumns { get; set; } = new List<string>();

        /// <summary>
        /// 键分隔符（多列组合时使用）
        /// </summary>
        public string KeySeparator { get; set; } = "|";

        /// <summary>
        /// 值字段（单列）
        /// </summary>
        public string ValueColumn { get; set; }

        /// <summary>
        /// 是否返回完整对象作为值（如果为 true，则忽略 ValueColumn）
        /// </summary>
        public bool UseFullObjectAsValue { get; set; } = false;
    }
}
