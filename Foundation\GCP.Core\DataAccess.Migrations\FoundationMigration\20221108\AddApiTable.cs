﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20221108121802, "初始化API表")]
    public class AddApiTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_API").WithDescription("API定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("REQUEST_TYPE").AsAnsiString(50).Nullable().WithColumnDescription("请求类型")
               .WithColumn("HTTP_URL").AsAnsiString(200).Nullable().WithColumnDescription("请求地址")

               .WithColumn("DIR_CODE").AsAnsiString(50).Nullable().WithColumnDescription("目录编码")
               .WithColumn("API_NAME").AsAnsiString(200).WithColumnDescription("API名称")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               .WithColumn("FUNCTION_ID").AsAnsiString(36).Nullable().WithColumnDescription("函数ID")
               .WithColumn("CLUSTER_ID").AsAnsiString(36).Nullable().WithColumnDescription("集群ID")
               .WithColumn("HOSTS").AsAnsiString(200).Nullable().WithColumnDescription("匹配主机名, 支持多个逗号分隔")
               .WithColumn("HEADERS").AsAnsiString(2000).Nullable().WithColumnDescription("匹配请求头, json对象")
               .WithColumn("QUERY_PARAMETERS").AsAnsiString(2000).Nullable().WithColumnDescription("匹配url参数, json对象")
               .WithColumn("BASE_URL").AsAnsiString(200).Nullable().WithColumnDescription("API基础地址")
               .WithColumn("API_TYPE").AsInt16().WithDefaultValue(1).WithColumnDescription("API类型, 1-系统API, 2-第三方API, 3-网关API")
               .WithColumn("BODY").AsAnsiString(4000).Nullable().WithColumnDescription("API请求体")
               .WithColumn("RESPONSE").AsAnsiString(4000).Nullable().WithColumnDescription("API返回体")
               .WithColumn("TIMEOUT_IN_SECONDS").AsInt16().Nullable().WithColumnDescription("超时时间（秒）")
               .WithColumn("RESPONSE_ID").AsAnsiString(36).Nullable().WithColumnDescription("响应体配置ID");
            ;

            Create.Index("API_URL_IDX")
                .OnTable("LC_API")
                .OnColumn("HTTP_URL").Ascending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .WithOptions().Unique();
        }

        public override void Down()
        {
            Delete.Table("LC_API");
        }
    }
}
