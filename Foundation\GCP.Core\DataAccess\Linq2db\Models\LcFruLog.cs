// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 流程运行日志
	/// </summary>
	[Table("lc_fru_log")]
	public class LcFruLog
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"       , CanBeNull = false, IsPrimaryKey = true)] public string   Id        { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:流程运行实例 ID
		/// </summary>
		[Column("PROC_ID"  , CanBeNull = false                     )] public string   ProcId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:流程步骤实例 ID
		/// </summary>
		[Column("STEP_ID"                                          )] public string?  StepId    { get; set; } // varchar(36)
		/// <summary>
		/// Description:日志级别 INFO/ERROR/WARN/DEBUG
		/// </summary>
		[Column("LEVEL"    , CanBeNull = false                     )] public string   Level     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:日志类别 FLOW/ACTION
		/// </summary>
		[Column("CATEGORY" , CanBeNull = false                     )] public string   Category  { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:日志时间戳
		/// </summary>
		[Column("TIMESTAMP"                                        )] public DateTime Timestamp { get; set; } // datetime
		/// <summary>
		/// Description:日志信息
		/// </summary>
		[Column("MESSAGE"  , CanBeNull = false                     )] public string   Message   { get; set; } = null!; // longtext
		/// <summary>
		/// Description:异常信息
		/// </summary>
		[Column("EXCEPTION"                                        )] public string?  Exception { get; set; } // longtext
	}
}
