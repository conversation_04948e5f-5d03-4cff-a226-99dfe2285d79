// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 第三方用户定义
	/// </summary>
	[Table("lc_app_user")]
	public class LcAppUser : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"                  , CanBeNull = false, IsPrimaryKey = true)] public string    Id                 { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                                 )] public DateTime  TimeCreate         { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"             , CanBeNull = false                     )] public string    Creator            { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                               )] public DateTime? TimeModified       { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                                    )] public string?   Modifier           { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                       )] public short     State              { get; set; } // smallint
		/// <summary>
		/// Description:用户名
		/// </summary>
		[Column("USER_NAME"           , CanBeNull = false                     )] public string    UserName           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:密码
		/// </summary>
		[Column("PASSWORD"            , CanBeNull = false                     )] public string    Password           { get; set; } = null!; // varchar(200)
		/// <summary>
		/// Description:授权类型
		/// </summary>
		[Column("AUTH_TYPE"           , CanBeNull = false                     )] public string    AuthType           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:分类
		/// </summary>
		[Column("CATEGORY"            , CanBeNull = false                     )] public string    Category           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:系统编码
		/// </summary>
		[Column("APP_ID"              , CanBeNull = false                     )] public string    AppId              { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:开始生效时间
		/// </summary>
		[Column("START_EFFECTIVE_DATE"                                        )] public DateTime  StartEffectiveDate { get; set; } // date
		/// <summary>
		/// Description:结束生效时间
		/// </summary>
		[Column("END_EFFECTIVE_DATE"                                          )] public DateTime? EndEffectiveDate   { get; set; } // date
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                                 )] public string?   Description        { get; set; } // varchar(200)
	}
}
