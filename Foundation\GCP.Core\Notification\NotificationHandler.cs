﻿using GCP.Common;
using Microsoft.Extensions.Configuration;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class NotificationHandler
    {
        public static IServiceCollection AddNotificationHandler(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<INotificationService, NotificationService>();

            return services;
        }
    }
}
