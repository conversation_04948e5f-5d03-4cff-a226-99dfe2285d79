namespace GCP.Iot.Models
{
    public class EquipmentDataChangedEventArgs : EventArgs
    {
        public string EquipmentId { get; }
        public string EquipmentType { get; }
        public Dictionary<string, object> Values { get; }
        public DateTime Timestamp { get; }

        public EquipmentDataChangedEventArgs(string equipmentId, string equipmentType, Dictionary<string, object> values)
        {
            EquipmentId = equipmentId;
            EquipmentType = equipmentType;
            Values = values;
            Timestamp = DateTime.Now;
        }
    }
}