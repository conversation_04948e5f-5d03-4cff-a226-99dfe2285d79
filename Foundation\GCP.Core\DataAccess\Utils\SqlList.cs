﻿using System.Data;
using System.Collections;
using System.Data.Common;

namespace GCP.DataAccess
{
    public class SqlList : List<SqlBuilder<int>>
    {
        private SqlSession session;

        public SqlList(IDbContext dbContext)
        {
            this.session = new SqlSession(dbContext);
        }

        public SqlBuilderBase AddSql(string sqlString, params object[] args)
        {
            var sqlBuilder = new SqlBuilder<int>(session);
            this.Add(sqlBuilder);
            return sqlBuilder.Append(sqlString, args);
        }

        public void AddExecute(string sqlString, object obj)
        {
            IEnumerable multiExec = DbHelper.GetMultiExec(obj);
            if (multiExec != null)
            {
                foreach (var parm in multiExec)
                {
                    this.Add(new SqlBuilder<int>(session).Init(sqlString, parm));
                }
            }
            else
            {
                this.Add(new SqlBuilder<int>(session).Init(sqlString, obj));
            }
        }

        public void AddInsert(SqlBuilder<int> sqlBuilder)
        {
            sqlBuilder.SqlType = "INSERT";
            this.Add(sqlBuilder);
        }

        public void AddInsert<T>(T obj, string tableName = null)
        {
            this.AddExecute(SqlBuilderBase.GetInsertSql(session.DbProvider, obj, tableName), obj);
        }

        public void AddUpdate(SqlBuilder<int> sqlBuilder)
        {
            sqlBuilder.SqlType = "UPDATE";
            this.Add(sqlBuilder);
        }
        public void AddUpdate(string tableName, object setObj, object whereObj = null)
        {
            this.Add(SqlBuilderBase.GetUpdateSql(session, tableName, setObj, whereObj));
        }
        public void AddUpdate<T>(T setObj, object whereObj = null)
        {
            this.AddUpdate(DbHelper.GetTableName(setObj.GetType()), setObj, whereObj);
        }

        public void AddDelete(SqlBuilder<int> sqlBuilder)
        {
            sqlBuilder.SqlType = "DELETE";
            this.Add(sqlBuilder);
        }
        public void AddDelete(string tableName, object whereObj)
        {
            this.Add(SqlBuilderBase.GetDeleteSql(session, tableName, whereObj));
        }

        public async Task<int> BatchExecute(DataTable dt = null, bool isRowByRow = false, int commandTimeout = 60, bool openTransaction = true, bool exceptionSkip = false, Func<Exception, string, Task> onException = null, int batchSize = 500)
        {
            if (this.Count == 0 && dt == null)
            {
                return 0;
                //throw new Exception("SQL list cannot be null. Please use the [Add] method.");
            }
            int rowsAffected = 0;
            DbTransaction tran = null;
            await using var conn = session.CreateConnection();
            await conn.OpenAsync();
            if (openTransaction)
                tran = await conn.BeginTransactionAsync();

            if (isRowByRow)
            {
                foreach (var sql in this)
                {
                    try
                    {
                        sql.CommandTimeout = 5;
                        rowsAffected += await sql.ExecuteAsync(conn, tran).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        if (exceptionSkip && onException != null)
                        {
                            await onException.Invoke(ex, sql.ExecutableSql).ConfigureAwait(false);
                        }
                        else
                        {
                            throw;
                        }
                    }
                }
            }
            else
            {
                if (dt is { Rows.Count: > 0 })
                {
                    conn.BulkCopy(dt, tran);
                    rowsAffected += dt.Rows.Count;
                }

                if (this.Count > 0)
                {
                    //var isOracle = this.session.DbProvider.GetDbProviderType() == DbProviderType.Oracle;

                    foreach (var batch in this.Chunk(batchSize))
                    {
                        rowsAffected += await conn.BatchExecute(batch, tran).ConfigureAwait(false);
                    }
                }
            }

            if (openTransaction)
                await tran.CommitAsync();
            return rowsAffected;
        }
    }
}
