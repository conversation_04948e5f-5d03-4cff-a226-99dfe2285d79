using Npgsql;
using System.Data;

namespace GCP.DataAccess
{
    internal static class PostgreSqlBulkCopyProvider
    {
        private static NpgsqlBinaryImporter PrepareOptionsForDataTable(NpgsqlConnection connection, string tableName, DataTable dt, NpgsqlTransaction transaction = null)
        {
            var columnList = string.Join(", ", dt.Columns.Cast<DataColumn>().Select(col => col.ColumnName));
            return connection.BeginBinaryImport($"COPY {tableName} ({columnList}) FROM STDIN (FORMAT BINARY)");
        }

        private static NpgsqlBinaryImporter PrepareOptionsForDataReader(NpgsqlConnection connection, string tableName, IDataReader reader, NpgsqlTransaction transaction = null)
        {
            var columns = new string[reader.FieldCount];
            for (int i = 0; i < reader.FieldCount; i++)
            {
                columns[i] = reader.GetName(i);
            }
            var columnList = string.Join(", ", columns);
            return connection.BeginBinaryImport($"COPY {tableName} ({columnList}) FROM STDIN (FORMAT BINARY)");
        }

        internal static void PostgreSqlBulkCopy(this IDbConnection connection, string tableName, IDataReader reader, IDbTransaction transaction = null)
        {
            var npgsqlConnection = (NpgsqlConnection)connection;
            using (var writer = PrepareOptionsForDataReader(npgsqlConnection, tableName, reader, (NpgsqlTransaction)transaction))
            {
                var columnCount = reader.FieldCount;
                while (reader.Read())
                {
                    writer.StartRow();
                    for (int i = 0; i < columnCount; i++)
                    {
                        if (reader.IsDBNull(i))
                            writer.WriteNull();
                        else
                            writer.Write(reader.GetValue(i));
                    }
                }
                writer.Complete();
            }
        }

        internal static void PostgreSqlBulkCopy(this IDbConnection connection, string tableName, DataTable dt, IDbTransaction transaction = null)
        {
            var npgsqlConnection = (NpgsqlConnection)connection;
            using (var writer = PrepareOptionsForDataTable(npgsqlConnection, tableName, dt, (NpgsqlTransaction)transaction))
            {
                foreach (DataRow row in dt.Rows)
                {
                    writer.StartRow();
                    foreach (DataColumn col in dt.Columns)
                    {
                        if (row.IsNull(col))
                            writer.WriteNull();
                        else
                            writer.Write(row[col]);
                    }
                }
                writer.Complete();
            }
        }
    }
} 