﻿using GCP.Common;
using GCP.DataAccess;
using Jint;
using System.Data;

namespace GCP.Functions.Common
{
    /// <summary>
    /// js 控制台日志
    /// </summary>
    public class JavascriptConsole
    {
        private readonly FunctionContext _ctx;
        private readonly SqlContextLogger _logger;
        private readonly Engine _engine;
        public JavascriptConsole(Engine engine, FunctionContext ctx)
        {
            _ctx = ctx;
            _logger = ctx.SqlLog;
            _engine = engine;
        }

        public void log(string level, object data)
        {
            string msg = "";
            if (data == null)
            {
                msg = "null";
            }
            else if (data is string dataStr)
            {
                msg = dataStr;
            }
            else if (data is IDbContext || data is IDbConnection)
            {
                _ =_logger.WriteLog(level, "无法打印保密数据", true);
            }
            else
            {
                msg = JsonHelper.Serialize(data);
            }

            var name = _ctx.Current?.ScriptName;
            if (!string.IsNullOrEmpty(name))
            {
                name += " - ";
            }
            _ = _logger.WriteLog(level, name + msg, true);
        }

        public void log(object data)
        {
            log("INFO", data);
        }

        public void info(object data)
        {
            log("INFO", data);
        }

        public void trace()
        {
            _ = _logger.WriteLog("TRACE", $"Trace{Environment.NewLine}{_engine.Advanced.StackTrace}", true);
        }

        public void debug(object data)
        {
            log("DEBUG", data);
        }

        public void warn(object data)
        {
            log("WARN", data);
        }

        public void error(object data)
        {
            log("ERROR", data);
        }
    }
}
