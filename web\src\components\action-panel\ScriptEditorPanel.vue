<template>
  <t-space direction="vertical" style="width: 100%">
    <editor
      ref="editorRef"
      v-model:value="scriptValue"
      language="typescript"
      :enable-intellisense="true"
      :current-variables="actionFlowStore.currentVariables"
      :local-variables="actionFlowStore.localVariables"
      :global-variables="globalVariables"
      :functions="functionList"
      style="height: 250px"
    ></editor>
    <t-tabs default-value="3">
      <t-tab-panel value="1" label="局部变量">
        <variable-tree
          :filter-text="searchText"
          :variable-list="actionFlowStore.localVariables"
          @dblclick="onDblclickVariable"
        ></variable-tree>
      </t-tab-panel>
      <t-tab-panel value="2" label="全局变量">
        <variable-tree
          :filter-text="searchText"
          :variable-list="globalVariables"
          @dblclick="onDblclickVariable"
        ></variable-tree>
      </t-tab-panel>
      <t-tab-panel value="3" label="函数">
        <enhanced-function-list @dblclick="onDblclickFunction"></enhanced-function-list>
      </t-tab-panel>
      <template #action>
        <t-input v-model="searchText" class="search-input" size="small" placeholder="搜索" clearable>
          <template #prefixIcon>
            <search-icon></search-icon>
          </template>
        </t-input>
      </template>
    </t-tabs>
  </t-space>
</template>
<script lang="ts">
export default {
  name: 'ScriptEditorPanel',
};
</script>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { SearchIcon } from 'tdesign-icons-vue-next';
import { onActivated, onMounted, ref, watch } from 'vue';

import { useActionFlowStore } from '@/components/action-panel/store/index';
import Editor from '@/components/editor/index.vue';
import { getAllFunctions, type FunctionItem } from '@/composables/services/functionDataService';

import EnhancedFunctionList from './EnhancedFunctionList.vue';
import { FlowData } from './model';
import { getGlobalVariables } from './utils';
import VariableTree from './VariableTree.vue';

const globalVariables = ref<FlowData[]>([]);

// 初始化数据
const initializeData = async () => {
  globalVariables.value = await getGlobalVariables();
  await loadFunctions();
};

onActivated(initializeData);
onMounted(initializeData);

// 函数列表数据，用于智能提示
const functionList = ref<FunctionItem[]>([]);

// 加载函数数据
const loadFunctions = async () => {
  try {
    const functions = await getAllFunctions();
    functionList.value = functions;
    console.log(`ScriptEditorPanel: 已加载 ${functions.length} 个函数`);
  } catch (error) {
    console.error('ScriptEditorPanel: 加载函数数据失败:', error);
    functionList.value = [];
  }
};

const props = defineProps<{
  script: string;
}>();

const editorRef = ref();
const searchText = ref('');
const scriptValue = ref<string>(props.script);
const actionFlowStore = useActionFlowStore();

const onDblclickVariable = ({ path }: { path: string; item: any }) => {
  const prefix = '_data.';
  editorRef.value.insertText(prefix + path);
};

const onDblclickFunction = (func: FunctionItem) => {
  editorRef.value.insertText(func.script);
};

const emits = defineEmits(['update:script']);
watch(
  () => scriptValue.value,
  debounce((newValue) => {
    emits('update:script', newValue);
  }, 500),
);
</script>
<style lang="less" scoped>
.search-input {
  position: relative;
  top: 8px;
  width: 180px;
}
</style>
