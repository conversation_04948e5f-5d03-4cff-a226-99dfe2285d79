﻿using GCP.Core.Ai;
using GCP.Core.Ai.Models;
using Microsoft.Extensions.Configuration;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class McpHandlerExtensions
    {
        public static IServiceCollection AddMcpSetting(this IServiceCollection services, IConfiguration configuration)
        {
            McpService.mcpServerDictionary = configuration.GetSection("mcpSettings").Get<McpServerDictionary>();

            return services;
        }

        public static IServiceCollection AddMcpService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddMcpServer().WithToolsFromAssembly();

            return services;
        }
    }
}
