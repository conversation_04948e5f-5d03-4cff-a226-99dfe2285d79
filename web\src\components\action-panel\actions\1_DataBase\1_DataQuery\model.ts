import { DataSourceTableData, FlowDataValue } from '@/components/action-panel/model';

export interface ArgsInfo {
  name: string;
  dataSource: string;
  autoPaged: boolean;
  isPaging: boolean;
  hasTotal: boolean;
  pageSize?: FlowDataValue;
  pageIndex?: FlowDataValue;
  description: string;
  operateType: 'sql' | 'configure';
  sqlInfo?: SqlInfo;
  configureInfo?: ConfigureItem[];
  useRoot?: boolean; // 控制是否使用根节点，新增动作默认true，历史配置默认false
}

export interface SqlInfo {
  sql: string;
  parameters: any[];
}

export interface ConfigureItem {
  id: string;
  tableData: DataSourceTableData;
}
