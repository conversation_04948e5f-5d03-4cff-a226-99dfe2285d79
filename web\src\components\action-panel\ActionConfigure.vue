<template>
  <CmpContainer full>
    <CmpRow>
      <CmpCard :span="leftPanelSpan" title="动作" class="action-card-container">
        <div class="action-list">
          <template v-if="actionFlowStore.noAction">
            <div class="empty-action">暂无动作</div>
          </template>
          <action-list
            :start-id="actionFlowStore.startId"
            :active-id="actionFlowStore.currentStep?.id"
            draggable
          ></action-list>
        </div>

        <!-- 拖拽分隔线 - 放在动作卡片内部右侧 -->
        <div class="resize-handle" @mousedown="startResize">
          <div class="resize-handle-line"></div>
        </div>
      </CmpCard>

      <CmpCard :span="rightPanelSpan" :title="actionFlowStore.currentActionTitle">
        <div v-if="!actionFlowStore.noAction" class="action-form-container">
          <action-form v-if="actionFlowStore.currentStep?.id"></action-form>
          <t-space v-else direction="vertical" align="center" style="width: 100%; margin-top: 120px">
            <t-empty size="large" title="请选择一个动作" />
          </t-space>
        </div>
      </CmpCard>
    </CmpRow>
  </CmpContainer>
</template>
<script lang="ts">
export default {
  name: 'ActionConfigure',
};
</script>
<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';
import ActionForm from './ActionForm.vue';
import ActionList from './ActionList.vue';
import { useActionFlowStore } from './store/index';

const actionFlowStore = useActionFlowStore();

// 左侧面板宽度配置
const STORAGE_KEY = 'action-configure-panel-width';
const DEFAULT_WIDTH = 320;
const minWidth = 200; // 最小宽度
const maxWidth = 600; // 最大宽度

// 从localStorage读取保存的宽度
const getSavedWidth = (): number => {
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) {
    const width = parseInt(saved, 10);
    if (width >= minWidth && width <= maxWidth) {
      return width;
    }
  }
  return DEFAULT_WIDTH;
};

const leftPanelWidth = ref(getSavedWidth());

// 将像素宽度转换为栅格span值（基于容器宽度的百分比）
const leftPanelSpan = computed(() => {
  // 假设容器总宽度为1200px，12栅格系统
  const containerWidth = 1200;
  const percentage = leftPanelWidth.value / containerWidth;
  const span = Math.round(percentage * 12);
  return Math.max(2, Math.min(8, span)); // 限制在2-8之间
});

const rightPanelSpan = computed(() => {
  return 12 - leftPanelSpan.value;
});

// 拖拽状态
const isResizing = ref(false);
const startX = ref(0);
const startWidth = ref(0);

// 开始拖拽调整宽度
const startResize = (event: MouseEvent) => {
  isResizing.value = true;
  startX.value = event.clientX;
  startWidth.value = leftPanelWidth.value;

  // 添加全局事件监听
  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);

  // 防止文本选择
  document.body.style.userSelect = 'none';
  document.body.style.cursor = 'col-resize';
};

// 处理拖拽调整
const handleResize = (event: MouseEvent) => {
  if (!isResizing.value) return;

  const deltaX = event.clientX - startX.value;
  const newWidth = startWidth.value + deltaX;

  // 限制宽度范围
  if (newWidth >= minWidth && newWidth <= maxWidth) {
    leftPanelWidth.value = newWidth;
  }
};

// 保存宽度到localStorage
const saveWidth = (width: number) => {
  localStorage.setItem(STORAGE_KEY, width.toString());
};

// 停止拖拽调整
const stopResize = () => {
  isResizing.value = false;

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);

  // 恢复样式
  document.body.style.userSelect = '';
  document.body.style.cursor = '';

  // 保存当前宽度
  saveWidth(leftPanelWidth.value);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  if (isResizing.value) {
    stopResize();
  }
});
</script>
<style lang="less" scoped>
// .action-card-container {
//   position: relative;

//   :deep(.t-card) {
//     height: 100%;
//     display: flex;
//     flex-direction: column;

//     .t-card__body {
//       flex: 1;
//       overflow: hidden;
//       position: relative;
//     }
//   }
// }

.resize-handle {
  position: absolute;
  top: 0;
  right: -12px;
  width: 8px;
  height: 100%;
  cursor: col-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);

    .resize-handle-line {
      background-color: var(--td-brand-color);
    }
  }

  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.resize-handle-line {
  width: 2px;
  height: 40px;
  background-color: #d0d7de;
  border-radius: 1px;
  transition: background-color 0.2s ease;
}

.action-list {
  position: relative;
  // height: 100%;
  // overflow: auto;

  .empty-action {
    padding: 45px 20px;
    text-align: center;
    color: #999;
    font-size: 16px;
  }
}

.action-btn {
  margin-top: 16px;
}

.action-form-container {
  padding-bottom: 60px;
  // height: 100%;
  // overflow: auto;
  // border-left: 1px solid var(--td-border-level-1-color);
}
</style>
