using GCP.Common;
using GCP.FunctionPool.ActionLibrary;

namespace GCP.Functions.Common.Services
{
    [Function("actionLibraryExecution", "动作库执行服务")]
    internal class ActionLibraryExecutionService : BaseService
    {
        [Function("execute", "执行动作库")]
        public async Task<object> Execute(string actionLibraryId, object inputData = null)
        {
            var executor = new ActionLibraryExecutor(this.Context, this.SolutionId, this.ProjectId);
            
            // 获取流程上下文信息
            string flowProcId = this.Context.trackId;
            string flowStepId = this.Context.Current?.StepId;
            
            var result = await executor.ExecuteAsync(actionLibraryId, inputData, flowProcId, flowStepId);
            
            if (result.Success)
            {
                return result.Result;
            }
            else
            {
                throw new CustomException($"动作库执行失败: {result.Error}");
            }
        }

        [Function("executeWithResult", "执行动作库并返回详细结果")]
        public async Task<ActionLibraryExecutionResult> ExecuteWithResult(string actionLibraryId, object inputData = null)
        {
            var executor = new ActionLibraryExecutor(this.Context, this.SolutionId, this.ProjectId);
            
            // 获取流程上下文信息
            string flowProcId = this.Context.trackId;
            string flowStepId = this.Context.Current?.StepId;
            
            return await executor.ExecuteAsync(actionLibraryId, inputData, flowProcId, flowStepId);
        }

        [Function("executeBatch", "批量执行动作库")]
        public async Task<List<ActionLibraryExecutionResult>> ExecuteBatch(List<ActionLibraryExecutionConfig> executions)
        {
            var executor = new ActionLibraryExecutor(this.Context, this.SolutionId, this.ProjectId);
            
            // 为每个执行配置设置流程上下文信息
            string flowProcId = this.Context.trackId;
            string flowStepId = this.Context.Current?.StepId;
            
            foreach (var execution in executions)
            {
                if (string.IsNullOrEmpty(execution.FlowProcId))
                    execution.FlowProcId = flowProcId;
                if (string.IsNullOrEmpty(execution.FlowStepId))
                    execution.FlowStepId = flowStepId;
            }
            
            return await executor.ExecuteBatchAsync(executions);
        }

        [Function("executeByName", "根据名称执行动作库")]
        public async Task<object> ExecuteByName(string actionLibraryName, object inputData = null, string category = null)
        {
            // 根据名称查找动作库
            var actionLibraryService = new ActionLibraryService();
            var actionLibraries = actionLibraryService.GetAll(actionLibraryName, category, "active", 1, 10);

            var actionLibrary = actionLibraries.List.FirstOrDefault(a =>
                a.Name.Equals(actionLibraryName, StringComparison.OrdinalIgnoreCase));
            
            if (actionLibrary == null)
            {
                throw new CustomException($"未找到名称为 '{actionLibraryName}' 的动作库");
            }
            
            return await Execute(actionLibrary.Id, inputData);
        }

        [Function("executeConditional", "条件执行动作库")]
        public async Task<object> ExecuteConditional(string condition, string actionLibraryId, object inputData = null)
        {
            // 简单的条件判断，可以扩展为更复杂的表达式引擎
            bool shouldExecute = false;
            
            if (bool.TryParse(condition, out bool boolCondition))
            {
                shouldExecute = boolCondition;
            }
            else if (condition.Equals("true", StringComparison.OrdinalIgnoreCase))
            {
                shouldExecute = true;
            }
            else if (condition.Equals("false", StringComparison.OrdinalIgnoreCase))
            {
                shouldExecute = false;
            }
            else
            {
                // 可以在这里添加更复杂的条件表达式解析
                throw new CustomException($"不支持的条件表达式: {condition}");
            }
            
            if (shouldExecute)
            {
                return await Execute(actionLibraryId, inputData);
            }
            else
            {
                return new { Skipped = true, Reason = "条件不满足" };
            }
        }

        [Function("executeWithTimeout", "带超时的动作库执行")]
        public async Task<object> ExecuteWithTimeout(string actionLibraryId, object inputData = null, int timeoutSeconds = 30)
        {
            var executor = new ActionLibraryExecutor(this.Context, this.SolutionId, this.ProjectId);
            
            // 获取流程上下文信息
            string flowProcId = this.Context.trackId;
            string flowStepId = this.Context.Current?.StepId;
            
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
            
            try
            {
                var task = executor.ExecuteAsync(actionLibraryId, inputData, flowProcId, flowStepId);
                var result = await task.WaitAsync(cts.Token);
                
                if (result.Success)
                {
                    return result.Result;
                }
                else
                {
                    throw new CustomException($"动作库执行失败: {result.Error}");
                }
            }
            catch (OperationCanceledException)
            {
                throw new CustomException($"动作库执行超时 ({timeoutSeconds}秒)");
            }
        }

        [Function("executeParallel", "并行执行多个动作库")]
        public async Task<List<ActionLibraryExecutionResult>> ExecuteParallel(List<string> actionLibraryIds, object inputData = null)
        {
            var executor = new ActionLibraryExecutor(this.Context, this.SolutionId, this.ProjectId);
            
            // 获取流程上下文信息
            string flowProcId = this.Context.trackId;
            string flowStepId = this.Context.Current?.StepId;
            
            var tasks = actionLibraryIds.Select(id => 
                executor.ExecuteAsync(id, inputData, flowProcId, flowStepId)).ToArray();
            
            var results = await Task.WhenAll(tasks);
            return results.ToList();
        }

        [Function("executeChain", "链式执行动作库")]
        public async Task<object> ExecuteChain(List<string> actionLibraryIds, object initialInputData = null)
        {
            var executor = new ActionLibraryExecutor(this.Context, this.SolutionId, this.ProjectId);
            
            // 获取流程上下文信息
            string flowProcId = this.Context.trackId;
            string flowStepId = this.Context.Current?.StepId;
            
            object currentData = initialInputData;
            
            foreach (var actionLibraryId in actionLibraryIds)
            {
                var result = await executor.ExecuteAsync(actionLibraryId, currentData, flowProcId, flowStepId);
                
                if (!result.Success)
                {
                    throw new CustomException($"动作库链式执行失败，在动作库 {actionLibraryId}: {result.Error}");
                }
                
                // 将当前结果作为下一个动作库的输入
                currentData = result.Result;
            }
            
            return currentData;
        }

        [Function("getExecutionStatus", "获取动作库执行状态")]
        public object GetExecutionStatus(string executionId)
        {
            using var db = this.GetDb();
            
            var log = (from l in db.LcActionLibraryLogs
                      where l.ExecutionId == executionId &&
                      l.SolutionId == this.SolutionId &&
                      l.ProjectId == this.ProjectId
                      select l).FirstOrDefault();
            
            if (log == null)
            {
                return new { Found = false };
            }
            
            return new
            {
                Found = true,
                ExecutionId = log.ExecutionId,
                ActionLibraryId = log.ActionLibraryId,
                Status = log.Status,
                ExecutionTime = log.ExecutionTimeMs,
                StartTime = log.StartTime,
                EndTime = log.EndTime,
                ErrorMessage = log.ErrorMessage
            };
        }
    }
}
