{"name": "发送异常通知", "function": "notificationAction.sendNotificationWithException", "icon": "error-circle", "version": "1.0.0", "description": "发送包含异常信息的通知消息，适用于错误处理和异常监控场景", "args": [{"name": "channelId", "type": "string", "required": true, "description": "通知通道ID"}, {"name": "title", "type": "string", "required": true, "description": "通知标题"}, {"name": "message", "type": "string", "required": true, "description": "异常消息"}, {"name": "stackTrace", "type": "string", "required": false, "description": "堆栈跟踪信息"}], "result": [{"name": "success", "type": "boolean", "description": "发送是否成功"}]}