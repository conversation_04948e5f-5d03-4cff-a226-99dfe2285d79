﻿using Acornima.Ast;
using GCP.Functions.Common;
using Jint;
using Jint.Native.Function;


namespace GCP.FunctionPool.Builder
{
    class JavaScriptResolver
    {
        internal static string MainSpecifier => "main";
        internal static string HandlerSpecifier => "handler";

        /// <summary>
        /// JavaScript（单）函数处理器, 在线处理
        /// </summary>
        /// <param name="path"></param>
        /// <param name="sourceCode"></param>
        /// <param name="assemblyInfo"></param>
        /// <param name="resolverType"></param>
        internal static void Handler(string path, string name, string sourceCode, FunctionInfo assemblyInfo, FunctionResolveType resolverType, Action<FunctionInfo> initFunc = null)
        {
            if (assemblyInfo == null)
                assemblyInfo = new FunctionInfo(path, name);

            var module = Engine.PrepareModule(sourceCode);

            var engine = new Engine();

            engine.Modules.Add(path, t => t.AddModule(module));
            var ns = engine.Modules.Import(path);

            Function func = null;
            switch (resolverType)
            {
                case FunctionResolveType.PURE:
                    var main = ns.Get(MainSpecifier)?.AsFunctionInstance();
                    if (main != null)
                    {
                        func = main;
                    }
                    break;
                case FunctionResolveType.MIDDLEWARE:
                    var handler = ns.Get(HandlerSpecifier)?.AsFunctionInstance();
                    if (handler != null)
                    {
                        func = handler;
                    }
                    break;
                case FunctionResolveType.FLOW:
                    break;
                default:
                    break;
            }

            if (func != null && assemblyInfo.LoadScript(path, module, resolverType))
            {
                initFunc?.Invoke(assemblyInfo);
                FunctionCompiler.DicFunction.AddOrUpdate(path, assemblyInfo, (key, t) => assemblyInfo);
            }
        }

        /// <summary>
        /// JavaScript文件处理器, 支持多函数export
        /// </summary>
        /// <param name="path"></param>
        /// <param name="sourceCode"></param>
        /// <param name="assemblyInfo"></param>
        internal static void Handler(string basePath, string path, string sourceCode, FunctionInfo assemblyInfo = null)
        {
            //if (assemblyInfo == null)
            //    assemblyInfo = new FunctionInfo(path);

            var engine = JavascriptEngine.CreateEngine(basePath);
            var ns = engine.Modules.Import($"./{MainSpecifier}.js");


            var keys = ns.GetOwnPropertyKeys();
            foreach (var jsKey in keys)
            {
                if (!jsKey.IsSymbol())
                {
                    var key = jsKey.ToString();
                    var jsPath = path + (key == MainSpecifier ? "" : "/" + key);
                    assemblyInfo = new FunctionInfo(jsPath, key);

                    if (assemblyInfo.LoadScript(jsPath, new Prepared<Module>(), FunctionResolveType.PURE, basePath, key))
                    {
                        FunctionCompiler.DicFunction.AddOrUpdate(jsPath, assemblyInfo, (key, t) => assemblyInfo);
                    }
                }
            }
            var main = ns.Get(MainSpecifier)?.AsFunctionInstance();

            //FunctionResolveType resolverType = FunctionResolveType.PURE;
            //Function func = null;
            //if (main != null)
            //{
            //    func = main;
            //}
            //else
            //{
            //    var handler = ns.Get(HandlerSpecifier)?.AsFunctionInstance();
            //    if (handler != null)
            //    {
            //        func = handler;
            //        resolverType = FunctionResolveType.MIDDLEWARE;
            //    }
            //}

            //if (func != null && assemblyInfo.LoadScript(path, new Prepared<Module>(), resolverType, basePath))
            //{
            //    FunctionCompiler.dicFunction.AddOrUpdate(path, assemblyInfo, (key, t) => assemblyInfo);
            //}
        }

        /// <summary>
        /// 获取JavaScript函数参数
        /// </summary>
        /// <param name="function"></param>
        /// <returns></returns>
        internal static string[] GetFunctionParameters(IFunction function, out int length)
        {
            length = 0;
            ref readonly var functionDeclarationParams = ref function.Params;
            var count = functionDeclarationParams.Count;
            var parameterNames = new List<string>(count);
            for (var i = 0; i < count; i++)
            {
                var parameter = functionDeclarationParams[i];
                var type = parameter.Type;

                if (parameter is Identifier id)
                {
                    parameterNames.Add(id.Name);
                    length++;
                }
                else if (parameter is not Literal)
                {
                    GetBoundNames(
                        parameter,
                        parameterNames);
                }
            }

            return parameterNames.ToArray();
        }

        private static void GetBoundNames(Node parameter, List<string> target)
        {
            while (true)
            {
                if (parameter is Identifier identifier)
                {
                    target.Add(identifier.Name);
                    return;
                }
                else if (parameter is AssignmentPattern assignmentPattern)
                {
                    parameter = assignmentPattern.Left;
                    continue;
                }
                else
                {
                    // RestElement or ObjectPattern or ArrayPattern not supported
                    throw new NotImplementedException("Unsupported parameter type");
                }
            }
        }
    }
}
