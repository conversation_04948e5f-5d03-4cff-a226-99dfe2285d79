import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowControl, FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { createNumberRootNode } from '@/components/action-panel/utils/rootNodeHelper';

import { ArgsInfo } from './model';

const control: FlowControl = {
  while: {
    loopType: 'count',
    nextId: '',
  },
};
export type whileInfo = typeof control.while;

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();
  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      description: '',
    } as ArgsInfo),
    ...(actionFlowStore.currentStep.args || {}),
  }) as ArgsInfo;

  state.whileData = cloneDeep(actionFlowStore.currentStep.control?.while || control.while) as whileInfo;
};

export const useWhileStore = defineStore('While', {
  state: () => {
    const state = { args: null, whileData: null } as { args: ArgsInfo; whileData: whileInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [
        {
          id: 'result',
          key: 'result',
          description: '页码',
          type: 'int',
        },
      ] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 使用根节点绑定
      const rootNode = createNumberRootNode('页码');
      actionFlowStore.setStepResultData([rootNode]);

      actionFlowStore.setCurrentControl({ while: this.whileData });
    },
  },
});
