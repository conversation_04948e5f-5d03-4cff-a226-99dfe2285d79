// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	public partial class GcpFlowDb : DbBase
	{
		/// <summary>
		/// 历史流程运行细节
		/// </summary>
		public ITable<LcFhiDetail>   LcFhiDetails   => this.GetTable<LcFhiDetail>();
		/// <summary>
		/// 历史流程实例
		/// </summary>
		public ITable<LcFhiProc>     LcFhiProcs     => this.GetTable<LcFhiProc>();
		/// <summary>
		/// 流程统计数据
		/// </summary>
		public ITable<LcFhiStat>     LcFhiStats     => this.GetTable<LcFhiStat>();
		/// <summary>
		/// 流程运行日志
		/// </summary>
		public ITable<LcFruLog>      LcFruLogs      => this.GetTable<LcFruLog>();
		/// <summary>
		/// 流程运行实例
		/// </summary>
		public ITable<LcFruProc>     LcFruProcs     => this.GetTable<LcFruProc>();
		/// <summary>
		/// 流程步骤实例
		/// </summary>
		public ITable<LcFruStep>     LcFruSteps     => this.GetTable<LcFruStep>();
		/// <summary>
		/// 流程运行实例变量
		/// </summary>
		public ITable<LcFruVariable> LcFruVariables => this.GetTable<LcFruVariable>();
	}

	public static partial class ExtensionMethods
	{
		#region Table Extensions
		public static LcFhiDetail? Find(this ITable<LcFhiDetail> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFhiDetail?> FindAsync(this ITable<LcFhiDetail> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFhiProc? Find(this ITable<LcFhiProc> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFhiProc?> FindAsync(this ITable<LcFhiProc> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFhiStat? Find(this ITable<LcFhiStat> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFhiStat?> FindAsync(this ITable<LcFhiStat> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFruLog? Find(this ITable<LcFruLog> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFruLog?> FindAsync(this ITable<LcFruLog> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFruProc? Find(this ITable<LcFruProc> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFruProc?> FindAsync(this ITable<LcFruProc> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFruStep? Find(this ITable<LcFruStep> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFruStep?> FindAsync(this ITable<LcFruStep> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFruVariable? Find(this ITable<LcFruVariable> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFruVariable?> FindAsync(this ITable<LcFruVariable> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}
		#endregion
	}
}
