﻿using ModelContextProtocol.Client;
using ModelContextProtocol.Protocol.Transport;
using ModelContextProtocol;
using GCP.Core.Ai.Models;

namespace GCP.Core.Ai
{
    public class McpService
    {
        internal static McpServerDictionary mcpServerDictionary { get; set; }

        public static async Task<List<McpClientTool>> GetToolsAsync()
        {
            var serverList = new List<MCPServerConfig>();
            try
            {
                if (mcpServerDictionary?.Servers != null)
                {
                    foreach (var server in mcpServerDictionary.Servers)
                    {
                        MCPServerConfig serverConfig = new MCPServerConfig();
                        serverConfig.Name = server.Key;
                        serverConfig.Command = server.Value.Command;
                        serverConfig.Args = server.Value.Args;
                        serverConfig.TransportType = server.Value.Command == "sse" ? TransportTypes.Sse : TransportTypes.StdIo;
                        serverList.Add(serverConfig);
                    }
                }

                McpClientOptions options = new()
                {
                    ClientInfo = new() { Name = "GCP-Studio", Version = "1.0.0" }
                };

                List<McpServerConfig> mcpServerConfigs = new List<McpServerConfig>();

                foreach (var server in serverList)
                {
                    McpServerConfig config;
                    if (server.TransportType == TransportTypes.StdIo)
                    {
                        config = new()
                        {
                            Id = server.Name,
                            Name = server.Name,
                            TransportType = server.TransportType,
                            TransportOptions = new()
                            {
                                ["command"] = server.Command,
                                ["arguments"] = server.Args
                            }
                        };
                    }
                    else
                    {
                        config = new()
                        {
                            Id = server.Name,
                            Name = server.Name,
                            TransportType = server.TransportType,
                            Location = server.Args
                        };
                    }

                    mcpServerConfigs.Add(config);
                }

                List<McpClientTool> mappedTools = new List<McpClientTool>();

                foreach (var config in mcpServerConfigs)
                {
                    var client = await McpClientFactory.CreateAsync(config);
                    var listToolsResult = await client.ListToolsAsync();

    //                var result = await client.CallToolAsync(
    //"ListTables",
    //new Dictionary<string, object?>() { ["connectionString"] = "Data Source=E:/DevSoft/duckdb/scm_gcp.db" });

    //                Console.WriteLine(JsonHelper.Serialize(result));

                    mappedTools.AddRange(listToolsResult);
                }

                return mappedTools;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing settings: {ex.Message}");
                return null;
            }
        }
    }
}
