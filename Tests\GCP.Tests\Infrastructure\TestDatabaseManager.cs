using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Data.Sqlite;
using MySqlConnector;
using GCP.DataAccess.Migrations;
using System.Reflection;

namespace GCP.Tests.Infrastructure
{
    /// <summary>
    /// 测试数据库管理器
    /// </summary>
    public class TestDatabaseManager : IDisposable
    {
        private readonly TestDatabaseConfiguration _config;
        private readonly ILogger<TestDatabaseManager> _logger;
        private readonly string _projectRoot;
        private bool _disposed = false;

        public TestDatabaseManager(IConfiguration configuration, ILogger<TestDatabaseManager> logger)
        {
            _config = configuration.GetSection("dbSettings").Get<TestDatabaseConfiguration>()
                     ?? new TestDatabaseConfiguration();
            _logger = logger;
            _projectRoot = GetProjectRoot();

            InitializeDatabaseDirectories();
        }

        /// <summary>
        /// 获取项目根目录
        /// </summary>
        private string GetProjectRoot()
        {
            var currentDirectory = Directory.GetCurrentDirectory();
            var projectRoot = currentDirectory;

            // 查找包含.csproj文件的目录
            while (!Directory.GetFiles(projectRoot, "*.csproj").Any() && Directory.GetParent(projectRoot) != null)
            {
                projectRoot = Directory.GetParent(projectRoot)!.FullName;
            }

            return projectRoot;
        }

        /// <summary>
        /// 初始化数据库目录
        /// </summary>
        private void InitializeDatabaseDirectories()
        {
            // 如果是SQLite，确保TestData目录存在
            if (_config.DataSource.Default.ProviderName == "SQLite")
            {
                var testDataDir = Path.Combine(_projectRoot, "TestData");
                if (!Directory.Exists(testDataDir))
                {
                    Directory.CreateDirectory(testDataDir);
                    _logger.LogInformation("创建测试数据目录: {Directory}", testDataDir);
                }
            }
        }

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        public string GetConnectionString()
        {
            return GetConnectionString(_config.DataSource.Default);
        }

        /// <summary>
        /// 获取数据库提供程序名称
        /// </summary>
        public string GetProviderName()
        {
            return _config.DataSource.Default.ProviderName;
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        private string GetConnectionString(DatabaseConfig dbConfig)
        {
            if (dbConfig.ProviderName == "SQLite")
            {
                return GetSQLiteConnectionString(dbConfig);
            }
            return dbConfig.ConnectionString;
        }

        /// <summary>
        /// 获取SQLite连接字符串
        /// </summary>
        private string GetSQLiteConnectionString(DatabaseConfig dbConfig)
        {
            if (!string.IsNullOrEmpty(dbConfig.ConnectionString))
            {
                // 如果是相对路径，转换为绝对路径
                if (dbConfig.ConnectionString.StartsWith("Data Source="))
                {
                    var dataSourceValue = dbConfig.ConnectionString[12..]; // 使用范围操作符替代Substring
                    if (!Path.IsPathRooted(dataSourceValue))
                    {
                        var absolutePath = Path.Combine(_projectRoot, dataSourceValue);
                        return $"Data Source={absolutePath}";
                    }
                }
                return dbConfig.ConnectionString;
            }

            throw new InvalidOperationException("SQLite数据库配置缺少连接字符串");
        }

        /// <summary>
        /// 运行数据库迁移
        /// </summary>
        public async Task RunMigrationsAsync()
        {
            try
            {
                // 确保数据库目录存在
                if (_config.DataSource.Default.ProviderName == "SQLite")
                {
                    EnsureSQLiteDatabaseDirectories();
                }
                else if (_config.DataSource.Default.ProviderName == "MySql")
                {
                    await EnsureMySQLDatabasesExistAsync();
                }

                // 运行数据库迁移
                var connectionString = GetConnectionString();
                var migrationService = new MigrationsService(_config.DataSource.Default.ProviderName, connectionString);
                var migrationsAssembly = Assembly.GetAssembly(typeof(MigrationsService));
                migrationService.Run(migrationsAssembly);

                _logger.LogInformation("数据库迁移完成: {Provider}", _config.DataSource.Default.ProviderName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库迁移失败");
                throw;
            }
        }

        /// <summary>
        /// 确保SQLite数据库目录存在
        /// </summary>
        private void EnsureSQLiteDatabaseDirectories()
        {
            if (_config.DataSource.Default.ProviderName == "SQLite")
            {
                var dbPath = GetSQLiteFilePath(_config.DataSource.Default);
                EnsureDirectoryExists(Path.GetDirectoryName(dbPath));
            }
        }

        /// <summary>
        /// 获取SQLite文件路径
        /// </summary>
        private string GetSQLiteFilePath(DatabaseConfig dbConfig)
        {
            var connectionString = GetSQLiteConnectionString(dbConfig);
            var dataSourcePrefix = "Data Source=";
            if (connectionString.StartsWith(dataSourcePrefix))
            {
                return connectionString[dataSourcePrefix.Length..]; // 使用范围操作符
            }
            throw new InvalidOperationException("无效的SQLite连接字符串格式");
        }

        /// <summary>
        /// 确保目录存在
        /// </summary>
        private void EnsureDirectoryExists(string? directoryPath)
        {
            if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
                _logger.LogInformation("创建目录: {Directory}", directoryPath);
            }
        }

        /// <summary>
        /// 确保MySQL数据库存在
        /// </summary>
        private async Task EnsureMySQLDatabasesExistAsync()
        {
            if (_config.DataSource.Default.ProviderName == "MySql")
            {
                await EnsureMySQLDatabaseExistsAsync(_config.DataSource.Default);
            }
        }

        /// <summary>
        /// 确保MySQL数据库存在
        /// </summary>
        private async Task EnsureMySQLDatabaseExistsAsync(DatabaseConfig dbConfig)
        {
            var connectionStringBuilder = new MySqlConnectionStringBuilder(dbConfig.ConnectionString);
            var databaseName = connectionStringBuilder.Database;

            // 移除数据库名称，连接到MySQL服务器
            connectionStringBuilder.Database = "";
            var serverConnectionString = connectionStringBuilder.ToString();

            using var connection = new MySqlConnection(serverConnectionString);
            await connection.OpenAsync();

            var command = connection.CreateCommand();
            command.CommandText = $"CREATE DATABASE IF NOT EXISTS `{databaseName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("确保MySQL数据库存在: {Database}", databaseName);
        }

        /// <summary>
        /// 清理数据库
        /// </summary>
        public async Task CleanupAsync()
        {
            if (_disposed) return;

            try
            {
                if (_config.CleanupStrategy == "ClearTables")
                {
                    await ClearAllTablesAsync();
                }
                else if (_config.CleanupStrategy == "DeleteFiles")
                {
                    DeleteSQLiteFiles();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理数据库时发生错误");
            }
        }

        /// <summary>
        /// 清空所有表内容
        /// </summary>
        private async Task ClearAllTablesAsync()
        {
            await ClearDatabaseTablesAsync(GetConnectionString(), GetProviderName());
        }

        /// <summary>
        /// 清空数据库表内容
        /// </summary>
        private async Task ClearDatabaseTablesAsync(string connectionString, string providerName)
        {
            if (providerName == "SQLite")
            {
                await ClearSQLiteTablesAsync(connectionString);
            }
            else if (providerName == "MySql")
            {
                await ClearMySQLTablesAsync(connectionString);
            }
        }

        /// <summary>
        /// 清空SQLite表内容
        /// </summary>
        private async Task ClearSQLiteTablesAsync(string connectionString)
        {
            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();

            // 获取所有表名（排除系统表）
            var getTablesCommand = connection.CreateCommand();
            getTablesCommand.CommandText = @"
                SELECT name FROM sqlite_master 
                WHERE type='table' 
                AND name NOT LIKE 'sqlite_%' 
                AND name != 'VersionInfo'
                ORDER BY name";

            var tableNames = new List<string>();
            using (var reader = await getTablesCommand.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    tableNames.Add(reader.GetString(0));
                }
            }

            // 禁用外键约束
            var disableFkCommand = connection.CreateCommand();
            disableFkCommand.CommandText = "PRAGMA foreign_keys = OFF";
            await disableFkCommand.ExecuteNonQueryAsync();

            // 清空每个表
            foreach (var tableName in tableNames)
            {
                var deleteCommand = connection.CreateCommand();
                deleteCommand.CommandText = $"DELETE FROM [{tableName}]";
                await deleteCommand.ExecuteNonQueryAsync();
                
                _logger.LogDebug("清空表: {TableName}", tableName);
            }

            // 重新启用外键约束
            var enableFkCommand = connection.CreateCommand();
            enableFkCommand.CommandText = "PRAGMA foreign_keys = ON";
            await enableFkCommand.ExecuteNonQueryAsync();

            _logger.LogInformation("SQLite数据库表内容已清空");
        }

        /// <summary>
        /// 清空MySQL表内容
        /// </summary>
        private async Task ClearMySQLTablesAsync(string connectionString)
        {
            using var connection = new MySqlConnection(connectionString);
            await connection.OpenAsync();

            // 获取所有表名
            var getTablesCommand = connection.CreateCommand();
            getTablesCommand.CommandText = @"
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME != 'VersionInfo'
                ORDER BY TABLE_NAME";

            var tableNames = new List<string>();
            using (var reader = await getTablesCommand.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    tableNames.Add(reader.GetString(0));
                }
            }

            // 禁用外键检查
            var disableFkCommand = connection.CreateCommand();
            disableFkCommand.CommandText = "SET FOREIGN_KEY_CHECKS = 0";
            await disableFkCommand.ExecuteNonQueryAsync();

            // 清空每个表
            foreach (var tableName in tableNames)
            {
                var deleteCommand = connection.CreateCommand();
                deleteCommand.CommandText = $"DELETE FROM `{tableName}`";
                await deleteCommand.ExecuteNonQueryAsync();
                
                _logger.LogDebug("清空表: {TableName}", tableName);
            }

            // 重新启用外键检查
            var enableFkCommand = connection.CreateCommand();
            enableFkCommand.CommandText = "SET FOREIGN_KEY_CHECKS = 1";
            await enableFkCommand.ExecuteNonQueryAsync();

            _logger.LogInformation("MySQL数据库表内容已清空");
        }

        /// <summary>
        /// 删除SQLite文件
        /// </summary>
        private void DeleteSQLiteFiles()
        {
            try
            {
                if (_config.DataSource.Default.ProviderName == "SQLite")
                {
                    var dbPath = GetSQLiteFilePath(_config.DataSource.Default);
                    if (File.Exists(dbPath))
                    {
                        File.Delete(dbPath);
                        _logger.LogInformation("删除SQLite文件: {FilePath}", dbPath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除SQLite文件时发生错误");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                CleanupAsync().GetAwaiter().GetResult();
                _disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }
}
