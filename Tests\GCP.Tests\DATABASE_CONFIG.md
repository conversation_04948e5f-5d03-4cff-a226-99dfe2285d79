# GCP 测试项目数据库配置说明

## 概述

GCP 测试项目支持灵活的数据库配置，可以在 SQLite 和 MySQL 之间切换，并提供多种数据清理策略。

## 数据库配置

### 配置文件

测试项目使用 `appsettings.test.json` 作为唯一配置文件：

- `appsettings.test.json` - 测试数据库配置
- `appsettings.mysql.example.json` - MySQL 配置示例（仅供参考）

### 配置结构

```json
{
  "dbSettings": {
    "DataSource": {
      "Default": {
        "ConnectionString": "Data Source=TestData/test_gcp.db",
        "ProviderName": "SQLite"
      }
    },
    "CleanupStrategy": "ClearTables"  // 清理策略: ClearTables 或 DeleteFiles
  }
}
```

**说明**：
- `DataSource.Default`: 数据库配置（主数据库和流程数据库使用同一个数据库）
- `ProviderName`: 数据库提供程序名称，支持：`SQLite`、`MySql`、`SqlServer`、`Oracle`、`PostgreSQL`
- `ConnectionString`: 数据库连接字符串
- `CleanupStrategy`: 测试后的清理策略

## 数据库提供程序

### SQLite（默认）

- **优点**: 轻量级，无需额外安装，适合单元测试
- **配置**: 数据库文件存储在项目根目录的 `TestData` 文件夹中
- **文件位置**: `TestData/test_gcp.db`

### MySQL

- **优点**: 与生产环境一致，适合集成测试
- **要求**: 需要运行 MySQL 服务器
- **配置**: 通过连接字符串连接到 MySQL 服务器
- **数据库**: 自动创建测试数据库（如果不存在）

## 数据清理策略

### ClearTables（推荐）

- **行为**: 清空所有表的内容，保留表结构和数据库文件
- **优点**: 
  - 保留数据库文件，避免重复创建开销
  - 保留表结构和索引
  - 测试运行更快
- **适用**: SQLite 和 MySQL

### DeleteFiles

- **行为**: 删除 SQLite 数据库文件
- **优点**: 完全清理，确保干净的测试环境
- **缺点**: 每次测试都需要重新创建数据库和表结构
- **适用**: 仅 SQLite

## 使用方法

### 1. 使用默认 SQLite 配置

```bash
# 运行测试（使用默认配置）
dotnet test Tests/GCP.Tests/GCP.Tests.csproj
```

### 2. 切换到 MySQL

修改 `appsettings.test.json` 文件中的 `ProviderName` 为 `"MySql"` 并设置相应的连接字符串：

```bash
# 运行测试
dotnet test Tests/GCP.Tests/GCP.Tests.csproj
```

### 3. 自定义配置

编辑 `appsettings.test.json` 文件，修改以下配置：

```json
{
  "dbSettings": {
    "DataSource": {
      "Default": {
        "ConnectionString": "Server=your-mysql-host;Database=your_test_db;Uid=your_username;Pwd=your_password;",
        "ProviderName": "MySql"
      }
    }
  }
}
```

## 数据库迁移

测试框架会自动运行数据库迁移，创建所需的表结构：

- 解决方案和项目表
- 函数和 API 表
- 工作流相关表
- 用户和权限表
- IoT 设备表
- 消息事件表
- AI 聊天表

## 测试数据管理

### TestDataBuilder

使用 `TestDataBuilder` 类创建测试数据：

```csharp
// 创建测试解决方案
var solution = TestDataBuilder.CreateTestSolution("测试解决方案");

// 创建测试项目
var project = TestDataBuilder.CreateTestProject("测试项目", solution.Id);

// 创建测试 API
var api = TestDataBuilder.CreateTestApi("测试API", solution.Id, project.Id);
```

### 数据库访问

在测试中访问数据库：

```csharp
public class MyTest : DatabaseTestBase
{
    public MyTest(ITestOutputHelper output) : base(output) { }

    [Fact]
    public async Task MyTestMethod()
    {
        // 获取数据库实例
        using var db = GetDatabase();
        
        // 执行数据库操作
        var result = await db.LcSolutions.ToListAsync();
        
        // 断言
        result.Should().NotBeNull();
    }
}
```

## 故障排除

### SQLite 问题

1. **文件锁定**: 确保没有其他进程占用数据库文件
2. **权限问题**: 确保对 TestData 目录有写权限
3. **路径问题**: 检查相对路径是否正确

### MySQL 问题

1. **连接失败**: 检查 MySQL 服务是否运行
2. **权限问题**: 确保用户有创建数据库的权限
3. **端口问题**: 检查端口配置是否正确

### 常见错误

- `IDataContext is disposed`: 数据库连接被提前释放，检查 using 语句
- `Table doesn't exist`: 数据库迁移失败，检查迁移配置
- `Connection string invalid`: 检查连接字符串格式

## 性能优化

1. **使用 SQLite**: 对于单元测试，SQLite 性能更好
2. **清理策略**: 使用 `ClearTables` 而不是 `DeleteFiles`
3. **并行测试**: SQLite 支持更好的并行测试
4. **内存数据库**: 考虑使用 SQLite 内存数据库（`:memory:`）

## 最佳实践

1. **隔离测试**: 每个测试类使用独立的数据库实例
2. **清理数据**: 测试后自动清理数据
3. **事务回滚**: 在可能的情况下使用事务回滚
4. **模拟数据**: 使用 TestDataBuilder 创建一致的测试数据
5. **配置管理**: 使用不同的配置文件管理不同环境

## 配置示例

### SQLite 配置（默认）

```json
{
  "dbSettings": {
    "DataSource": {
      "Default": {
        "ConnectionString": "Data Source=TestData/test_gcp.db",
        "ProviderName": "SQLite"
      }
    },
    "CleanupStrategy": "ClearTables"
  }
}
```

### MySQL 配置

```json
{
  "dbSettings": {
    "DataSource": {
      "Default": {
        "ConnectionString": "Server=localhost;Database=test_gcp;Uid=root;Pwd=password;",
        "ProviderName": "MySql"
      }
    },
    "CleanupStrategy": "ClearTables"
  }
}
```
