<template>
  <div class="action-library-params-config">
    <t-form ref="formRef" :data="formData" layout="vertical">
      <!-- 输入参数配置 -->
      <div class="params-section">
        <div class="section-header">
          <h3>输入参数配置</h3>
          <t-space>
            <t-button size="small" theme="default" @click="generateInputSchemaFromFlow">
              <template #icon>
                <refresh-icon />
              </template>
              从函数流生成
            </t-button>
            <t-button size="small" theme="primary" @click="addInputParam">
              <template #icon>
                <add-icon />
              </template>
              添加参数
            </t-button>
          </t-space>
        </div>

        <div class="params-editor">
          <t-tabs v-model="inputParamsTab" size="medium">
            <t-tab-panel value="visual" label="可视化编辑">
              <div class="visual-editor">
                <t-table
                  :data="inputParams"
                  :columns="paramColumns"
                  size="small"
                  :pagination="false"
                  max-height="400"
                >
                  <template #name="{ row, rowIndex }">
                    <t-input v-model="row.name" placeholder="参数名" size="small" />
                  </template>
                  <template #type="{ row }">
                    <t-select v-model="row.type" :options="typeOptions" size="small" />
                  </template>
                  <template #required="{ row }">
                    <t-switch v-model="row.required" />
                  </template>
                  <template #description="{ row }">
                    <t-input v-model="row.description" placeholder="参数描述" size="small" />
                  </template>
                  <template #defaultValue="{ row }">
                    <t-input v-model="row.defaultValue" placeholder="默认值" size="small" />
                  </template>
                  <template #actions="{ rowIndex }">
                    <t-button size="small" variant="text" theme="danger" @click="removeInputParam(rowIndex)">
                      <template #icon>
                        <delete-icon />
                      </template>
                    </t-button>
                  </template>
                </t-table>
              </div>
            </t-tab-panel>
            <t-tab-panel value="json" label="JSON编辑">
              <div class="json-editor">
                <editor
                  v-model:value="formData.inputSchemaJson"
                  language="json"
                  style="height: 400px"
                  :read-only="false"
                  @change="onInputSchemaChange"
                />
              </div>
            </t-tab-panel>
          </t-tabs>
        </div>
      </div>

      <!-- 输出参数配置 -->
      <div class="params-section">
        <div class="section-header">
          <h3>输出参数配置</h3>
          <t-space>
            <t-button size="small" theme="default" @click="generateOutputSchemaFromFlow">
              <template #icon>
                <refresh-icon />
              </template>
              从函数流生成
            </t-button>
            <t-button size="small" theme="primary" @click="addOutputParam">
              <template #icon>
                <add-icon />
              </template>
              添加参数
            </t-button>
          </t-space>
        </div>

        <div class="params-editor">
          <t-tabs v-model="outputParamsTab" size="medium">
            <t-tab-panel value="visual" label="可视化编辑">
              <div class="visual-editor">
                <t-table
                  :data="outputParams"
                  :columns="paramColumns"
                  size="small"
                  :pagination="false"
                  max-height="400"
                >
                  <template #name="{ row, rowIndex }">
                    <t-input v-model="row.name" placeholder="参数名" size="small" />
                  </template>
                  <template #type="{ row }">
                    <t-select v-model="row.type" :options="typeOptions" size="small" />
                  </template>
                  <template #required="{ row }">
                    <t-switch v-model="row.required" />
                  </template>
                  <template #description="{ row }">
                    <t-input v-model="row.description" placeholder="参数描述" size="small" />
                  </template>
                  <template #defaultValue="{ row }">
                    <t-input v-model="row.defaultValue" placeholder="默认值" size="small" />
                  </template>
                  <template #actions="{ rowIndex }">
                    <t-button size="small" variant="text" theme="danger" @click="removeOutputParam(rowIndex)">
                      <template #icon>
                        <delete-icon />
                      </template>
                    </t-button>
                  </template>
                </t-table>
              </div>
            </t-tab-panel>
            <t-tab-panel value="json" label="JSON编辑">
              <div class="json-editor">
                <editor
                  v-model:value="formData.outputSchemaJson"
                  language="json"
                  style="height: 400px"
                  :read-only="false"
                  @change="onOutputSchemaChange"
                />
              </div>
            </t-tab-panel>
          </t-tabs>
        </div>
      </div>

      <!-- 测试数据配置 -->
      <div class="params-section">
        <div class="section-header">
          <h3>测试数据配置</h3>
          <t-space>
            <t-button size="small" theme="default" @click="generateTestDataFromSchema">
              <template #icon>
                <refresh-icon />
              </template>
              从参数生成
            </t-button>
            <t-button size="small" theme="default" @click="validateTestData">
              <template #icon>
                <check-icon />
              </template>
              验证数据
            </t-button>
          </t-space>
        </div>

        <div class="test-data-editor">
          <editor
            v-model:value="formData.testDataJson"
            language="json"
            style="height: 300px"
            :read-only="false"
          />
        </div>
      </div>
    </t-form>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryParamsConfig',
};
</script>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { AddIcon, DeleteIcon, RefreshIcon, CheckIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import Editor from '@/components/editor/index.vue';

interface ActionLibraryData {
  id?: string;
  functionFlowId?: string;
  functionFlowVersion?: number;
  inputSchemaJson?: string;
  outputSchemaJson?: string;
  testDataJson?: string;
}

interface ParamDefinition {
  name: string;
  type: string;
  required: boolean;
  description?: string;
  defaultValue?: string;
}

const props = defineProps<{
  modelValue: ActionLibraryData;
}>();

const emits = defineEmits<{
  'update:modelValue': [value: ActionLibraryData];
}>();

const formRef = ref();
const formData = ref<ActionLibraryData>({ ...props.modelValue });
const inputParamsTab = ref('visual');
const outputParamsTab = ref('visual');

// 输入参数列表
const inputParams = ref<ParamDefinition[]>([]);
// 输出参数列表
const outputParams = ref<ParamDefinition[]>([]);

// 参数类型选项
const typeOptions = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '整数', value: 'integer' },
  { label: '布尔值', value: 'boolean' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' },
  { label: '日期', value: 'date' },
  { label: '时间', value: 'datetime' },
];

// 参数表格列定义
const paramColumns = [
  { colKey: 'name', title: '参数名', width: 150 },
  { colKey: 'type', title: '类型', width: 120 },
  { colKey: 'required', title: '必填', width: 80 },
  { colKey: 'description', title: '描述', width: 200 },
  { colKey: 'defaultValue', title: '默认值', width: 150 },
  { colKey: 'actions', title: '操作', width: 80 },
];

// 监听表单数据变化
watch(
  () => formData.value,
  (newValue) => {
    emits('update:modelValue', { ...newValue });
  },
  { deep: true }
);

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...newValue };
    loadParamsFromSchema();
  },
  { deep: true }
);

// 监听参数列表变化，同步到JSON
watch(
  () => inputParams.value,
  () => {
    syncInputParamsToJson();
  },
  { deep: true }
);

watch(
  () => outputParams.value,
  () => {
    syncOutputParamsToJson();
  },
  { deep: true }
);

// 从Schema加载参数
const loadParamsFromSchema = () => {
  try {
    if (formData.value.inputSchemaJson) {
      const schema = JSON.parse(formData.value.inputSchemaJson);
      inputParams.value = schemaToParams(schema);
    }
  } catch (error) {
    console.error('解析输入参数Schema失败:', error);
  }

  try {
    if (formData.value.outputSchemaJson) {
      const schema = JSON.parse(formData.value.outputSchemaJson);
      outputParams.value = schemaToParams(schema);
    }
  } catch (error) {
    console.error('解析输出参数Schema失败:', error);
  }
};

// Schema转换为参数列表
const schemaToParams = (schema: any): ParamDefinition[] => {
  if (!schema || !schema.properties) return [];
  
  const params: ParamDefinition[] = [];
  const required = schema.required || [];
  
  for (const [name, prop] of Object.entries(schema.properties)) {
    const propDef = prop as any;
    params.push({
      name,
      type: propDef.type || 'string',
      required: required.includes(name),
      description: propDef.description || '',
      defaultValue: propDef.default || '',
    });
  }
  
  return params;
};

// 参数列表转换为Schema
const paramsToSchema = (params: ParamDefinition[]) => {
  const properties: any = {};
  const required: string[] = [];
  
  params.forEach(param => {
    properties[param.name] = {
      type: param.type,
      description: param.description || '',
    };
    
    if (param.defaultValue) {
      properties[param.name].default = param.defaultValue;
    }
    
    if (param.required) {
      required.push(param.name);
    }
  });
  
  return {
    type: 'object',
    properties,
    required,
  };
};

// 同步输入参数到JSON
const syncInputParamsToJson = () => {
  try {
    const schema = paramsToSchema(inputParams.value);
    formData.value.inputSchemaJson = JSON.stringify(schema, null, 2);
  } catch (error) {
    console.error('同步输入参数到JSON失败:', error);
  }
};

// 同步输出参数到JSON
const syncOutputParamsToJson = () => {
  try {
    const schema = paramsToSchema(outputParams.value);
    formData.value.outputSchemaJson = JSON.stringify(schema, null, 2);
  } catch (error) {
    console.error('同步输出参数到JSON失败:', error);
  }
};

// 输入Schema变化处理
const onInputSchemaChange = () => {
  try {
    if (formData.value.inputSchemaJson) {
      const schema = JSON.parse(formData.value.inputSchemaJson);
      inputParams.value = schemaToParams(schema);
    }
  } catch (error) {
    // JSON格式错误时不更新参数列表
  }
};

// 输出Schema变化处理
const onOutputSchemaChange = () => {
  try {
    if (formData.value.outputSchemaJson) {
      const schema = JSON.parse(formData.value.outputSchemaJson);
      outputParams.value = schemaToParams(schema);
    }
  } catch (error) {
    // JSON格式错误时不更新参数列表
  }
};

// 添加输入参数
const addInputParam = () => {
  inputParams.value.push({
    name: '',
    type: 'string',
    required: false,
    description: '',
    defaultValue: '',
  });
};

// 删除输入参数
const removeInputParam = (index: number) => {
  inputParams.value.splice(index, 1);
};

// 添加输出参数
const addOutputParam = () => {
  outputParams.value.push({
    name: '',
    type: 'string',
    required: false,
    description: '',
    defaultValue: '',
  });
};

// 删除输出参数
const removeOutputParam = (index: number) => {
  outputParams.value.splice(index, 1);
};

// 从函数流生成输入Schema
const generateInputSchemaFromFlow = () => {
  // 这里可以分析函数流的输入变量来生成Schema
  MessagePlugin.info('从函数流生成输入Schema功能待实现');
};

// 从函数流生成输出Schema
const generateOutputSchemaFromFlow = () => {
  // 这里可以分析函数流的输出变量来生成Schema
  MessagePlugin.info('从函数流生成输出Schema功能待实现');
};

// 从参数生成测试数据
const generateTestDataFromSchema = () => {
  try {
    const testData: any = {};
    
    inputParams.value.forEach(param => {
      let value = param.defaultValue;
      
      if (!value) {
        switch (param.type) {
          case 'string':
            value = `示例${param.name}`;
            break;
          case 'number':
          case 'integer':
            value = 0;
            break;
          case 'boolean':
            value = false;
            break;
          case 'array':
            value = [];
            break;
          case 'object':
            value = {};
            break;
          case 'date':
            value = new Date().toISOString().split('T')[0];
            break;
          case 'datetime':
            value = new Date().toISOString();
            break;
          default:
            value = '';
        }
      }
      
      testData[param.name] = value;
    });
    
    formData.value.testDataJson = JSON.stringify(testData, null, 2);
    MessagePlugin.success('测试数据生成成功');
  } catch (error) {
    MessagePlugin.error(`生成测试数据失败: ${error.message}`);
  }
};

// 验证测试数据
const validateTestData = () => {
  try {
    if (!formData.value.testDataJson) {
      MessagePlugin.warning('请先输入测试数据');
      return;
    }
    
    const testData = JSON.parse(formData.value.testDataJson);
    
    // 验证必填参数
    const missingRequired: string[] = [];
    inputParams.value.forEach(param => {
      if (param.required && !(param.name in testData)) {
        missingRequired.push(param.name);
      }
    });
    
    if (missingRequired.length > 0) {
      MessagePlugin.error(`缺少必填参数: ${missingRequired.join(', ')}`);
      return;
    }
    
    MessagePlugin.success('测试数据验证通过');
  } catch (error) {
    MessagePlugin.error(`测试数据格式错误: ${error.message}`);
  }
};

// 验证表单
const validate = async () => {
  try {
    // 验证JSON格式
    if (formData.value.inputSchemaJson) {
      JSON.parse(formData.value.inputSchemaJson);
    }
    if (formData.value.outputSchemaJson) {
      JSON.parse(formData.value.outputSchemaJson);
    }
    if (formData.value.testDataJson) {
      JSON.parse(formData.value.testDataJson);
    }
    
    return true;
  } catch (error) {
    MessagePlugin.error(`JSON格式错误: ${error.message}`);
    return false;
  }
};

defineExpose({
  validate,
});
</script>

<style lang="less" scoped>
.action-library-params-config {
  max-width: 1200px;
  margin: 0 auto;

  .params-section {
    margin-bottom: 48px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .params-editor {
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      overflow: hidden;

      .visual-editor {
        padding: 16px;
      }

      .json-editor {
        border-top: 1px solid var(--td-border-level-1-color);
      }
    }

    .test-data-editor {
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      overflow: hidden;
    }
  }
}
</style>
