<template>
  <div>
    <t-tag v-if="status === -1" theme="danger" variant="light-outline">
      <close-circle-filled-icon />
      失败
    </t-tag>
    <t-tag v-if="status === 0" theme="warning" variant="light-outline">
      <time-filled-icon />
      运行中
    </t-tag>
    <t-tag v-if="status === 1" theme="success" variant="light-outline">
      <check-circle-filled-icon />
      已运行
    </t-tag>
  </div>
</template>
<script lang="ts">
export default {
  name: 'LogStatusTag',
};
</script>
<script setup lang="ts">
import { CheckCircleFilledIcon, CloseCircleFilledIcon, TimeFilledIcon } from 'tdesign-icons-vue-next';

const props = defineProps<{
  status?: number;
}>();
</script>
<style lang="less" scoped></style>
