<template>
  <div :class="sideNavCls">
    <t-menu
      :class="menuCls"
      :theme="theme"
      :value="active"
      :collapsed="collapsed"
      :expanded="expanded"
      :expand-mutex="menuAutoCollapsed"
      @expand="onExpanded"
    >
      <template #logo>
        <!-- <span v-if="showLogo" :class="`${prefix}-side-nav-logo-wrapper`" @click="goHome">
          <component :is="getLogo()" :class="logoCls" />
        </span> -->
        <div v-if="showLogo" class="logo-container" @click="projectVisible = true">
          {{ collapsed ? '' : title }}
        </div>
      </template>
      <menu-content :nav-data="menu" />
      <template #operations>
        <!-- <span :class="versionCls"> {{ !collapsed ? 'TDesign Starter' : '' }} {{ pgk.version }} </span> -->
      </template>
    </t-menu>
    <div :class="`${prefix}-side-nav-placeholder${collapsed ? '-hidden' : ''}`"></div>
  </div>
  <project-card-dialog v-model:visible="projectVisible" />
</template>

<script setup lang="ts">
import { difference, isEmpty, remove, union } from 'lodash-es';
import { MenuValue } from 'tdesign-vue-next';
import type { PropType } from 'vue';
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import ProjectCardDialog from '@/components/project-card/index.vue';
// import { useRouter } from 'vue-router';
// import AssetLogoFull from '@/assets/assets-logo-full.svg?component';
// import AssetLogo from '@/assets/assets-t-logo.svg?component';
import { prefix } from '@/config/global';
import { getActive } from '@/router';
import { useSettingStore, useUserStore } from '@/store';
import type { MenuRoute, ModeType } from '@/types/interface';

const route = useRoute();
// import pgk from '../../../package.json';
import MenuContent from './MenuContent.vue';

const userStore = useUserStore();

const MIN_POINT = 992 - 1;

const props = defineProps({
  menu: {
    type: Array as PropType<MenuRoute[]>,
    default: () => [],
  },
  showLogo: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  isFixed: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  layout: {
    type: String as PropType<string>,
    default: '',
  },
  headerHeight: {
    type: String as PropType<string>,
    default: '64px',
  },
  theme: {
    type: String as PropType<ModeType>,
    default: 'light',
  },
  isCompact: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
});

const projectVisible = ref(false);
const title = computed(() => {
  return userStore.projectName || '数据集成平台';
});

const collapsed = computed(() => useSettingStore().isSidebarCompact);
const menuAutoCollapsed = computed(() => useSettingStore().menuAutoCollapsed);

const active = computed(() => getActive(route));

const expanded = ref<MenuValue[]>([]);

watch(
  () => active.value,
  (path) => {
    const parentPath = path.substring(0, path.lastIndexOf('/'));
    expanded.value = menuAutoCollapsed.value ? [parentPath] : union([parentPath], expanded.value);
  },
);

const onExpanded = (value: MenuValue[]) => {
  const currentOperationMenu = difference(expanded.value, value);
  const allExpanded = union(value, expanded.value);
  remove(allExpanded, (item) => currentOperationMenu.includes(item));
  expanded.value = allExpanded;
};

// const sideMode = computed(() => {
//   const { theme } = props;
//   return theme === 'dark';
// });
const sideNavCls = computed(() => {
  const { isCompact } = props;
  return [
    `${prefix}-sidebar-layout`,
    {
      [`${prefix}-sidebar-compact`]: isCompact,
    },
  ];
});
// const logoCls = computed(() => {
//   return [
//     `${prefix}-side-nav-logo-${collapsed.value ? 't' : 'tdesign'}-logo`,
//     {
//       [`${prefix}-side-nav-dark`]: sideMode.value,
//     },
//   ];
// });
// const versionCls = computed(() => {
//   return [
//     `version-container`,
//     {
//       [`${prefix}-side-nav-dark`]: sideMode.value,
//     },
//   ];
// });
const menuCls = computed(() => {
  const { showLogo, isFixed, layout } = props;
  return [
    `${prefix}-side-nav`,
    {
      [`${prefix}-side-nav-no-logo`]: !showLogo,
      [`${prefix}-side-nav-no-fixed`]: !isFixed,
      [`${prefix}-side-nav-mix-fixed`]: layout === 'mix' && isFixed,
    },
  ];
});

// const router = useRouter();
const settingStore = useSettingStore();

const autoCollapsed = () => {
  const isCompact = window.innerWidth <= MIN_POINT;
  settingStore.updateConfig({
    isSidebarCompact: isCompact,
  });
};

onMounted(() => {
  const path = getActive(route);
  const parentPath = path.substring(0, path.lastIndexOf('/'));
  expanded.value = union([parentPath], expanded.value);
  autoCollapsed();
  window.onresize = () => {
    autoCollapsed();
  };

  if (isEmpty(userStore.projectId)) {
    projectVisible.value = true;
  }
});

// const goHome = () => {
//   router.push('/dashboard/base');
// };

// const getLogo = () => {
//   if (collapsed.value) return AssetLogo;
//   return AssetLogoFull;
// };
</script>

<style lang="less" scoped>
.logo-container {
  color: #ddd;
  font-size: 18px;
  line-height: 56px;
  width: 100%;
  font-weight: 700;
}
</style>
