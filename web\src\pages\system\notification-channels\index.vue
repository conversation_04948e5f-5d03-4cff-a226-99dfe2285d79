<template>
  <div class="notification-channels-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">通知渠道</h1>
          <p class="page-description">管理和配置各种通知渠道，支持邮件、钉钉、飞书等多种通知方式</p>
        </div>
        <div class="action-section">
          <t-button theme="primary" size="large" @click="handleAdd">
            <template #icon>
              <add-icon />
            </template>
            新增通道
          </t-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-container">
        <div class="search-left">
          <div class="search-input-group">
            <t-input
              v-model="searchForm.keyword"
              placeholder="搜索通道名称..."
              clearable
              class="search-input"
              @enter="handleSearch"
            >
              <template #prefix-icon>
                <search-icon />
              </template>
            </t-input>
            <t-select
              v-model="searchForm.channelType"
              :options="channelTypes"
              placeholder="选择类型"
              clearable
              class="search-select"
            >
            </t-select>
          </div>
        </div>
        <div class="search-right">
          <t-space>
            <t-button theme="primary" @click="handleSearch">
              <template #icon><search-icon /></template>
              搜索
            </t-button>
            <t-button theme="default" variant="outline" @click="handleReset"> 重置 </t-button>
          </t-space>
        </div>
      </div>
    </div>

    <!-- 通道列表 -->
    <div class="channels-section">
      <div class="section-header">
        <div class="section-title">
          <h3>通道列表</h3>
          <span class="section-count">共 {{ pagination.total }} 个通道</span>
        </div>
        <div class="section-actions">
          <t-space>
            <t-button-group>
              <t-button :theme="viewMode === 'card' ? 'primary' : 'default'" @click="viewMode = 'card'">
                卡片视图
              </t-button>
              <t-button :theme="viewMode === 'table' ? 'primary' : 'default'" @click="viewMode = 'table'">
                表格视图
              </t-button>
            </t-button-group>
            <t-button theme="default" variant="outline" @click="loadData">
              <template #icon>
                <refresh-icon />
              </template>
              刷新
            </t-button>
          </t-space>
        </div>
      </div>

      <!-- 卡片布局 -->
      <div v-if="viewMode === 'card'" class="cards-container">
        <div v-if="tableData.length === 0 && !loading" class="empty-state">
          <t-empty description="暂无通知渠道，点击上方按钮添加">
            <template #image>
              <notification-icon size="64px" />
            </template>
          </t-empty>
        </div>
        <div v-else class="cards-grid">
          <div v-for="channel in tableData" :key="channel.id" class="channel-card">
            <div class="card-header">
              <div class="channel-info">
                <div class="channel-icon" :class="getChannelIconClass(channel.channelType)">
                  <mail-icon v-if="channel.channelType === 'Email'" />
                  <notification-icon v-else />
                </div>
                <div class="channel-details">
                  <div class="channel-name">{{ channel.channelName }}</div>
                  <div class="channel-type">{{ getChannelTypeText(channel.channelType) }}</div>
                </div>
              </div>
              <div class="channel-status">
                <t-tag :theme="channel.isEnabled ? 'success' : 'default'" variant="light">
                  {{ channel.isEnabled ? '启用' : '禁用' }}
                </t-tag>
              </div>
            </div>
            <div class="card-content">
              <div class="channel-description">
                {{ channel.description || '暂无描述' }}
              </div>
              <div class="channel-meta">
                <div class="meta-row">
                  <div class="meta-item">
                    <span class="meta-text">间隔: {{ channel.intervalSeconds }}秒</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-text">{{ formatDate(channel.timeCreate) }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-actions">
              <t-button size="small" variant="text" theme="primary" @click="handleTest(channel)"> 测试 </t-button>
              <t-button size="small" variant="text" theme="primary" @click="handleEdit(channel)"> 编辑 </t-button>
              <t-button size="small" variant="text" theme="danger" @click="handleDelete(channel)"> 删除 </t-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格布局 -->
      <div v-else class="table-container">
        <t-table
          :data="tableData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
          row-key="id"
          stripe
          hover
          size="medium"
        >
          <template #isEnabled="{ row }">
            <t-tag :theme="row.isEnabled ? 'success' : 'default'" variant="light">
              {{ row.isEnabled ? '启用' : '禁用' }}
            </t-tag>
          </template>
          <template #actions="{ row }">
            <div class="table-actions">
              <t-link theme="primary" size="small" @click="handleTest(row)">测试</t-link>
              <t-divider layout="vertical" />
              <t-link theme="primary" size="small" @click="handleEdit(row)">编辑</t-link>
              <t-divider layout="vertical" />
              <t-link theme="danger" size="small" @click="handleDelete(row)">删除</t-link>
            </div>
          </template>
        </t-table>
      </div>

      <!-- 分页 -->
      <div v-if="viewMode === 'card' && pagination.total > 0" class="pagination-container">
        <t-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-jumper="pagination.showJumper"
          :show-size-changer="pagination.showSizeChanger"
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <notification-channel-dialog
      v-model:visible="dialogVisible"
      :channel-data="currentChannel"
      :channel-types="channelTypes"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { AddIcon, RefreshIcon, NotificationIcon, MailIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { api, Services } from '@/api/system';
import NotificationChannelDialog from './components/NotificationChannelDialog.vue';

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const dialogVisible = ref(false);
const currentChannel = ref(null);
const channelTypes = ref([]);
const viewMode = ref('card'); // 'card' | 'table'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  channelType: '',
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
});

// 表格列配置
const columns = [
  {
    colKey: 'channelName',
    title: '通道名称',
    width: 200,
  },
  {
    colKey: 'channelType',
    title: '通道类型',
    width: 120,
    cell: (_h: any, { row }: any) => {
      return getChannelTypeText(row.channelType);
    },
  },
  {
    colKey: 'description',
    title: '描述',
    ellipsis: true,
  },
  {
    colKey: 'isEnabled',
    title: '状态',
    width: 100,
  },
  {
    colKey: 'intervalSeconds',
    title: '发送间隔(秒)',
    width: 120,
  },
  {
    colKey: 'timeCreate',
    title: '创建时间',
    width: 180,
    cell: (_h: any, { row }: any) => {
      return formatDate(row.timeCreate);
    },
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 200,
  },
];

// 生命周期
onMounted(() => {
  loadChannelTypes();
  loadData();
});

// 工具方法
const getChannelTypeText = (type: string) => {
  const typeMap = {
    Email: '邮件通知',
    Dingtalk: '钉钉通知',
    Feishu: '飞书通知',
    Weixin: '企业微信通知',
  };
  return typeMap[type] || type;
};

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString();
};

const getChannelIconClass = (type: string) => {
  const classMap = {
    Email: 'email-icon',
    Dingtalk: 'dingtalk-icon',
    Feishu: 'feishu-icon',
    Weixin: 'weixin-icon',
  };
  return classMap[type] || 'default-icon';
};

// 方法
const loadChannelTypes = async () => {
  try {
    const response = await api.run(Services.notificationChannelGetChannelTypes);
    channelTypes.value = response || [];
  } catch (error) {
    console.error('加载通道类型失败:', error);
  }
};

const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      keyword: searchForm.keyword,
      channelType: searchForm.channelType,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize,
    };

    const response = await api.run(Services.notificationChannelGetAll, params);
    tableData.value = response?.list || [];
    pagination.total = response?.paging?.total || 0;
  } catch (error) {
    console.error('加载数据失败:', error);
    MessagePlugin.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  searchForm.keyword = '';
  searchForm.channelType = '';
  pagination.current = 1;
  loadData();
};

const handlePageChange = (pageInfo: any) => {
  pagination.current = typeof pageInfo === 'number' ? pageInfo : pageInfo.current;
  loadData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  loadData();
};

const handleAdd = () => {
  currentChannel.value = null;
  dialogVisible.value = true;
};

const handleEdit = (row: any) => {
  currentChannel.value = { ...row };
  dialogVisible.value = true;
};

const handleTest = async (row: any) => {
  try {
    loading.value = true;
    const response = await api.run(Services.notificationChannelTestChannel, { id: row.id });
    if (response) {
      MessagePlugin.success('测试通知发送成功');
    } else {
      MessagePlugin.error('测试通知发送失败');
    }
  } catch (error) {
    console.error('测试通知失败:', error);
    MessagePlugin.error('测试通知失败');
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (row: any) => {
  const confirmed = await new Promise((resolve) => {
    DialogPlugin.confirm({
      header: '确认删除',
      body: `确定要删除通道"${row.channelName}"吗？`,
      onConfirm: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });

  if (!confirmed) return;

  try {
    await api.run(Services.notificationChannelDelete, { id: row.id });
    MessagePlugin.success('删除成功');
    loadData();
  } catch (error) {
    console.error('删除失败:', error);
    MessagePlugin.error('删除失败');
  }
};

const handleDialogSuccess = () => {
  dialogVisible.value = false;
  loadData();
};
</script>

<style lang="less" scoped>
.notification-channels-page {
  min-height: 100vh;
  background: var(--td-bg-color-page);

  .page-header {
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    margin-bottom: 24px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 32px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }

        .page-description {
          margin: 0;
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  .search-section {
    max-width: 1200px;
    margin: 0 auto 16px auto;
    padding: 0 24px;

    .search-container {
      background: var(--td-bg-color-container);
      border-radius: 6px;
      padding: 16px;
      border: 1px solid var(--td-border-level-1-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      .search-left {
        flex: 1;

        .search-input-group {
          display: flex;
          gap: 12px;
          align-items: center;

          .search-input {
            flex: 1;
            max-width: 300px;
          }

          .search-select {
            width: 160px;
          }
        }
      }

      .search-right {
        :deep(.t-button) {
          border-radius: 4px;
          padding: 6px 16px;
          font-weight: 400;
        }
      }
    }
  }

  .channels-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    .section-header {
      background: var(--td-bg-color-container);
      border-radius: 4px 4px 0 0;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--td-border-level-1-color);

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: var(--td-text-color-primary);
        }

        .section-count {
          font-size: 12px;
          color: var(--td-text-color-placeholder);
          background: var(--td-bg-color-component);
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }

    .cards-container {
      background: var(--td-bg-color-container);
      padding: 16px;

      .empty-state {
        padding: 40px 0;
        text-align: center;

        :deep(.t-empty) {
          .t-empty__image {
            margin-bottom: 12px;
            opacity: 0.5;
          }

          .t-empty__description {
            color: var(--td-text-color-placeholder);
            font-size: 14px;
          }
        }
      }

      .cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 16px;
      }

      .channel-card {
        background: var(--td-bg-color-container);
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        padding: 16px;
        transition: border-color 0.2s ease;
        display: flex;
        flex-direction: column;

        &:hover {
          border-color: var(--td-brand-color);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .channel-info {
            display: flex;
            align-items: center;
            flex: 1;

            .channel-icon {
              width: 32px;
              height: 32px;
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              font-size: 16px;
              background: var(--td-bg-color-component);
              color: var(--td-text-color-secondary);

              &.email-icon {
                background: #f0f0f0;
                color: #666;
              }

              &.dingtalk-icon {
                background: #e6f7ff;
                color: #1890ff;
              }

              &.feishu-icon {
                background: #e6fffb;
                color: #00b894;
              }

              &.weixin-icon {
                background: #f6ffed;
                color: #52c41a;
              }

              &.default-icon {
                background: var(--td-bg-color-component);
                color: var(--td-text-color-secondary);
              }
            }

            .channel-details {
              .channel-name {
                font-size: 14px;
                font-weight: 500;
                color: var(--td-text-color-primary);
                margin-bottom: 2px;
                line-height: 1.4;
              }

              .channel-type {
                font-size: 12px;
                color: var(--td-text-color-placeholder);
                font-weight: 400;
              }
            }
          }

          .channel-status {
            :deep(.t-tag) {
              border-radius: 4px;
              font-weight: 400;
              padding: 2px 8px;
              font-size: 12px;
            }
          }
        }

        .card-content {
          flex: 1;
          margin-bottom: 12px;

          .channel-description {
            font-size: 12px;
            color: var(--td-text-color-placeholder);
            line-height: 1.5;
            margin-bottom: 12px;
            min-height: 32px;
            background: var(--td-bg-color-component);
            padding: 8px;
            border-radius: 4px;
          }

          .channel-meta {
            .meta-row {
              display: flex;
              flex-direction: column;
              gap: 6px;
            }

            .meta-item {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: var(--td-text-color-placeholder);

              .meta-text {
                font-weight: 400;
              }
            }
          }
        }

        .card-actions {
          display: flex;
          justify-content: space-between;
          gap: 6px;
          padding-top: 12px;
          border-top: 1px solid var(--td-border-level-1-color);

          :deep(.t-button) {
            border-radius: 4px;
            font-weight: 400;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
    }

    .table-container {
      background: var(--td-bg-color-container);
      border-radius: 0 0 4px 4px;
      overflow: hidden;

      .table-actions {
        display: flex;
        align-items: center;
        gap: 0;

        :deep(.t-link) {
          padding: 4px 8px;
          font-size: 12px;
        }

        :deep(.t-divider) {
          margin: 0 4px;
          height: 12px;
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      padding: 16px;
      background: var(--td-bg-color-container);
      border-radius: 0 0 4px 4px;
      border-top: 1px solid var(--td-border-level-1-color);
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .notification-channels-page {
    .page-header .header-content,
    .overview-section,
    .search-section,
    .channels-section {
      max-width: 100%;
      padding-left: 16px;
      padding-right: 16px;
    }
  }
}

@media (max-width: 768px) {
  .notification-channels-page {
    .overview-section {
      :deep(.t-col) {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 16px;
      }
    }

    .channels-section .cards-container {
      :deep(.t-col) {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
