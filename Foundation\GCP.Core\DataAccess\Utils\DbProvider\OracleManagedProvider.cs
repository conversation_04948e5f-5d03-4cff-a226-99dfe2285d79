﻿using System.Data.Common;

namespace GCP.DataAccess
{
    internal class OracleManagedProvider : BaseDbProvider
    {
        public override string ProviderName
        {
            get
            {
                return "Oracle.ManagedDataAccess.Client";
            }
        }

        public override bool NotUseParameter
        {
            get
            {
                return false;
            }
        }

        public override DbProviderFactory Factory => Oracle.ManagedDataAccess.Client.OracleClientFactory.Instance;
        public override DbProviderType ProviderType => DbProviderType.Oracle;

        public override string GetParameterName(string parameterName)
        {
            return parameterName[0] == ':' ? parameterName : ":" + parameterName;
        }

        public override string GetPagingSql(int startIndex, int length, string selectSql, string orderBySql = "")
        {
            return string.Format("SELECT * FROM (SELECT t.*, ROWNUM v_rowno FROM ({2} {3}) t WHERE rownum <= {0} + {1}) t WHERE t.v_rowno > {0}", startIndex, length, selectSql, orderBySql);
        }

        public override string GetTimeFormatStr(DateTime time)
        {
            return "TO_DATE('" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss')";
        }

        public override string GetTimeSql()
        {
            return "SELECT SYSDATE FROM DUAL";
        }

        public override string GetTableSizeSql(string tableName, string schemaName = null)
        {
            var sql = @"
SELECT
    segment_name AS TABLE_NAME,
	sum(bytes) / 1024 / 1024 AS TABLE_SIZE
FROM
	user_extents
WHERE
	segment_type = 'TABLE'
";
            if (!string.IsNullOrEmpty(tableName))
            {
                sql += " and segment_name = '" + tableName.Replace("'", "''") + "'";
            }

            sql += @"
GROUP BY
	segment_name
";
            return sql;
        }
    }
}
