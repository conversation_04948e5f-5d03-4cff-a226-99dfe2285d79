﻿using Polly;
using Polly.CircuitBreaker;
using Polly.Fallback;
using Polly.Retry;
using Polly.Timeout;
using Serilog;
using System.Threading.RateLimiting;

namespace GCP.Common
{
    public class ResilienceHandle<T>
    {
        /// <summary>
        /// 管道名称（用于日志提示）
        /// </summary>
        private readonly string _pipelineName;
        private readonly PredicateBuilder<T> _predicateBuilder = null;

        public ResilienceHandle(string pipelineName)
        {
            _pipelineName = pipelineName;
            _predicateBuilder = new PredicateBuilder<T>()
                .Handle<Exception>();
            //.HandleResult(static t => t?.GetType() != typeof(CustomException));
        }


        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int? Timeout { get; set; }

        #region 重试
        /// <summary>
        /// 重试次数
        /// </summary>
        public int? RetryCount { get; set; }
        /// <summary>
        /// 重试延迟秒数
        /// </summary>
        public int? RetryDelaysInSeconds { get; set; }
        /// <summary>
        /// 重试退避类型
        /// 常量退避(Constant): 每次尝试的退避时间是恒定的。200ms, 200ms, 200ms, etc.
        /// 线性退避(Linear) : 退避时间以线性方式增长, 允许更精确的控制。100ms, 200ms, 300ms, 400ms, etc.
        /// 指数退避(Exponential) : 退避时间以指数方式增长, 使用2的幂次。200ms, 400ms, 800ms.
        /// </summary>
        public DelayBackoffType RetryBackoffType { get; set; }
        #endregion

        #region 熔断
        /// <summary>
        /// 熔断故障比例
        /// </summary>
        public double? CircuitBreakerFailureThreshold { get; set; }
        /// <summary>
        /// 滚动时间（秒）
        /// </summary>
        public int? CircuitBreakerSamplingDuration { get; set; }
        /// <summary>
        /// 最小吞吐量
        /// </summary>
        public int? CircuitBreakerMinimumThroughput { get; set; }
        /// <summary>
        /// 熔断持续时间, 之后会恢复（秒）
        /// </summary>
        public int? CircuitBreakerDurationOfBreak { get; set; }
        #endregion

        #region 舱壁（隔离约束每个服务的关键资源, 如最大并行、最大操作, 从而降低主机CPU、线程、内存等）
        /// <summary>
        /// 最大并行
        /// </summary>
        public int? BulkheadMaxParallelization { get; set; }
        /// <summary>
        /// 随时可能正在排队的最大操作数
        /// </summary>
        public int BulkheadMaxQueuingActions { get; set; }
        #endregion

        #region 全局速率限制（一般按照用户或客户端限制速率）
        /// <summary>
        /// 每个时间跨度允许的执行次数
        /// </summary>
        public int? RateLimitNumberOfExecutions { get; set; }
        /// <summary>
        /// 允许执行的频率（秒）
        /// </summary>
        public int? RateLimitPerTimeSpan { get; set; }
        #endregion

        public ResiliencePipeline<T> Build(Func<Exception, Task> errorAction = null, Func<Task> fallbackAction = null)
        {
            var pipelineBuilder = new ResiliencePipelineBuilder<T>();
            // 全局异常处理
            if (errorAction != null)
            {
                fallbackAction ??= () => Task.CompletedTask;
                var optionsOnFallback = new FallbackStrategyOptions<T>
                {
                    ShouldHandle = _predicateBuilder,
                    FallbackAction = async args =>
                    {
                        await fallbackAction().ConfigureAwait(false);
                        return default;
                    },
                    OnFallback = async args =>
                    {
                        var exception = args.Outcome.Exception!;
                        if (exception != null)
                        {
                            await errorAction(exception).ConfigureAwait(false);
                        }
                    }
                };
                pipelineBuilder.AddFallback(optionsOnFallback);
            }

            // 超时
            if (Timeout.HasValue)
            {
                var optionsOnTimeout = new TimeoutStrategyOptions
                {
                    Timeout = TimeSpan.FromSeconds(Timeout.Value),
                    OnTimeout = args =>
                    {
                        Log.Error("[{PipelineName}] - {ContextOperationKey}: 在执行 {TimeoutTotalSeconds} 秒后超时", _pipelineName, args.Context.OperationKey, args.Timeout.TotalSeconds);
                        return default;
                    }
                };

                pipelineBuilder.AddTimeout(optionsOnTimeout);
            }

            // 重试
            if (RetryCount.HasValue && RetryDelaysInSeconds.HasValue)
            {
                var optionsOnRetry = new RetryStrategyOptions<T>
                {
                    ShouldHandle = _predicateBuilder,
                    MaxRetryAttempts = RetryCount.Value,
                    BackoffType = RetryBackoffType,
                    Delay = TimeSpan.FromSeconds(RetryDelaysInSeconds.Value),
                    OnRetry = args =>
                    {
                        //var exception = args.Outcome.Exception!;
                        //if (exception != null)
                        //    Log.Error("[{PipelineName}] - {ContextOperationKey}: 重试, 尝试{ArgsAttemptNumber} 次, 延迟 {ArgsDuration}, 异常：{ExceptionMessage}", _pipelineName, args.Context.OperationKey, args.AttemptNumber, args.Duration, exception.Message);
                        // 安全地记录日志，即使没有异常（例如，由结果触发时）
                        if (args.Outcome.Exception != null)
                        {
                            Log.Warning("[{PipelineName}] - 重试第 {AttemptNumber} 次。延迟: {Delay}。原因: 异常 -> {ExceptionType}: {ExceptionMessage}",
                                _pipelineName, args.AttemptNumber, args.Duration, args.Outcome.Exception.GetType().Name, args.Outcome.Exception.Message);
                        }
                        else
                        {
                            // 如果配置了 HandleResult，可以在这里记录由返回值触发的重试
                            Log.Warning("[{PipelineName}] - 重试第 {AttemptNumber} 次。延迟: {Delay}。原因: 不符合预期的返回值 -> {Result}",
                                _pipelineName, args.AttemptNumber, args.Duration, args.Outcome.Result);
                        }
                        return default;
                    }
                };
                pipelineBuilder.AddRetry(optionsOnRetry);
            }

            // 熔断
            if (CircuitBreakerFailureThreshold.HasValue && CircuitBreakerSamplingDuration.HasValue && CircuitBreakerMinimumThroughput.HasValue && CircuitBreakerDurationOfBreak.HasValue)
            {
                var optionsOnCircuitBreaker = new CircuitBreakerStrategyOptions<T>
                {
                    FailureRatio = CircuitBreakerFailureThreshold.Value,
                    SamplingDuration = TimeSpan.FromSeconds(CircuitBreakerSamplingDuration.Value),
                    MinimumThroughput = CircuitBreakerMinimumThroughput.Value,
                    BreakDuration = TimeSpan.FromSeconds(CircuitBreakerDurationOfBreak.Value),
                };
                pipelineBuilder.AddCircuitBreaker(optionsOnCircuitBreaker);
            }

            // 舱壁, 限制最大并行和最大排队
            if (BulkheadMaxParallelization.HasValue)
            {
                pipelineBuilder.AddConcurrencyLimiter(BulkheadMaxParallelization.Value, BulkheadMaxQueuingActions);
            }

            // 全局速率限制
            if (RateLimitNumberOfExecutions.HasValue && RateLimitPerTimeSpan.HasValue)
            {
                var optionsOnRateLimit = new SlidingWindowRateLimiter(new SlidingWindowRateLimiterOptions
                {
                    PermitLimit = RateLimitNumberOfExecutions.Value,
                    Window = TimeSpan.FromMinutes(RateLimitPerTimeSpan.Value),
                });
                pipelineBuilder.AddRateLimiter(optionsOnRateLimit);
            }

            return pipelineBuilder.Build();
        }
    }
}
