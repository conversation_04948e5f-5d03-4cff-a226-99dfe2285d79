using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("dirTree", "目录树服务")]
    class DirTreeService : BaseService
    {
        public DirTreeService() : base()
        {
        }

        public DirTreeService(BaseService service) : base(service)
        {
        }

        public DirTreeService(FunctionContext context, string sulutionId, string projectId) : base(context, sulutionId, projectId)
        {
        }

        [Function("getAll", "获取目录树列表")]
        public List<LcDirTree> GetAll(string dirType = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDirTrees
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        (string.IsNullOrEmpty(dirType) || a.DirType == dirType)
                        orderby a.Seq
                        select a).ToList();
            return data;
        }

        [Function("get", "获取目录信息")]
        public LcDirTree Get(string id)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDirTrees
                        where a.Id == id &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId
                        select a).FirstOrDefault();
            return data;
        }

        [Function("getByCode", "根据编码获取目录信息")]
        public LcDirTree GetByCode(string dirCode)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDirTrees
                        where a.DirCode == dirCode &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId
                        select a).FirstOrDefault();
            return data;
        }

        [Function("getChildren", "获取子目录列表")]
        public List<LcDirTree> GetChildren(string parentId)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDirTrees
                        where a.ParentId == parentId &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId
                        orderby a.Seq
                        select a).ToList();
            return data;
        }

        [Function("save", "保存目录信息")]
        public void Save(LcDirTree data)
        {
            using var db = this.GetDb();
            if (string.IsNullOrEmpty(data.Id))
            {
                data.SolutionId = this.SolutionId;
                this.InsertData(data, db);
            }
            else
            {
                var oldData = db.LcDirTrees.FirstOrDefault(t => t.Id == data.Id && t.SolutionId == this.SolutionId)
                    ?? throw new Exception("目录不存在");

                oldData.State = 1;
                oldData.DirCode = data.DirCode;
                oldData.DirName = data.DirName;
                oldData.Description = data.Description;
                oldData.LeafId = data.LeafId;

                this.UpdateData(oldData, db);
            }
        }

        [Function("delete", "删除目录")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            var data = db.LcDirTrees.FirstOrDefault(t => t.Id == id && t.SolutionId == this.SolutionId)
                ?? throw new Exception("目录不存在");

            // 检查是否有子目录
            var hasChildren = db.LcDirTrees.Any(t => t.ParentId == id && t.State == 1);
            if (hasChildren)
            {
                throw new Exception("请先删除子目录");
            }

            db.LcDirTrees.Delete(t => t.Id == id);
        }

        [Function("move", "移动目录")]
        public void Move(string id, string targetParentId)
        {
            using var db = this.GetDb();
            var data = db.LcDirTrees.FirstOrDefault(t => t.Id == id && t.SolutionId == this.SolutionId)
                ?? throw new Exception("目录不存在");

            // 检查目标父目录是否存在
            if (targetParentId != "ROOT")
            {
                var targetParent = db.LcDirTrees.FirstOrDefault(t => t.Id == targetParentId && t.SolutionId == this.SolutionId && t.State == 1)
                    ?? throw new Exception("目标父目录不存在");

                // 检查是否形成循环引用
                var parent = targetParent;
                if (parent != null && parent.ParentId != "ROOT")
                {
                    if (parent.Id == id)
                    {
                        throw new Exception("不能将目录移动到其子目录下");
                    }

                    if (parent.IsLeaf == 'Y')
                    {
                        targetParentId = db.LcDirTrees.FirstOrDefault(t => t.Id == parent.ParentId && t.State == 1)?.ParentId ?? "ROOT";
                    }
                }
            }

            data.ParentId = targetParentId;
            this.UpdateData(data, db);
        }

        [Function("getLeafList", "获取叶子列表")]
        public List<DirTreeVO> GetLeafList(string dirType, string keyword = null)
        {
            using var db = this.GetDb();
            return (from a in db.LcDirTrees
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.DirType == dirType &&
                        a.IsLeaf == 'Y' &&
                        (string.IsNullOrEmpty(keyword) || (a.DirName.Contains(keyword)) || (a.Description.Contains(keyword) || a.DirCode.Contains(keyword)))
                        orderby a.Seq
                        select new DirTreeVO
                        {
                            Id = a.Id,
                            Label = a.DirName,
                            Value = a.DirCode ?? a.Id,
                            Description = a.Description,
                            IsLeaf = a.IsLeaf == 'Y',
                            LeafId = a.LeafId,
                            ParentId = a.ParentId
                        }).ToList();
        }

        [Function("getTreeItem", "获取目录项")]
        public DirTreeVO GetTreeItem(string id)
        {
            var item = Get(id);
            if (item == null)
            {
                throw new Exception("目录不存在");
            }

            var result = new DirTreeVO
            {
                Id = item.Id,
                Label = item.DirName,
                Value = item.DirCode ?? item.Id,
                Description = item.Description,
                IsLeaf = item.IsLeaf == 'Y',
                LeafId = item.LeafId,
                ParentId = item.ParentId
            };

            return result;
        }

        [Function("getTree", "获取目录树")]
        public List<DirTreeVO> GetTree(string dirType)
        {
            var allDirs = GetAll(dirType);
            var result = BuildDirTree(allDirs, "ROOT");
            return result;
        }

        private List<DirTreeVO> BuildDirTree(List<LcDirTree> allDirs, string parentId)
        {
            var children = allDirs.Where(d => d.ParentId == parentId).ToList();
            var result = new List<DirTreeVO>();

            foreach (var dir in children)
            {
                var node = new DirTreeVO
                {
                    Id = dir.Id,
                    Label = dir.DirName,
                    Value = dir.DirCode ?? dir.Id,
                    Description = dir.Description,
                    Children = BuildDirTree(allDirs, dir.Id),
                    IsLeaf = dir.IsLeaf == 'Y',
                    LeafId = dir.LeafId,
                    ParentId = dir.ParentId
                };

                result.Add(node);
            }

            return result;
        }
    }
}