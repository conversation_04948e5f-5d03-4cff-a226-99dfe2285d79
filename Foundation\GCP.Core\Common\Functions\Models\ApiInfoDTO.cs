﻿using GCP.DataAccess;

namespace GCP.Common
{
    public class ApiInfoDTO
    {
        public LcApi Basice { get; set; }

        public ApiInfoDTO()
        {

        }
        public ApiInfoDTO(LcApi basice)
        {
            Basice = basice;
        }

        private bool? hasResponse;

        public bool HasResponse
        {
            get
            {
                if (hasResponse.HasValue) { return hasResponse.Value; }
                hasResponse = !string.IsNullOrEmpty(Basice.Response);
                return hasResponse.Value;
            }
        }

        private List<FlowData> responseData;
        public List<FlowData> ResponseData
        {
            get
            {
                if (HasResponse)
                {
                    responseData = JsonHelper.Deserialize<List<FlowData>>(Basice.Response);
                }
                return responseData;
            }
        }

        private bool? hasHeader;
        public bool HasHeader
        {
            get
            {
                if (hasHeader.HasValue) { return hasHeader.Value; }
                hasHeader = !string.IsNullOrEmpty(Basice.Headers);
                return hasHeader.Value;
            }
        }

        private List<FlowData> headerData;
        public List<FlowData> HeaderData
        {
            get
            {
                if (HasHeader)
                {
                    headerData = JsonHelper.Deserialize<List<FlowData>>(Basice.Headers);
                }
                return headerData;
            }
        }

        private bool? hasParams;
        public bool HasParams
        {
            get
            {
                if (hasParams.HasValue) { return hasParams.Value; }
                hasParams = !string.IsNullOrEmpty(Basice.QueryParameters);
                return hasParams.Value;
            }
        }

        private List<FlowData> paramsData;
        public List<FlowData> ParamsData
        {
            get
            {
                if (HasParams)
                {
                    paramsData = JsonHelper.Deserialize<List<FlowData>>(Basice.QueryParameters);
                }
                return paramsData;
            }
        }

        private bool? hasBody;
        public bool HasBody
        {
            get
            {
                if (hasBody.HasValue) { return hasBody.Value; }
                hasBody = !string.IsNullOrEmpty(Basice.Body) && Basice.Body != "[]";
                return hasBody.Value;
            }
        }


        private List<FlowData> bodyData;
        public List<FlowData> BodyData
        {
            get
            {
                if (HasBody)
                {
                    bodyData = JsonHelper.Deserialize<List<FlowData>>(Basice.Body);
                }
                return bodyData;
            }
        }
    }
}
