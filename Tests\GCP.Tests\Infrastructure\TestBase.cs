using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using Xunit.Abstractions;
using GCP.Common;

namespace GCP.Tests.Infrastructure
{
    /// <summary>
    /// 测试基类，提供通用的测试基础设施
    /// </summary>
    public abstract class TestBase : IDisposable
    {
        protected readonly ITestOutputHelper Output;
        protected readonly IServiceProvider ServiceProvider;
        protected readonly IConfiguration Configuration;
        protected readonly Microsoft.Extensions.Logging.ILogger Logger;

        protected TestBase(ITestOutputHelper output)
        {
            Output = output;
            
            // 配置Serilog输出到测试控制台
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateLogger();

            // 构建配置
            Configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.test.json", optional: false)
                .AddEnvironmentVariables()
                .Build();

            // 构建服务容器
            var services = new ServiceCollection();
            ConfigureServices(services);
            ServiceProvider = services.BuildServiceProvider();

            // 设置ServiceLocator，这样FunctionContext就能正常工作
            ServiceLocator.SetServices(ServiceProvider);

            Logger = ServiceProvider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<TestBase>>();

            ConfigureData();
        }

        protected virtual void ConfigureData()
        {

        }

        /// <summary>
        /// 配置测试服务
        /// </summary>
        protected virtual void ConfigureServices(IServiceCollection services)
        {
            // 添加配置
            services.AddSingleton(Configuration);
            
            // 添加日志
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // 添加基础服务
            services.AddSingleton<ServiceLocator>();
            
            // 配置数据库连接（使用内存数据库进行测试）
            if (Configuration.GetValue<bool>("TestSettings:UseInMemoryDatabase"))
            {
                ConfigureInMemoryDatabase(services);
            }
            else
            {
                ConfigureTestDatabase(services);
            }

            // 添加自定义服务
            ConfigureTestServices(services);
        }

        /// <summary>
        /// 配置内存数据库
        /// </summary>
        protected virtual void ConfigureInMemoryDatabase(IServiceCollection services)
        {
            // 这里可以配置内存数据库连接
            // 由于项目使用Linq2DB，需要配置相应的内存数据库提供程序
        }

        /// <summary>
        /// 配置测试数据库
        /// </summary>
        protected virtual void ConfigureTestDatabase(IServiceCollection services)
        {
            // 配置测试数据库连接
        }

        /// <summary>
        /// 配置测试特定的服务
        /// </summary>
        protected virtual void ConfigureTestServices(IServiceCollection services)
        {
            // 子类可以重写此方法来添加特定的测试服务
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        protected T GetService<T>() where T : notnull
        {
            return ServiceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// 获取可选服务实例
        /// </summary>
        protected T? GetOptionalService<T>() where T : class
        {
            return ServiceProvider.GetService<T>();
        }

        public virtual void Dispose()
        {
            if (ServiceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
            Log.CloseAndFlush();
            GC.SuppressFinalize(this);
        }
    }
}
