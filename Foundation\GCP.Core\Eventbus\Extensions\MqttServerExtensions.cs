using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MQTTnet.AspNetCore;
using MQTTnet.Protocol;
using MQTTnet.Server;
using Serilog;

namespace GCP.Eventbus.Extensions
{
    public static class MqttServerExtensions
    {
        public static void ConfiguredMqttServer(this WebApplicationBuilder builder)
        {
            var mqttSettings = builder.Configuration.GetSection("mqttSettings").Get<MqttSettings>() ?? new MqttSettings();

            builder.Services.AddSingleton(mqttSettings);

            // 注册 MQTT 服务
            builder.WebHost.ConfigureKestrel(options =>
            {
                if (mqttSettings.Enable)
                {
                    options.ListenAnyIP(mqttSettings.Port, listenOptions =>
                    {
                        listenOptions.UseMqtt();
                    });
                }
            });
        }

        public static void AddMqttServices(this IServiceCollection services)
        {
            // 注册 MQTT 服务
            services.AddHostedMqttServer(mqttServer => mqttServer.WithoutDefaultEndpoint())
                .AddMqttConnectionHandler()
                .AddConnections();
            services.AddSingleton<MqttController>();
        }

        public static void UseMyMqttServer(this WebApplication app)
        {
            app.MapConnectionHandler<MqttConnectionHandler>(
                "/mqtt",
                httpConnectionDispatcherOptions =>
                {
                    httpConnectionDispatcherOptions.WebSockets.SubProtocolSelector =
                        protocolList => protocolList.FirstOrDefault() ?? string.Empty;
                });

            app.UseMqttServer(server =>
            {
                var mqttController = app.Services.GetRequiredService<MqttController>();
                server.ValidatingConnectionAsync += mqttController.ValidateConnection;
                server.ClientConnectedAsync += mqttController.OnClientConnected;
                server.ClientDisconnectedAsync += args =>
                {
                    Log.Information("MQTT 客户端 {ArgsClientId} 已断开连接。", args.ClientId);
                    return Task.CompletedTask;
                };
            });
        }
    }

    public sealed class MqttSettings
    {
        public bool Enable { get; set; } = true;
        public int Port { get; set; } = 1883;
        public string Username { get; set; }
        public string Password { get; set; }
    }

    sealed class MqttController(MqttSettings settings)
    {
        public Task OnClientConnected(ClientConnectedEventArgs eventArgs)
        {
            Log.Information("MQTT 客户端 {EventArgsClientId} 已连接。", eventArgs.ClientId);
            return Task.CompletedTask;
        }

        public Task ValidateConnection(ValidatingConnectionEventArgs eventArgs)
        {
            if (!string.IsNullOrEmpty(settings.Username))
            {
                if (eventArgs.UserName != settings.Username || eventArgs.Password != settings.Password)
                {
                    eventArgs.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
                    Log.Warning("MQTT 客户端 {EventArgsClientId} 因凭据无效而连接失败。", eventArgs.ClientId);
                }
                return Task.CompletedTask;
            }

            //Log.Information("MQTT 客户端 {EventArgsClientId} 正在连接。", eventArgs.ClientId);
            return Task.CompletedTask;
        }
    }
}