# 根节点绑定规范

## 概述

为了简化动作组件的输出结构，避免不必要的嵌套层级，我们引入了根节点绑定机制。当动作只有一个输出结果时，直接绑定到根节点，而不是创建额外的子节点。

## 根节点规范

### 1. 固定名称
- **根节点名称固定为 "ROOT"**
- **不可修改，避免造成歧义**
- 使用常量 `ROOT_NODE_KEY` 来引用，避免硬编码

### 2. 节点结构
```typescript
{
  id: 'ROOT',
  key: 'ROOT',
  description: '描述信息',
  type: 'object' | 'array' | 'string' | 'number' | 'boolean',
  value: {
    type: 'variable',
    variableType: 'current',
    variableValue: 'result',
  },
  children?: FlowData[]
}
```

### 3. 使用场景
根节点绑定适用于以下情况：
- 动作只有一个主要输出结果
- 不需要复杂的嵌套结构
- 希望简化变量访问路径

## 实现指南

### 1. 使用工具函数
```typescript
import { createObjectRootNode, createArrayRootNode, ROOT_NODE_KEY } from '@/components/action-panel/utils/rootNodeHelper';

// 创建对象类型根节点
const rootNode = createObjectRootNode('请求结果');

// 创建数组类型根节点
const rootNode = createArrayRootNode('查询结果');
```

### 2. 动作组件实现
在动作组件的 `setArgs` 方法中：
```typescript
setArgs(args: ArgsInfo) {
  const actionFlowStore = useActionFlowStore();
  actionFlowStore.setCurrentName(args.name);
  actionFlowStore.setCurrentArgs(args);

  // 创建根节点绑定
  const rootNode = createObjectRootNode('结果描述');
  actionFlowStore.setStepResultData([rootNode]);
}
```

### 3. 模板配置
在动作组件的模板中，设置 `showRootNode="false"`：
```vue
<variable-list :data="currentStep.result" :show-root-node="false"></variable-list>
```

## 已适配的动作组件

### 1. 数据查询动作 (`1_DataBase/1_DataQuery`)
- **非分页情况**：直接绑定到 ROOT 节点，类型为 array
- **分页情况**：保持原有的 list 变量逻辑

### 2. 数据保存动作 (`1_DataBase/2_DataSave`)
- 直接绑定到 ROOT 节点，类型为 array
- 包含保存结果的详细信息

### 3. API 请求动作 (`2_Network/1_ApiRequest`)
- 直接绑定到 ROOT 节点，类型为 object
- 包含请求响应结果

### 4. 分支控制动作 (`4_Control/1_Branch`)
- 直接绑定到 ROOT 节点，类型为 boolean
- 包含判断结果

## 优势

### 1. 简化数据结构
```javascript
// 修改前
{
  "response": {
    "data": "实际结果"
  }
}

// 修改后
{
  "ROOT": "实际结果"
}
```

### 2. 减少嵌套层级
- 原来：`response.data`
- 现在：`ROOT`

### 3. 统一命名规范
- 所有根节点都使用 "ROOT" 名称
- 避免不同动作使用不同的根节点名称造成混淆

## 注意事项

### 1. 向后兼容
- 现有的动作组件仍然可以正常工作
- 只有明确修改的组件才会使用根节点绑定

### 2. 不适用场景
以下情况不建议使用根节点绑定：
- 动作有多个输出结果
- 需要复杂的嵌套结构
- 控制流动作（如循环、分支）的内部变量

### 3. 命名约定
- 始终使用 `ROOT_NODE_KEY` 常量
- 不要硬编码 "ROOT" 字符串
- 根节点描述应该清晰说明结果内容

## 工具函数参考

### createRootNode(type, description, children?)
创建通用根节点

### createObjectRootNode(description?, children?)
创建对象类型根节点

### createArrayRootNode(description?, children?)
创建数组类型根节点

### createStringRootNode(description?)
创建字符串类型根节点

### createNumberRootNode(description?)
创建数字类型根节点

### createBooleanRootNode(description?)
创建布尔类型根节点

### shouldUseRootNodeBinding(outputCount, hasComplexStructure?)
判断是否应该使用根节点绑定

### setRootNodeResult(actionFlowStore, rootNode)
设置根节点结果数据
