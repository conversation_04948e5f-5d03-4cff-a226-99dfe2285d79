﻿using System.Data;
using System.Data.Common;

namespace GCP.DataAccess
{
    public partial class DbContext : IDbContext
    {
        public string ConnectionString { get; private set; }
        public IDbProvider DbProvider { get; private set; }
        private DbProviderFactory factory { get; set; }
        public int CommandTimeout { get; set; }

        public DbContext()
        {
            this.SetDefaultConnection();
        }

        //public DbContext(string connectionStringName) : this() {
        //    this.SetDefaultConnection(connectionStringName);
        //}

        public DbContext(string connectionString, DbProviderType dbProviderType)
        {
            this.SetDefaultConnection(connectionString, dbProviderType);
        }

        public DbContext SetDefaultConnection()
        {
            return this.SetDefaultConnection(DbSettings.DefaultConnectionString, DbProviderInfo.GetDbProvider(DbSettings.DefaultDbProvider));
        }


        public DbContext SetDefaultConnection(string connectionString, DbProviderType dbProviderType)
        {
            return this.SetDefaultConnection(connectionString, DbProviderInfo.GetDbProvider(dbProviderType));
        }

        public DbContext SetDefaultConnection(string connectionString, string dbProvider)
        {
            return this.SetDefaultConnection(connectionString, DbProviderInfo.GetDbProvider(dbProvider));
        }

        public DbContext SetDefaultConnection(string connectionString, IDbProvider dbProvider)
        {
            this.ConnectionString = connectionString;
            this.DbProvider = dbProvider;
            this.factory = DbFactory.GetFactory(this.DbProvider.ProviderName);
            this.CommandTimeout = DbSettings.CommandTimeout;
            return this;
        }

        public DbConnection CreateConnection(string connectionString = null)
        {
            return this.factory.CreateConnection(connectionString ?? this.ConnectionString);
        }

        public DbConnection CreateOpenConnection(string connectionString = null)
        {
            var conn = this.factory.CreateConnection(connectionString ?? this.ConnectionString);
            conn.Open();
            return conn;
        }

        public async Task<DbConnection> CreateOpenConnectionAsync(string connectionString = null)
        {
            var conn = this.factory.CreateConnection(connectionString ?? this.ConnectionString);
            await conn.OpenAsync();
            return conn;
        }

        /// <summary>
        /// 使用数据库事务, 如果为空则自动创建
        /// </summary>
        /// <param name="tran"></param>
        /// <param name="func"></param>
        public T UseTransaction<T>(IDbTransaction tran, Func<IDbConnection, IDbTransaction, T> func)
        {
            bool notHasTran = tran == null;
            IDbConnection conn = null;
            try
            {
                if (notHasTran)
                {
                    conn = CreateConnection();
                    conn.Open();
                    tran = conn.BeginTransaction();
                }
                else
                {
                    conn = tran.Connection;
                }
                var result = func(conn, tran);
                if (notHasTran) tran.Commit();
                return result;
            }
            finally
            {
                if (notHasTran) conn.Close();
            }
        }

        /// <summary>
        /// 使用数据库连接, 如果为空则自动创建
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="func"></param>
        public T UseConnection<T>(IDbConnection connection, Func<IDbConnection, T> func)
        {
            bool notHasConn = connection == null;
            try
            {
                if (notHasConn)
                {
                    connection = CreateConnection();
                    connection.Open();
                }
                return func(connection);
            }
            finally
            {
                if (notHasConn) connection.Close();
            }
        }

        public void RegisterProvider(string name, IDbProvider dbProvider)
        {
            DbProviderInfo.Register(name, dbProvider);
        }

        public DbProviderFactory GetFactory()
        {
            return factory;
        }
    }
}
