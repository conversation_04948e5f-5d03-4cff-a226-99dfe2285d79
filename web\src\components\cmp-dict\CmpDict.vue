<template>
  <div style="width: 100%">
    <t-select v-model="model" v-model:popup-visible="popupVisible" placeholder="继承" clearable>
      <t-option v-for="item in options" :key="item.value" :value="item.value" :label="item.label">
        <slot>
          <t-tooltip placement="left" :content="item.description">
            {{ item.label }}
          </t-tooltip>
        </slot>
      </t-option>
      <template #panelBottomContent>
        <div class="select-panel-footer">
          <t-button variant="text" block @click="onClickAddOrEdit">新增 或 编辑</t-button>
        </div>
      </template>
    </t-select>
    <t-dialog v-model:visible="dialogVisible" width="70%" header="字典维护" @confirm="onConfirmDialog">
      <t-table
        class="small-table"
        row-key="dictCode"
        :columns="tableColumns"
        :data="tableData"
        table-layout="fixed"
        :max-height="500"
        bordered
      >
        <template #bottomContent>
          <t-button style="margin-top: 6px" variant="dashed" block @click="onClickAddRow">新 增</t-button>
        </template>
      </t-table>
    </t-dialog>
  </div>
</template>
<script lang="tsx">
export default {
  name: 'CmpDict',
};
</script>
<script setup lang="tsx">
import { Input, MessagePlugin, TableProps } from 'tdesign-vue-next';
import { computed, defineModel, ref, watch } from 'vue';

import { api, Services } from '@/api/system';

export interface CmpDictProps {
  groupCode: string;
}

const props = withDefaults(defineProps<CmpDictProps>(), {
  groupCode: '',
});

const model = defineModel<string>();

const options = ref([]);

const fetchOptions = async (groupCode) => {
  await api.run(Services.dataDictionaryGetOptionsByGroupCode, { groupCode }).then((data) => {
    options.value = data;
  });
};

const fetchData = async (groupCode) => {
  await api.run(Services.dataDictionaryGetByGroupCode, { groupCode }).then((data) => {
    tableData.value = data;
  });
};

watch(
  () => props.groupCode,
  (val, oldVal) => {
    if (val !== oldVal) fetchOptions(val);
  },
  {
    immediate: true,
  },
);

const popupVisible = ref(false);
const dialogVisible = ref(false);
const onClickAddOrEdit = () => {
  popupVisible.value = false;
  dialogVisible.value = true;
  fetchData(props.groupCode);
};

const tableData = ref([]);
const tableColumns = computed<TableProps['columns']>(() => [
  {
    title: '编码',
    colKey: 'dictCode',
    width: 200,
    edit: {
      component: Input,
      props: {
        clearable: true,
      },
      keepEditMode: true,
      abortEditOnEvent: ['onBlur'],
      onEdited: (context) => {
        tableData.value.splice(context.rowIndex, 1, context.newRowData);
      },
      rules: [
        {
          required: true,
          message: '不能为空',
        },
      ],
    },
  },
  {
    title: '名称',
    colKey: 'dictName',
    width: 200,
    edit: {
      component: Input,
      props: {
        clearable: true,
      },
      keepEditMode: true,
      abortEditOnEvent: ['onBlur'],
      onEdited: (context) => {
        tableData.value.splice(context.rowIndex, 1, context.newRowData);
      },
      rules: [
        {
          required: true,
          message: '不能为空',
        },
      ],
    },
  },
  {
    title: '值',
    colKey: 'dictValue',
    edit: {
      component: Input,
      props: {
        clearable: true,
      },
      keepEditMode: true,
      abortEditOnEvent: ['onBlur'],
      onEdited: (context) => {
        tableData.value.splice(context.rowIndex, 1, context.newRowData);
      },
      rules: [
        {
          required: true,
          message: '不能为空',
        },
      ],
    },
  },
]);

const onClickAddRow = () => {
  const newData = {
    dictCode: '',
    dictName: '',
    dictValue: '',
  };
  tableData.value.push(newData);
};

const onConfirmDialog = () => {
  api
    .run(Services.dataDictionarySaveListByGroup, {
      groupCode: props.groupCode,
      data: tableData.value,
    })
    .then(() => {
      dialogVisible.value = false;
      fetchOptions(props.groupCode);
      MessagePlugin.success('保存成功');
    });
};
</script>
<style lang="less" scoped>
@import '@/style/table.less';
.select-panel-footer {
  border-top: 1px solid var(--td-component-stroke);
  // padding: 6px;
}
</style>
