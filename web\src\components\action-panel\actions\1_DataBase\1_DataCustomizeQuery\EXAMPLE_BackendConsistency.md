# 前后端输出结构一致性示例

## 概述

本文档展示了不同配置下，后端实际返回的数据结构与前端输出参数结构的一致性。

## 场景对比

### 1. 列表类型（非分页）

**前端配置：**
```json
{
  "isPaging": false,
  "outputConfig": { "type": "list" }
}
```

**后端返回：**
```json
[
  { "id": 1, "name": "Alice", "email": "<EMAIL>" },
  { "id": 2, "name": "<PERSON>", "email": "<EMAIL>" }
]
```

**前端输出参数：**
```
result (array)
├── id (int)
├── name (string)
└── email (string)
```

**访问方式：** `result[0].name` → "Alice"

---

### 2. 单条记录类型

**前端配置：**
```json
{
  "isPaging": false,
  "outputConfig": { "type": "single" }
}
```

**后端返回：**
```json
{ "id": 1, "name": "Alice", "email": "<EMAIL>" }
```

**前端输出参数：**
```
result (object)
├── id (int)
├── name (string)
└── email (string)
```

**访问方式：** `result.name` → "Alice"

---

### 3. 字典类型

**前端配置：**
```json
{
  "isPaging": false,
  "outputConfig": {
    "type": "dictionary",
    "dictionaryConfig": {
      "keyColumns": ["id"],
      "valueColumn": "name"
    }
  }
}
```

**后端返回：**
```json
{
  "1": "Alice",
  "2": "Bob"
}
```

**前端输出参数：**
```
result (object)
```

**访问方式：** `result["1"]` → "Alice"

---

### 4. 记录数量类型

**前端配置：**
```json
{
  "isPaging": false,
  "outputConfig": { "type": "count" }
}
```

**后端返回：**
```json
2
```

**前端输出参数：**
```
result (int)
```

**访问方式：** `result` → 2

---

### 5. 分页且有总数（忽略输出配置）

**前端配置：**
```json
{
  "isPaging": true,
  "hasTotal": true,
  "outputConfig": { "type": "count" } // 这个会被忽略！
}
```

**后端返回：**
```json
{
  "total": 100,
  "list": [
    { "id": 1, "name": "Alice", "email": "<EMAIL>" },
    { "id": 2, "name": "Bob", "email": "<EMAIL>" }
  ]
}
```

**前端输出参数：**
```
result (object)
├── total (int)
└── list (array)
    ├── id (int)
    ├── name (string)
    └── email (string)
```

**访问方式：** 
- `result.total` → 100
- `result.list[0].name` → "Alice"

---

### 6. 分页但无总数（遵循输出配置）

**前端配置：**
```json
{
  "isPaging": true,
  "hasTotal": false,
  "outputConfig": { "type": "count" }
}
```

**后端返回：**
```json
2
```

**前端输出参数：**
```
result (int)
```

**访问方式：** `result` → 2

## 后端处理逻辑

### DataQuery.cs 关键代码

```csharp
// 分页且有总数：固定返回结构
if (data.IsPaging && data.HasTotal) {
    var result = await sb.GetPagingDataAsync(pageSize, pageIndex, orderBy);
    return new {
        total = result.Total,
        list = result.List
    };
}

// 非分页：根据输出配置处理
if (data.OutputConfig.Type == OutputType.Single) {
    return await sb.GetAsync(); // 直接返回单个对象
}
else if (data.OutputConfig.Type == OutputType.Count) {
    return await sb.CountAsync(); // 直接返回数字
}
else {
    var result = await sb.GetListAsync();
    return ProcessOutputResult(result, data.OutputConfig);
}
```

### ProcessOutputResult 方法

```csharp
private static object ProcessOutputResult(IList<dynamic> result, OutputConfig outputConfig) {
    switch (outputConfig.Type) {
        case OutputType.Single:
            return result.FirstOrDefault();
        case OutputType.Dictionary:
            return BuildDictionary(result, outputConfig.DictionaryConfig);
        case OutputType.Count:
            return result.Count;
        case OutputType.List:
        default:
            return result;
    }
}
```

## 前端适配逻辑

### Store.ts 关键代码

```typescript
variables() {
    const hasTotal = this.args.hasTotal && this.args.isPaging;
    
    // 分页且有总数：固定结构，忽略输出配置
    if (hasTotal) {
        return [{
            id: 'result',
            type: 'object',
            description: '分页结果',
            children: [
                { id: 'total', type: 'int', description: '总数' },
                { id: 'list', type: 'array', description: '数据清单', children: columnChildren }
            ]
        }];
    }
    
    // 其他情况：根据输出配置生成结构
    const outputType = this.args.outputConfig?.type || 'list';
    switch (outputType) {
        case 'count':
            return [{ id: 'result', type: 'int', description: '记录数量' }];
        case 'single':
            return [{ id: 'result', type: 'object', description: '单条记录', children: columnChildren }];
        case 'dictionary':
            return [{ id: 'result', type: 'object', description: '字典结果' }];
        case 'list':
        default:
            return [{ id: 'result', type: 'array', description: '查询结果', children: columnChildren }];
    }
}
```

## 验证方法

### 单元测试

```typescript
it('should match backend structure for paged output with total', () => {
    const args = {
        isPaging: true,
        hasTotal: true,
        outputConfig: { type: 'count' } // 会被忽略
    };
    
    const variables = store.variables;
    
    // 验证结构匹配后端返回的 {total: number, list: array}
    expect(variables[0].type).toBe('object');
    expect(variables[0].children).toHaveLength(2);
    expect(variables[0].children.find(c => c.id === 'total')).toBeDefined();
    expect(variables[0].children.find(c => c.id === 'list')).toBeDefined();
});
```

### 集成测试

1. **后端测试**：验证不同配置下的实际返回结构
2. **前端测试**：验证输出参数结构与后端一致
3. **端到端测试**：验证完整的数据流转

## 总结

通过确保前端输出参数结构与后端返回结构的完全一致，我们实现了：

1. **类型安全**：前端能正确预测后端返回的数据类型
2. **开发体验**：用户在配置后续步骤时能获得准确的字段提示
3. **运行时稳定**：避免因结构不匹配导致的运行时错误
4. **维护性**：前后端逻辑保持同步，便于维护和扩展
