import { api, Services } from '@/api/system';
import { updateIntelliSenseData } from '@/components/editor/scriptCompletion';

// 函数参数接口
export interface FunctionParameter {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  description?: string;
}

// 函数使用示例接口
export interface FunctionExample {
  title: string;
  code: string;
  result?: string;
  description?: string;
}

// 函数数据接口定义
export interface FunctionItem {
  value: string;
  label: string;
  script: string;
  remark: string;
  category?: string;
  categoryDisplayName?: string;
  returnType?: string;
  outputType?: string;
  parameters?: FunctionParameter[];
  examples?: FunctionExample[];
}

export interface FunctionCategory {
  value: string;
  label: string;
  children: FunctionItem[];
}

// 函数数据管理服务
class FunctionDataService {
  private static instance: FunctionDataService;
  private functionCategories: FunctionCategory[] = [];
  private allFunctions: FunctionItem[] = [];
  private isLoading = false;
  private loadPromise: Promise<FunctionCategory[]> | null = null;

  private constructor() {}

  public static getInstance(): FunctionDataService {
    if (!FunctionDataService.instance) {
      FunctionDataService.instance = new FunctionDataService();
    }
    return FunctionDataService.instance;
  }

  /**
   * 获取按分类分组的函数数据
   */
  public async getFunctionCategories(forceRefresh = false): Promise<FunctionCategory[]> {
    if (!forceRefresh && this.functionCategories.length > 0) {
      return this.functionCategories;
    }

    if (this.isLoading && this.loadPromise) {
      return this.loadPromise;
    }

    this.isLoading = true;
    this.loadPromise = this.loadFunctionData();

    try {
      const result = await this.loadPromise;
      return result;
    } finally {
      this.isLoading = false;
      this.loadPromise = null;
    }
  }

  /**
   * 获取扁平化的所有函数列表
   */
  public async getAllFunctions(forceRefresh = false): Promise<FunctionItem[]> {
    if (!forceRefresh && this.allFunctions.length > 0) {
      return this.allFunctions;
    }

    await this.getFunctionCategories(forceRefresh);
    return this.allFunctions;
  }

  /**
   * 从后端加载函数数据
   */
  private async loadFunctionData(): Promise<FunctionCategory[]> {
    try {
      console.log('开始从后端加载函数数据...');
      const data = await api.run(Services.utilsFunctionGetByCategory);

      if (!data || !Array.isArray(data)) {
        console.warn('后端返回的函数数据格式不正确:', data);
        return [];
      }

      this.functionCategories = data as FunctionCategory[];

      // 提取所有函数到扁平化列表
      this.allFunctions = this.extractAllFunctions(this.functionCategories);

      // 更新智能提示数据
      this.updateIntelliSense();

      console.log(`成功加载 ${this.functionCategories.length} 个函数分类，共 ${this.allFunctions.length} 个函数`);

      return this.functionCategories;
    } catch (error) {
      console.error('加载函数数据失败:', error);
      throw new Error(`加载函数数据失败: ${error.message || error}`);
    }
  }

  /**
   * 从分类数据中提取所有函数
   */
  private extractAllFunctions(categories: FunctionCategory[]): FunctionItem[] {
    const functions: FunctionItem[] = [];

    categories.forEach((category) => {
      if (category.children && Array.isArray(category.children)) {
        category.children.forEach((func) => {
          // 使用后端返回的示例数据，如果没有则生成默认示例
          const examples =
            func.examples && func.examples.length > 0 ? func.examples : this.generateFunctionExamples(func);

          functions.push({
            ...func,
            category: category.value,
            categoryDisplayName: category.label,
            examples,
          });
        });
      }
    });

    return functions;
  }

  /**
   * 生成函数使用示例
   */
  private generateFunctionExamples(func: FunctionItem): FunctionExample[] {
    const examples: FunctionExample[] = [];

    // 基本用法示例
    examples.push({
      title: '基本用法',
      code: func.script || `Utils.${func.value}(...)`,
    });

    // 根据函数类型生成特定示例
    if (func.parameters && func.parameters.length > 0) {
      const exampleParams = func.parameters.map((param) => {
        switch (param.type.toLowerCase()) {
          case 'string':
            return param.name === 'format' ? '"yyyy-MM-dd HH:mm:ss"' : `"${param.name}示例"`;
          case 'number':
          case 'decimal':
          case 'int32':
            return '123';
          case 'boolean':
          case 'bool':
            return 'true';
          case 'datetime':
          case 'date':
            return 'new Date()';
          case 'array':
            return '[1, 2, 3]';
          case 'object':
            return '{ key: "value" }';
          default:
            return param.defaultValue !== undefined
              ? typeof param.defaultValue === 'string'
                ? `"${param.defaultValue}"`
                : param.defaultValue
              : `${param.name}`;
        }
      });

      examples.push({
        title: '详细示例',
        code: `Utils.${func.value}(${exampleParams.join(', ')})`,
      });
    }

    return examples;
  }

  /**
   * 更新智能提示数据
   */
  private updateIntelliSense(): void {
    try {
      // 转换为智能提示需要的格式，包含完整的函数信息
      const intelliSenseFunctions = this.allFunctions.map((func) => ({
        value: func.value,
        label: func.label,
        script: func.script,
        remark: func.remark,
        category: func.category,
        categoryDisplayName: func.categoryDisplayName,
        parameters: func.parameters,
        returnType: func.returnType,
        outputType: func.outputType,
        examples: func.examples?.map((example) => ({
          title: example.title,
          code: example.code,
          result: example.result,
          description: example.description || '', // 确保description不为undefined
        })),
      }));

      // 更新智能提示
      updateIntelliSenseData({ functions: intelliSenseFunctions });

      console.log(`已更新智能提示，包含 ${intelliSenseFunctions.length} 个函数`);
      console.log(
        '函数详细信息:',
        intelliSenseFunctions.map((f) => ({
          value: f.value,
          label: f.label,
          parametersCount: f.parameters?.length || 0,
          returnType: f.returnType,
          examplesCount: f.examples?.length || 0,
        })),
      );
    } catch (error) {
      console.error('更新智能提示失败:', error);
    }
  }

  /**
   * 根据关键词搜索函数
   */
  public async searchFunctions(keyword: string): Promise<FunctionItem[]> {
    const allFunctions = await this.getAllFunctions();

    if (!keyword) {
      return allFunctions;
    }

    const lowerKeyword = keyword.toLowerCase();
    return allFunctions.filter(
      (func) =>
        func.label.toLowerCase().includes(lowerKeyword) ||
        func.value.toLowerCase().includes(lowerKeyword) ||
        func.remark.toLowerCase().includes(lowerKeyword),
    );
  }

  /**
   * 根据分类获取函数
   */
  public async getFunctionsByCategory(categoryValue: string): Promise<FunctionItem[]> {
    const categories = await this.getFunctionCategories();
    const category = categories.find((cat) => cat.value === categoryValue);
    return category?.children || [];
  }

  /**
   * 清除缓存，强制重新加载
   */
  public clearCache(): void {
    this.functionCategories = [];
    this.allFunctions = [];
    console.log('函数数据缓存已清除');
  }

  /**
   * 获取加载状态
   */
  public getLoadingState(): boolean {
    return this.isLoading;
  }
}

// 导出单例实例
export const functionDataService = FunctionDataService.getInstance();

// 导出便捷方法
export const getFunctionCategories = (forceRefresh = false) => functionDataService.getFunctionCategories(forceRefresh);

export const getAllFunctions = (forceRefresh = false) => functionDataService.getAllFunctions(forceRefresh);

export const searchFunctions = (keyword: string) => functionDataService.searchFunctions(keyword);

export const getFunctionsByCategory = (categoryValue: string) =>
  functionDataService.getFunctionsByCategory(categoryValue);

export const clearFunctionCache = () => functionDataService.clearCache();
