﻿using GCP.Common;
using Serilog;
using System.Text;

namespace GCP.FunctionPool
{
    class BasicAuthMiddleware : IFunctionMiddleware
    {
        internal static async Task Handler(FunctionContext ctx, Func<Task> next)
        {
            var req = ctx.GetHttpRequest();
            var auth = req.Headers.Authorization;

            if (auth.Count == 0)
            {
                throw new CustomException("无权限访问", 401);
            }

            //Log.Debug("Basic Authorization: {auth}", auth[0]);
            var base64Info = auth[0].Substring(6);
            var authArr = Encoding.UTF8.GetString(Convert.FromBase64String(base64Info)).Split(":");
            var username = authArr[0];
            var pwd = authArr[1];

            Log.Information("ctx.globalData: {globalData}", ctx.globalData);
            dynamic globeBasicAuth = ctx.globalData["globeBasicAuth"];

            if (globeBasicAuth != null)
            {
                if (username == globeBasicAuth[0].ToString() && pwd == globeBasicAuth[1].ToString())
                {
                    await next();
                }
                else
                {
                    throw new CustomException("用户名或密码不存在", 401);
                }
            }
        }
    }
}
