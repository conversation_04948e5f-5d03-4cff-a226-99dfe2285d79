// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 数据源
	/// </summary>
	[Table("lc_data_source")]
	public class LcDataSource : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"               , CanBeNull = false, IsPrimaryKey = true)] public string    Id               { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                              )] public DateTime  TimeCreate       { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"          , CanBeNull = false                     )] public string    Creator          { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                            )] public DateTime? TimeModified     { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                                 )] public string?   Modifier         { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                    )] public short     State            { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"      , CanBeNull = false                     )] public string    SolutionId       { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"       , CanBeNull = false                     )] public string    ProjectId        { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:数据源名称
		/// </summary>
		[Column("DATA_SOURCE_NAME" , CanBeNull = false                     )] public string    DataSourceName   { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:数据提供者 Oracle、SqlServer、MySql、PostgreSQL、Doris
		/// </summary>
		[Column("DATA_PROVIDER"    , CanBeNull = false                     )] public string    DataProvider     { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:根据特定方式连接 HOST:地址用户密码 URL:连接串
		/// </summary>
		[Column("CONNECT_BY"       , CanBeNull = false                     )] public string    ConnectBy        { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:数据连接串
		/// </summary>
		[Column("CONNECTION_STRING"                                        )] public string?   ConnectionString { get; set; } // varchar(500)
		/// <summary>
		/// Description:服务器地址
		/// </summary>
		[Column("SERVER_ADDRESS"                                           )] public string?   ServerAddress    { get; set; } // varchar(50)
		/// <summary>
		/// Description:端口
		/// </summary>
		[Column("PORT"                                                     )] public short?    Port             { get; set; } // smallint
		/// <summary>
		/// Description:数据库
		/// </summary>
		[Column("DATABASE"                                                 )] public string?   Database         { get; set; } // varchar(50)
		/// <summary>
		/// Description:用户名
		/// </summary>
		[Column("USER_ID"                                                  )] public string?   UserId           { get; set; } // varchar(50)
		/// <summary>
		/// Description:密码
		/// </summary>
		[Column("PASSWORD"                                                 )] public string?   Password         { get; set; } // varchar(255)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                              )] public string?   Description      { get; set; } // varchar(200)
		/// <summary>
		/// Description:分组
		/// </summary>
		[Column("GROUP"                                                    )] public string?   Group            { get; set; } // varchar(50)
	}
}
