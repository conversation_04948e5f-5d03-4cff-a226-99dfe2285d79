// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 数据字典
	/// </summary>
	[Table("lc_data_dict")]
	public class LcDataDict : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"           , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                          )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"      , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                        )] public DateTime? TimeModified { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                             )] public string?   Modifier     { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                )] public short     State        { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"  , CanBeNull = false                     )] public string    SolutionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"   , CanBeNull = false                     )] public string    ProjectId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:目录编码
		/// </summary>
		[Column("DIR_CODE"                                             )] public string?   DirCode      { get; set; } // varchar(50)
		/// <summary>
		/// Description:分组编码
		/// </summary>
		[Column("GROUP_CODE"                                           )] public string?   GroupCode    { get; set; } // varchar(50)
		/// <summary>
		/// Description:字典排序
		/// </summary>
		[Column("SEQ"                                                  )] public short     Seq          { get; set; } // smallint
		/// <summary>
		/// Description:字典编码
		/// </summary>
		[Column("DICT_CODE"    , CanBeNull = false                     )] public string    DictCode     { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:字典名称
		/// </summary>
		[Column("DICT_NAME"                                            )] public string?   DictName     { get; set; } // varchar(100)
		/// <summary>
		/// Description:字典值
		/// </summary>
		[Column("DICT_VALUE"                                           )] public string?   DictValue    { get; set; } // varchar(2000)
		/// <summary>
		/// Description:字典类型
		/// </summary>
		[Column("DICT_TYPE"                                            )] public string?   DictType     { get; set; } // varchar(100)
		/// <summary>
		/// Description:是否默认 Y:是
		/// </summary>
		[Column("IS_DEFAULT"                                           )] public char?     IsDefault    { get; set; } // char(1)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                          )] public string?   Description  { get; set; } // varchar(200)
	}
}
