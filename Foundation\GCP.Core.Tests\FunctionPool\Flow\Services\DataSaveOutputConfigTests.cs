using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.FunctionPool.Flow.Services;
using GCP.Functions.Common;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace GCP.Core.Tests.FunctionPool.Flow.Services
{
    public class DataSaveOutputConfigTests : IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly DbContext _dbContext;
        private readonly FunctionContext _functionContext;

        public DataSaveOutputConfigTests()
        {
            var services = new ServiceCollection();
            
            // 配置SQLite数据库
            services.AddSingleton<IDbConfigListener>(provider => new TestDbConfigListener());
            services.AddScoped<DbContext>();
            services.AddScoped<FunctionContext>();
            
            _serviceProvider = services.BuildServiceProvider();
            _dbContext = _serviceProvider.GetRequiredService<DbContext>();
            _functionContext = _serviceProvider.GetRequiredService<FunctionContext>();
            
            // 初始化测试数据库
            InitializeTestDatabase();
        }

        private void InitializeTestDatabase()
        {
            // 创建测试表
            _dbContext.Execute(@"
                CREATE TABLE IF NOT EXISTS test_save_output (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    email TEXT,
                    age INTEGER,
                    department TEXT
                )");

            // 清空表数据
            _dbContext.Execute("DELETE FROM test_save_output");
        }

        [Fact]
        public async Task SaveData_WithNoneOutputType_ShouldReturnEmptyList()
        {
            // Arrange
            var testData = new List<Dictionary<string, object>>
            {
                new() { ["name"] = "Alice", ["email"] = "<EMAIL>", ["age"] = 25, ["department"] = "IT" },
                new() { ["name"] = "Bob", ["email"] = "<EMAIL>", ["age"] = 30, ["department"] = "HR" }
            };

            _functionContext.SetVariable("testData", testData);

            var dataSaveData = new DataSaveData
            {
                Name = "测试保存-无输出",
                DataSource = "test",
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "testData" },
                OperateType = DataSaveOperationType.Insert,
                OutputConfig = new DataSaveOutputConfig
                {
                    Type = DataSaveOutputType.None
                },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "test_save_output",
                    Columns = new List<ColumnInfo>
                    {
                        new() { ColumnName = "id", DataType = "int", IsPrimaryKey = true, IsCondition = true },
                        new() { ColumnName = "name", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" } },
                        new() { ColumnName = "email", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.email" } },
                        new() { ColumnName = "age", DataType = "int", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.age" } },
                        new() { ColumnName = "department", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.department" } }
                    }
                }
            };

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            // Act
            var result = await dataSaveService.SaveData(dataSaveData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<Dictionary<string, object>>>(result);
            var resultList = (List<Dictionary<string, object>>)result;
            Assert.Empty(resultList);

            // 验证数据确实被保存到数据库
            var savedRecords = _dbContext.Query<dynamic>("SELECT COUNT(*) as count FROM test_save_output");
            Assert.Equal(2, savedRecords.First().count);
        }

        [Fact]
        public async Task SaveData_WithListOutputType_ShouldReturnList()
        {
            // Arrange
            var testData = new List<Dictionary<string, object>>
            {
                new() { ["name"] = "Charlie", ["email"] = "<EMAIL>", ["age"] = 35, ["department"] = "Finance" }
            };

            _functionContext.SetVariable("testData", testData);

            var dataSaveData = new DataSaveData
            {
                Name = "测试保存-列表输出",
                DataSource = "test",
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "testData" },
                OperateType = DataSaveOperationType.Insert,
                OutputConfig = new DataSaveOutputConfig
                {
                    Type = DataSaveOutputType.List
                },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "test_save_output",
                    Columns = new List<ColumnInfo>
                    {
                        new() { ColumnName = "id", DataType = "int", IsPrimaryKey = true, IsCondition = true },
                        new() { ColumnName = "name", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" } },
                        new() { ColumnName = "email", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.email" } },
                        new() { ColumnName = "age", DataType = "int", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.age" } },
                        new() { ColumnName = "department", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.department" } }
                    }
                }
            };

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            // Act
            var result = await dataSaveService.SaveData(dataSaveData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<Dictionary<string, object>>>(result);
            var resultList = (List<Dictionary<string, object>>)result;
            Assert.Single(resultList);
            
            var firstItem = resultList.First();
            Assert.Equal("Charlie", firstItem["name"]);
            Assert.Equal("<EMAIL>", firstItem["email"]);
            Assert.Equal(35, firstItem["age"]);
            Assert.Equal("Finance", firstItem["department"]);
        }

        [Fact]
        public async Task SaveData_WithDictionaryOutputType_ShouldReturnDictionary()
        {
            // Arrange
            var testData = new List<Dictionary<string, object>>
            {
                new() { ["name"] = "David", ["email"] = "<EMAIL>", ["age"] = 28, ["department"] = "IT" },
                new() { ["name"] = "Eve", ["email"] = "<EMAIL>", ["age"] = 32, ["department"] = "HR" }
            };

            _functionContext.SetVariable("testData", testData);

            var dataSaveData = new DataSaveData
            {
                Name = "测试保存-字典输出",
                DataSource = "test",
                SourceDataPath = new DataValue { Type = "variable", VariableValue = "testData" },
                OperateType = DataSaveOperationType.Insert,
                OutputConfig = new DataSaveOutputConfig
                {
                    Type = DataSaveOutputType.Dictionary,
                    DictionaryConfig = new DataSaveDictionaryConfig
                    {
                        KeyColumns = new List<string> { "name", "department" },
                        KeySeparator = "|",
                        ValueColumn = "email",
                        UseFullObjectAsValue = false
                    }
                },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "test_save_output",
                    Columns = new List<ColumnInfo>
                    {
                        new() { ColumnName = "id", DataType = "int", IsPrimaryKey = true, IsCondition = true },
                        new() { ColumnName = "name", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.name" } },
                        new() { ColumnName = "email", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.email" } },
                        new() { ColumnName = "age", DataType = "int", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.age" } },
                        new() { ColumnName = "department", DataType = "string", IsInsert = true, ColumnValue = new DataValue { Type = "variable", VariableValue = "item.department" } }
                    }
                }
            };

            var dataSaveService = new DataSave
            {
                Context = _functionContext
            };

            // Act
            var result = await dataSaveService.SaveData(dataSaveData);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<Dictionary<string, object>>(result);
            var resultDict = (Dictionary<string, object>)result;
            
            Assert.Equal(2, resultDict.Count);
            Assert.True(resultDict.ContainsKey("David|IT"));
            Assert.True(resultDict.ContainsKey("Eve|HR"));
            Assert.Equal("<EMAIL>", resultDict["David|IT"]);
            Assert.Equal("<EMAIL>", resultDict["Eve|HR"]);
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
            _serviceProvider?.Dispose();
            
            // 清理测试数据库文件
            if (File.Exists("test_datasave_output.db"))
            {
                File.Delete("test_datasave_output.db");
            }
        }
    }

    public class TestDbConfigListener : IDbConfigListener
    {
        public DbConfig GetDbConfig(string dataSourceId)
        {
            return new DbConfig
            {
                ConnectionString = "Data Source=test_datasave_output.db",
                DbProvider = DbProvider.SQLite
            };
        }
    }
}
