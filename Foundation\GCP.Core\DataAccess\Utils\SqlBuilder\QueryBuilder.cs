﻿using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using GCP.Common;

namespace GCP.DataAccess
{
    public enum DbOrderByType
    {
        Asc = 0,
        Desc = 1
    }

    public enum DbJoinType
    {
        InnerJoin = 0,
        LeftJoin = 1,
        RightJoin = 2,
        FullJoin = 3
    }

    public class SetColunms : List<bool>
    {
        public SetColunms(params bool[] collection)
        {
            this.AddRange(collection);
        }
    }

    public class ExecuteSqlBuilder
    {
        public static ISqlBuilder<T> Update<T>(DbConnection connection, Expression<Func<T, SetColunms>> set, Expression<Func<T, bool>> where, string tableName = null)
        {
            var sql = new SqlBuilder<T>(connection);

            sql.sqlBuilder.Append("UPDATE ");
            sql.sqlBuilder.Append(tableName ?? DbHelper.GetTableName<T>());
            sql.sqlBuilder.Append(" SET");

            ConditionEvaluator colBuilder = new ConditionEvaluator(set, false, false, false);
            sql.Append(colBuilder.Conditions.Join(", "), colBuilder.Arguments.ToArray());

            ConditionEvaluator whereBuilder = new ConditionEvaluator(where, false, false);
            sql.sqlBuilder.Append(" WHERE");
            sql.Append(whereBuilder.Condition, whereBuilder.Arguments.ToArray());

            return sql;
        }

        public static ISqlBuilder<T> Update<T>(DbConnection connection, object setObj, Expression<Func<T, bool>> where, string tableName = null, object ignoreSetColumns = null)
        {
            var hasIgnoreSetColumns = ignoreSetColumns != null;
            var ignoreSetColumnNames = new List<string>();
            if (hasIgnoreSetColumns)
            {
                ignoreSetColumnNames = EntityCache.GetFields(ignoreSetColumns.GetType()).Select(t => t.Key).ToList();
            }

            var sql = new SqlBuilder<T>(connection);

            sql.sqlBuilder.Append("UPDATE ");
            sql.sqlBuilder.Append(tableName ?? DbHelper.GetTableName<T>());
            sql.sqlBuilder.Append(" SET ");

            var setFields = EntityCache.GetFields(setObj.GetType());
            var dic = new Dictionary<string, object>();
            var provider = connection.GetDbProvider();
            bool isFirst = true;
            var sb = new StringBuilder();
            foreach (var item in setFields)
            {
                if (ignoreSetColumnNames.Contains(item.Key)) continue;

                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sql.sqlBuilder.Append(", ");
                }
                sql.sqlBuilder.Append(item.Key);
                sql.sqlBuilder.Append(" = ");
                sql.sqlBuilder.Append(provider.GetParameterName("v_set_" + item.Key));

                dic.Add("v_set_" + item.Key, item.Value.GetValue(setObj));
            }
            sql.AddParameters(dic);

            ConditionEvaluator whereBuilder = new ConditionEvaluator(where, false, false);
            sql.sqlBuilder.Append(" WHERE");
            sql.Append(whereBuilder.Condition, whereBuilder.Arguments.ToArray());

            return sql;
        }

        public static ISqlBuilder<T> Delete<T>(DbConnection connection, Expression<Func<T, bool>> where, string tableName = null)
        {
            ConditionEvaluator builder = new ConditionEvaluator(where, false, false);
            return new SqlBuilder<T>(connection).Append("DELETE FROM " + (tableName ?? DbHelper.GetTableName<T>()) + " WHERE " + builder.Condition, builder.Arguments.ToArray());
        }
    }

    public class QuerySqlBuilder<T> : SqlBuilderBase, ISqlBuilder<T>
    {
        private Dictionary<string, Type> tableTypes { get; set; }
        private Dictionary<string, ISqlBuilder> tableSql { get; set; }
        private Dictionary<string, TableJoin> tableJoin { get; set; }
        private Dictionary<string, object[]> whereList { get; set; }
        private List<string> groupByList { get; set; }
        private Dictionary<string, object[]> havingList { get; set; }
        private List<string> orderByList { get; set; }
        protected bool IsDistinct { get; set; }

        public QuerySqlBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QuerySqlBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QuerySqlBuilder(SqlSession session) : base(session)
        {
        }

        internal override void Init(DbParameters dbParameters, SqlSession session)
        {
            tableTypes = new Dictionary<string, Type>();
            tableSql = new Dictionary<string, ISqlBuilder>();
            tableJoin = new Dictionary<string, TableJoin>();
            whereList = new Dictionary<string, object[]>();
            groupByList = new List<string>();
            havingList = new Dictionary<string, object[]>();
            orderByList = new List<string>();

            base.Init(dbParameters, session);
        }

        internal override void BeforeGet()
        {
            if (!this.CanExecute)
            {
                this._Select(null);
            }
        }

        /// <summary>
        /// 检查表格别名是否一致
        /// </summary>
        protected void CheckTableAlias(ParameterExpression parameter)
        {
            Type type;
            if (tableTypes.TryGetValue(parameter.Name, out type))
            {
                if (type != parameter.Type)
                {
                    throw new Exception("Please modify to the same table alias!");
                }
            }
            else
            {
                tableTypes.Add(parameter.Name, parameter.Type);
            }
        }

        protected void CheckTableAlias(LambdaExpression lambda)
        {
            foreach (var item in lambda.Parameters)
            {
                CheckTableAlias(item);
            }
        }

        /// <summary>
        /// 根据输入表达式获取当前表类型
        /// </summary>
        protected Dictionary<string, Type> GetTableTypes(LambdaExpression lambda)
        {
            if (lambda == null) return this.tableTypes;
            var dic = new Dictionary<string, Type>();
            foreach (var item in lambda.Parameters)
            {
                CheckTableAlias(item);
                dic.Add(item.Name, item.Type);
            }
            return dic;
        }

        protected void _InitSubQuery(Expression exp)
        {
            if (exp == null) return;
            var lambda = exp as LambdaExpression;
            CheckTableAlias(lambda);
            TableEvaluator builder = new TableEvaluator(lambda, tableTypes, tableSql);
        }

        internal QuerySqlBuilder<T> InitSubQuery(Expression<Func<T, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        protected void _Select(Expression exp)
        {
            var lambda = exp as LambdaExpression;
            var currTableTypes = this.GetTableTypes(lambda);
            var sql = new SqlBuilder<T>(session);

            sql.sqlBuilder.Append("SELECT ");

            if (this.IsDistinct)
            {
                sql.sqlBuilder.Append("DISTINCT ");
            }

            int n = 0;
            if (exp == null || lambda.Body.NodeType == ExpressionType.Parameter)
            {
                if (currTableTypes.Count == 0)
                {
                    currTableTypes.Add("t", typeof(T));
                }
                foreach (var item in currTableTypes)
                {
                    if (n != 0) sql.sqlBuilder.Append(", ");
                    n++;

                    var entity = EntityCache.GetFields(item.Value);
                    sql.sqlBuilder.Append(entity.Join(", ", t => string.Format("{0}.{1}", item.Key, t.Value.Name)));
                }
            }
            else
            {
                ColumnEvaluator builder = new ColumnEvaluator(lambda, currTableTypes);
                for (int i = 0; i < builder.ColumnNameWithAlias.Count; i++)
                {
                    if (i != 0) sql.sqlBuilder.Append(", ");
                    sql.sqlBuilder.Append(builder.ColumnNameWithAlias[i]);
                }
            }

            sql.sqlBuilder.Append(" FROM");

            n = 0;
            foreach (var item in currTableTypes.OrderBy(t => this.tableJoin.ContainsKey(t.Key)))
            {
                string alias = item.Key;
                bool isFirst = n == 0;
                n++;

                bool hasJoin = this.tableJoin.ContainsKey(alias);
                if (hasJoin)
                {
                    var joinObj = this.tableJoin[alias];
                    if (!isFirst)
                    {
                        string joinSql = "";
                        switch (joinObj.JoinType)
                        {
                            case DbJoinType.InnerJoin:
                                joinSql = " INNER JOIN";
                                break;
                            case DbJoinType.LeftJoin:
                                joinSql = " LEFT JOIN";
                                break;
                            case DbJoinType.RightJoin:
                                joinSql = " RIGHT JOIN";
                                break;
                            case DbJoinType.FullJoin:
                                joinSql = " FULL JOIN";
                                break;
                        }
                        sql.sqlBuilder.Append(joinSql);
                    }
                }
                else
                {
                    if (!isFirst)
                    {
                        sql.sqlBuilder.Append(", ");
                    }
                }

                ISqlBuilder tableSql;
                if (this.tableSql.TryGetValue(alias, out tableSql))
                {
                    sql.Append(string.Format("({0}) {1}", tableSql.SqlString, alias)).AddParameters(tableSql.Parameters);
                }
                else
                {
                    sql.Append(string.Format("{0} {1}", item.Value.Name, alias));
                }

                if (hasJoin)
                {
                    sql.sqlBuilder.Append(" ON ");
                    var joinObj = this.tableJoin[alias];
                    sql.Append(joinObj.JoinSql, joinObj.Arguments.ToArray());
                }
            }

            if (whereList.Count != 0)
            {
                sql.sqlBuilder.Append(" WHERE");
                n = 0;
                foreach (var item in whereList)
                {
                    if (n == 0)
                    {
                        sql.Append(item.Key, item.Value);
                    }
                    else
                    {
                        sql.sqlBuilder.Append(" AND (");
                        sql.Append(item.Key, item.Value);
                        sql.sqlBuilder.Append(")");
                    }
                    n++;
                }
            }

            if (groupByList.Count != 0)
            {
                sql.sqlBuilder.Append(" GROUP BY ");
                sql.sqlBuilder.Append(groupByList.Join(", "));
            }

            if (havingList.Count != 0)
            {
                sql.sqlBuilder.Append(" HAVING");
                foreach (var item in havingList)
                {
                    sql.Append(item.Key, item.Value);
                }
            }

            if (orderByList.Count != 0)
            {
                sql.sqlBuilder.Append(" ORDER BY ");
                sql.sqlBuilder.Append(orderByList.Join(", "));
            }

            this.sqlBuilder = sql.sqlBuilder;
            this.dbParameters = sql.dbParameters;
            this.CanExecute = true;
        }

        protected void _Join(Expression exp, DbJoinType joinType)
        {
            var lambda = exp as LambdaExpression;
            CheckTableAlias(lambda);
            TableJoinEvaluator builder = new TableJoinEvaluator(lambda, this.tableTypes, this.tableJoin, joinType);
        }

        protected void _Where(Expression exp)
        {
            var lambda = exp as LambdaExpression;
            CheckTableAlias(lambda);
            ConditionEvaluator builder = new ConditionEvaluator(lambda, this.isFilter);
            if (builder.Condition != null)
                whereList.Add(builder.Condition, builder.Arguments.ToArray());
        }

        protected void _GroupBy(Expression exp)
        {
            var lambda = exp as LambdaExpression;
            CheckTableAlias(lambda);
            ColumnEvaluator builder = new ColumnEvaluator(lambda);
            groupByList.AddRange(builder.ColumnNames);
        }

        protected void _Having(Expression exp)
        {
            var lambda = exp as LambdaExpression;
            CheckTableAlias(lambda);
            ConditionEvaluator builder = new ConditionEvaluator(lambda, false);
            havingList.Add(builder.Condition, builder.Arguments.ToArray());
        }

        protected void _OrderBy(Expression exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            var lambda = exp as LambdaExpression;
            CheckTableAlias(lambda);
            ColumnEvaluator builder = new ColumnEvaluator(lambda);
            orderByList.AddRange(builder.ColumnNames.Select(t => t + (orderByType == DbOrderByType.Asc ? " ASC" : " DESC")));
        }


        public QuerySqlBuilder<T> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        /// <summary>
        /// 剔除重复值
        /// </summary>
        public QuerySqlBuilder<T> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }

        /// <summary>
        /// 表连接(默认内连接)
        /// </summary>
        public QuerySqlBuilder<T> Join(Expression<Func<T, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        /// <summary>
        /// 左连接
        /// </summary>
        public QuerySqlBuilder<T> LeftJoin(Expression<Func<T, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }

        /// <summary>
        /// 查询所有的列
        /// </summary>
        public QuerySqlBuilder<T> Select()
        {
            _Select(null);
            return this;
        }
        /// <summary>
        /// 选择查询的列
        /// </summary>
        public QuerySqlBuilder<T> Select(Expression<Func<T, object>> exp)
        {
            _Select(exp);
            return this;
        }

        /// <summary>
        /// 筛选
        /// </summary>
        public QuerySqlBuilder<T> Where(Expression<Func<T, bool>> exp)
        {
            _Where(exp);
            return this;
        }

        /// <summary>
        /// 分组
        /// </summary>
        public QuerySqlBuilder<T> GroupBy(Expression<Func<T, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }

        /// <summary>
        /// 分组数据加上筛选
        /// </summary>
        public QuerySqlBuilder<T> Having(Expression<Func<T, bool>> exp)
        {
            _Having(exp);
            return this;
        }

        /// <summary>
        /// 排序（默认Asc）
        /// </summary>
        public QuerySqlBuilder<T> OrderBy(Expression<Func<T, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }

    #region 2 table
    public class QueryBuilder<T, T1> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1> InitSubQuery(Expression<Func<T, T1, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1> Select(Expression<Func<T, T1, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1> Join(Expression<Func<T, T1, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1> LeftJoin(Expression<Func<T, T1, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1> Where(Expression<Func<T, T1, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1> GroupBy(Expression<Func<T, T1, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1> Having(Expression<Func<T, T1, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1> OrderBy(Expression<Func<T, T1, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 3 table
    public class QueryBuilder<T, T1, T2> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2> InitSubQuery(Expression<Func<T, T1, T2, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2> Select(Expression<Func<T, T1, T2, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2> Join(Expression<Func<T, T1, T2, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2> LeftJoin(Expression<Func<T, T1, T2, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2> Where(Expression<Func<T, T1, T2, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2> GroupBy(Expression<Func<T, T1, T2, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2> Having(Expression<Func<T, T1, T2, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2> OrderBy(Expression<Func<T, T1, T2, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 4 table
    public class QueryBuilder<T, T1, T2, T3> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2, T3> InitSubQuery(Expression<Func<T, T1, T2, T3, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2, T3> Select(Expression<Func<T, T1, T2, T3, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2, T3> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3> Join(Expression<Func<T, T1, T2, T3, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3> LeftJoin(Expression<Func<T, T1, T2, T3, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3> Where(Expression<Func<T, T1, T2, T3, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3> GroupBy(Expression<Func<T, T1, T2, T3, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3> Having(Expression<Func<T, T1, T2, T3, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3> OrderBy(Expression<Func<T, T1, T2, T3, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 5 table
    public class QueryBuilder<T, T1, T2, T3, T4> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2, T3, T4> InitSubQuery(Expression<Func<T, T1, T2, T3, T4, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4> Select(Expression<Func<T, T1, T2, T3, T4, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2, T3, T4> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4> Join(Expression<Func<T, T1, T2, T3, T4, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4> LeftJoin(Expression<Func<T, T1, T2, T3, T4, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4> Where(Expression<Func<T, T1, T2, T3, T4, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4> GroupBy(Expression<Func<T, T1, T2, T3, T4, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4> Having(Expression<Func<T, T1, T2, T3, T4, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4> OrderBy(Expression<Func<T, T1, T2, T3, T4, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 6 table
    public class QueryBuilder<T, T1, T2, T3, T4, T5> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2, T3, T4, T5> InitSubQuery(Expression<Func<T, T1, T2, T3, T4, T5, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5> Select(Expression<Func<T, T1, T2, T3, T4, T5, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2, T3, T4, T5> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5> Join(Expression<Func<T, T1, T2, T3, T4, T5, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5> LeftJoin(Expression<Func<T, T1, T2, T3, T4, T5, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5> Where(Expression<Func<T, T1, T2, T3, T4, T5, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5> GroupBy(Expression<Func<T, T1, T2, T3, T4, T5, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5> Having(Expression<Func<T, T1, T2, T3, T4, T5, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5> OrderBy(Expression<Func<T, T1, T2, T3, T4, T5, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 7 table
    public class QueryBuilder<T, T1, T2, T3, T4, T5, T6> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2, T3, T4, T5, T6> InitSubQuery(Expression<Func<T, T1, T2, T3, T4, T5, T6, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6> Select(Expression<Func<T, T1, T2, T3, T4, T5, T6, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6> Join(Expression<Func<T, T1, T2, T3, T4, T5, T6, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6> LeftJoin(Expression<Func<T, T1, T2, T3, T4, T5, T6, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6> Where(Expression<Func<T, T1, T2, T3, T4, T5, T6, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6> GroupBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6> Having(Expression<Func<T, T1, T2, T3, T4, T5, T6, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6> OrderBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 8 table
    public class QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> InitSubQuery(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Select(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Join(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> LeftJoin(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Where(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> GroupBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Having(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> OrderBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 9 table
    public class QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> InitSubQuery(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Select(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Join(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> LeftJoin(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Where(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> GroupBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Having(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> OrderBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion

    #region 10 table
    public class QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> : QuerySqlBuilder<T>
    {
        public QueryBuilder(IDbContext dbContext) : base(dbContext)
        {
        }

        public QueryBuilder(DbConnection connection) : base(connection)
        {
        }

        internal QueryBuilder(SqlSession session) : base(session)
        {
        }

        internal QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> InitSubQuery(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, object>> exp)
        {
            _InitSubQuery(exp);
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Filter(bool filter = true)
        {
            this.isFilter = filter;
            return this;
        }

        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Distinct()
        {
            this.IsDistinct = true;
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Select(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, object>> exp)
        {
            _Select(exp);
            return this;
        }
        public new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Select()
        {
            _Select(null);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Join(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, bool>> exp, DbJoinType joinType = DbJoinType.InnerJoin)
        {
            _Join(exp, joinType);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> LeftJoin(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, bool>> exp)
        {
            _Join(exp, DbJoinType.LeftJoin);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Where(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, bool>> exp)
        {
            _Where(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> GroupBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, object>> exp)
        {
            _GroupBy(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Having(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, bool>> exp)
        {
            _Having(exp);
            return this;
        }
        public QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> OrderBy(Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, object>> exp, DbOrderByType orderByType = DbOrderByType.Asc)
        {
            _OrderBy(exp, orderByType);
            return this;
        }
    }
    #endregion
}
