﻿using Microsoft.Extensions.Configuration;

namespace GCP.Common
{
    public static class SqlLogSettings
    {
        public static bool EnableLocalDatabase { get; set; } = false;
        public static string ConnectionString { get; set; } = "";
        public static int ChannelCapacity { get; set; } = 10000;

        /// <summary>
        /// Job流程日志状态
        /// </summary>
        public static FlowLogStatus JobStatus { get; set; } = new FlowLogStatus();
        /// <summary>
        /// API调用日志状态
        /// </summary>
        public static FlowLogStatus ApiStatus { get; set; } = new FlowLogStatus();
        /// <summary>
        /// 消息日志状态
        /// </summary>
        public static FlowLogStatus MessageStatus { get; set; } = new FlowLogStatus();

        internal static void Load(IConfiguration configuration)
        {
            EnableLocalDatabase = configuration.GetValue<bool>("dbSettings:Logging:EnableLocalDatabase", false);
            ChannelCapacity = configuration.GetValue<int>("dbSettings:Logging:ChannelCapacity", 10000);

            JobStatus.Flow = configuration.GetValue<bool>("dbSettings:Logging:Job:Flow", true);
            JobStatus.Step = configuration.GetValue<bool>("dbSettings:Logging:Job:Step", true);

            ApiStatus.Flow = configuration.GetValue<bool>("dbSettings:Logging:Api:Flow", true);
            ApiStatus.Step = configuration.GetValue<bool>("dbSettings:Logging:Api:Step", true);

            MessageStatus.Flow = configuration.GetValue<bool>("dbSettings:Logging:Message:Flow", true);
            MessageStatus.Step = configuration.GetValue<bool>("dbSettings:Logging:Message:Step", true);
        }
    }

    public class FlowLogStatus
    {
        public bool Flow { get; set; } = true;
        public bool Step { get; set; } = true;
    }

}
