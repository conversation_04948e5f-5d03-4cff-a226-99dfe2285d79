﻿using System.Data;
using System.Data.Common;
using System.Collections.Concurrent;

namespace GCP.DataAccess
{
    internal static class DbFactory {
        static ConcurrentDictionary<DbProviderFactory, IDbProvider> dicDbProvider = 
            new ConcurrentDictionary<DbProviderFactory, IDbProvider>(8, 8);

        static DbFactory()
        {
        }

        internal static DbProviderFactory GetFactory(string providerName) {
            return DbProviderFactories.GetFactory(providerName);
        }

        internal static DbProviderFactory GetFactory(this IDbConnection connection) {
            return DbProviderFactories.GetFactory((DbConnection)connection);
        }

        internal static IDbProvider GetDbProvider(this DbProviderFactory factory) {
            return dicDbProvider.GetOrAdd(factory, f => {
                string typeName = factory.GetType().Namespace;
                foreach (var item in DbProviderInfo._dbProviderMap)
                {
                    if (item.Value.ProviderName == typeName)
                    {
                        return item.Value;
                    }
                }
                return null;
            });
        }

        internal static IDbProvider GetDbProvider(this IDbConnection connection) {
            return connection.GetFactory().GetDbProvider();
        }

        public static DbConnection CreateConnection(this DbProviderFactory factory, string connectionString) {
            var connection = factory.CreateConnection();
            connection.ConnectionString = connectionString;
            return connection;
        }
    }
}
