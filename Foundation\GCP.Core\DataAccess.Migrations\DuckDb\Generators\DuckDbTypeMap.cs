using System.Data;

using FluentMigrator.Runner.Generators.Base;

namespace GCP.Core.DataAccess.Migrations.Generators.DuckDb
{
    internal class DuckDbTypeMap : TypeMapBase, IDuckDbTypeMap
    {
        private const int DuckDbMaxVarcharSize = 65535;
        private const int DuckDbDecimalCapacity = 38;

        public DuckDbTypeMap()
        {
            SetupTypeMaps();
        }

        protected virtual void SetupDuckDbTypeMaps()
        {
            SetTypeMap(DbType.AnsiStringFixedLength, "VARCHAR");
            SetTypeMap(DbType.AnsiStringFixedLength, "VARCHAR", int.MaxValue);
            SetTypeMap(DbType.AnsiString, "VARCHAR");
            SetTypeMap(DbType.AnsiString, "VARCHAR", DuckDbMaxVarcharSize);
            SetTypeMap(DbType.AnsiString, "VARCHAR", int.MaxValue);
            SetTypeMap(DbType.Binary, "BLOB");
            SetTypeMap(DbType.Binary, "BLOB", int.MaxValue);
            SetTypeMap(DbType.Boolean, "BOOLEAN");
            SetTypeMap(DbType.Byte, "UTINYINT");
            SetTypeMap(DbType.SByte, "TINYINT");
            SetTypeMap(DbType.Currency, "DECIMAL");
            SetTypeMap(DbType.Date, "DATE");
            SetTypeMap(DbType.DateTime, "TIMESTAMP");
            SetTypeMap(DbType.DateTime2, "TIMESTAMP");
            SetTypeMap(DbType.DateTimeOffset, "TIMETZ");
            SetTypeMap(DbType.Decimal, "DECIMAL");
            SetTypeMap(DbType.Decimal, "DECIMAL", DuckDbDecimalCapacity);
            SetTypeMap(DbType.Double, "DOUBLE");
            SetTypeMap(DbType.Guid, "UUID");
            SetTypeMap(DbType.Int16, "SMALLINT");
            SetTypeMap(DbType.UInt16, "USMALLINT");
            SetTypeMap(DbType.Int32, "INTEGER");
            SetTypeMap(DbType.UInt32, "UINTEGER");
            SetTypeMap(DbType.Int64, "BIGINT");
            SetTypeMap(DbType.UInt64, "UBIGINT");
            SetTypeMap(DbType.Single, "REAL");
            SetTypeMap(DbType.StringFixedLength, "VARCHAR");
            SetTypeMap(DbType.StringFixedLength, "VARCHAR", int.MaxValue);
            SetTypeMap(DbType.String, "VARCHAR");
            SetTypeMap(DbType.String, "VARCHAR", DuckDbMaxVarcharSize);
            SetTypeMap(DbType.String, "VARCHAR", int.MaxValue);
            SetTypeMap(DbType.Time, "TIME");

            SetTypeMap(DbType.Xml, "VARCHAR");
            SetTypeMap(DbType.Object, "VARCHAR");
        }

        protected sealed override void SetupTypeMaps()
        {
            SetupDuckDbTypeMaps();
        }
    }
}
