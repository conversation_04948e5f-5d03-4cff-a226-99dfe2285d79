﻿using GCP.Common;
using System.Collections.Concurrent;

namespace GCP.FunctionPool.Builder
{
    /// <summary>
    /// 函数解析类型
    /// </summary>
    enum FunctionResolveType
    {
        // 使用程序集, 支持多个class和Attribute
        ASSEMBLY,
        // 纯函数, 无Attribute
        PURE,
        // 中间件函数
        MIDDLEWARE,
        // 工作流, DSL语法树, 结合多个函数一起执行
        FLOW
    }

    /// <summary>
    /// 函数语言
    /// </summary>
    enum FunctionCodeLanguage
    {
        CSharp,
        JavaScript,
        Json
    }

    internal class FunctionCompiler
    {
        internal static ConcurrentDictionary<string, FunctionInfo> DicFunction { get; set; } = new ConcurrentDictionary<string, FunctionInfo>();

        /// <summary>
        /// 编译代码获取可执行函数类
        /// </summary>
        /// <returns></returns>
        internal static void LoadCode(string path, string name, string sourceCode, FunctionResolveType resolverType = FunctionResolveType.PURE, FunctionCodeLanguage codeLanguage = FunctionCodeLanguage.CSharp, Action<FunctionInfo> initFunc = null)
        {
            //if (codeLanguage == FunctionCodeLanguage.CSharp)
            //{
            //    CSharpResolver.Handler(path, name, sourceCode, resolverType, initFunc);
            //}
            if (codeLanguage == FunctionCodeLanguage.JavaScript)
            {
                JavaScriptResolver.Handler(path, name, sourceCode, null, resolverType, initFunc);
            }

            if (resolverType == FunctionResolveType.FLOW)
            {
                FlowResolver.Handler(path, name, sourceCode, null, codeLanguage, initFunc);
            }
        }

        /// <summary>
        /// 卸载可执行函数类
        /// </summary>
        /// <param name="path"></param>
        internal static void Unload(string path)
        {
            FunctionInfo assemblyInfo;
            if (DicFunction.TryGetValue(path, out assemblyInfo))
            {
                if (!assemblyInfo.AllowUnload) throw new CustomException("无法卸载内置函数！");
                assemblyInfo.UnloadFunction();
                DicFunction.TryRemove(path, out _);
            }
        }
    }
}
