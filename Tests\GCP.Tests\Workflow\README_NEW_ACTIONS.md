# 缓存和设备测点动作测试

本文档描述了新添加的缓存和设备测点动作的测试代码。

## 测试文件结构

### 1. CacheActionTests.cs
**缓存动作测试类**

测试覆盖的功能：
- ✅ **缓存写入** (`cacheWrite`)
  - 有效数据写入
  - 带过期时间的写入
  - 永久缓存写入（无过期时间）
  - 空键验证

- ✅ **缓存读取** (`cacheRead`)
  - 读取已存在的缓存
  - 读取不存在的缓存（返回默认值）

- ✅ **缓存删除** (`cacheRemove`)
  - 删除已存在的缓存

- ✅ **缓存存在检查** (`cacheExists`)
  - 检查已存在的缓存
  - 检查不存在的缓存

- ✅ **项目隔离测试**
  - 不同项目的缓存数据隔离
  - ProjectId前缀机制验证

### 2. IotEquipmentActionTests.cs
**设备测点动作测试类**

测试覆盖的功能：
- ✅ **设备测点写入** (`iotVariableWrite`)
  - 有效数据写入
  - 无效设备处理
  - 写入失败处理

- ✅ **设备测点读取** (`iotVariableRead`)
  - 有效数据读取
  - 无效设备处理

- ✅ **设备所有参数读取** (`iotEquipmentRead`)
  - 读取设备所有变量值

- ✅ **批量设备参数读取** (`iotMultiEquipmentRead`)
  - 批量读取多个设备的参数

### 3. CacheAndIotIntegrationTests.cs
**缓存和设备集成测试类**

测试覆盖的功能：
- ✅ **设备数据缓存集成**
  - 读取设备数据并缓存
  - 从缓存读取设备数据

- ✅ **设备配置缓存优化**
  - 缓存设备配置信息
  - 配置存在性检查

- ✅ **项目隔离集成测试**
  - 缓存和设备操作的项目隔离
  - 跨项目数据访问控制

## 测试技术特点

### Mock技术
- 使用 **Moq** 框架模拟设备通信管理器
- 模拟设备通信任务的各种状态
- 模拟成功/失败的设备操作

### 缓存测试
- 使用 **EasyCaching** 内存缓存进行测试
- 验证缓存键的ProjectId前缀机制
- 测试缓存过期时间设置

### 数据库集成
- 继承 `DatabaseTestBase` 获得完整的数据库环境
- 支持SQLite和MySQL测试数据库
- 自动清理测试数据

### 错误处理测试
- 验证各种异常情况的处理
- 测试自定义异常的抛出
- 验证错误日志记录

## 运行测试

### 运行所有新动作测试
```powershell
# 在项目根目录执行
.\Tests\run-new-action-tests.ps1
```

### 运行特定测试类
```bash
# 缓存动作测试
dotnet test --filter "FullyQualifiedName~CacheActionTests"

# 设备测点动作测试  
dotnet test --filter "FullyQualifiedName~IotEquipmentActionTests"

# 集成测试
dotnet test --filter "FullyQualifiedName~CacheAndIotIntegrationTests"
```

### 运行特定测试方法
```bash
# 缓存写入测试
dotnet test --filter "FullyQualifiedName~CacheWrite_WithValidData_ShouldWriteSuccessfully"

# 设备写入测试
dotnet test --filter "FullyQualifiedName~IotVariableWrite_WithValidData_ShouldWriteSuccessfully"
```

## 测试数据准备

测试使用以下测试数据：
- **项目ID**: `test-project-001`, `test-project-002`
- **设备ID**: `device001`, `device002`
- **缓存键**: `test_key_001`, `permanent_key`
- **设备地址**: `D100`, `Temperature`
- **数据类型**: `Int16`, `Float`

## 验证的业务场景

### 1. 多租户缓存隔离
- 不同项目的缓存数据完全隔离
- 缓存键自动添加ProjectId前缀
- 项目间无法访问彼此的缓存数据

### 2. 设备通信容错
- 设备不存在时的错误处理
- 设备通信失败时的异常处理
- 设备数据读取的容错机制

### 3. 缓存性能优化
- 设备配置信息缓存
- 设备数据临时缓存
- 缓存过期时间管理

### 4. 日志记录完整性
- 操作成功的信息日志
- 操作失败的错误日志
- 持久化参数记录

## 测试覆盖率

- **缓存操作**: 100% 覆盖所有4个缓存动作
- **设备操作**: 100% 覆盖所有4个设备动作
- **错误场景**: 覆盖主要异常情况
- **集成场景**: 覆盖缓存与设备的组合使用
- **项目隔离**: 验证多租户数据安全

## 注意事项

1. **Mock限制**: 由于EquipmentCommunicationTask是具体类，Mock可能有限制
2. **数据库依赖**: 测试需要数据库环境支持
3. **缓存清理**: 测试间需要清理缓存状态
4. **异步操作**: 所有设备操作都是异步的，需要正确处理await
