﻿using System.Text;
using System.Text.Json.Serialization;

namespace GCP.Common
{
    public class FunctionProvider
    {
        /// <summary>
        /// 函数路径
        /// </summary>
        public string Path { get; set; }


        /// <summary>
        /// 流程步骤ID
        /// </summary>
        public string StepId { get; set; }
        /// <summary>
        /// 流程步骤名称
        /// </summary>
        public string StepName { get; set; }
        /// <summary>
        /// 函数名称
        /// </summary>
        public string FunctionName { get; set; }
        /// <summary>
        /// 脚本名称
        /// </summary>
        public string ScriptName { get; set; }
        /// <summary>
        /// 是否出错
        /// </summary>
        public bool HasError { get; set; }

        /// <summary>
        /// 流程步骤动作ID
        /// </summary>
        public string ActionId { get; set; }
        /// <summary>
        /// 执行耗时(毫秒)
        /// </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int SeqNo { get; set; }
        /// <summary>
        /// 层级
        /// </summary>
        public int Level { get; set; }
        /// <summary>
        /// 是否为流程
        /// </summary>
        public bool IsFlow { get; set; }

        /// <summary>
        /// 当前执行函数目标, 可用来传值
        /// </summary>
        public object Target { get; set; }

        /// <summary>
        /// 中间件
        /// </summary>
        public string[] Middlewares { get; set; }

        /// <summary>
        /// 当前执行函数相关信息
        /// </summary>
        public IDictionary<string, object> Args { get; set; }
        /// <summary>
        /// 执行结果
        /// </summary>
        public object Result { get; set; }

        /// <summary>
        /// 参数
        /// </summary>
        public StringBuilder ArgsBuilder { get; set; }

        /// <summary>
        /// 父函数上下文
        /// </summary>
        [JsonIgnore]
        internal FunctionProvider Parent { get; set; }

        /// <summary>
        /// 子函数上下文列表
        /// </summary>
        [JsonIgnore]
        internal List<FunctionProvider> Children { get; } = [];

        public FunctionProvider() { }

        public FunctionProvider(string path, object target = null)
        {
            SeqNo = 0;
            Path = path;
            Target = target;
        }

    }
}
