#!/bin/bash

# Shell脚本：自动化部署、备份和重启应用
#
# 功能:
# 1. 备份现有的 'linux-x64' 目录，并自动清理旧备份，仅保留最新的2个。
# 2. 删除旧的 'linux-x64_*.zip' 格式的压缩包。
# 3. 解压作为参数传入的最新压缩包。
# 4. 为解压后的 'linux-x64/GCP.App' 文件赋予执行权限。
# 5. 执行 './restart.sh' 脚本来重启应用。
#
# 用法:
# 将此脚本和新的压缩包放在同一个目录下，然后执行：
# ./deploy.sh <新的压缩包文件名>
# 例如:
# ./deploy.sh linux-x64_250701.zip

# --- 配置 ---
APP_DIR="linux-x64"                     # 应用目录名
BACKUP_PREFIX="linux-x64_backup"        # 备份文件前缀
RESTART_SCRIPT="./restart.sh"           # 重启脚本路径
KEEP_BACKUPS=2                          # 需要保留的备份数量

# --- 脚本开始 ---

# 设置脚本在遇到错误时立即退出
set -e

# 检查用户是否提供了新的压缩包文件名作为参数
if [ -z "$1" ]; then
    echo "错误：请提供新的压缩包文件名作为第一个参数。"
    echo "用法: $0 <package_name.zip>"
    exit 1
fi

NEW_PACKAGE="$1"

echo "----------------------------------------"
echo "部署脚本开始执行..."
echo "----------------------------------------"

# --- 步骤 1: 备份旧的应用目录并清理老旧备份 ---
echo "步骤 1: 检查并备份旧的应用目录..."
if [ -d "$APP_DIR" ]; then
    # 创建带时间戳的备份名
    BACKUP_NAME="${BACKUP_PREFIX}_$(date +%Y%m%d_%H%M%S)"
    echo "发现旧目录 '$APP_DIR'，正在备份为 '$BACKUP_NAME'..."
    # 使用 mv 进行重命名备份，比 cp 更快
    mv "$APP_DIR" "$BACKUP_NAME"
    echo "备份完成。"

    echo "开始清理旧的备份，只保留最新的 $KEEP_BACKUPS 个..."
    # 使用 ls -td 列出所有备份（按时间倒序），
    # 使用 tail 跳过需要保留的备份数量，
    # 剩下的（即更旧的）通过 xargs 删除。
    # -r 选项确保在没有输入时，xargs 不会运行 rm。
    ls -td ${BACKUP_PREFIX}_* | tail -n +$((KEEP_BACKUPS + 1)) | xargs -r rm -rf
    echo "旧备份清理完成。"
else
    echo "未发现旧目录 '$APP_DIR'，跳过备份步骤。"
fi
echo ""

# --- 步骤 2: 删除旧的安装压缩包 ---
echo "步骤 2: 删除旧的安装压缩包..."
# 使用 find 查找所有匹配 'linux-x64_*.zip' 格式的文件，
# 但排除掉我们正要安装的新包，然后删除它们。
# -maxdepth 1 确保只在当前目录查找。
# -print 会打印出被删除的文件名。
find . -maxdepth 1 -name "${APP_DIR}_*.zip" -not -name "$NEW_PACKAGE" -print -delete
echo "旧安装包清理完成。"
echo ""

# --- 步骤 3: 解压新包并赋权 ---
echo "步骤 3: 解压新包并为应用赋权..."
if [ -f "$NEW_PACKAGE" ]; then
    echo "正在解压 '$NEW_PACKAGE'..."
    unzip -q "$NEW_PACKAGE" # -q 选项表示静默模式，减少不必要的输出
    echo "解压完成。"

    APP_EXECUTABLE="./$APP_DIR/GCP.App"
    if [ -f "$APP_EXECUTABLE" ]; then
        echo "为 '$APP_EXECUTABLE' 添加执行权限..."
        chmod +x "$APP_EXECUTABLE"
        echo "赋权完成。"
    else
        echo "错误：解压后未找到应用文件 '$APP_EXECUTABLE'！请检查压缩包内容。"
        exit 1
    fi
else
    echo "错误：找不到新的压缩包 '$NEW_PACKAGE'！"
    exit 1
fi
echo ""

# --- 步骤 4: 运行重启脚本 ---
echo "步骤 4: 执行重启脚本..."
if [ -x "$RESTART_SCRIPT" ]; then
    echo "正在执行 '$RESTART_SCRIPT'..."
    $RESTART_SCRIPT
    echo "重启脚本执行完毕。"
else
    echo "警告：重启脚本 '$RESTART_SCRIPT' 不存在或没有执行权限，已跳过此步骤。"
fi
echo ""

echo "----------------------------------------"
echo "所有操作成功完成！"
echo "----------------------------------------"
