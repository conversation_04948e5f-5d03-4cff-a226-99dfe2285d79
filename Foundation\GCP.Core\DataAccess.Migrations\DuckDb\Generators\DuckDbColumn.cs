using FluentMigrator.Model;
using FluentMigrator.Runner.Generators.Base;

namespace GCP.Core.DataAccess.Migrations.Generators.DuckDb
{
    internal class DuckDbColumn : ColumnBase<IDuckDbTypeMap>
    {
        public DuckDbColumn(DuckDbQuoter quoter, IDuckDbTypeMap typeMap)
            : base(typeMap, quoter)
        {
            AlterClauseOrder = new List<Func<ColumnDefinition, string>> { FormatAlterType, FormatAlterNullable };
        }

        public string FormatAlterDefaultValue(string column, object defaultValue)
        {
            string formatDefaultValue = FormatDefaultValue(new ColumnDefinition { Name = column, DefaultValue = defaultValue });

            return string.Format("SET {0}", formatDefaultValue);
        }

        private string FormatAlterNullable(ColumnDefinition column)
        {
            if (!column.IsNullable.HasValue)
                return "";

            if (column.IsNullable.Value)
                return "DROP NOT NULL";

            return "SET NOT NULL";
        }

        private string FormatAlterType(ColumnDefinition column)
        {
            return $"TYPE {GetColumnType(column)}";
        }

        protected IList<Func<ColumnDefinition, string>> AlterClauseOrder { get; set; }

        public string GenerateAlterClauses(ColumnDefinition column)
        {
            var clauses = new List<string>();
            foreach (var action in AlterClauseOrder)
            {
                string columnClause = action(column);
                if (!string.IsNullOrEmpty(columnClause))
                    clauses.Add(string.Format("ALTER {0} {1}", Quoter.QuoteColumnName(column.Name), columnClause));
            }

            return string.Join(", ", clauses.ToArray());
        }

        /// <inheritdoc />
        protected override string FormatNullable(ColumnDefinition column)
        {
            if (column.IsNullable == true && column.Type == null && !string.IsNullOrEmpty(column.CustomType))
            {
                return "NULL";
            }

            return base.FormatNullable(column);
        }

        /// <inheritdoc />
        protected override string FormatIdentity(ColumnDefinition column)
        {
            return string.Empty;
        }

        /// <inheritdoc />
        public override string AddPrimaryKeyConstraint(string tableName, IEnumerable<ColumnDefinition> primaryKeyColumns)
        {
            var columnDefinitions = primaryKeyColumns.ToList();

            string cols = string.Empty;
            bool first = true;
            foreach (var col in columnDefinitions)
            {
                if (first)
                    first = false;
                else
                    cols += ",";
                cols += Quoter.QuoteColumnName(col.Name);
            }

            return string.Format(", PRIMARY KEY ({0})", cols);
        }

        /// <inheritdoc />
        protected override string FormatType(ColumnDefinition column)
        {
            return ColumnBaseFormatType(column);
        }

        protected string ColumnBaseFormatType(ColumnDefinition column)
        {
            return base.FormatType(column);
        }

        public string GetColumnType(ColumnDefinition column)
        {
            return FormatType(column);
        }
    }
}
