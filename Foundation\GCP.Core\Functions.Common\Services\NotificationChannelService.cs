using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using GCP.Models;
using LinqToDB;
using System.Text.Json;

namespace GCP.Functions.Common.Services
{
    [Function("notificationChannel", "通知通道服务")]
    internal class NotificationChannelService : BaseService
    {
        [Function("getAll", "获取通知通道列表")]
        public PagingData<LcNotificationChannel> GetAll(string keyword = null, string channelType = null, 
            int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var query = from c in db.LcNotificationChannels
                        where c.State == 1 &&
                        c.SolutionId == this.SolutionId &&
                        c.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(keyword) || c.ChannelName.Contains(keyword) || 
                         (!string.IsNullOrEmpty(c.Description) && c.Description.Contains(keyword))) &&
                        (string.IsNullOrEmpty(channelType) || c.ChannelType == channelType)
                        orderby c.TimeCreate descending
                        select c;

            var total = query.Count();
            var data = query.Skip((pageIndex - 1) * pageSize)
                           .Take(pageSize)
                           .ToList();

            return new PagingData<LcNotificationChannel>
            {
                List = data,
                Paging = new PagingInfo(total, pageSize, pageIndex)
            };
        }

        [Function("get", "获取通知通道详情")]
        public LcNotificationChannel Get(string id)
        {
            using var db = this.GetDb();
            var data = (from c in db.LcNotificationChannels
                        where c.Id == id &&
                        c.State == 1 &&
                        c.SolutionId == this.SolutionId &&
                        c.ProjectId == this.ProjectId
                        select c).FirstOrDefault();
            
            if (data == null)
            {
                throw new CustomException("通知通道不存在");
            }
            
            return data;
        }

        [Function("save", "保存通知通道")]
        public string Save(LcNotificationChannel channel)
        {
            channel.SolutionId = this.SolutionId;
            channel.ProjectId = this.ProjectId;

            using var db = this.GetDb();
            db.BeginTransaction();

            try
            {
                // 验证配置JSON格式
                ValidateChannelConfig(channel.ChannelType, channel.ConfigJson);

                if (string.IsNullOrEmpty(channel.Id))
                {
                    // 新增
                    channel.Id = TUID.NewTUID().ToString();
                    channel.TimeCreate = DateTime.Now;
                    channel.Creator = this.UserName;
                    channel.State = 1;

                    // 检查名称是否重复
                    var existingChannel = db.LcNotificationChannels
                        .FirstOrDefault(c => c.ChannelName == channel.ChannelName && 
                                           c.SolutionId == channel.SolutionId && 
                                           c.ProjectId == channel.ProjectId && 
                                           c.State == 1);
                    
                    if (existingChannel != null)
                    {
                        throw new CustomException($"通知通道名称 {channel.ChannelName} 已存在");
                    }

                    db.Insert(channel);
                }
                else
                {
                    // 更新
                    var existingChannel = db.LcNotificationChannels
                        .FirstOrDefault(c => c.Id == channel.Id && 
                                           c.SolutionId == channel.SolutionId && 
                                           c.ProjectId == channel.ProjectId && 
                                           c.State == 1);
                    
                    if (existingChannel == null)
                    {
                        throw new CustomException("通知通道不存在");
                    }

                    // 检查名称是否重复（排除自己）
                    var duplicateChannel = db.LcNotificationChannels
                        .FirstOrDefault(c => c.ChannelName == channel.ChannelName && 
                                           c.Id != channel.Id &&
                                           c.SolutionId == channel.SolutionId && 
                                           c.ProjectId == channel.ProjectId && 
                                           c.State == 1);
                    
                    if (duplicateChannel != null)
                    {
                        throw new CustomException($"通知通道名称 {channel.ChannelName} 已存在");
                    }

                    existingChannel.ChannelName = channel.ChannelName;
                    existingChannel.ChannelType = channel.ChannelType;
                    existingChannel.Description = channel.Description;
                    existingChannel.IsEnabled = channel.IsEnabled;
                    existingChannel.ConfigJson = channel.ConfigJson;
                    existingChannel.IntervalSeconds = channel.IntervalSeconds;
                    existingChannel.TimeModified = DateTime.Now;
                    existingChannel.Modifier = this.UserName;

                    db.Update(existingChannel);
                }

                db.CommitTransaction();
                return channel.Id;
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("delete", "删除通知通道")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            var channel = db.LcNotificationChannels
                .FirstOrDefault(c => c.Id == id && 
                               c.SolutionId == this.SolutionId && 
                               c.ProjectId == this.ProjectId && 
                               c.State == 1);
            
            if (channel == null)
            {
                throw new CustomException("通知通道不存在");
            }

            channel.State = 0;
            channel.TimeModified = DateTime.Now;
            channel.Modifier = this.UserName;

            db.Update(channel);
        }

        [Function("getChannelTypes", "获取支持的通知通道类型")]
        public List<OptionVO> GetChannelTypes()
        {
            return
            [
                new OptionVO { Value = "Email", Label = "邮件通知" },
                new OptionVO { Value = "Dingtalk", Label = "钉钉通知" },
                new OptionVO { Value = "Feishu", Label = "飞书通知" },
                new OptionVO { Value = "Weixin", Label = "企业微信通知" }
            ];
        }

        [Function("testChannel", "测试通知通道")]
        public async Task<bool> TestChannel(string id)
        {
            var notificationService = ServiceLocator.GetScopedService<NotificationService>();

            return await notificationService.TestNotificationChannelAsync(id);
        }

        /// <summary>
        /// 验证通道配置JSON格式
        /// </summary>
        private void ValidateChannelConfig(string channelType, string configJson)
        {
            try
            {
                switch (channelType.ToLower())
                {
                    case "email":
                        JsonSerializer.Deserialize<EmailNotificationConfig>(configJson);
                        break;
                    case "dingtalk":
                        JsonSerializer.Deserialize<DingtalkNotificationConfig>(configJson);
                        break;
                    case "feishu":
                        JsonSerializer.Deserialize<FeishuNotificationConfig>(configJson);
                        break;
                    case "weixin":
                        JsonSerializer.Deserialize<WeixinNotificationConfig>(configJson);
                        break;
                    default:
                        throw new CustomException($"不支持的通知类型: {channelType}");
                }
            }
            catch (JsonException ex)
            {
                throw new CustomException($"配置JSON格式错误: {ex.Message}");
            }
        }
    }
}
