<template>
  <div class="login-wrapper">
    <!-- <login-header /> -->

    <div class="login-container">
      <div class="title-container">
        <!-- <h1 class="title margin-no font-white">{{ t('pages.login.loginTitle') }}</h1> -->
        <h1 class="title font-white">数据集成平台</h1>
        <!-- <div class="sub-title">
          <p class="tip">{{ type == 'register' ? t('pages.login.existAccount') : t('pages.login.noAccount') }}</p>
          <p class="tip" @click="switchType(type == 'register' ? 'login' : 'register')">
            {{ type == 'register' ? t('pages.login.signIn') : t('pages.login.createAccount') }}
          </p>
        </div> -->
      </div>

      <login v-if="type === 'login'" />
      <!-- <register v-else @register-success="switchType('login')" /> -->
      <!-- <tdesign-setting /> -->
    </div>

    <footer class="copyright">Copyright @ 2021-2023 . All Rights Reserved</footer>
  </div>
</template>
<script lang="ts">
export default {
  name: 'LoginIndex',
};
</script>
<script setup lang="ts">
import { ref } from 'vue';

import { t } from '@/locales';

// import TdesignSetting from '@/layouts/setting.vue';
// import LoginHeader from './components/Header.vue';
import Login from './components/Login.vue';
// import Register from './components/Register.vue';

const type = ref('login');
// const switchType = (val: string) => {
//   type.value = val;
// };
</script>

<style lang="less" scoped>
@import './index.less';

.font-white {
  color: #fff !important;
}
</style>
