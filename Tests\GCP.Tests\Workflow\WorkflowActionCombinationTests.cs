using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.FunctionPool;
using GCP.DataAccess;
using GCP.Common;
using LinqToDB;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 工作流动作组合测试类
    /// </summary>
    public class WorkflowActionCombinationTests : DatabaseTestBase
    {
        public WorkflowActionCombinationTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task DataQueryAndBranch_ShouldWorkTogether()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowJson = CreateDataQueryAndBranchWorkflow();
            
            // Act
            var executor = new FlowExecutor(workflowJson);
            var context = CreateTestContext();
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("查询和分支组合工作流应该返回结果");
            Output.WriteLine($"查询和分支组合结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public async Task DataQueryAndForEach_ShouldProcessEachItem()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowJson = CreateDataQueryAndForEachWorkflow();
            
            // Act
            var executor = new FlowExecutor(workflowJson);
            var context = CreateTestContext();
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("查询和循环组合工作流应该返回结果");
            Output.WriteLine($"查询和循环组合结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public async Task BranchAndDelay_ShouldExecuteConditionally()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowJson = CreateBranchAndDelayWorkflow();
            
            // Act
            var executor = new FlowExecutor(workflowJson);
            var context = CreateTestContext();
            context.globalData["condition"] = true;
            
            var startTime = DateTime.Now;
            var result = await executor.Run(context);
            var endTime = DateTime.Now;

            // Assert
            result.Should().NotBeNull("分支和延迟组合工作流应该返回结果");
            var duration = (endTime - startTime).TotalMilliseconds;
            duration.Should().BeGreaterThanOrEqualTo(90, "条件为true时应该执行延迟");
            Output.WriteLine($"分支和延迟组合执行时间: {duration}毫秒");
        }

        [Fact]
        public async Task DataQuerySaveAndApi_ShouldProcessDataPipeline()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowJson = CreateDataPipelineWorkflow();
            
            // Act
            var executor = new FlowExecutor(workflowJson);
            var context = CreateTestContext();
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("数据管道工作流应该返回结果");
            Output.WriteLine($"数据管道结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public async Task NestedBranchAndLoop_ShouldHandleComplexLogic()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowJson = CreateNestedBranchAndLoopWorkflow();
            
            // Act
            var executor = new FlowExecutor(workflowJson);
            var context = CreateTestContext();
            context.globalData["items"] = new[] { 1, 2, 3 };
            context.globalData["processItems"] = true;
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("嵌套分支和循环工作流应该返回结果");
            Output.WriteLine($"嵌套分支和循环结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public async Task ErrorHandlingWithTryCatch_ShouldCatchExceptions()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowJson = CreateErrorHandlingWorkflow();
            
            // Act
            var executor = new FlowExecutor(workflowJson);
            var context = CreateTestContext();
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("错误处理工作流应该返回结果");
            Output.WriteLine($"错误处理结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public async Task TransactionWithRollback_ShouldMaintainDataIntegrity()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowJson = CreateTransactionWorkflow();
            
            // Act
            var executor = new FlowExecutor(workflowJson);
            var context = CreateTestContext();
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("事务工作流应该返回结果");
            Output.WriteLine($"事务处理结果: {JsonHelper.Serialize(result)}");
        }

        /// <summary>
        /// 创建测试上下文
        /// </summary>
        private FunctionContext CreateTestContext()
        {
            var context = new FunctionContext
            {
                globalData = new Dictionary<string, object>(),
                Args = new Dictionary<string, object>(),
                Persistence = false,
                trackId = TUID.NewTUID().ToString()
            };

            var dbContext = GetService<IDbContext>();
            context.LocalDbContext.Value = dbContext;

            // 初始化Current属性
            context.Current = new FunctionProvider
            {
                FunctionName = "动作组合测试工作流",
                Level = 0,
                SeqNo = 0,
                IsFlow = true
            };

            return context;
        }

        /// <summary>
        /// 创建数据查询和分支组合工作流
        /// </summary>
        private string CreateDataQueryAndBranchWorkflow()
        {
            var workflow = new
            {
                id = "query-branch-workflow",
                version = 1,
                persistence = false,
                data = new object[0],
                body = new object[]
                {
                    new
                    {
                        id = "step-001",
                        name = "查询数据",
                        function = "dataCustomizeQuery",
                        args = new
                        {
                            name = "查询测试数据",
                            dataSource = "test-datasource-001",
                            operateType = "sql",
                            sqlInfo = new
                            {
                                sql = "SELECT 1 as count",
                                parameters = new object[0]
                            },
                            isPaging = false,
                            autoPaged = false
                        },
                        nextId = "step-002",
                        result = new object[]
                        {
                            new
                            {
                                key = "queryResult",
                                type = "array",
                                value = new { type = "variable", variableValue = "result" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-002",
                        name = "判断数据",
                        function = "dataBranch",
                        args = new
                        {
                            name = "判断是否有数据",
                            type = "script",
                            script = "_data.step001.queryResult && _data.step001.queryResult.length > 0"
                        },
                        controlType = "branch",
                        control = new
                        {
                            branch = new
                            {
                                hasData = new { nextId = "step-003" },
                                noData = new { nextId = "step-004" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-003",
                        name = "有数据处理",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "50" } }
                    },
                    new
                    {
                        id = "step-004",
                        name = "无数据处理",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "10" } }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建数据查询和循环组合工作流
        /// </summary>
        private string CreateDataQueryAndForEachWorkflow()
        {
            var workflow = new
            {
                id = "query-foreach-workflow",
                version = 1,
                persistence = false,
                data = new object[0],
                body = new object[]
                {
                    new
                    {
                        id = "step-001",
                        name = "查询数据列表",
                        function = "dataCustomizeQuery",
                        args = new
                        {
                            name = "查询数据列表",
                            dataSource = "test-datasource-001",
                            operateType = "sql",
                            sqlInfo = new
                            {
                                sql = "SELECT 1 as id UNION SELECT 2 UNION SELECT 3",
                                parameters = new object[0]
                            },
                            isPaging = false,
                            autoPaged = false
                        },
                        nextId = "step-002",
                        result = new object[]
                        {
                            new
                            {
                                key = "dataList",
                                type = "array",
                                value = new { type = "variable", variableValue = "result" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-002",
                        name = "循环处理数据",
                        function = "dataForEach",
                        args = new
                        {
                            name = "循环处理每个数据项",
                            dataSource = new { type = "variable", variableValue = "_data.step001.dataList" }
                        },
                        controlType = "forEach",
                        control = new
                        {
                            forEach = new
                            {
                                async = false,
                                list = "_data.step001.dataList",
                                item = new object[0],
                                nextId = "step-003"
                            }
                        }
                    },
                    new
                    {
                        id = "step-003",
                        name = "处理单个项目",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "10" } }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建分支和延迟组合工作流
        /// </summary>
        private string CreateBranchAndDelayWorkflow()
        {
            var workflow = new
            {
                id = "branch-delay-workflow",
                version = 1,
                persistence = false,
                data = new object[]
                {
                    new
                    {
                        id = "condition-var",
                        key = "condition",
                        type = "bool",
                        value = new { type = "text", textValue = "true" }
                    }
                },
                body = new object[]
                {
                    new
                    {
                        id = "step-001",
                        name = "条件判断",
                        function = "dataBranch",
                        args = new
                        {
                            name = "判断条件",
                            type = "script",
                            script = "_data.condition === true"
                        },
                        controlType = "branch",
                        control = new
                        {
                            branch = new
                            {
                                hasData = new { nextId = "step-002" },
                                noData = new { nextId = "step-003" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-002",
                        name = "条件为真时延迟",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "100" } }
                    },
                    new
                    {
                        id = "step-003",
                        name = "条件为假时快速完成",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "1" } }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建数据管道工作流（查询->保存->API）
        /// </summary>
        private string CreateDataPipelineWorkflow()
        {
            var workflow = new
            {
                id = "data-pipeline-workflow",
                version = 1,
                persistence = false,
                data = new object[0],
                body = new object[]
                {
                    new
                    {
                        id = "step-001",
                        name = "查询源数据",
                        function = "dataCustomizeQuery",
                        args = new
                        {
                            name = "查询源数据",
                            dataSource = "test-datasource-001",
                            operateType = "sql",
                            sqlInfo = new
                            {
                                sql = "SELECT 'test' as name, 1 as value",
                                parameters = new object[0]
                            },
                            isPaging = false,
                            autoPaged = false
                        },
                        nextId = "step-002",
                        result = new object[]
                        {
                            new
                            {
                                key = "sourceData",
                                type = "array",
                                value = new { type = "variable", variableValue = "result" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-002",
                        name = "保存处理后数据",
                        function = "dataSave",
                        args = new
                        {
                            name = "保存数据",
                            dataSource = "test-datasource-001",
                            operateType = "Save",
                            sourceDataPath = new { type = "variable", variableValue = "_data.step001.sourceData" },
                            configureInfo = new
                            {
                                tableName = "test_output",
                                columns = new object[]
                                {
                                    new { columnName = "name", dataType = "varchar", maxLength = 50 },
                                    new { columnName = "value", dataType = "int" }
                                }
                            }
                        },
                        nextId = "step-003",
                        result = new object[]
                        {
                            new
                            {
                                key = "saveResult",
                                type = "array",
                                value = new { type = "variable", variableValue = "result" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-003",
                        name = "调用API通知",
                        function = "apiRequest",
                        args = new
                        {
                            name = "通知API",
                            apiId = "test-api-001",
                            inputs = new object[]
                            {
                                new
                                {
                                    key = "data",
                                    type = "object",
                                    value = new { type = "variable", variableValue = "_data.step002.saveResult" }
                                }
                            }
                        }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建嵌套分支和循环工作流
        /// </summary>
        private string CreateNestedBranchAndLoopWorkflow()
        {
            var workflow = new
            {
                id = "nested-branch-loop-workflow",
                version = 1,
                persistence = false,
                data = new object[]
                {
                    new
                    {
                        id = "items-var",
                        key = "items",
                        type = "array",
                        value = new { type = "text", textValue = "[]" }
                    },
                    new
                    {
                        id = "process-var",
                        key = "processItems",
                        type = "bool",
                        value = new { type = "text", textValue = "false" }
                    }
                },
                body = new object[]
                {
                    new
                    {
                        id = "step-001",
                        name = "判断是否处理项目",
                        function = "dataBranch",
                        args = new
                        {
                            name = "判断处理标志",
                            type = "script",
                            script = "_data.processItems === true"
                        },
                        controlType = "branch",
                        control = new
                        {
                            branch = new
                            {
                                hasData = new { nextId = "step-002" },
                                noData = new { nextId = "step-004" }
                            }
                        }
                    },
                    new
                    {
                        id = "step-002",
                        name = "循环处理项目",
                        function = "dataForEach",
                        args = new
                        {
                            name = "循环处理每个项目",
                            dataSource = new { type = "variable", variableValue = "_data.items" }
                        },
                        controlType = "forEach",
                        control = new
                        {
                            forEach = new
                            {
                                async = false,
                                list = "_data.items",
                                item = new object[0],
                                nextId = "step-003"
                            }
                        }
                    },
                    new
                    {
                        id = "step-003",
                        name = "处理单个项目",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "5" } }
                    },
                    new
                    {
                        id = "step-004",
                        name = "跳过处理",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "1" } }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建错误处理工作流
        /// </summary>
        private string CreateErrorHandlingWorkflow()
        {
            var workflow = new
            {
                id = "error-handling-workflow",
                version = 1,
                persistence = false,
                data = new object[0],
                body = new object[]
                {
                    new
                    {
                        id = "step-001",
                        name = "尝试执行",
                        function = "controlTryCatch",
                        controlType = "tryCatch",
                        control = new
                        {
                            tryCatch = new
                            {
                                nextId = "step-002",
                                allowRetry = true,
                                retryCount = 3,
                                retryDelaysInSeconds = 1
                            }
                        }
                    },
                    new
                    {
                        id = "step-002",
                        name = "可能出错的操作",
                        function = "runScript",
                        args = new
                        {
                            name = "执行脚本",
                            script = "Math.random() > 0.5 ? 'success' : (() => { throw new Error('random error'); })()"
                        },
                        nextId = "step-003"
                    },
                    new
                    {
                        id = "step-003",
                        name = "完成处理",
                        function = "controlDelay",
                        args = new { millisecondsDelay = new { type = "text", textValue = "10" } }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建事务工作流
        /// </summary>
        private string CreateTransactionWorkflow()
        {
            var workflow = new
            {
                id = "transaction-workflow",
                version = 1,
                persistence = false,
                data = new object[0],
                body = new object[]
                {
                    new
                    {
                        id = "step-001",
                        name = "开始事务",
                        function = "dataTransaction",
                        controlType = "transaction",
                        control = new
                        {
                            transaction = new
                            {
                                rollbackType = "auto",
                                undoToPerStep = true,
                                nextId = "step-002"
                            }
                        }
                    },
                    new
                    {
                        id = "step-002",
                        name = "事务操作1",
                        function = "dataSave",
                        args = new
                        {
                            name = "保存数据1",
                            dataSource = "test-datasource-001",
                            operateType = "Save",
                            sourceDataPath = new { type = "script", scriptValue = "[{id: 1, name: 'test1'}]" },
                            configureInfo = new
                            {
                                tableName = "test_transaction",
                                columns = new object[]
                                {
                                    new { columnName = "id", dataType = "int", isPrimaryKey = true },
                                    new { columnName = "name", dataType = "varchar", maxLength = 50 }
                                }
                            }
                        },
                        nextId = "step-003"
                    },
                    new
                    {
                        id = "step-003",
                        name = "事务操作2",
                        function = "dataSave",
                        args = new
                        {
                            name = "保存数据2",
                            dataSource = "test-datasource-001",
                            operateType = "Save",
                            sourceDataPath = new { type = "script", scriptValue = "[{id: 2, name: 'test2'}]" },
                            configureInfo = new
                            {
                                tableName = "test_transaction",
                                columns = new object[]
                                {
                                    new { columnName = "id", dataType = "int", isPrimaryKey = true },
                                    new { columnName = "name", dataType = "varchar", maxLength = 50 }
                                }
                            }
                        }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        protected override async Task SeedTestDataAsync(GcpDb db)
        {
            try
            {
                // 创建基础测试数据 - 使用唯一ID避免冲突
                var timestamp = DateTime.Now.Ticks.ToString().Substring(10); // 使用时间戳后8位
                var solutionId = $"test-sol-combo-{timestamp}";
                var projectId = $"test-proj-combo-{timestamp}";

                var solution = TestDataBuilder.CreateTestSolution("工作流组合测试解决方案");
                solution.Id = solutionId;
                await db.InsertAsync(solution);

                var project = TestDataBuilder.CreateTestProject("工作流组合测试项目", solutionId);
                project.Id = projectId;
                await db.InsertAsync(project);

                // 创建测试数据源
                var dataSource = TestDataBuilder.CreateTestDataSource("测试数据源", solutionId, projectId);
                dataSource.Id = "test-datasource-001"; // 使用固定ID，与测试中的DataSource匹配
                dataSource.ConnectBy = "ConnectionString"; // 设置必需字段
                await db.InsertAsync(dataSource);

                // 创建测试API
                var api = TestDataBuilder.CreateTestApi("测试API", solutionId, projectId);
                api.Id = $"test-api-combo-{timestamp}";
                await db.InsertAsync(api);

                Output.WriteLine("工作流组合测试数据初始化完成");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"工作流组合测试数据初始化失败: {ex.Message}");
                throw;
            }
        }
    }
}
