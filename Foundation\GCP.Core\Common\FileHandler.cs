﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.FileProviders;
using Microsoft.Net.Http.Headers;

namespace Microsoft.Extensions.DependencyInjection
{
    public class FileHandler
    {
        public static void PrepareResponseAsync(StaticFileResponseContext ctx)
        {
            const int durationInSeconds = 0;
            ctx.Context.Response.Headers[HeaderNames.CacheControl] =
                "must-revalidate,max-age=" + durationInSeconds;
        }
    }

    public static class FileHandlerExtensions
    {
        public static IApplicationBuilder UseCustomFileServer(this IApplicationBuilder builder, IConfiguration configuration)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

            var provider = new FileExtensionContentTypeProvider();
            configuration.GetSection("FileServer:MimeType").GetChildren()
                    .ToList().ForEach(x =>
                    {
                        var key = x.GetValue<string>("Key");
                        var value = x.GetValue<string>("Value");

                        provider.Mappings[key] = value;
                    });

            // wwwroot
            var defaultDirPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
            if (!Directory.Exists(defaultDirPath))
            {
                Directory.CreateDirectory(defaultDirPath);
            }

            builder.UseDefaultFiles();
            builder.UseStaticFiles(new StaticFileOptions
            {
                ContentTypeProvider = provider,
                OnPrepareResponse = FileHandler.PrepareResponseAsync
            });

            configuration.GetSection("FileServer:VirtualPath").GetChildren()
                .ToList().ForEach(x =>
                {
                    var enableDirectoryBrowsing = x.GetValue<bool>("EnableDirectoryBrowsing");
                    var isAbsolutePath = x.GetValue<bool>("IsAbsolutePath");
                    var requestPath = x.GetValue<string>("RequestPath");
                    var fileProviderStr = x.GetValue<string>("FileProvider");

                    builder.Map(requestPath, app =>
                    {
                        var fileProvider = new PhysicalFileProvider(isAbsolutePath ? fileProviderStr : Path.Combine(Directory.GetCurrentDirectory(), fileProviderStr));
                        var options = new FileServerOptions();
                        options.StaticFileOptions.ContentTypeProvider = provider;
                        options.StaticFileOptions.OnPrepareResponse = FileHandler.PrepareResponseAsync;
                        options.FileProvider = fileProvider;
                        options.RequestPath = requestPath;
                        options.EnableDirectoryBrowsing = enableDirectoryBrowsing;
                        app.UseFileServer(options);
                    });
                    
                    //builder.UseFileServer(options);
                });

            return builder;
        }
    }
}
