﻿using System.Data;
using System.Linq.Expressions;
using System.Data.Common;

namespace GCP.DataAccess
{
    /// <summary>
    /// POCO Extension（高级扩展, 动态POCO序列化）
    /// </summary>
    public static class POCOAsyncExtension
    {
        public static async Task<T> GetAsync<T>(this DbConnection connection, string sqlString, CommandType cmdType = CommandType.Text, DbTransaction tran = null, params IDataParameter[] parms)
        {
            return await (await connection.ExecuteReaderAsync(tran, sqlString, cmdType, CommandBehavior.SingleRow, parms).ConfigureAwait(false)).GetAsync<T>().ConfigureAwait(false);
        }
        public static async Task<T> GetAsync<T>(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text)
        {
            return await (await connection.ExecuteReaderAsync(sqlString, parms, cmdType, CommandBehavior.SingleRow).ConfigureAwait(false)).GetAsync<T>().ConfigureAwait(false);
        }
        public static async Task<T> GetAsync<T>(this ISqlBuilder<T> sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return await GetAsync<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.Parameters).ConfigureAwait(false);
        }
        public static async Task<T> GetAsync<T>(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return await GetAsync<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.Parameters).ConfigureAwait(false);
        }


        public static async Task<List<T>> GetListAsync<T>(this DbConnection connection, string sqlString, CommandType cmdType = CommandType.Text, DbTransaction tran = null, params IDataParameter[] parms)
        {
            return await (await connection.ExecuteReaderAsync(tran, sqlString, cmdType, CommandBehavior.Default, parms).ConfigureAwait(false)).GetListAsync<T>().ConfigureAwait(false);
        }
        public static async Task<List<T>> GetListAsync<T>(this DbConnection connection, string sqlString, object parms, CommandType cmdType = CommandType.Text)
        {
            return await (await connection.ExecuteReaderAsync(sqlString, parms, cmdType).ConfigureAwait(false)).GetListAsync<T>().ConfigureAwait(false);
        }
        public static async Task<List<T>> GetListAsync<T>(this ISqlBuilder<T> sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return await GetListAsync<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.Parameters).ConfigureAwait(false);
        }
        public static async Task<List<T>> GetListAsync<T>(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return await GetListAsync<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, cmdType, tran, sqlBuilder.Parameters).ConfigureAwait(false);
        }

        public static async Task<Dictionary<string, object>> GetDictionaryAsync<T>(this DbConnection connection, Func<T, string> keySelector, Func<T, object> elementSelector, string sqlString, CommandType cmdType = CommandType.Text, DbTransaction tran = null, params IDataParameter[] parms)
        {
            return await (await connection.ExecuteReaderAsync(tran, sqlString, cmdType, CommandBehavior.Default, parms).ConfigureAwait(false)).GetDictionaryAsync<T>(keySelector, elementSelector).ConfigureAwait(false);
        }
        public static async Task<Dictionary<string, object>> GetDictionaryAsync<T>(this ISqlBuilder<T> sqlBuilder, Func<T, string> keySelector, Func<T, object> elementSelector, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return await GetDictionaryAsync<T>(connection ?? sqlBuilder.CreateConnection(), keySelector, elementSelector, sqlBuilder.SqlString, cmdType, tran, sqlBuilder.Parameters).ConfigureAwait(false);
        }
        public static async Task<Dictionary<string, object>> GetDictionaryAsync<T>(this ISqlBuilder sqlBuilder, Func<T, string> keySelector, Func<T, object> elementSelector, DbConnection connection = null, DbTransaction tran = null, CommandType cmdType = CommandType.Text)
        {
            return await GetDictionaryAsync<T>(connection ?? sqlBuilder.CreateConnection(), keySelector, elementSelector, sqlBuilder.SqlString, cmdType, tran, sqlBuilder.Parameters).ConfigureAwait(false);
        }


        public static async Task<List<T>> GetListPagedAsync<T>(this DbConnection connection, string sqlString, IDataParameter[] parms, int startIndex, int length, string orderBySql = "", DbTransaction tran = null)
        {
            sqlString = DbProviderInfo.GetPagingSql(connection.GetDbProvider(), startIndex, length, sqlString, orderBySql);
            return await GetListAsync<T>(connection, sqlString, CommandType.Text, tran, parms).ConfigureAwait(false);
        }
        public static async Task<List<T>> GetListPagedAsync<T>(this DbConnection connection, string sqlString, object parms, int startIndex, int length, string orderBySql = "", DbTransaction tran = null)
        {
            return await GetListPagedBaseAsync<T>(new SqlBuilder<T>(connection).Init(sqlString, parms), startIndex, length, orderBySql, connection, tran).ConfigureAwait(false);
        }
        public static async Task<List<T>> GetListPagedAsync<T>(this ISqlBuilder<T> sqlBuilder, int startIndex, int length, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return await GetListPagedBaseAsync<T>(sqlBuilder, startIndex, length, orderBySql, connection, tran).ConfigureAwait(false);
        }
        public static async Task<List<T>> GetListPagedAsync<T>(this ISqlBuilder sqlBuilder, int startIndex, int length, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return await GetListPagedBaseAsync<T>(sqlBuilder, startIndex, length, orderBySql, connection, tran).ConfigureAwait(false);
        }
        private static async Task<List<T>> GetListPagedBaseAsync<T>(this ISqlBuilder sqlBuilder, int startIndex, int length, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return await GetListPagedAsync<T>(connection ?? sqlBuilder.CreateConnection(), sqlBuilder.SqlString, sqlBuilder.Parameters, startIndex, length, orderBySql, tran).ConfigureAwait(false);
        }


        public static async Task<PagingData<T>> GetPagingDataAsync<T>(this DbConnection connection, string sqlString, IDataParameter[] parms, int pageSize, int pageIndex, string orderBySql = "", DbTransaction tran = null)
        {
            return await GetPagingDataAsync<T>(new SqlBuilder<T>(connection).Init(sqlString).AddParameters(parms), pageSize, pageIndex, orderBySql, connection, tran).ConfigureAwait(false);
        }
        public static async Task<PagingData<T>> GetPagingDataAsync<T>(this DbConnection connection, string sqlString, object parms, int pageSize, int pageIndex, string orderBySql = "", DbTransaction tran = null)
        {
            return await GetPagingDataAsync<T>(new SqlBuilder<T>(connection).Init(sqlString, parms), pageSize, pageIndex, orderBySql, connection, tran).ConfigureAwait(false);
        }
        public static async Task<PagingData<T>> GetPagingDataAsync<T>(this ISqlBuilder<T> sqlBuilder, int pageSize, int pageIndex, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return await GetPagingDataBaseAsync<T>(sqlBuilder, pageSize, pageIndex, orderBySql, connection, tran).ConfigureAwait(false);
        }
        public static async Task<PagingData<T>> GetPagingDataAsync<T>(this ISqlBuilder sqlBuilder, int pageSize, int pageIndex, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            return await GetPagingDataBaseAsync<T>(sqlBuilder, pageSize, pageIndex, orderBySql, connection, tran).ConfigureAwait(false);
        }
        private static async Task<PagingData<T>> GetPagingDataBaseAsync<T>(this ISqlBuilder sqlBuilder, int pageSize, int pageIndex, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            if (connection == null) connection = sqlBuilder.CreateConnection();
            var wasClosed = connection.State == ConnectionState.Closed;
            try
            {
                if (wasClosed) connection.Open();
                var pagingData = new PagingData<T>();
                pagingData.Paging = new PagingInfo(await sqlBuilder.CountAsync(connection, tran), pageSize, pageIndex);
                pagingData.List = await sqlBuilder.GetListPagedAsync<T>((pageIndex - 1) * pageSize, pageSize, orderBySql, connection, tran).ConfigureAwait(false);
                return pagingData;
            }
            finally
            {
                if (wasClosed) connection.Close();
            }
        }


        public static async IAsyncEnumerable<List<T>> GetPagedIEnumerableAsync<T>(this DbConnection connection, string sqlString, IDataParameter[] parms, int pageSize, string orderBySql = "", DbTransaction tran = null)
        {
            await foreach (var dataPage in GetPagedIEnumerableBaseAsync<T>(new SqlBuilder<T>(connection).Init(sqlString).AddParameters(parms), pageSize, orderBySql, connection, tran).ConfigureAwait(false))
            {
                yield return dataPage;
            }
        }
        public static async IAsyncEnumerable<List<T>> GetPagedIEnumerableAsync<T>(this DbConnection connection, string sqlString, object parms, int pageSize, string orderBySql = "", DbTransaction tran = null)
        {
            await foreach (var dataPage in GetPagedIEnumerableBaseAsync<T>(new SqlBuilder<T>(connection).Init(sqlString, parms), pageSize, orderBySql, connection, tran).ConfigureAwait(false))
            {
                yield return dataPage;
            }
        }
        public static async IAsyncEnumerable<List<T>> GetPagedIEnumerableAsync<T>(this ISqlBuilder<T> sqlBuilder, int pageSize, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            await foreach (var dataPage in GetPagedIEnumerableBaseAsync<T>(sqlBuilder, pageSize, orderBySql, connection, tran).ConfigureAwait(false))
            {
                yield return dataPage;
            }
        }

        public static async IAsyncEnumerable<List<T>> GetPagedIEnumerableAsync<T>(this ISqlBuilder sqlBuilder, int pageSize, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            await foreach (var dataPage in GetPagedIEnumerableBaseAsync<T>(sqlBuilder, pageSize, orderBySql, connection, tran).ConfigureAwait(false))
            {
                yield return dataPage;
            }
        }
        private static async IAsyncEnumerable<List<T>> GetPagedIEnumerableBaseAsync<T>(this ISqlBuilder sqlBuilder, int pageSize, string orderBySql = "", DbConnection connection = null, DbTransaction tran = null)
        {
            int pageIndex = 1;
            while (true)
            {
                var dataPage = await sqlBuilder.GetListPagedAsync<T>((pageIndex - 1) * pageSize, pageSize, orderBySql, connection, tran).ConfigureAwait(false);
                if (dataPage.Count == 0)
                {
                    break;
                }
                yield return dataPage;
                pageIndex++;
            }
        }


        public static async Task<int> InsertAsync<T>(this DbConnection connection, T obj, DbTransaction tran = null, string tableName = null)
        {
            return await connection.ExecuteAsync(SqlBuilderBase.GetInsertSql(connection, obj, tableName), obj, CommandType.Text, tran);
        }
        public static async Task<int> InsertAsync<T>(this DbTransaction tran, T obj, string tableName = null)
        {
            return await InsertAsync(tran.Connection, obj, tran, tableName);
        }
        public static async Task<int> InsertAsync<T>(this IDbContext dbContext, T obj, string tableName = null, DbTransaction tran = null)
        {
            return await InsertAsync(dbContext.CreateConnection(), obj, tran, tableName);
        }


        public static async Task<int> UpdateAsync<T>(this DbConnection connection, T setObj, object whereObj = null, string tableName = null, object ignoreSetColumns = null, string idColumnName = "id", DbTransaction tran = null)
        {
            return await SqlBuilderBase.GetUpdateSql(connection, tableName ?? DbHelper.GetTableName(setObj.GetType()), setObj, whereObj, ignoreSetColumns, idColumnName).ExecuteAsync(tran);
        }
        public static async Task<int> UpdateAsync<T>(this DbTransaction tran, T setObj, object whereObj = null, string tableName = null, object ignoreSetColumns = null, string idColumnName = "id")
        {
            return await UpdateAsync(tran.Connection, setObj, whereObj, tableName, ignoreSetColumns, idColumnName, tran);
        }
        public static async Task<int> UpdateAsync<T>(this IDbContext dbContext, T setObj, object whereObj = null, string tableName = null, object ignoreSetColumns = null, DbTransaction tran = null, string idColumnName = "id")
        {
            return await UpdateAsync(dbContext.CreateConnection(), setObj, whereObj, tableName, ignoreSetColumns, idColumnName, tran);
        }

        public static async Task<int> UpdateAsync<T>(this DbConnection connection, object setObj, Expression<Func<T, bool>> where, string tableName = null, object ignoreSetColumns = null, DbTransaction tran = null)
        {
            return await ExecuteSqlBuilder.Update(connection, setObj, where, tableName, ignoreSetColumns).ExecuteAsync(tran);
        }
        public static async Task<int> UpdateAsync<T>(this DbTransaction tran, object setObj, Expression<Func<T, bool>> where, string tableName = null, object ignoreSetColumns = null)
        {
            return await UpdateAsync(tran.Connection, setObj, where, tableName, ignoreSetColumns, tran);
        }
        public static async Task<int> UpdateAsync<T>(this IDbContext dbContext, object setObj, Expression<Func<T, bool>> where, string tableName = null, object ignoreSetColumns = null, DbTransaction tran = null)
        {
            return await UpdateAsync(dbContext.CreateConnection(), setObj, where, tableName, ignoreSetColumns, tran);
        }

        public static async Task<int> UpdateAsync<T>(this DbConnection connection, Expression<Func<T, SetColunms>> set, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return await ExecuteSqlBuilder.Update(connection, set, where, tableName).ExecuteAsync(tran);
        }
        public static async Task<int> UpdateAsync<T>(this DbTransaction tran, Expression<Func<T, SetColunms>> set, Expression<Func<T, bool>> where, string tableName = null)
        {
            return await UpdateAsync(tran.Connection, set, where, tableName, tran);
        }
        public static async Task<int> UpdateAsync<T>(this IDbContext dbContext, Expression<Func<T, SetColunms>> set, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return await UpdateAsync(dbContext.CreateConnection(), set, where, tableName, tran);
        }



        public static async Task<int> DeleteAsync<T>(this DbConnection connection, object whereObj, string tableName = null, string idColumnName = "id", DbTransaction tran = null)
        {
            return await SqlBuilderBase.GetDeleteSql(connection, tableName ?? DbHelper.GetTableName(whereObj.GetType()), whereObj, idColumnName).ExecuteAsync(connection, tran);
        }
        public static async Task<int> DeleteAsync<T>(this IDbContext dbContext, object whereObj, string tableName = null, string idColumnName = "id", DbTransaction tran = null)
        {
            return await DeleteAsync<T>(dbContext.CreateConnection(), whereObj, tableName, idColumnName, tran);
        }
        public static async Task<int> DeleteAsync<T>(this DbConnection connection, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return await ExecuteSqlBuilder.Delete(connection, where, tableName).ExecuteAsync(tran);
        }
        public static async Task<int> DeleteAsync<T>(this DbTransaction tran, Expression<Func<T, bool>> where, string tableName = null)
        {
            return await DeleteAsync(tran.Connection, where, tableName, tran);
        }
        public static async Task<int> DeleteAsync<T>(this IDbContext dbContext, Expression<Func<T, bool>> where, string tableName = null, DbTransaction tran = null)
        {
            return await DeleteAsync(dbContext.CreateConnection(), where, tableName, tran);
        }


        public static async Task<DateTime> GetDbTimeAsync(this DbConnection connection)
        {
            return await connection.GetAsync<DateTime>(connection.GetDbProvider().GetTimeSql());
        }
        public static async Task<DateTime> GetDbTimeAsync(this IDbContext db)
        {
            using (var connection = db.CreateConnection())
            {
                return await connection.GetAsync<DateTime>(connection.GetDbProvider().GetTimeSql());
            }
        }

        public static async Task<int> CountAsync(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            string sqlString = "select count(1) from (" + sqlBuilder.SqlString + ") t";
            return await GetAsync<int>(connection ?? sqlBuilder.CreateConnection(), sqlString, CommandType.Text, tran, sqlBuilder.Parameters);
        }

        public static async Task<bool> ExistsAsync(this ISqlBuilder sqlBuilder, DbConnection connection = null, DbTransaction tran = null)
        {
            return (await sqlBuilder.ExecuteScalarAsync(connection, tran)) != null;
        }
    }
}
