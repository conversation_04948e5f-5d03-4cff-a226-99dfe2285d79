/* eslint-disable new-cap */
import './sqlCompletion';
import './scriptCompletion';

import { loader } from '@guolao/vue-monaco-editor';
import * as monaco from 'monaco-editor';
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';

// eslint-disable-next-line no-restricted-globals
(globalThis as any).MonacoEnvironment = {
  getWorker(_: any, label: string) {
    if (label === 'json') {
      return new jsonWorker();
    }

    if (label === 'html') {
      return new htmlWorker();
    }

    if (label === 'typescript' || label === 'javascript') {
      return new tsWorker();
    }

    return new editorWorker();
  },
};
// validation settings - 启用语义验证以支持类型检查
monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
  noSemanticValidation: false, // 启用语义验证以支持类型提示
  noSyntaxValidation: false, // 启用语法验证
  diagnosticCodesToIgnore: [1108, 1005, 1003], // 忽略一些常见的错误代码
});

// JavaScript validation settings
monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
  noSemanticValidation: false, // 启用语义验证以支持类型提示
  noSyntaxValidation: false, // 启用语法验证
  diagnosticCodesToIgnore: [1108, 1005, 1003], // 忽略一些常见的错误代码
});

// compiler options
monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
  target: monaco.languages.typescript.ScriptTarget.ES2017,
  strict: false, // JavaScript 不需要严格模式
  allowNonTsExtensions: true,
  checkJs: true, // 启用 JavaScript 类型检查
  allowJs: true,
});

loader.config({
  monaco,
});
