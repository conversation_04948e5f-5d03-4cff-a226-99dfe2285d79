﻿using Serilog;

namespace GCP.Common
{
    public class Logger : LoggerBase<string>
    {
        public Logger(int capacity = 320) : base(capacity)
        {
        }

        public override async Task Write(string message)
        {
            await Write("INFO", message);
        }

        public async Task Write(string level, string message)
        {
            message = $"[{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss,fff")}] [{level}] {message}" + Environment.NewLine;
            await channel.Writer.WriteAsync(message);
            Log.Debug(message);
        }
    }
}
