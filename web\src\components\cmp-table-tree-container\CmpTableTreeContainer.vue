<template>
  <div class="table-tree-container">
    <div class="list-tree-wrapper">
      <div class="list-tree-operator">
        <cmp-dir-tree
          ref="dirTreeRef"
          :name="props.name"
          type="tree"
          :dir-type="props.dirType"
          @select="onTreeNodeClick"
        >
          <template #item="{ item }">
            <slot v-if="item.leafId" name="item" :item="item" />
            <div v-else>{{ item.label }}</div>
          </template>
        </cmp-dir-tree>
      </div>
      <div class="list-tree-content">
        <!-- 目录模式：显示表格 -->
        <div v-show="!isEditMode">
          <t-row justify="space-between">
            <t-space>
              <t-button v-if="currentNode" theme="primary" @click="onAddData"> 新增 </t-button>
            </t-space>
            <t-input
              v-model="searchKeyword"
              class="search-input"
              placeholder="请输入搜索关键字"
              clearable
              @enter="onSearch"
            >
              <template #suffix-icon>
                <search-icon size="var(--td-comp-size-xxxs)" />
              </template>
            </t-input>
          </t-row>
          <t-table
            row-key="id"
            :data="tableData"
            :columns="columns"
            :loading="tableLoading"
            :pagination="pagination"
            @page-change="onPageChange"
          >
            <template #op="slotProps">
              <t-space>
                <slot name="row-operator" :row="slotProps.row" />
                <t-link theme="danger" hover="color" @click="() => onDeleteData(slotProps.row.id)">删除</t-link>
              </t-space>
            </template>
          </t-table>
        </div>

        <!-- 编辑模式：显示编辑页面 -->
        <div v-show="isEditMode">
          <t-row justify="space-between" class="mb-4">
            <t-button theme="default" @click="onBackToList">
              <template #icon>
                <chevron-left-icon />
              </template>
            </t-button>
            <t-space>
              <t-button v-if="currentData.id" theme="default" @click="onDeleteCurrentData"> 删除 </t-button>
              <slot name="edit-operator" :current-data="currentData" />
              <t-button theme="primary" @click="onSaveData"> 保存 </t-button>
            </t-space>
          </t-row>

          <!-- 使用抽离的表单组件 -->
          <slot name="form" :current-data="currentData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'CmpTableTreeContainer',
};
</script>

<script setup lang="ts">
import { ChevronLeftIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { DialogPlugin, MessagePlugin, PageInfo, type PrimaryTableCol, TableRowData } from 'tdesign-vue-next';
import { onActivated, ref } from 'vue';

import CmpDirTree from '@/components/cmp-dir-tree/CmpDirTree.vue';
import { DirTreeData, useDirTreeService } from '@/composables/services/dirTree';

const props = defineProps<{
  name: string;
  dirType: string;
  columns: PrimaryTableCol[];
  getDefaultData: (currentNode: any) => TableRowData;
  getCurrentData: () => { data: TableRowData; dirData: DirTreeData };
  getByDirCode: (dirCode: string) => Promise<TableRowData[]>;
  save: (data: TableRowData) => Promise<string>;
  remove: (id: string) => Promise<void>;
  getById: (id: string) => Promise<TableRowData>;
}>();

const dirTreeRef = ref();
const dirTreeService = useDirTreeService();
const currentNode = ref<any>(null);
const tableData = ref<TableRowData[]>([]);
const tableLoading = ref(false);
const searchKeyword = ref('');

// 是否处于编辑模式
const isEditMode = ref(false);

// 是否处于叶子节点模式
const isLeafMode = ref(false);

// 编辑中的数据
const currentData = ref<TableRowData>({});

const pagination = ref({
  total: 0,
  pageSize: 20,
  current: 1,
});

// 加载变量列表
const loadData = async (id?: string) => {
  tableLoading.value = true;
  try {
    const data = await props.getByDirCode(id || currentNode.value.id);

    tableData.value = data;

    pagination.value.total = data.length;
  } finally {
    tableLoading.value = false;
  }
};

// 加载叶子节点变量
const loadLeafData = async (leafId: string) => {
  if (!leafId) {
    return;
  }

  const data = await props.getById(leafId);
  currentData.value = data;
};

// 树节点点击
const onTreeNodeClick = async (node: any) => {
  currentNode.value = node;
  isEditMode.value = false;
  // 判断是否为叶子节点
  if (node.isLeaf) {
    isLeafMode.value = true;
    // 加载叶子节点变量数据
    await loadLeafData(node.leafId);
    isEditMode.value = true;
  } else {
    isLeafMode.value = false;
    await loadData();
  }
};

// 返回目录
const onBackToDirectory = async () => {
  // 返回上级目录
  if (currentNode.value?.parentId) {
    await dirTreeService
      .getTreeItem(currentNode.value.parentId)
      .then((parent) => {
        if (parent) {
          onTreeNodeClick(parent);
        }
        isLeafMode.value = false;
      })
      .catch((err) => {
        MessagePlugin.error('获取父级目录失败');
      });
  } else {
    isLeafMode.value = false;
  }
};

// 返回列表
const onBackToList = async () => {
  if (isLeafMode.value) {
    await onBackToDirectory();
  } else {
    await loadData();
  }
  isEditMode.value = false;
};

// 分页
const onPageChange = (pageInfo: PageInfo, newDataSource: TableRowData[]) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

// 搜索
const onSearch = () => {
  pagination.value.current = 1;
  loadData();
};

const emit = defineEmits<{
  (e: 'validate', data: TableRowData): Promise<boolean>;
}>();

// 新增
const onAddData = () => {
  if (!currentNode.value) return;

  currentData.value = props.getDefaultData(currentNode.value);

  isEditMode.value = true;
};

// 保存
const onSaveData = async () => {
  const { data, dirData } = props.getCurrentData();

  currentData.value = data;
  // 使用单条保存接口
  const varData = {
    ...currentData.value,
  };

  const validateResult = await emit('validate', varData);
  if (validateResult === false) {
    MessagePlugin.warning('请检查表单内容');
    return;
  }

  const id = await props.save(varData);

  let dirId = '';
  if (currentData.value.id) {
    if (!currentNode.value.isLeaf) {
      MessagePlugin.warning('不能更新非叶子节点的变量');
      return;
    }
    dirId = currentNode.value.id;
  }

  await dirTreeService.save({
    ...dirData,
    id: dirId,
    parentId: currentNode.value.id,
    dirType: props.dirType,
    isLeaf: 'Y',
    leafId: id,
  });

  MessagePlugin.success(currentData.value.id ? '更新成功' : '新增成功');
  await onBackToList();

  // 刷新目录树
  dirTreeRef.value?.loadData();
};

// 删除当前编辑的项
const onDeleteCurrentData = async () => {
  onDeleteData(currentData.value.id);
};

// 删除
const onDeleteData = async (id) => {
  if (!id) return;

  const confirmDia = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除该数据吗？`,
    onConfirm: async () => {
      // 使用单条删除接口
      await props.remove(id);

      MessagePlugin.success('删除成功');
      await onBackToList();

      // 刷新目录树
      dirTreeRef.value?.loadData();
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

onActivated(() => {
  dirTreeRef.value.loadData();
});
</script>

<style lang="less" scoped>
.table-tree-container {
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
}

.list-tree-wrapper {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.list-tree-operator {
  width: 280px;
  border-right: 1px solid var(--td-border-level-1-color);
  overflow: auto;
}

.list-tree-content {
  flex: 1;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
  overflow: auto;
}

.search-input {
  width: 300px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
