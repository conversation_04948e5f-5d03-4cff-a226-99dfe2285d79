{"name": "发送通知", "function": "notificationAction.sendNotification", "icon": "notification", "version": "1.0.0", "description": "通过配置的通知通道发送通知消息，支持邮件、钉钉、飞书、企业微信等多种通知方式", "args": [{"name": "channelId", "type": "string", "required": true, "description": "通知通道ID"}, {"name": "title", "type": "string", "required": true, "description": "通知标题"}, {"name": "content", "type": "string", "required": true, "description": "通知内容"}], "result": [{"name": "success", "type": "boolean", "description": "发送是否成功"}]}