import { DeviceIcon, RouterWaveIcon, InstallIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/iot',
    component: Layout,
    redirect: '/iot/equipment',
    name: 'iot',
    meta: {
      title: {
        zh_CN: '设备',
        en_US: 'IoT',
      },
      icon: shallowRef(DeviceIcon),
      orderNo: 3,
    },
    children: [
      {
        path: 'equipment',
        name: 'Equipment',
        component: () => import('@/pages/iot/equipment/index.vue'),
        meta: {
          title: {
            zh_CN: '设备管理',
            en_US: 'Equipment Management',
          },
          icon: shallowRef(RouterWaveIcon),
        },
      },
      {
        path: 'equipment-variable',
        name: 'EquipmentVariable',
        component: () => import('@/pages/iot/equipment-variable/index.vue'),
        meta: {
          title: {
            zh_CN: '设备变量',
            en_US: 'Equipment Variable',
          },
          icon: shallowRef(InstallIcon),
        },
      },
    ],
  },
];
