<template>
  <div
    ref="dropZoneRef"
    :class="dropZoneClass"
    @dragover.prevent="onDragOver"
    @dragleave="onDragLeave"
    @drop.prevent="onDrop"
  >
    <!-- 水平辅助线 -->
    <div
      v-if="showDropIndicator && (props.position === 'before' || props.position === 'after')"
      :class="['drop-guide-line', `drop-guide-line-${props.position}`]"
    ></div>
    <!-- 容器内拖拽指示器 - 只在空容器时显示 -->
    <div v-if="showDropIndicator && props.position === 'inside' && isEmptyContainer" class="drop-indicator">
      <div class="drop-indicator-line"></div>
      <div class="drop-indicator-text">拖拽到此处</div>
    </div>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useDragManager, type DropZoneInfo } from './composables/useDragManager';
import { FlowStep } from './model';

interface Props {
  parentId?: string;
  targetItem?: FlowStep;
  position: 'before' | 'inside' | 'after';
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const dropZoneRef = ref<HTMLElement>();
const isHovering = ref(false);
const {
  isDragging,
  canDrop,
  handleDrop,
  registerDropZone,
  unregisterDropZone,
  setHoveringZone,
  isCurrentHoveringZone,
} = useDragManager();

const dropZoneId = computed(() => {
  return `${props.parentId || 'root'}-${props.targetItem?.id || 'empty'}-${props.position}`;
});

const dropInfo = computed<DropZoneInfo>(() => ({
  parentId: props.parentId,
  targetItem: props.targetItem,
  position: props.position,
  element: dropZoneRef.value!,
}));

const canDropHere = computed(() => {
  return !props.disabled && canDrop(dropInfo.value);
});

const showDropIndicator = computed(() => {
  return isDragging.value && isHovering.value && canDropHere.value && isCurrentHoveringZone(dropZoneId.value);
});

// 只在当前悬停的区域显示边框
const showDropZoneBorder = computed(() => {
  return isDragging.value && canDropHere.value && isCurrentHoveringZone(dropZoneId.value);
});

// 判断是否为空容器（用于inside位置）
const isEmptyContainer = computed(() => {
  if (props.position !== 'inside') return false;

  const container = dropZoneRef.value;
  if (!container) return false;

  // 检查是否有 empty-drop-zone 元素（根级别的空容器）
  const hasEmptyZone = container.querySelector('.empty-drop-zone');
  if (hasEmptyZone) return true;

  // 检查是否有 action-list 但没有 action-list-item（控制块内部的空容器）
  const actionList = container.querySelector('.action-list');
  if (actionList) {
    const hasListItems = actionList.querySelector('.action-list-item:not(.action-list-item-end)');
    return !hasListItems;
  }

  return false;
});

const dropIndicatorText = computed(() => {
  switch (props.position) {
    case 'before':
      return `插入到【${props.targetItem?.name}】前面`;
    case 'after':
      return `插入到【${props.targetItem?.name}】后面`;
    case 'inside':
      return props.targetItem ? `插入到【${props.targetItem.name}】内部` : '插入到此容器';
    default:
      return '放置到此处';
  }
});

const dropZoneClass = computed(() => {
  return [
    'drag-drop-zone',
    {
      'drop-zone-active': isDragging.value && canDropHere.value,
      'drop-zone-hover': showDropZoneBorder.value,
      'drop-zone-disabled': props.disabled || !canDropHere.value,
    },
  ];
});

const onDragOver = (event: DragEvent) => {
  if (!canDropHere.value) return;

  event.preventDefault();
  isHovering.value = true;
  setHoveringZone(dropZoneId.value);
};

const onDragLeave = (event: DragEvent) => {
  // 检查是否真的离开了放置区域
  const rect = dropZoneRef.value?.getBoundingClientRect();
  if (rect) {
    const { clientX, clientY } = event;
    if (clientX < rect.left || clientX > rect.right || clientY < rect.top || clientY > rect.bottom) {
      isHovering.value = false;
      // 如果当前悬停的是这个区域，清除悬停状态
      if (isCurrentHoveringZone(dropZoneId.value)) {
        setHoveringZone(null);
      }
    }
  }
};

const onDrop = (event: DragEvent) => {
  if (!canDropHere.value) return;

  event.preventDefault();
  isHovering.value = false;
  setHoveringZone(null);
  handleDrop(dropInfo.value);
};

onMounted(() => {
  if (dropZoneRef.value) {
    registerDropZone(dropZoneId.value, dropZoneRef.value);
  }
});

onUnmounted(() => {
  unregisterDropZone(dropZoneId.value);
});
</script>

<style lang="less" scoped>
.drag-drop-zone {
  position: relative;
  transition: all 0.15s ease;

  // 恢复区域边框显示，但移除背景
  &.drop-zone-hover {
    &::before {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      right: 1px;
      bottom: 1px;
      border: 2px dashed var(--td-brand-color);
      border-radius: 4px;
      pointer-events: none;
      z-index: 5;
    }
  }

  &.drop-zone-disabled {
    &::before {
      display: none;
    }
  }
}

// 水平辅助线样式
.drop-guide-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--td-brand-color);
  border-radius: 1px;
  z-index: 10;
  pointer-events: none;

  &.drop-guide-line-before {
    top: -1px;
  }

  &.drop-guide-line-after {
    bottom: -1px;
  }
}

.drop-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.drop-indicator-line {
  width: 60px;
  height: 2px;
  background-color: var(--td-brand-color);
  border-radius: 1px;
}

.drop-indicator-text {
  background-color: var(--td-brand-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}
</style>
