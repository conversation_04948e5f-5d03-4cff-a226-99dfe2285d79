﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20240701002000, "初始化用户角色表")]
    public class AddUserRoleTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_USER_ROLE").WithDescription("系统用户关联角色定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")

               .WithColumn("USER_ID").AsAnsiString(36).WithColumnDescription("用户 ID")
               .WithColumn("ROLE_ID").AsAnsiString(36).WithColumnDescription("角色 ID")
               ;

            Create.Index("LC_USER_ROLE_IDX")
                .OnTable("LC_USER_ROLE")
                .OnColumn("USER_ID").Ascending()
                .OnColumn("ROLE_ID").Ascending()
                .WithOptions().Unique();
        }

        public override void Down()
        {
            Delete.Table("LC_USER_ROLE");
        }
    }
}
