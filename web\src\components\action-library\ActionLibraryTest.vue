<template>
  <div class="action-library-test">
    <div class="test-section">
      <div class="section-header">
        <h3>测试执行</h3>
        <t-space>
          <t-button theme="primary" @click="executeTest" :loading="executing" :disabled="!actionLibraryData.id">
            <template #icon>
              <play-icon />
            </template>
            执行测试
          </t-button>
          <t-button theme="default" @click="clearResults">
            <template #icon>
              <clear-icon />
            </template>
            清空结果
          </t-button>
        </t-space>
      </div>

      <!-- 输入数据配置 -->
      <div class="input-section">
        <div class="input-header">
          <h4>输入数据</h4>
          <t-space>
            <t-button size="small" theme="default" @click="loadTestData">
              <template #icon>
                <refresh-icon />
              </template>
              加载测试数据
            </t-button>
            <t-button size="small" theme="default" @click="validateInputData">
              <template #icon>
                <check-icon />
              </template>
              验证数据
            </t-button>
          </t-space>
        </div>
        <div class="input-editor">
          <editor
            v-model:value="inputDataJson"
            language="json"
            style="height: 300px"
            :read-only="false"
            placeholder="请输入测试数据JSON"
          />
        </div>
      </div>

      <!-- 执行结果 -->
      <div v-if="executionResult" class="result-section">
        <div class="result-header">
          <h4>执行结果</h4>
          <t-space>
            <t-tag :theme="executionResult.success ? 'success' : 'danger'">
              {{ executionResult.success ? '成功' : '失败' }}
            </t-tag>
            <span class="execution-time">执行时间: {{ executionResult.executionTime }}ms</span>
            <span class="execution-id">执行ID: {{ executionResult.executionId }}</span>
          </t-space>
        </div>

        <t-tabs v-model="resultTab" size="medium">
          <t-tab-panel value="result" label="执行结果">
            <div class="result-content">
              <code-preview
                :code="formatResult(executionResult.result)"
                language="json"
                :show-copy="true"
                :max-height="400"
                :is-dark="false"
              />
            </div>
          </t-tab-panel>
          
          <t-tab-panel v-if="!executionResult.success" value="error" label="错误信息">
            <div class="error-content">
              <t-alert theme="error" :message="executionResult.error" />
              <div v-if="executionResult.stackTrace" class="stack-trace">
                <h5>堆栈跟踪</h5>
                <pre>{{ executionResult.stackTrace }}</pre>
              </div>
            </div>
          </t-tab-panel>

          <t-tab-panel value="details" label="执行详情">
            <div class="details-content">
              <t-descriptions :column="2" bordered>
                <t-descriptions-item label="动作库ID">{{ executionResult.actionLibraryId }}</t-descriptions-item>
                <t-descriptions-item label="动作库名称">{{ executionResult.actionLibraryName }}</t-descriptions-item>
                <t-descriptions-item label="执行ID">{{ executionResult.executionId }}</t-descriptions-item>
                <t-descriptions-item label="执行状态">
                  <t-tag :theme="executionResult.success ? 'success' : 'danger'">
                    {{ executionResult.success ? '成功' : '失败' }}
                  </t-tag>
                </t-descriptions-item>
                <t-descriptions-item label="执行时间">{{ executionResult.executionTime }}ms</t-descriptions-item>
                <t-descriptions-item label="执行时间">{{ formatDateTime(executionResult.startTime) }}</t-descriptions-item>
              </t-descriptions>
            </div>
          </t-tab-panel>
        </t-tabs>
      </div>

      <!-- 历史执行记录 -->
      <div class="history-section">
        <div class="history-header">
          <h4>最近执行记录</h4>
          <t-button size="small" theme="default" @click="loadExecutionHistory">
            <template #icon>
              <refresh-icon />
            </template>
            刷新
          </t-button>
        </div>
        <div class="history-content">
          <t-table
            :data="executionHistory"
            :columns="historyColumns"
            size="small"
            :pagination="false"
            max-height="300"
            :loading="loadingHistory"
          >
            <template #status="{ row }">
              <t-tag :theme="getStatusTheme(row.status)">
                {{ getStatusText(row.status) }}
              </t-tag>
            </template>
            <template #executionTime="{ row }">
              {{ row.executionTimeMs }}ms
            </template>
            <template #startTime="{ row }">
              {{ formatDateTime(row.startTime) }}
            </template>
            <template #actions="{ row }">
              <t-space>
                <t-button size="small" variant="text" @click="viewExecutionDetail(row)">
                  查看
                </t-button>
                <t-button size="small" variant="text" @click="rerunExecution(row)">
                  重新执行
                </t-button>
              </t-space>
            </template>
          </t-table>
        </div>
      </div>
    </div>

    <!-- 执行详情对话框 -->
    <t-dialog
      v-model:visible="showDetailDialog"
      header="执行详情"
      width="800px"
      height="600px"
      :footer="false"
    >
      <div v-if="selectedExecution" class="execution-detail">
        <t-descriptions :column="2" bordered>
          <t-descriptions-item label="执行ID">{{ selectedExecution.executionId }}</t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag :theme="getStatusTheme(selectedExecution.status)">
              {{ getStatusText(selectedExecution.status) }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="执行时间">{{ selectedExecution.executionTimeMs }}ms</t-descriptions-item>
          <t-descriptions-item label="开始时间">{{ formatDateTime(selectedExecution.startTime) }}</t-descriptions-item>
          <t-descriptions-item label="结束时间">{{ formatDateTime(selectedExecution.endTime) }}</t-descriptions-item>
          <t-descriptions-item label="执行者">{{ selectedExecution.creator }}</t-descriptions-item>
        </t-descriptions>

        <t-divider>输入数据</t-divider>
        <code-preview
          :code="selectedExecution.inputData || '无'"
          language="json"
          :show-copy="true"
          :max-height="200"
          :is-dark="false"
        />

        <t-divider>输出数据</t-divider>
        <code-preview
          :code="selectedExecution.outputData || '无'"
          language="json"
          :show-copy="true"
          :max-height="200"
          :is-dark="false"
        />

        <div v-if="selectedExecution.errorMessage" class="error-section">
          <t-divider>错误信息</t-divider>
          <t-alert theme="error" :message="selectedExecution.errorMessage" />
          <div v-if="selectedExecution.stackTrace" class="stack-trace">
            <h5>堆栈跟踪</h5>
            <pre>{{ selectedExecution.stackTrace }}</pre>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryTest',
};
</script>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { PlayIcon, ClearIcon, RefreshIcon, CheckIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import Editor from '@/components/editor/index.vue';
import CodePreview from '@/components/code-preview/index.vue';

interface ActionLibraryData {
  id?: string;
  name?: string;
  testDataJson?: string;
  inputSchemaJson?: string;
}

interface ExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  stackTrace?: string;
  executionTime: number;
  executionId: string;
  actionLibraryId: string;
  actionLibraryName?: string;
  startTime?: string;
}

interface ExecutionLog {
  id: string;
  executionId: string;
  status: string;
  executionTimeMs: number;
  startTime: string;
  endTime?: string;
  inputData?: string;
  outputData?: string;
  errorMessage?: string;
  stackTrace?: string;
  creator: string;
}

const props = defineProps<{
  modelValue: ActionLibraryData;
}>();

const emits = defineEmits<{
  'update:modelValue': [value: ActionLibraryData];
  'test-execute': [inputData: any];
}>();

const actionLibraryData = ref<ActionLibraryData>({ ...props.modelValue });
const inputDataJson = ref<string>('{}');
const executing = ref(false);
const executionResult = ref<ExecutionResult | null>(null);
const resultTab = ref('result');
const executionHistory = ref<ExecutionLog[]>([]);
const loadingHistory = ref(false);
const showDetailDialog = ref(false);
const selectedExecution = ref<ExecutionLog | null>(null);

// 历史记录表格列定义
const historyColumns = [
  { colKey: 'executionId', title: '执行ID', width: 120, ellipsis: true },
  { colKey: 'status', title: '状态', width: 80 },
  { colKey: 'executionTime', title: '执行时间', width: 100 },
  { colKey: 'startTime', title: '开始时间', width: 160 },
  { colKey: 'actions', title: '操作', width: 120 },
];

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    actionLibraryData.value = { ...newValue };
    if (newValue.testDataJson) {
      inputDataJson.value = newValue.testDataJson;
    }
  },
  { deep: true }
);

// 加载测试数据
const loadTestData = () => {
  if (actionLibraryData.value.testDataJson) {
    inputDataJson.value = actionLibraryData.value.testDataJson;
    MessagePlugin.success('测试数据加载成功');
  } else {
    MessagePlugin.warning('未配置测试数据');
  }
};

// 验证输入数据
const validateInputData = () => {
  try {
    if (!inputDataJson.value.trim()) {
      MessagePlugin.warning('请输入测试数据');
      return false;
    }
    
    const inputData = JSON.parse(inputDataJson.value);
    
    // 如果有输入参数Schema，进行验证
    if (actionLibraryData.value.inputSchemaJson) {
      const schema = JSON.parse(actionLibraryData.value.inputSchemaJson);
      // 这里可以添加更详细的Schema验证
      if (schema.required) {
        const missing = schema.required.filter((field: string) => !(field in inputData));
        if (missing.length > 0) {
          MessagePlugin.error(`缺少必填字段: ${missing.join(', ')}`);
          return false;
        }
      }
    }
    
    MessagePlugin.success('输入数据验证通过');
    return true;
  } catch (error) {
    MessagePlugin.error(`输入数据格式错误: ${error.message}`);
    return false;
  }
};

// 执行测试
const executeTest = async () => {
  if (!validateInputData()) return;
  
  executing.value = true;
  try {
    const inputData = JSON.parse(inputDataJson.value);
    const result = await emits('test-execute', inputData);
    
    executionResult.value = {
      success: true,
      result: result.result,
      executionTime: result.executionTime,
      executionId: result.executionId,
      actionLibraryId: actionLibraryData.value.id!,
      actionLibraryName: actionLibraryData.value.name,
      startTime: new Date().toISOString(),
    };
    
    resultTab.value = 'result';
    
    // 刷新执行历史
    setTimeout(() => {
      loadExecutionHistory();
    }, 1000);
    
  } catch (error) {
    executionResult.value = {
      success: false,
      error: error.message,
      executionTime: 0,
      executionId: '',
      actionLibraryId: actionLibraryData.value.id!,
      actionLibraryName: actionLibraryData.value.name,
    };
    
    resultTab.value = 'error';
  } finally {
    executing.value = false;
  }
};

// 清空结果
const clearResults = () => {
  executionResult.value = null;
};

// 格式化结果
const formatResult = (result: any) => {
  if (result === null || result === undefined) {
    return 'null';
  }
  if (typeof result === 'string') {
    try {
      const parsed = JSON.parse(result);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return result;
    }
  }
  return JSON.stringify(result, null, 2);
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  switch (status) {
    case 'success': return 'success';
    case 'error': return 'danger';
    case 'timeout': return 'warning';
    default: return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功';
    case 'error': return '失败';
    case 'timeout': return '超时';
    default: return status;
  }
};

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

// 加载执行历史
const loadExecutionHistory = async () => {
  if (!actionLibraryData.value.id) return;
  
  loadingHistory.value = true;
  try {
    const data = await api.run(Services.actionLibraryGetExecutionLogs, {
      id: actionLibraryData.value.id,
      pageIndex: 1,
      pageSize: 10,
    });
    executionHistory.value = data.data || [];
  } catch (error) {
    MessagePlugin.error(`加载执行历史失败: ${error.message}`);
  } finally {
    loadingHistory.value = false;
  }
};

// 查看执行详情
const viewExecutionDetail = (execution: ExecutionLog) => {
  selectedExecution.value = execution;
  showDetailDialog.value = true;
};

// 重新执行
const rerunExecution = async (execution: ExecutionLog) => {
  if (execution.inputData) {
    inputDataJson.value = execution.inputData;
    await executeTest();
  } else {
    MessagePlugin.warning('该执行记录没有输入数据');
  }
};

onMounted(() => {
  if (actionLibraryData.value.id) {
    loadExecutionHistory();
  }
});
</script>

<style lang="less" scoped>
.action-library-test {
  max-width: 1200px;
  margin: 0 auto;

  .test-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .input-section,
    .result-section,
    .history-section {
      margin-bottom: 32px;

      .input-header,
      .result-header,
      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }

        .execution-time,
        .execution-id {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }

      .input-editor {
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        overflow: hidden;
      }

      .result-content,
      .error-content,
      .details-content {
        padding: 16px;
      }

      .error-content {
        .stack-trace {
          margin-top: 16px;

          h5 {
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
          }

          pre {
            background: var(--td-bg-color-code);
            padding: 12px;
            border-radius: 4px;
            font-size: 12px;
            line-height: 1.4;
            overflow: auto;
            max-height: 200px;
          }
        }
      }
    }
  }

  .execution-detail {
    .error-section {
      margin-top: 24px;

      .stack-trace {
        margin-top: 16px;

        h5 {
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
        }

        pre {
          background: var(--td-bg-color-code);
          padding: 12px;
          border-radius: 4px;
          font-size: 12px;
          line-height: 1.4;
          overflow: auto;
          max-height: 200px;
        }
      }
    }
  }
}
</style>
