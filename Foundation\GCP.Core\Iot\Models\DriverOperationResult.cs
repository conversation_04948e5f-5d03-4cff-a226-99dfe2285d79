namespace GCP.Iot.Models
{
    public class DriverOperationResult
    {
        /// <summary>
        /// 操作时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 原始值
        /// </summary>
        public object RawValue { get; set; }

        /// <summary>
        /// 转换后的值
        /// </summary>
        public object ProcessedValue { get; set; }

        /// <summary>
        /// 操作状态
        /// </summary>
        public OperationStatus Status { get; set; } = OperationStatus.Unknown;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 变量ID
        /// </summary>
        public string VariableId { get; set; }
    }

    public enum OperationStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 操作成功
        /// </summary>
        Success = 1,

        /// <summary>
        /// 操作失败
        /// </summary>
        Failed = 2,

        /// <summary>
        /// 设备未连接
        /// </summary>
        NotConnected = 3,

        /// <summary>
        /// 地址无效
        /// </summary>
        InvalidAddress = 4,

        /// <summary>
        /// 数据类型错误
        /// </summary>
        DataTypeError = 5,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout = 6
    }
}