<template>
  <div
    style="width: 100%"
    :title="description"
    @click="onClickShowMore"
    @mouseover.stop="showEdit = true"
    @mouseleave.stop="showEdit = false"
    @blur="showEdit = false"
  >
    <t-input v-if="(autoHideEdit && showEdit) || !autoHideEdit" v-bind="targetAttrs" class="value-input">
      <template #suffixIcon>
        <more-icon v-if="!autoHideEdit" />
      </template>
    </t-input>
    <div v-else :class="'value-input-text' + (disabled ? ' is-disabled' : '')">
      <div v-if="description">
        {{ description }}
      </div>
      <div v-else class="value-input-text-placeholder">{{ placeholder }}</div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ValueInput',
};
</script>
<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import { MoreIcon } from 'tdesign-icons-vue-next';
import { InputProps } from 'tdesign-vue-next';
import { computed, ref, useAttrs, watch, watchEffect } from 'vue';

import { useActionFlowStore } from '@/components/action-panel/store/index';

import { FlowDataValue } from './model';
import { getRandomId } from './utils';

const showEdit = ref(false);
const actionFlowStore = useActionFlowStore();

interface ValueInputProps extends Omit<InputProps, 'options'> {
  dataValue: FlowDataValue | undefined;
  onlyVariable?: boolean;
  limitTypes?: string[];
  autoHideEdit?: boolean;
}

const props = withDefaults(defineProps<ValueInputProps>(), {
  size: 'small',
  readonly: false,
  borderless: true,
  placeholder: '请输入或选择值',
  autoHideEdit: true,
});
const attrs: Partial<ValueInputProps> = useAttrs();
const targetAttrs = computed<ValueInputProps>(() => {
  return { ...attrs, ...props, modelValue: description.value };
});

const emits = defineEmits(['update:data-value', 'variable-change']);

const dataValue = ref<FlowDataValue>(
  props.dataValue || {
    type: props.onlyVariable ? 'variable' : 'text',
    dataType: props.limitTypes?.join(','),
    variableType: '',
    variableName: '',
    variableValue: '',
    textValue: '',
    scriptName: '',
    scriptValue: '',
  },
);

watchEffect(() => {
  dataValue.value = props.dataValue || {
    type: props.onlyVariable ? 'variable' : 'text',
    dataType: props.limitTypes?.join(','),
    variableType: '',
    variableName: '',
    variableValue: '',
    textValue: '',
    scriptName: '',
    scriptValue: '',
  };
});

const description = computed(() => {
  switch (dataValue.value.type) {
    case 'text':
      return dataValue.value.textValue;
    case 'variable':
      return dataValue.value.variableName || dataValue.value.variableValue;
    case 'script':
      return dataValue.value.scriptName || '未命名脚本';
    default:
      return '';
  }
});

const valueId = ref('');
const onClickShowMore = () => {
  actionFlowStore.isSaveValue = false;
  actionFlowStore.showValueDialog = true;
  valueId.value = getRandomId();
  actionFlowStore.currentValueInputData = {
    id: valueId.value,
    value: cloneDeep(dataValue.value),
    onlyVariable: props.onlyVariable,
    limitTypes: props.limitTypes,
  };
};

watch(
  () => actionFlowStore.isSaveValue,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && actionFlowStore.currentValueInputData?.id === valueId.value) {
      emits('update:data-value', actionFlowStore.currentValueInputData.value);
      emits('variable-change', actionFlowStore.currentValueInputData.value);
    }
  },
);
</script>
<style lang="less" scoped>
.value-input {
  :deep(> .t-input) {
    // background-color: var(--td-bg-color-component-disabled);
    cursor: pointer;

    > .t-input__inner {
      cursor: pointer;
    }
  }
}

.value-input-text {
  font-size: 12px;
  height: 24px;
  padding: 1px 8px;
  border: 1px solid transparent;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  position: relative;
  color: var(--td-text-color-primary);
  &.is-disabled {
    border: none;
    background-color: var(--td-bg-color-component-disabled);
  }
  .value-input-text-placeholder {
    color: #999;
  }
}
</style>
