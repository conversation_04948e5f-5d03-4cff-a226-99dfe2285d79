﻿using System.Linq.Expressions;

namespace GCP.DataAccess
{
    /// <summary>
    /// SqlBuilder Extension（中级扩展, 动态sql语句和参数）
    /// </summary>
    public static class QueryBuilderExtension
    {
        public static QuerySqlBuilder<T> Query<T>(this IDbContext db)
        {
            return new QuerySqlBuilder<T>(db);
        }
        public static QueryBuilder<T, T1> Query<T, T1>(this IDbContext db)
        {
            return new QueryBuilder<T, T1>(db);
        }
        public static QueryBuilder<T, T1, T2> Query<T, T1, T2>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2>(db);
        }
        public static QueryBuilder<T, T1, T2, T3> Query<T, T1, T2, T3>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2, T3>(db);
        }
        public static QueryBuilder<T, T1, T2, T3, T4> Query<T, T1, T2, T3, T4>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2, T3, T4>(db);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5> Query<T, T1, T2, T3, T4, T5>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5>(db);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6> Query<T, T1, T2, T3, T4, T5, T6>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6>(db);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Query<T, T1, T2, T3, T4, T5, T6, T7>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7>(db);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Query<T, T1, T2, T3, T4, T5, T6, T7, T8>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8>(db);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Query<T, T1, T2, T3, T4, T5, T6, T7, T8, T9>(this IDbContext db)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9>(db);
        }

        public static QuerySqlBuilder<T> Query<T>(this IDbContext db, Expression<Func<T, object>> exp)
        {
            return new QuerySqlBuilder<T>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1> Query<T, T1>(this IDbContext db, Expression<Func<T, T1, object>> exp)
        {
            return new QueryBuilder<T, T1>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2> Query<T, T1, T2>(this IDbContext db, Expression<Func<T, T1, T2, object>> exp)
        {
            return new QueryBuilder<T, T1, T2>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2, T3> Query<T, T1, T2, T3>(this IDbContext db, Expression<Func<T, T1, T2, T3, object>> exp)
        {
            return new QueryBuilder<T, T1, T2, T3>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2, T3, T4> Query<T, T1, T2, T3, T4>(this IDbContext db, Expression<Func<T, T1, T2, T3, T4, object>> exp)
        {
            return new QueryBuilder<T, T1, T2, T3, T4>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5> Query<T, T1, T2, T3, T4, T5>(this IDbContext db, Expression<Func<T, T1, T2, T3, T4, T5, object>> exp)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6> Query<T, T1, T2, T3, T4, T5, T6>(this IDbContext db, Expression<Func<T, T1, T2, T3, T4, T5, T6, object>> exp)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7> Query<T, T1, T2, T3, T4, T5, T6, T7>(this IDbContext db, Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, object>> exp)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8> Query<T, T1, T2, T3, T4, T5, T6, T7, T8>(this IDbContext db, Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, object>> exp)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8>(db).InitSubQuery(exp);
        }
        public static QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9> Query<T, T1, T2, T3, T4, T5, T6, T7, T8, T9>(this IDbContext db, Expression<Func<T, T1, T2, T3, T4, T5, T6, T7, T8, T9, object>> exp)
        {
            return new QueryBuilder<T, T1, T2, T3, T4, T5, T6, T7, T8, T9>(db).InitSubQuery(exp);
        }
    }
}
