namespace GCP.Common
{
    public interface IMessageBus : IAsyncDisposable
    {
        string Name { get; }
        MessageBusType Type { get; }
        bool IsConnected { get; }
        
        Task ConnectAsync(CancellationToken cancellationToken = default);
        Task DisconnectAsync(CancellationToken cancellationToken = default);
        
        Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default);
        Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default);
        Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default);
        Task SubscribeBatchAsync(string topic, Func<List<MessageEnvelope>, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default);
        Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default);
    }

    public interface IMessageBusFactory
    {
        Task<IMessageBus> CreateAsync(MessageBusOptions options);
    }

    public interface IMessageBusManager
    {
        IReadOnlyDictionary<string, IMessageBus> MessageBuses { get; }
        IReadOnlyDictionary<string, IMessageConsumer> Consumers { get; }
        
        Task<IMessageBus> AddMessageBusAsync(MessageBusOptions options, CancellationToken cancellationToken = default);
        Task RemoveMessageBusAsync(string name, CancellationToken cancellationToken = default);
        
        Task<IMessageConsumer> AddConsumerAsync(ConsumerOptions options, CancellationToken cancellationToken = default);
        Task RemoveConsumerAsync(string name, CancellationToken cancellationToken = default);
        
        Task StartAllAsync(CancellationToken cancellationToken = default);
        Task StopAllAsync(CancellationToken cancellationToken = default);
    }

    public interface IMessageConsumer : IAsyncDisposable
    {
        string Name { get; }
        ConsumerOptions Options { get; }
        bool IsRunning { get; }
        
        Task StartAsync(CancellationToken cancellationToken = default);
        Task StopAsync(CancellationToken cancellationToken = default);
    }
} 