# 数据集成平台

## 代码提交规范
| 类型       | 含义                           | 示例                               |
| ---------- | ------------------------------ | ---------------------------------- |
| build      | 构建系统或外部依赖的变更       | `build: 更新构建工具或依赖库`       |
| chore      | 项目配置或工具的变更           | `chore: 更新项目配置或工具`         |
| ci         | 持续集成 (CI) 配置的变更       | `ci: 更新 CI/CD 配置文件`           |
| docs       | 文档的变更                     | `docs: 更新文档`                    |
| feat       | 新功能的添加                   | `feat: 添加新功能`                  |
| fix        | 修复 bug                       | `fix: 修复 bug`                     |
| perf       | 性能优化的提交                 | `perf: 提交性能优化代码`            |
| refactor   | 代码重构，不涉及功能变更       | `refactor: 代码重构`                |
| revert     | 撤销之前的提交                 | `revert: 撤销之前的提交`            |
| style      | 代码风格变更                   | `style: 代码风格变更`               |
| test       | 添加或修改测试代码             | `test: 添加或修改测试代码`          |
| types      | TypeScript 类型定义文件的变更  | `types: 更新类型定义文件`            |