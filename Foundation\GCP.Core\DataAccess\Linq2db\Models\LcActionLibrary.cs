using LinqToDB.Mapping;

namespace GCP.DataAccess
{
    /// <summary>
    /// 动作库管理
    /// </summary>
    [Table("lc_action_library")]
    public class LcActionLibrary : IBaseEntity
    {
        /// <summary>
        /// Description:数据行 ID 号
        /// </summary>
        [Column("ID", CanBeNull = false, IsPrimaryKey = true)] public string Id { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:创建时间
        /// </summary>
        [Column("TIME_CREATE")] public DateTime TimeCreate { get; set; } // datetime
        /// <summary>
        /// Description:创建人
        /// </summary>
        [Column("CREATOR", CanBeNull = false)] public string Creator { get; set; } = null!; // varchar(80)
        /// <summary>
        /// Description:更新时间
        /// </summary>
        [Column("TIME_MODIFIED")] public DateTime? TimeModified { get; set; } // datetime
        /// <summary>
        /// Description:更新数据行的用户
        /// </summary>
        [Column("MODIFIER")] public string? Modifier { get; set; } // varchar(80)
        /// <summary>
        /// Description:可用状态
        /// </summary>
        [Column("STATE", CanBeNull = false)] public short State { get; set; } // smallint
        /// <summary>
        /// Description:解决方案 ID
        /// </summary>
        [Column("SOLUTION_ID", CanBeNull = false)] public string SolutionId { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:项目 ID
        /// </summary>
        [Column("PROJECT_ID", CanBeNull = false)] public string ProjectId { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:动作库名称
        /// </summary>
        [Column("NAME", CanBeNull = false)] public string Name { get; set; } = null!; // varchar(200)
        /// <summary>
        /// Description:动作库描述
        /// </summary>
        [Column("DESCRIPTION")] public string? Description { get; set; } // varchar(500)
        /// <summary>
        /// Description:动作库分类
        /// </summary>
        [Column("CATEGORY")] public string? Category { get; set; } // varchar(100)
        /// <summary>
        /// Description:标签，逗号分隔
        /// </summary>
        [Column("TAGS")] public string? Tags { get; set; } // varchar(500)
        /// <summary>
        /// Description:版本号
        /// </summary>
        [Column("VERSION", CanBeNull = false)] public int Version { get; set; } = 1; // int
        /// <summary>
        /// Description:状态 active、inactive、draft
        /// </summary>
        [Column("STATUS", CanBeNull = false)] public string Status { get; set; } = "active"; // varchar(20)
        /// <summary>
        /// Description:关联的函数流ID
        /// </summary>
        [Column("FUNCTION_FLOW_ID", CanBeNull = false)] public string FunctionFlowId { get; set; } = null!; // varchar(36)
        /// <summary>
        /// Description:关联的函数流版本
        /// </summary>
        [Column("FUNCTION_FLOW_VERSION")] public long? FunctionFlowVersion { get; set; } // bigint
        /// <summary>
        /// Description:输入参数Schema JSON
        /// </summary>
        [Column("INPUT_SCHEMA_JSON")] public string? InputSchemaJson { get; set; } // text
        /// <summary>
        /// Description:输出参数Schema JSON
        /// </summary>
        [Column("OUTPUT_SCHEMA_JSON")] public string? OutputSchemaJson { get; set; } // text
        /// <summary>
        /// Description:测试数据JSON
        /// </summary>
        [Column("TEST_DATA_JSON")] public string? TestDataJson { get; set; } // text
        /// <summary>
        /// Description:执行次数
        /// </summary>
        [Column("EXECUTION_COUNT", CanBeNull = false)] public long ExecutionCount { get; set; } = 0; // bigint
        /// <summary>
        /// Description:最后执行时间
        /// </summary>
        [Column("LAST_EXECUTION_TIME")] public DateTime? LastExecutionTime { get; set; } // datetime
        /// <summary>
        /// Description:平均执行时间(毫秒)
        /// </summary>
        [Column("AVERAGE_EXECUTION_TIME")] public int? AverageExecutionTime { get; set; } // int
    }
}
