// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 标签映射
	/// </summary>
	[Table("lc_tag_mapping")]
	public class LcTagMapping : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"           , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                          )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"      , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                        )] public DateTime? TimeModified { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                             )] public string?   Modifier     { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                )] public short     State        { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"  , CanBeNull = false                     )] public string    SolutionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"   , CanBeNull = false                     )] public string    ProjectId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:标签 ID
		/// </summary>
		[Column("TAG_ID"       , CanBeNull = false                     )] public string    TagId        { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:映射对象 ID
		/// </summary>
		[Column("OBJECT_ID"    , CanBeNull = false                     )] public string    ObjectId     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:映射对象类型 M:模型 F:函数 D:目录 P:项目
		/// </summary>
		[Column("OBJECT_TYPE"  , CanBeNull = false                     )] public string    ObjectType   { get; set; } = null!; // varchar(50)
	}
}
