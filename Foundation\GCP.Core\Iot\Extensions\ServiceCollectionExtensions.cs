using GCP.Iot.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.Iot.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddIotServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 注册设备通信管理器
            services.AddSingleton<EquipmentCommunicationManager>();
            // 注册驱动管理器
            services.AddSingleton<DriverManager>();
            // 注册驱动元数据服务
            services.AddSingleton<DriverMetadataService>();
            // 注册设备初始化器
            services.AddSingleton<EquipmentInitializer>();

            return services;
        }
    }
}