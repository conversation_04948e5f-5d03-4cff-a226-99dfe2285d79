<template>
  <t-tag v-bind="targetAttrs" :theme="methodTheme">{{ props.method }}</t-tag>
</template>
<script lang="ts">
export default {
  name: 'ApiMethodTag',
};
</script>
<script setup lang="ts">
import { TagProps } from 'tdesign-vue-next';
import { computed, useAttrs } from 'vue';

export interface ApiMethodTagProps extends Omit<TagProps, 'options'> {
  method?: string;
}

const props = withDefaults(defineProps<ApiMethodTagProps>(), {
  method: 'GET',
  variant: 'light',
});
const attrs: Partial<ApiMethodTagProps> = useAttrs();
const targetAttrs = computed<ApiMethodTagProps>(() => {
  return { ...attrs, ...props };
});
const methodTheme = computed(() => {
  switch (props.method) {
    case 'GET':
      return 'default';
    case 'POST':
      return 'primary';
    case 'PUT':
      return 'success';
    case 'PATCH':
      return 'warning';
    case 'DELETE':
      return 'danger';
    default:
      return 'default';
  }
});
</script>
<style lang="less" scoped></style>
