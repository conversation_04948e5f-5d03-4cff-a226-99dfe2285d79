<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col v-if="whileData.loopType === 'count'" :span="6">
            <t-form-item label="循环次数">
              <value-input
                v-model:data-value="whileData.loopCount"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="循环类型">
              <t-radio-group v-model="whileData.loopType" name="loopType" :options="options"></t-radio-group>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <action-form-title title="输出参数"> </action-form-title>
        <variable-list :data="currentStep.result"></variable-list>

        <div v-if="whileData.loopType === 'while' || whileData.loopType === 'doWhile'">
          <action-form-title title="条件"> </action-form-title>
          <script-editor-panel v-model:script="whileData.condition"></script-editor-panel>
        </div>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'LoopActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { watch } from 'vue';

import ValueInput from '@/components/action-panel/ValueInput.vue';
import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import ScriptEditorPanel from '@/components/action-panel/ScriptEditorPanel.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { useWhileStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataStore = useWhileStore();
const options = [
  { label: '次数循环', value: 'count' },
  { label: '条件循环', value: 'while' },
  { label: '执行一次后条件循环', value: 'doWhile' },
  { label: '无限循环（直至中断）', value: 'infinite' },
];

const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData, whileData } = storeToRefs(dataStore);

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataStore.updateState();
    dataStore.setArgs(formData.value);
  },
  {
    immediate: true,
  },
);

watch(
  () => ({
    ...formData.value,
    ...whileData.value,
  }),
  () => {
    dataStore.setArgs(formData.value);
  },
  {
    deep: true,
  },
);
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 706px;
  padding: 16px;
}
</style>
