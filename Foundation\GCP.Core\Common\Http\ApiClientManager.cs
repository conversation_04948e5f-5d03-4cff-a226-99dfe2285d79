using RestSharp;
using System.Collections.Concurrent;

namespace GCP.Common
{
    /// <summary>
    /// API客户端管理器，支持不同类型的API配置
    /// </summary>
    public class ApiClientManager : IDisposable
    {
        private readonly RestClientFactory _clientFactory;
        private readonly ConcurrentDictionary<string, ApiClientConfig> _apiConfigs;
        private bool _disposed = false;

        public ApiClientManager()
        {
            _clientFactory = new RestClientFactory();
            _apiConfigs = new ConcurrentDictionary<string, ApiClientConfig>();
        }

        /// <summary>
        /// 注册API配置
        /// </summary>
        public void RegisterApiConfig(string apiType, ApiClientConfig config)
        {
            _apiConfigs.AddOrUpdate(apiType, config, (key, oldValue) => config);
        }

        /// <summary>
        /// 获取指定类型的API客户端
        /// </summary>
        public RestClient GetClient(string apiType, int? overrideTimeoutSeconds = null)
        {
            if (!_apiConfigs.TryGetValue(apiType, out var config))
            {
                // 如果没有找到配置，使用默认配置
                config = ApiClientConfig.Default;
            }

            // 如果指定了覆盖超时时间，则使用覆盖值
            var timeoutSeconds = overrideTimeoutSeconds ?? config.TimeoutInSeconds;

            return _clientFactory.GetClient(
                timeoutSeconds, 
                config.BaseUrl, 
                config.DefaultHeaders
            );
        }

        /// <summary>
        /// 根据API数据获取客户端
        /// </summary>
        public RestClient GetClient(int? timeoutInSeconds = null, string baseUrl = null, Dictionary<string, string> additionalHeaders = null)
        {
            return _clientFactory.GetClient(timeoutInSeconds, baseUrl, additionalHeaders);
        }

        /// <summary>
        /// 清理指定类型的客户端缓存
        /// </summary>
        public void ClearCache(string apiType)
        {
            if (_apiConfigs.TryGetValue(apiType, out var config))
            {
                _clientFactory.ClearCache(config.TimeoutInSeconds, config.BaseUrl, config.DefaultHeaders);
            }
        }

        /// <summary>
        /// 清理所有缓存
        /// </summary>
        public void ClearAllCache()
        {
            _clientFactory.ClearAllCache();
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        public ApiClientStats GetStats()
        {
            return new ApiClientStats
            {
                CachedClientCount = _clientFactory.CachedClientCount,
                RegisteredApiTypes = _apiConfigs.Keys.ToList()
            };
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _clientFactory?.Dispose();
                _apiConfigs.Clear();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// API客户端配置
    /// </summary>
    public class ApiClientConfig
    {
        public int? TimeoutInSeconds { get; set; }
        public string BaseUrl { get; set; }
        public Dictionary<string, string> DefaultHeaders { get; set; }
        public bool EnableRetry { get; set; } = false;
        public int MaxRetries { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// 默认配置
        /// </summary>
        public static ApiClientConfig Default => new()
        {
            TimeoutInSeconds = 7200, // 2小时
            DefaultHeaders = new Dictionary<string, string>
            {
                { "Content-Type", "application/json; charset=utf-8" }
            }
        };

        /// <summary>
        /// 快速API配置（短超时）
        /// </summary>
        public static ApiClientConfig Fast => new()
        {
            TimeoutInSeconds = 30,
            DefaultHeaders = new Dictionary<string, string>
            {
                { "Content-Type", "application/json; charset=utf-8" }
            }
        };

        /// <summary>
        /// 长时间运行的API配置
        /// </summary>
        public static ApiClientConfig LongRunning => new()
        {
            TimeoutInSeconds = 3600, // 1小时
            DefaultHeaders = new Dictionary<string, string>
            {
                { "Content-Type", "application/json; charset=utf-8" }
            }
        };

        /// <summary>
        /// 文件上传API配置
        /// </summary>
        public static ApiClientConfig FileUpload => new()
        {
            TimeoutInSeconds = 1800, // 30分钟
            DefaultHeaders = new Dictionary<string, string>
            {
                // 文件上传不设置Content-Type，让RestSharp自动处理
            }
        };
    }

    /// <summary>
    /// API客户端统计信息
    /// </summary>
    public class ApiClientStats
    {
        public int CachedClientCount { get; set; }
        public List<string> RegisteredApiTypes { get; set; } = new();
    }

    /// <summary>
    /// 预定义的API类型常量
    /// </summary>
    public static class ApiTypes
    {
        public const string Default = "default";
        public const string Fast = "fast";
        public const string LongRunning = "long_running";
        public const string FileUpload = "file_upload";
        public const string ThirdParty = "third_party";
        public const string Internal = "internal";
        public const string Webhook = "webhook";
    }

    /// <summary>
    /// API客户端管理器的单例实现
    /// </summary>
    public static class GlobalApiClientManager
    {
        private static readonly Lazy<ApiClientManager> _instance = new(() =>
        {
            var manager = new ApiClientManager();
            
            // 注册预定义的配置
            manager.RegisterApiConfig(ApiTypes.Default, ApiClientConfig.Default);
            manager.RegisterApiConfig(ApiTypes.Fast, ApiClientConfig.Fast);
            manager.RegisterApiConfig(ApiTypes.LongRunning, ApiClientConfig.LongRunning);
            manager.RegisterApiConfig(ApiTypes.FileUpload, ApiClientConfig.FileUpload);
            
            return manager;
        });

        public static ApiClientManager Instance => _instance.Value;
    }
}
