<template>
  <div class="code-preview">
    <div class="code-preview-content" ref="codeContent" v-html="codeHtml"></div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'CodePreview',
};
</script>
<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core';
import { createHighlighter } from 'shiki';
import type { Highlighter } from 'shiki';
import { onUnmounted, ref, watch, nextTick } from 'vue';

const props = withDefaults(
  defineProps<{
    code?: string;
    lang?: string; // 支持多语言
    isDark?: boolean;
  }>(),
  {
    isDark: true,
  },
);

const isDark = ref(props.isDark);
const codeHtml = ref('');
const isInitializing = ref(false);
const codeContent = ref<HTMLElement | null>(null);
const highlighter = ref<Highlighter>();

const supportLangs = ['log', 'json', 'text', 'xml', 'sql', 'javascript'];

/** 初始化高亮器 */
async function initHighlighter() {
  if (highlighter.value) return;
  try {
    isInitializing.value = true;
    highlighter.value = await createHighlighter({
      themes: ['github-light', 'github-dark'],
      langs: supportLangs,
    });
  } catch (error) {
    console.error('初始化代码高亮失败:', error);
  } finally {
    isInitializing.value = false;
  }
}

/** 销毁高亮器 */
function highlighterDispose() {
  highlighter.value?.dispose();
  highlighter.value = void 0;
}

/** 生成高亮html */
async function highlighterCode() {
  if (!highlighter.value) await initHighlighter();
  if (!highlighter.value) return;
  const codeText = props.code ?? '';
  const lang = props.lang && supportLangs.includes(props.lang) ? props.lang : 'text';
  let html = '';
  try {
    html = highlighter.value.codeToHtml(codeText, {
      lang,
      theme: isDark.value ? 'github-dark' : 'github-light',
    });
  } catch {
    html = highlighter.value.codeToHtml(codeText, {
      lang: 'text',
      theme: isDark.value ? 'github-dark' : 'github-light',
    });
  }
  codeHtml.value = html;
  // 自动滚动到底部
  nextTick(() => {
    if (codeContent.value) {
      codeContent.value.scrollTop = codeContent.value.scrollHeight;
    }
  });
}

const watchFn = useDebounceFn(highlighterCode, 200);
watch([() => props.code, () => props.lang, () => props.isDark], () => {
  isDark.value = props.isDark ?? true;
  watchFn();
});

initHighlighter().then(highlighterCode);
onUnmounted(highlighterDispose);
</script>

<style lang="less" scoped>
.code-preview {
  height: 100%;
}
.code-preview-content {
  height: 100%;
  overflow: auto;
  background: transparent;
}

.code-preview-content :deep(.shiki) {
  white-space: pre;
  padding: 8px 0 8px 8px;
  margin-top: 0 !important;
  border-radius: 4px;
  height: 95%;
}
</style>
