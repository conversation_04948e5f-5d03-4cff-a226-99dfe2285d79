<template>
  <cmp-container full>
    <!-- 顶部 card  -->
    <top-panel :trigger-type="triggerType" />
    <!-- 中部图表  -->
    <middle-chart :trigger-type="triggerType" />
    <!-- 列表排名 -->
    <rank-list :trigger-type="triggerType" />
    <div class="trigger-container">
      <div class="trigger-item">
        <t-select style="width: 180px" label="统计：" v-model="triggerType" :options="triggerOptions" />
      </div>
    </div>
  </cmp-container>
</template>

<script lang="ts">
export default {
  name: 'ServiceBase',
};
</script>

<script setup lang="ts">
import { ref } from 'vue';
import MiddleChart from './components/MiddleChart.vue';
import RankList from './components/RankList.vue';
import TopPanel from './components/TopPanel.vue';

const triggerType = ref('');
const triggerOptions = ref([
  { label: '全部', value: '' },
  { label: 'API', value: 'API' },
  { label: '定时任务', value: 'JOB' },
  { label: '消息队列', value: 'EVENT' },
]);
</script>

<style lang="less" scoped>
.trigger-container {
  position: fixed;
  margin: auto;
  background-color: var(--bg-color-theme-transparent);
  padding: 8px;
  box-sizing: border-box;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);

  z-index: 999;
  transition: width 0.3s;
  left: 50%;
  user-select: auto;
  top: 5px;

  .trigger-item {
    :deep(.t-input) {
      border-radius: 6px;
      height: 32px;
      padding: 0 24px;
    }
  }
}
</style>
