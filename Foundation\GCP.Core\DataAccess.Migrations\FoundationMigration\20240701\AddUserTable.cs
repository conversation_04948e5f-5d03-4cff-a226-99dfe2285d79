﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20240701001000, "初始化用户表")]
    public class AddUserTable : Migration
    {
        public override void Up()
        {
            Create.Table("LC_USER").WithDescription("系统用户定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")

               .WithColumn("USER_NAME").AsAnsiString(36).WithColumnDescription("用户名")
               .WithColumn("DISPLAY_NAME").AsAnsiString(36).WithColumnDescription("显示名称")
               .WithColumn("PASSWORD").AsAnsiString(200).WithColumnDescription("密码")
               
               .WithColumn("EMAIL").AsAnsiString(200).Nullable().WithColumnDescription("邮箱")
               .WithColumn("PHONE").AsAnsiString(36).Nullable().WithColumnDescription("手机号")
               ;

            Create.Index("LC_USER_IDX")
                .OnTable("LC_USER")
                .OnColumn("USER_NAME").Ascending()
                .WithOptions().Unique();


            Create.Table("LC_ROLE").WithDescription("系统角色定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")

               .WithColumn("ROLE_CODE").AsAnsiString(36).WithColumnDescription("角色编码")
               .WithColumn("ROLE_NAME").AsAnsiString(200).WithColumnDescription("角色名称")
               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               ;

            Create.Index("LC_ROLE_IDX")
                .OnTable("LC_ROLE")
                .OnColumn("ROLE_CODE").Ascending()
                .WithOptions().Unique();



            Create.Table("LC_APP_USER").WithDescription("第三方用户定义")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")

               .WithColumn("USER_NAME").AsAnsiString(36).WithColumnDescription("用户名")
               .WithColumn("PASSWORD").AsAnsiString(200).WithColumnDescription("密码")

               .WithColumn("AUTH_TYPE").AsAnsiString(36).WithColumnDescription("授权类型")
               .WithColumn("CATEGORY").AsAnsiString(36).WithColumnDescription("分类")
               .WithColumn("APP_ID").AsAnsiString(36).WithColumnDescription("系统编码")
               .WithColumn("START_EFFECTIVE_DATE").AsDate().WithColumnDescription("开始生效时间")
               .WithColumn("END_EFFECTIVE_DATE").AsDate().Nullable().WithColumnDescription("结束生效时间")

               .WithColumn("DESCRIPTION").AsAnsiString(200).Nullable().WithColumnDescription("描述")
               ;

            Create.Index("LC_APP_USER_IDX")
                .OnTable("LC_APP_USER")
                .OnColumn("USER_NAME").Ascending()
                .OnColumn("APP_ID").Ascending()
                .WithOptions().Unique();
        }

        public override void Down()
        {
            Delete.Table("LC_USER");
            Delete.Table("LC_ROLE");
            Delete.Table("LC_APP_USER");
        }
    }
}
