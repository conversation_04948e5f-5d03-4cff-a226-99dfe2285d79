import dashboardBase from './dashboard-base';
import dashboardDetail from './dashboard-detail';
import detailBase from './detail-base';
import detailCard from './detail-card';
import detailDeploy from './detail-deploy';
import detailSecondary from './detail-secondary';
import formBase from './form-base';
import formStep from './form-step';
import listBase from './list-base';
import listCard from './list-card';
import listFilter from './list-filter';
import listTree from './list-tree';
import login from './login';
import result from './result';
import user from './user';

export default {
  dashboardBase,
  dashboardDetail,
  listBase,
  listCard,
  listFilter,
  listTree,
  detailBase,
  detailCard,
  detailDeploy,
  detailSecondary,
  formBase,
  formStep,
  user,
  login,
  result,
};
