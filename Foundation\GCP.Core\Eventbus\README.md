# Eventbus 消息总线

## 简介

Eventbus 是一个灵活的消息总线系统，支持多种消息中间件(Redis、RabbitMQ、Kafka、MQTT等)，提供统一的发布/订阅接口。

## 消费消息的方式

### 1. 传统回调方式

```csharp
// 订阅单条消息
await messageBus.SubscribeAsync("my-topic", async (message, ct) => 
{
    // 处理单条消息
    Console.WriteLine($"收到消息: {message.MessageId}");
}, options);

// 订阅批量消息
await messageBus.SubscribeBatchAsync("my-topic", async (messages, ct) => 
{
    // 处理批量消息
    Console.WriteLine($"收到批量消息: {messages.Count}条");
}, options);
```

### 2. IAsyncEnumerable方式 (推荐)

使用C# 8.0引入的`await foreach`语法，最简洁的消费方式：

```csharp
using GCP.Eventbus.Extensions;

// 单条消息消费
var messages = await messageBus.SubscribeAsEnumerableAsync("my-topic", options);
await foreach (var message in messages)
{
    // 处理单条消息
    Console.WriteLine($"收到消息: {message.MessageId}");
}

// 批量消息消费
var batches = await messageBus.SubscribeBatchAsEnumerableAsync("my-topic", options);
await foreach (var batch in batches)
{
    // 处理批量消息
    Console.WriteLine($"收到批量消息: {batch.Count}条");
}
```

这种方式使代码更加线性化，避免回调嵌套，可读性更强。内部使用System.Threading.Channels提供高效的消息处理能力。

## 配置选项

### 消费者选项

```csharp
var options = new ConsumerOptions
{
    Name = "consumer-name",
    Settings = new Dictionary<string, string>
    {
        // 批处理配置
        ["BatchSize"] = "10",        // 单批次最大消息数
        ["BatchTimeoutMs"] = "1000", // 批处理超时时间(ms)
    }
};
```

## 批处理说明

批处理消息满足以下任一条件时会触发处理：

1. 收集的消息数量达到BatchSize设定值
2. 时间间隔达到BatchTimeoutMs设定值(确保低频消息不会长期等待) 