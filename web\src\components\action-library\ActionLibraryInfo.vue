<template>
  <div class="action-library-info">
    <div class="form-header">
      <h2>基础信息</h2>
      <p>配置动作库的基本信息和属性</p>
    </div>

    <div class="form-body">
      <t-form ref="formRef" :data="formData" layout="vertical" :rules="rules">
        <t-row :gutter="24">
          <t-col :span="6">
            <t-form-item label="动作库名称" name="name">
              <t-input v-model="formData.name" placeholder="请输入动作库名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="分类" name="category">
              <t-select
                v-model="formData.category"
                :options="categoryOptions"
                placeholder="请选择或输入分类"
                filterable
                creatable
                @create="onCreateCategory"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="24">
          <t-col :span="6">
            <t-form-item label="状态" name="status">
              <t-select v-model="formData.status" :options="statusOptions" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="版本" name="version">
              <t-input-number v-model="formData.version" :min="1" readonly />
            </t-form-item>
          </t-col>
        </t-row>

        <t-form-item label="描述" name="description">
          <t-textarea
            v-model="formData.description"
            placeholder="请输入动作库描述"
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </t-form-item>

        <t-form-item label="标签" name="tags">
          <t-tag-input v-model="tagList" placeholder="请输入标签，按回车添加" @change="onTagsChange" />
        </t-form-item>

        <!-- 统计信息 -->
        <t-divider>统计信息</t-divider>
        <t-row :gutter="24">
          <t-col :span="4">
            <t-form-item label="执行次数">
              <t-input :value="formData.executionCount?.toString() || '0'" readonly />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="平均执行时间(ms)">
              <t-input :value="formData.averageExecutionTime?.toString() || '-'" readonly />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="最后执行时间">
              <t-input :value="formatDateTime(formData.lastExecutionTime)" readonly />
            </t-form-item>
          </t-col>
        </t-row>

        <!-- 创建和更新信息 -->
        <t-divider>创建信息</t-divider>
        <t-row :gutter="24">
          <t-col :span="4">
            <t-form-item label="创建者">
              <t-input :value="formData.createdBy || '-'" readonly />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="创建时间">
              <t-input :value="formatDateTime(formData.createdAt)" readonly />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="更新时间">
              <t-input :value="formatDateTime(formData.updatedAt)" readonly />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryInfo',
};
</script>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { api, Services } from '@/api/system';
import { MessagePlugin } from 'tdesign-vue-next';

interface ActionLibraryData {
  id?: string;
  name: string;
  description?: string;
  category?: string;
  tags?: string;
  status: string;
  version: number;
  executionCount: number;
  lastExecutionTime?: string;
  averageExecutionTime?: number;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

const props = defineProps<{
  modelValue: ActionLibraryData;
}>();

const emits = defineEmits<{
  'update:modelValue': [value: ActionLibraryData];
}>();

const formRef = ref();
const formData = ref<ActionLibraryData>({ ...props.modelValue });
const categories = ref<string[]>([]);

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入动作库名称', trigger: 'blur' },
    { max: 200, message: '名称长度不能超过200个字符', trigger: 'blur' },
  ],
  category: [{ max: 100, message: '分类长度不能超过100个字符', trigger: 'blur' }],
  description: [{ max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }],
  tags: [{ max: 500, message: '标签长度不能超过500个字符', trigger: 'blur' }],
};

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '激活', value: 'active' },
  { label: '停用', value: 'inactive' },
];

// 分类选项
const categoryOptions = computed(() => {
  return categories.value.map((cat) => ({ label: cat, value: cat }));
});

// 标签列表
const tagList = computed({
  get: () => {
    if (!formData.value.tags) return [];
    return formData.value.tags.split(',').filter((tag) => tag.trim());
  },
  set: (value: string[]) => {
    formData.value.tags = value.join(',');
  },
});

// 监听表单数据变化
watch(
  () => formData.value,
  (newValue) => {
    emits('update:modelValue', { ...newValue });
  },
  { deep: true },
);

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...newValue };
  },
  { deep: true },
);

// 加载分类列表
const loadCategories = async () => {
  try {
    const data = await api.run(Services.actionLibraryGetCategories);
    categories.value = data || [];
  } catch (error) {
    console.error('加载分类列表失败:', error);
  }
};

// 创建新分类
const onCreateCategory = (value: string) => {
  if (value && !categories.value.includes(value)) {
    categories.value.push(value);
  }
  formData.value.category = value;
};

// 标签变化处理
const onTagsChange = (value: string[]) => {
  formData.value.tags = value.join(',');
};

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

// 验证表单
const validate = async () => {
  try {
    await formRef.value?.validate();
    return true;
  } catch (error) {
    return false;
  }
};

onMounted(() => {
  loadCategories();
});

defineExpose({
  validate,
});
</script>

<style lang="less" scoped>
.action-library-info {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  max-width: 1200px;

  .form-header {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--td-text-color-secondary);
    }
  }

  .form-body {
    flex: 1;
    overflow-y: auto;
    max-width: 1000px;

    .t-form {
      background: var(--td-bg-color-container);
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .t-divider {
      margin: 40px 0 24px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .t-form-item {
      margin-bottom: 24px;

      :deep(.t-form__label) {
        font-weight: 500;
        color: var(--td-text-color-primary);
        margin-bottom: 8px;
      }
    }

    .t-input,
    .t-select,
    .t-textarea,
    .t-input-number {
      width: 100%;

      &[readonly] {
        background: var(--td-bg-color-component);
        border-color: var(--td-border-level-2-color);
      }
    }

    .t-tag-input {
      width: 100%;
    }

    .t-row {
      margin-bottom: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .action-library-info {
    .form-body {
      max-width: 100%;

      .t-form {
        padding: 24px 16px;
      }

      .t-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
