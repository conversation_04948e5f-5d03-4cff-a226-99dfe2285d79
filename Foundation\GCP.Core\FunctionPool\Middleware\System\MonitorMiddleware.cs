﻿using GCP.Common;
using System.Diagnostics;

namespace GCP.FunctionPool
{
    internal class MonitorMiddleware : IFunctionMiddleware
    {
        public static async Task Handler(FunctionContext ctx, Func<Task> next)
        {
            _ = ctx.Log.Write($"函数 {ctx.Current.Path} 入参： {JsonHelper.Serialize(ctx.Current.Args)}");
            Stopwatch sw = new Stopwatch();
            sw.Start();
            await next();
            sw.Stop();
            _ = ctx.Log.Write($"函数 {ctx.Current.Path} 出参： {JsonHelper.Serialize(ctx.Current.Result)}");
            _ = ctx.Log.Write($"函数 {ctx.Current.Path} 消耗时间为 {sw.Elapsed.FormatString()}");
        }
    }
}
