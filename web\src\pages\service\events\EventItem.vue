<template>
  <t-row class="event-item">
    <t-col flex="auto" class="item-info">
      <div>
        <span class="item-title">{{ props.name }}</span>
      </div>
      <div class="item-desc" :title="props.desc">{{ props.desc }}</div>
      <div class="item-right">
        <t-tag
          v-if="props.tag"
          :theme="props.enabled ? 'success' : 'danger'"
          :variant="props.enabled ? 'dark' : 'light'"
          >{{ props.tag }}</t-tag
        >
      </div>
    </t-col>
  </t-row>
</template>
<script lang="ts">
export default {
  name: 'EventItem',
};
</script>
<script setup lang="ts">
const props = defineProps<{
  name: string;
  desc: string;
  tag?: string;
  enabled?: boolean;
}>();
</script>
<style lang="less" scoped>
.event-item {
  cursor: pointer;
  padding: 5px 10px 5px 0;
  flex-wrap: nowrap;
  border-radius: 4px;
  width: 100%;

  > div {
    display: inline-block;
  }

  .item-info {
    margin-left: 8px;
    position: relative;
    .item-title {
      font-size: 14px;
      font-weight: 500;
    }

    .item-desc {
      color: var(--td-text-color-placeholder);
      font-size: 12px;
      font-family: consolas, monospace;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-right {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}

.ml-2 {
  margin-left: 8px;
}
</style>
