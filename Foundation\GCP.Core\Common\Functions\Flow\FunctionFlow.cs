﻿using System.Runtime.Serialization;

namespace GCP.Common
{
    [DataContract]
    [Serializable]
    public sealed class FunctionFlow
    {
        /// <summary>
        /// 流程ID
        /// </summary>
        [DataMember(Name = "id")]
        public string Id { get; set; }

        /// <summary>
        /// 流程版本
        /// </summary>
        [DataMember(Name = "version")]
        public int? Version { get; set; }

        /// <summary>
        /// 全局中间件, 可多个
        /// </summary>
        [DataMember(Name = "middleware")]
        public List<string> Middleware { get; set; }

        /// <summary>
        /// 环境变量+（每个步骤执行结果）
        /// </summary>
        [DataMember(Name = "data")]
        public List<FlowData> Data { get; set; }

        /// <summary>
        /// 是否持久化, 支持断点执行（默认否, 开启会影响性能）
        /// </summary>
        [DataMember(Name = "persistence")]
        public bool Persistence { get; set; }

        /// <summary>
        /// 扁平存放所有步骤配置信息
        /// </summary>
        [DataMember(Name = "body")]
        public List<FlowStep> Body { get; set; }
    }
}
