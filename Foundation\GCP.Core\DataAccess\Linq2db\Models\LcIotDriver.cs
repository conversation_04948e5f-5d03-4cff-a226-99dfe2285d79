// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 设备驱动参数配置
	/// </summary>
	[Table("lc_iot_driver")]
	public class LcIotDriver : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"           , CanBeNull = false, IsPrimaryKey = true)] public string    Id           { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                          )] public DateTime  TimeCreate   { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"      , CanBeNull = false                     )] public string    Creator      { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                        )] public DateTime? TimeModified { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                             )] public string?   Modifier     { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                )] public short     State        { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"  , CanBeNull = false                     )] public string    SolutionId   { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"   , CanBeNull = false                     )] public string    ProjectId    { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:驱动编码
		/// </summary>
		[Column("DRIVER_CODE"  , CanBeNull = false                     )] public string    DriverCode   { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:设备ID，为空时表示驱动全局参数
		/// </summary>
		[Column("EQUIPMENT_ID"                                         )] public string?   EquipmentId  { get; set; } // varchar(36)
		/// <summary>
		/// Description:参数键
		/// </summary>
		[Column("PARAM_KEY"    , CanBeNull = false                     )] public string    ParamKey     { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:参数值
		/// </summary>
		[Column("PARAM_VALUE"                                          )] public string?   ParamValue   { get; set; } // varchar(200)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                          )] public string?   Description  { get; set; } // varchar(200)
	}
}
