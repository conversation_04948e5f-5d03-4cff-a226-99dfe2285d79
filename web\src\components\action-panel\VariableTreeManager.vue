<template>
  <div class="variable-tree-manager">
    <!-- 变量树显示区域 -->
    <div class="tree-display">
      <t-tree
        ref="treeRef"
        v-model:actived="activeNodeId"
        :data="treeData"
        :expand-level="2"
        activable
        transition
        hover
        line
        @click="onNodeClick"
        @dblclick="onNodeDblClick"
      >
        <template #label="{ node }">
          <div class="tree-node-content">
            <div class="node-main">
              <div class="node-info">
                <span class="node-name">{{ node.label }}</span>
                <t-tag v-if="node.data.type" size="small" theme="success" variant="outline" class="node-type">
                  {{ getTypeLabel(node.data.type) }}
                </t-tag>
              </div>
              <!-- 操作按钮 -->
              <div v-if="!node.data.isRoot" class="node-actions">
                <t-button size="small" theme="primary" variant="text" @click.stop="onEditVariable(node.data)">
                  <template #icon>
                    <edit-icon />
                  </template>
                </t-button>
                <t-button size="small" theme="danger" variant="text" @click.stop="onDeleteVariable(node.data)">
                  <template #icon>
                    <delete-icon />
                  </template>
                </t-button>
              </div>
            </div>
            <div v-if="node.data.description" class="node-description">
              {{ node.data.description }}
            </div>
          </div>
        </template>
      </t-tree>
    </div>

    <!-- 根节点操作提示 -->
    <div v-if="!hasVariables" class="empty-state">
      <t-icon name="variable" size="32px" style="color: var(--td-text-color-placeholder)" />
      <p>根节点下暂无变量</p>
      <p class="empty-tip">点击"添加变量"或选择已有变量开始</p>
    </div>

    <!-- 变量统计信息 -->
    <div v-if="hasVariables" class="variable-stats">
      <t-space size="small">
        <t-tag theme="primary" variant="light" size="small"> 共 {{ props.variables.length }} 个变量 </t-tag>
        <t-button size="small" theme="default" variant="text" @click="expandAll"> 展开全部 </t-button>
        <t-button size="small" theme="default" variant="text" @click="collapseAll"> 收起全部 </t-button>
      </t-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { EditIcon, DeleteIcon } from 'tdesign-icons-vue-next';

interface VariableItem {
  id: string;
  name: string;
  type: string;
  source: 'input' | 'temp' | 'local' | 'global' | 'custom';
  value?: any;
  description?: string;
  selectedVariable?: string;
  valueInputType?: 'simple' | 'editor';
}

interface TreeNode {
  id: string;
  label: string;
  value: string;
  type?: string;
  description?: string;
  isRoot?: boolean;
  variableId?: string;
  children?: TreeNode[];
}

const props = defineProps<{
  variables: VariableItem[];
}>();

const emits = defineEmits<{
  'node-click': [data: any];
  'node-dblclick': [data: any];
  'edit-variable': [variable: VariableItem];
  'delete-variable': [variable: VariableItem];
}>();

const treeRef = ref();
const activeNodeId = ref<string[]>([]);

// 类型标签映射
const typeLabels = {
  string: '字符串',
  number: '数字',
  boolean: '布尔值',
  object: '对象',
  array: '数组',
};

const getTypeLabel = (type: string) => {
  return typeLabels[type] || type;
};

// 检查是否有变量
const hasVariables = computed(() => {
  return props.variables && props.variables.length > 0;
});

// 构建树形数据
const treeData = computed(() => {
  const rootNode: TreeNode = {
    id: 'ROOT',
    label: 'ROOT',
    value: 'ROOT',
    type: 'object',
    description: hasVariables.value ? '根节点' : '根节点（暂无变量）',
    isRoot: true,
    children: [],
  };

  if (!hasVariables.value) {
    return [rootNode];
  }

  // 构建嵌套结构
  const pathMap = new Map<string, TreeNode>();

  // 先创建所有路径节点
  props.variables.forEach((variable) => {
    const pathParts = variable.name.split('.');
    let currentPath = '';

    pathParts.forEach((part, index) => {
      const parentPath = currentPath;
      currentPath = currentPath ? `${currentPath}.${part}` : part;

      if (!pathMap.has(currentPath)) {
        const isLeaf = index === pathParts.length - 1;
        const node: TreeNode = {
          id: currentPath,
          label: part,
          value: currentPath,
          type: isLeaf ? variable.type : 'object',
          description: isLeaf ? variable.description : `${part} 容器`,
          isRoot: false,
          variableId: isLeaf ? variable.id : undefined,
          children: [],
        };
        pathMap.set(currentPath, node);
      }
    });
  });

  // 构建树形结构
  pathMap.forEach((node, path) => {
    const pathParts = path.split('.');
    if (pathParts.length === 1) {
      // 顶级节点，添加到根节点下
      rootNode.children!.push(node);
    } else {
      // 子节点，找到父节点
      const parentPath = pathParts.slice(0, -1).join('.');
      const parentNode = pathMap.get(parentPath);
      if (parentNode) {
        parentNode.children!.push(node);
      }
    }
  });

  return [rootNode];
});

// 节点点击事件
const onNodeClick = (data: any) => {
  emits('node-click', data);
};

// 节点双击事件
const onNodeDblClick = (data: any) => {
  emits('node-dblclick', data);
};

// 编辑变量
const onEditVariable = (nodeData: any) => {
  if (nodeData.variableId) {
    const variable = props.variables.find((v) => v.id === nodeData.variableId);
    if (variable) {
      emits('edit-variable', variable);
    }
  }
};

// 删除变量
const onDeleteVariable = (nodeData: any) => {
  if (nodeData.variableId) {
    const variable = props.variables.find((v) => v.id === nodeData.variableId);
    if (variable) {
      emits('delete-variable', variable);
    }
  }
};

// 展开/收起方法
const expandAll = () => {
  if (treeRef.value) {
    const allNodeIds: string[] = [];
    const collectNodeIds = (nodes: TreeNode[]) => {
      nodes.forEach((node) => {
        allNodeIds.push(node.id);
        if (node.children && node.children.length > 0) {
          collectNodeIds(node.children);
        }
      });
    };
    collectNodeIds(treeData.value);
    treeRef.value.setExpanded(allNodeIds, true);
  }
};

const collapseAll = () => {
  if (treeRef.value) {
    treeRef.value.setExpanded(['ROOT'], true); // 保持根节点展开
  }
};

// 展开根节点
watch(
  treeData,
  () => {
    if (treeRef.value && hasVariables.value) {
      // 默认展开根节点
      treeRef.value.setExpanded(['ROOT'], true);
    }
  },
  { immediate: true },
);

defineExpose({
  expandAll,
  collapseAll,
});
</script>

<style scoped>
.variable-tree-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-display {
  flex: 1;
  overflow-y: auto;
}

.tree-node-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.tree-node-content:hover {
  background-color: var(--td-bg-color-container-hover);
}

.node-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.node-name {
  font-size: 13px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-type {
  font-size: 11px;
  flex-shrink: 0;
}

.node-description {
  font-size: 11px;
  color: var(--td-text-color-secondary);
  margin-left: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.tree-node-content:hover .node-actions {
  opacity: 1;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--td-text-color-placeholder);
}

.empty-state p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.empty-tip {
  font-size: 12px;
  opacity: 0.8;
}

.variable-stats {
  padding: 8px 12px;
  background-color: var(--td-bg-color-container);
  border-radius: 4px;
  margin-top: 8px;
  border: 1px solid var(--td-border-level-1-color);
}

/* 树组件全局样式 */
:deep(.t-tree__label) {
  width: 100%;
  padding: 0;
}

:deep(.t-tree__item) {
  margin-bottom: 2px;
}

:deep(.t-tree__line) {
  border-left: 1px dashed var(--td-border-level-2-color);
}

/* 根节点特殊样式 */
:deep(.t-tree__item[data-value='ROOT']) {
  background: linear-gradient(135deg, var(--td-brand-color-1), var(--td-brand-color-2));
  border: 1px solid var(--td-brand-color-3);
  border-radius: 6px;
  margin-bottom: 12px;
  padding: 4px;
}

:deep(.t-tree__item[data-value='ROOT'] .tree-node-content) {
  background: transparent;
}

:deep(.t-tree__item[data-value='ROOT'] .tree-node-content:hover) {
  background: rgba(255, 255, 255, 0.1);
}

:deep(.t-tree__item[data-value='ROOT'] .node-name) {
  font-weight: 600;
  color: var(--td-brand-color);
  font-size: 14px;
}

/* 子节点样式优化 */
:deep(.t-tree__item:not([data-value='ROOT'])) {
  border-radius: 4px;
  transition: all 0.2s ease;
}

:deep(.t-tree__item:not([data-value='ROOT']):hover) {
  background-color: var(--td-bg-color-container-hover);
}

/* 展开/收起图标样式 */
:deep(.t-tree__icon) {
  color: var(--td-text-color-secondary);
  transition: all 0.2s ease;
}

:deep(.t-tree__icon:hover) {
  color: var(--td-brand-color);
}
</style>
