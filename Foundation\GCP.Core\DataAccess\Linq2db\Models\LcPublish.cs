// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 系统发布配置
	/// </summary>
	[Table("lc_publish")]
	public class LcPublish : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"              , CanBeNull = false, IsPrimaryKey = true)] public string    Id              { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                             )] public DateTime  TimeCreate      { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"         , CanBeNull = false                     )] public string    Creator         { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                           )] public DateTime? TimeModified    { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                                )] public string?   Modifier        { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                   )] public short     State           { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"     , CanBeNull = false                     )] public string    SolutionId      { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"      , CanBeNull = false                     )] public string    ProjectId       { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:环境名称
		/// </summary>
		[Column("ENVIRONMENT_NAME", CanBeNull = false                     )] public string    EnvironmentName { get; set; } = null!; // varchar(255)
		/// <summary>
		/// Description:类型（正式环境、其他）
		/// </summary>
		[Column("ENVIRONMENT_TYPE", CanBeNull = false                     )] public string    EnvironmentType { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                             )] public string?   Description     { get; set; } // varchar(255)
		/// <summary>
		/// Description:标签
		/// </summary>
		[Column("TAG"                                                     )] public string?   Tag             { get; set; } // varchar(255)
		/// <summary>
		/// Description:标签颜色
		/// </summary>
		[Column("TAG_COLOR"                                               )] public string?   TagColor        { get; set; } // varchar(50)
		/// <summary>
		/// Description:服务地址
		/// </summary>
		[Column("SERVICE_ADDRESS"                                         )] public string?   ServiceAddress  { get; set; } // varchar(255)
		/// <summary>
		/// Description:服务ID
		/// </summary>
		[Column("SERVICE_ID"                                              )] public string?   ServiceId       { get; set; } // varchar(255)
		/// <summary>
		/// Description:服务Secret
		/// </summary>
		[Column("SERVICE_SECRET"                                          )] public string?   ServiceSecret   { get; set; } // varchar(255)
	}
}
