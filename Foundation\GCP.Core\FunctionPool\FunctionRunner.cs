﻿using GCP.Common;
using GCP.Common.Job;
using GCP.DataAccess;
using GCP.FunctionPool.Builder;
using GCP.Functions.Common.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System.Collections.Concurrent;

namespace GCP.FunctionPool
{
    /// <summary>
    /// 函数运行器
    /// </summary>
    public class FunctionRunner : IFunctionRunner
    {
        internal static ConcurrentDictionary<string, Func<Task>> StartupRunDic { get; set; } = [];
        internal static ConcurrentBag<JobAttribute> LocalJobs { get; set; } = [];
        internal static bool IsAOT { get; set; } = false;

        public static void Init(IConfiguration config = null, bool isAOT = false)
        {
            config ??= (IConfiguration)ServiceLocator.Current.GetService(typeof(IConfiguration));
            var codeDir = config["functionPool:FileProvider"].ToString();
            IsAOT = isAOT;

            Log.Information("开始载入函数");
            //if (IsAOT)
            //{
            //    //var configType = typeof(IConfigurationManager);
            //    //var serviceType = typeof(BaseService);
            //}
            //else
            //{
            //    CSharpResolver.Init(); // 初始化
            //}

            //支持数据库保存函数后, 本地函数降级, 除非手动配置路径
            if (!string.IsNullOrEmpty(codeDir))
            {
                codeDir = codeDir.Replace('\\', '/');
            }

            // 载入dll函数
            DllFileResolver.Handler(codeDir);
            if (!IsAOT)
            {
                // 载入cs和js文件函数
                CodeFileResolver.Handler(codeDir);
            }

            Log.Information("载入本地函数 {count} 个", FunctionCompiler.DicFunction.Count);
        }

        internal static string CommonPipelineKey => "$CommonPipeline";
        public static void InitRun(bool autoRun = true)
        {
            ResiliencePipelineManager.TryAdd(CommonPipelineKey, () =>
            {
                var handle = new ResilienceHandle<object>(CommonPipelineKey);

                return handle.Build((ex) => throw (ex switch
                {
                    CustomException => ex,
                    OperationCanceledException => ex,
                    _ => ex.InnerException ?? ex
                }));
            });

            LoadDbFunction();

            if(!autoRun) return;
            Task.Run(async () =>
            {
                await Task.WhenAll(StartupRunDic.Select(t => t.Value.Invoke())).ConfigureAwait(false);
                Log.Information("执行 自启动 函数 {funKey}", StartupRunDic.Keys);

                new JobService().Refresh();
                if (!LocalJobs.IsEmpty)
                {
                    var job = ServiceLocator.Current.GetService(typeof(IJob)) as IJob;
                    foreach (var jobInfo in LocalJobs)
                    {
                        if (string.IsNullOrEmpty(jobInfo.Path)) continue;
                        job.AddOrUpdate(jobInfo.JobId, async () => await new FunctionRunner().Execute(jobInfo.Path), jobInfo.JobCron, jobInfo.JobName, jobInfo.Description);
                        Log.Information("添加 JOB {jobName}, 执行时间：{cronDescription}", jobInfo.JobName, CronHelper.GetCronDescription(jobInfo.JobCron));
                    }
                }
                //StartupRunDic.Clear();
                //LocalJobs.Clear();
            });
        }

        internal static void LoadDbFunction(string funcId = null)
        {
            var service = new FunctionCodeService();
            var data = service.GetAll(funcId);

            if (funcId == null)
            {
                Log.Information("载入数据库函数 {count} 个", data.Count);
            }

            //data.ForEach(item =>
            Parallel.ForEach(data, item =>
            {
                var resolverType = FunctionResolveType.PURE;
                if (item.FunctionType == "MIDDLEWARE")
                    resolverType = FunctionResolveType.MIDDLEWARE;
                else if (item.FunctionType == "FLOW")
                    resolverType = FunctionResolveType.FLOW;

                FunctionCodeLanguage codeLanguage = FunctionCodeLanguage.JavaScript;
                switch (item.CodeLanguage.ToLower())
                {
                    case "json":
                        codeLanguage = FunctionCodeLanguage.Json;
                        break;
                    //case "csharp":
                    //    codeLanguage = FunctionCodeLanguage.CSharp;
                    //    break;
                    case "javascript":
                        codeLanguage = FunctionCodeLanguage.JavaScript;
                        break;
                }
                FunctionCompiler.LoadCode(item.Id, item.Description, item.Code, resolverType: resolverType, codeLanguage: codeLanguage, (funcInfo) =>
                {
                    funcInfo.SolutionId = item.SolutionId;
                    funcInfo.ProjectId = item.ProjectId;
                });
            });
        }

        /// <summary>
        /// 执行函数
        /// </summary>
        /// <param name="data"></param>
        /// <param name="prepareContext"></param>
        /// <param name="middlewares"></param>
        /// <returns></returns>
        public async Task<object> Execute(FunctionInvokeDTO data, Func<FunctionContext, Task> prepareContext = null, params FunctionMiddlewareDelegate[] middlewares)
        {
            var context = new FunctionContext();
            context.Args = data.args ?? new Dictionary<string, object>();
            //context.db = ServiceLocator.GetScopedService<IDbContext>();
            context.LocalDbContext.Value = context.scope.ServiceProvider.GetRequiredService<IDbContext>();
            var funcInfo = GetFunctionInfo(data.path);
            if (funcInfo?.FunctionProxy == null)
            {
                throw new CustomException("未找到对应函数：" + data.path);
            }

            context.Path = data.path;
            context.SolutionId = funcInfo.SolutionId;
            context.ProjectId = funcInfo.ProjectId;
            
            if (prepareContext != null) await prepareContext(context).ConfigureAwait(false);
            return await Execute(funcInfo, context, middlewares).ConfigureAwait(false);
        }

        public async Task<object> Execute(string path, FunctionContext context = null, params FunctionMiddlewareDelegate[] middlewares)
        {
            var funcInfo = GetFunctionInfo(path);
            if (funcInfo?.FunctionProxy == null)
            {
                throw new CustomException("未找到对应函数：" + path);
            }
            return await Execute(funcInfo, context, middlewares).ConfigureAwait(false);
        }

        /// <summary>
        /// 执行函数
        /// </summary>
        /// <param name="path"></param>
        /// <param name="context"></param>
        /// <param name="middlewares"></param>
        /// <returns></returns>
        /// <exception cref="CustomException"></exception>
        internal async Task<object> Execute(FunctionInfo funcInfo, FunctionContext context = null, params FunctionMiddlewareDelegate[] middlewares)
        {
            bool noContext = context == null;
            if (noContext)
            {
                context = new FunctionContext();
                context.LocalDbContext.Value = context.scope.ServiceProvider.GetRequiredService<IDbContext>();
            }

            if (context.Current?.IsFlow != true)
            {
                var executionProvider = new FunctionProvider();
                executionProvider.Path = funcInfo.Path;
                executionProvider.FunctionName = funcInfo.FunctionName;

                if (context.Current == null)
                {
                    executionProvider.Args = context.Args;
                }

                context.Current = executionProvider;
            }
            else
            {
                context.Current.Path = funcInfo.Path;
                context.Current.FunctionName = funcInfo.FunctionName;
            }

            var middlewareList = new List<FunctionMiddlewareDelegate>();

            if (context.Middlewares != null)
            {
                middlewareList.AddRange(context.Middlewares);
            }

            if (middlewares != null && middlewares.Length > 0)
            {
                middlewareList.AddRange(middlewares);
            }

            try
            {
                if (middlewareList.Count > 0)
                {
                    middlewareList.Reverse();
                    middlewareList.Add(async (ctx, next) =>
                    {
                        await funcInfo.FunctionProxy.Invoke(ctx).ConfigureAwait(false);
                    });
                    await MiddlewareHandler.Compose(middlewareList.ToArray()).Invoke(context).ConfigureAwait(false);
                }
                else
                {
                    await funcInfo.FunctionProxy.Invoke(context).ConfigureAwait(false);
                }
            }
            finally
            {
                if (noContext)
                    context?.Dispose();
            }

            return context.Result ?? context.Current?.Result;
        }

        /// <summary>
        /// 获取代理中间件
        /// </summary>
        internal static FunctionMiddlewareDelegate GetMiddlewareProxy(string path)
        {
            if (FunctionCompiler.DicFunction.TryGetValue(path, out var result))
            {
                return result.MiddlewareProxy;
            }
            return null;
        }

        /// <summary>
        /// 获取代理函数
        /// </summary>
        internal static FunctionInfo GetFunctionInfo(string path)
        {
            if (FunctionCompiler.DicFunction.TryGetValue(path, out var result))
            {
                return result;
            }
            return null;
        }
    }
}
