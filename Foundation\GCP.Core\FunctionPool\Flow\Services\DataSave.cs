﻿using Acornima.Ast;
using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.Functions.Common;
using Jint;
using System.Collections;
using System.Data;
using System.Text;

namespace GCP.FunctionPool.Flow.Services
{
    internal class DataSaveTable
    {
        public bool IsExists { get; set; }
        public Engine ScriptEngine { get; set; }
        public int PkHashCode { get; set; }

        public IDictionary<string, object> OldColumnData { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, object> ColumnData { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, object> PkConditions { get; set; } = new Dictionary<string, object>();

        internal IDictionary<string, object> SourceItemDict { get; set; }
    }

    class DataSave : DataBaseService
    {

        private readonly string _itemKey = "dataSourceItem";
        private readonly string _currentRowKey = "currentRow";

        /// <summary>
        /// 安全地设置引擎变量，确保不会有状态残留
        /// 通过明确设置变量值来避免状态污染，而不是创建新引擎实例
        /// </summary>
        private void SetEngineVariablesSafely(Engine engine, Dictionary<string, object> variables)
        {
            // 明确设置所有相关变量，确保没有残留状态
            // 这比创建新引擎实例要高效得多
            foreach (var kvp in variables)
            {
                engine.SetValue(kvp.Key, kvp.Value);
            }

            // 如果当前变量中没有 currentRow，则明确设置为 null
            if (!variables.ContainsKey(_currentRowKey))
            {
                engine.SetValue(_currentRowKey, (object)null);
            }
        }

        [Function("dataSave", "数据库保存")]
        public async Task<object> SaveData(DataSaveData data)
        {
            var db = this.GetDataContext(data.DataSource);

            var listObj = this.GetDataValue(data.SourceDataPath);
            var sourceList = new ArrayList();
            if ((data.SourceDataPath.DataType != null && data.SourceDataPath.DataType != "array") || listObj is not ICollection list)
            {
                sourceList.Add(listObj);
            }
            else
            {
                sourceList = new ArrayList(list);
            }

            if (sourceList.Count == 0)
            {
                return ProcessSaveOutputResult(new List<Dictionary<string, object>>(), data.OutputConfig);
            }

            bool isSave = data.OperateType == DataSaveOperationType.Save;
            bool isUpdate = data.OperateType == DataSaveOperationType.Update;
            bool isInsert = data.OperateType == DataSaveOperationType.Insert;
            bool hasUpdateSource = (isSave || isInsert) && data.SourceUpdateInfo != null && data.SourceUpdateInfo.Count > 0;
            Dictionary<string, string[]> sourceUpdateInfo = new Dictionary<string, string[]>();
            if (hasUpdateSource)
            {
                var updateFields = data.SourceUpdateInfo.Values.Distinct().ToArray();
                foreach (var item in updateFields)
                {
                    sourceUpdateInfo.Add(item, data.SourceUpdateInfo.Where(t => t.Value == item).Select(t => t.Key).ToArray());
                }
            }

            var skipException = data.ExceptionSkip;
            if (data.ExceptionSkip && this.Context.GlobalTransaction.Value)
            {
                skipException = false;
                await this.Context.SqlLog.Warn("事务中不能跳过异常，已自动关闭异常跳过功能");
            }

            var saveTableList = await GetConditionValue(data.ConfigureInfo, sourceList, db, isUpdate, hasUpdateSource, sourceUpdateInfo, skipException);
            if (saveTableList == null || saveTableList.Count == 0)
            {
                await this.Context.SqlLog.Warn($"{this.Context.Current.StepName} 中没有匹配的条件，跳过执行", true);
                return ProcessSaveOutputResult(new List<Dictionary<string, object>>(), data.OutputConfig);
            }

            var sqlList = new SqlList(db);
            DataTable dt = null;
            int updateCount = 0;
            int insertCount = 0;

            if (isSave || isUpdate)
            {
                UpdateData(data.ConfigureInfo, sqlList, db, saveTableList, ref updateCount, hasUpdateSource, sourceUpdateInfo, skipException);
            }
            if (isSave || isInsert)
            {
                if (data.OpenFastInsert)
                {
                    dt = InsertData(data.ConfigureInfo, db, saveTableList, ref insertCount, hasUpdateSource, sourceUpdateInfo, skipException);
                }
                else
                {
                    InsertData(data.ConfigureInfo, sqlList, db, saveTableList, ref insertCount, hasUpdateSource, sourceUpdateInfo, skipException);
                }
            }

            if (sqlList.Count > 0 || dt?.Rows.Count > 0)
            {
                if (this.Context.Persistence)
                {
                    await using var conn = db.CreateConnection();
                    this.Context.Current.ArgsBuilder = new StringBuilder();
                    this.Context.Current.ArgsBuilder.AppendLine($"DbProvider: {db.DbProvider.ProviderType.ToString()}");
                    this.Context.Current.ArgsBuilder.AppendLine($"DataSource: {conn.DataSource}");
                    this.Context.Current.ArgsBuilder.AppendLine($"Database: {conn.Database}");
                    this.Context.Current.ArgsBuilder.AppendLine($"TableName: {data.ConfigureInfo.TableName}");
                    if (data.OpenFastInsert)
                    {
                        this.Context.Current.ArgsBuilder.AppendLine($"OpenFastInsert: {data.OpenFastInsert}");
                    }
                    else
                    {
                        this.Context.Current.ArgsBuilder.AppendLine($"BatchSize: {data.BatchSize}");
                    }
                    this.Context.Current.ArgsBuilder.AppendLine();

                    foreach (var t in sqlList)
                    {
                        this.Context.Current.ArgsBuilder.AppendLine(t.ExecutableSql + ";");
                    }
                }

                // 如果有全局事务，则在在批量执行时不打开事务，由外部控制
                var openTransaction = !this.Context.GlobalTransaction.Value;
                var rowsAffected = await sqlList.BatchExecute(dt, isRowByRow: skipException, openTransaction: openTransaction, exceptionSkip: data.ExceptionSkip, onException: async (ex, sql) =>
                {
                    await this.Context.SqlLog.Warn($"{data.Name} 表 {data.ConfigureInfo.TableName} 跳过执行异常：{ex.Message}，SQL：{sql}", true);
                }, batchSize: data.BatchSize).ConfigureAwait(false);


                await this.Context.SqlLog.Info($"保存成功 {data.Name}, 更新数据量 {updateCount} 新增数据量 {insertCount}, 影响行数 {rowsAffected}", true);
                //Log.Information("保存成功 {name}, 更新数据量 {updateCount} 新增数据量 {insertCount}", data.Name, updateCount, insertCount);
            }

            var resultList = saveTableList.Select(t => t.ColumnData).ToList();
            return ProcessSaveOutputResult(resultList, data.OutputConfig);
        }

        private async Task<List<DataSaveTable>> GetConditionValue(DataSourceTableData table, ArrayList sourceList, DbContext db, bool isUpdate, bool hasUpdateSource, Dictionary<string, string[]> sourceUpdateInfo, bool skipException)
        {
            var conditions = table.Columns.Where(t => t.IsCondition).ToList();
            if (conditions.Count == 0)
            {
                throw new CustomException("条件不能为空");
            }
            var columns = conditions.Select(t => t.ColumnName).ToList();
            var pkConditions = table.Columns.Where(t => t.IsPrimaryKey).ToList();
            if (pkConditions.Count == 0)
            {
                throw new CustomException("主键不能为空");
            }

            var sb = new SqlBuilder<dynamic>(db);
            sb.Append("SELECT ");

            sb.Append(columns.Count > 1 ? $"CONCAT({columns.Join(",'|',")}) as \"mkey\"" : $"{columns.First()} as \"mkey\"");
            sb.Append(pkConditions.Count > 1
                ? $", CONCAT({pkConditions.Select(t => t.ColumnName).Join(",'|',")}) as \"id\""
                : $", {pkConditions.First().ColumnName} as \"id\"");

            sb.Append(" FROM " + table.TableName);
            sb.Append(" WHERE 1=1");
            sb.Append("AND (" + columns.Join(",") + ")");
            sb.Append("in (");

            var localValues = new List<string>();
            var savaTableList = new List<DataSaveTable>();
            var count = 0;
            var paramCount = 0;
            var engine = GetEngine();

            //if (hasUpdateSource)
            //{
            //    conditions = table.Columns.Where(t => (t.IsCondition || sourceUpdateInfo.ContainsKey(t.ColumnName)) && !t.IsPrimaryKey).ToList();
            //}
            // 预编译脚本
            var preparedScripts = new Dictionary<string, Prepared<Script>>();

            // 处理每一行数据
            for (int i = sourceList.Count - 1; i >= 0; i--)
            {
                var item = sourceList[i];
                if (item is not IDictionary<string, object> itemDict)
                {
                    throw new CustomException($"数据源 不是字典数组");
                }

                var paramList = new Dictionary<string, object>();
                var paramSourceList = new Dictionary<string, object>();
                var currentVariables = new Dictionary<string, object>() { { _itemKey, itemDict } };
                engine.SetEngineValue(currentVariables);

                var localValue = "";

                var hashCode = 7;
                bool continueOuterLoop = false;

                // 处理每一列
                foreach (var column in conditions)
                {
                    try
                    {
                        object value;
                        if (column.ColumnValue?.Type == "script")
                        {
                            if (!preparedScripts.TryGetValue(column.ColumnName, out var preparedScript))
                            {
                                preparedScripts[column.ColumnName] = preparedScript = Engine.PrepareScript(column.ColumnValue.ScriptValue);
                            }
                            var jsValue = engine.Evaluate(preparedScript);
                            // MySql复合条件必须类型一致
                            value = jsValue.GetObject().Parse(column.DataType);
                        }
                        else
                        {
                            value = this.GetDataValue(column.ColumnValue, engine, currentVariables, column.DataType);
                        }

                        var valueStr = column.DataType == "long" ? value?.ToString() : value;
                        if (column.IsCondition)
                        {
                            if (column.Required && value == null)
                            {
                                throw new CustomException($"{this.Context.Current.StepName} 字段 {column.ColumnName} {column.Description} 未找到匹配值，请检查数据是否正确：{JsonHelper.Serialize(itemDict)}");
                            }
                            paramList.Add(db.DbProvider.GetParameterName("p_" + column.ColumnName + paramCount), value);
                            if (value != null)
                            {
                                hashCode = hashCode * 13 + value.GetHashCode();
                            }
                            paramCount++;

                            localValue += "|" + valueStr;
                        }

                        paramSourceList.Add(column.ColumnName, valueStr);

                        if (hasUpdateSource && sourceUpdateInfo.TryGetValue(column.ColumnName, out var updateFields))
                        {
                            foreach (var updateField in updateFields)
                            {
                                itemDict[updateField] = valueStr;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (skipException)
                        {
                            await this.Context.SqlLog.Warn($"{this.Context.Current.StepName} 表 {table.TableName} 字段 {column.ColumnName} 跳过异常：{ex.Message}，数据：{JsonHelper.Serialize(itemDict)}", true);
                            continueOuterLoop = true;
                            break;
                        }
                        else
                        {
                            throw;
                        }
                    }
                }
                if (continueOuterLoop)
                {
                    sourceList.RemoveAt(i);
                    continue;
                }

                localValues.Add(localValue.TrimStart('|'));

                if (count > 0)
                {
                    sb.Append(",");
                }
                sb.Append("(" + paramList.Select(t => t.Key).Join(",") + ")");
                sb.AddParameters(paramList);

                savaTableList.Add(new DataSaveTable
                {
                    ScriptEngine = engine, // 复用共享引擎，在使用时确保变量正确设置
                    ColumnData = paramSourceList,
                    PkHashCode = hashCode,
                    SourceItemDict = itemDict,
                });
                count++;
            }
            sb.Append(")");

            if (localValues.Count == 0)
            {
                return null;
            }

            Dictionary<string, object> dic = new Dictionary<string, object>();
            if (isUpdate)
            {
                dic = await sb.GetDictionaryAsync(t => t.id, t => t.mkey).ConfigureAwait(false);
            }
            else
            {
                try
                {
                    dic = await sb.GetDictionaryAsync(t => t.mkey, t => t.id).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    throw new CustomException($"唯一条件【{columns.Join("|")}】重复：{ex.Message}");
                }
            }

            var updateValues = new List<string>();
            var updateTableInfos = new List<DataSaveTable>();
            for (int i = localValues.Count - 1; i >= 0; i--)
            {
                var key = localValues.ElementAt(i);
                var saveTableInfo = savaTableList[i];

                List<string> updatePks = new List<string>();

                if (isUpdate)
                {
                    if (updateValues.Contains(key))
                    {
                        continue;
                    }
                    updateValues.Add(key);
                    updatePks = dic.Where(t => t.Value.ToString() == key).Select(t => t.Key).ToList();
                    saveTableInfo.IsExists = updatePks.Count > 0;
                }
                else
                {
                    if (dic.TryGetValue(key, out var value))
                    {
                        saveTableInfo.IsExists = value != null;
                        updatePks.Add(value.ToString());
                    }
                }

                if (!saveTableInfo.IsExists) continue;

                saveTableInfo.PkConditions = [];
                Dictionary<string, List<string>> pkDic = new Dictionary<string, List<string>>();

                var hasMultipleIds = false;
                for (int j = 0; j < pkConditions.Count; j++)
                {
                    var pk = pkConditions[j];
                    List<string> pkList = [];

                    if (j == 0)
                    {
                        pkDic.Add(pk.ColumnName, pkList);
                    }
                    else
                    {
                        if (pkDic.TryGetValue(pk.ColumnName, out var pkValueList))
                        {
                            pkList = pkValueList;
                        }
                    }

                    foreach (var item in updatePks)
                    {
                        var pkValues = item.Split('|');
                        var value = pkValues[j];

                        pkList.Add(value);

                        if (value == null)
                        {
                            throw new CustomException($"唯一键 {pk.ColumnName} 值不能为空：" + saveTableInfo.ColumnData.Join(", "));
                        }
                    }

                    if (pkList.Count <= 0) continue;

                    if (!hasMultipleIds)
                        hasMultipleIds = pkList.Count > 1;

                    object columnValue = pkList.Count == 1 ? pkList[0] : pkList;
                    saveTableInfo.PkConditions.Add(pk.ColumnName, columnValue);
                    saveTableInfo.ColumnData[pk.ColumnName] = columnValue;
                    saveTableInfo.PkHashCode = saveTableInfo.PkHashCode * 13 + columnValue.GetHashCode();

                    if (hasUpdateSource && sourceUpdateInfo.TryGetValue(pk.ColumnName, out var sourceFields))
                    {
                        foreach (var sourceField in sourceFields)
                        {
                            saveTableInfo.SourceItemDict[sourceField] = columnValue;
                        }
                    }
                }

                if (!hasMultipleIds)
                    updateTableInfos.Add(saveTableInfo);
            }

            await QueryOldDataAsync(db, table, pkConditions, updateTableInfos, hasUpdateSource, sourceUpdateInfo).ConfigureAwait(false);

            return savaTableList;
        }

        /// <summary>
        /// 查询旧数据，用于在更新时获取旧数据
        /// </summary>
        private async Task QueryOldDataAsync(DbContext db, DataSourceTableData table, List<ColumnInfo> pkConditions, List<DataSaveTable> updateTableInfos, bool hasUpdateSource, Dictionary<string, string[]> sourceUpdateInfo)
        {
            // 旧数据查询
            var oldDataColumns = table.Columns.Where(t => t.IsQuery).ToList();
            if (oldDataColumns.Count > 0 && updateTableInfos.Count > 0)
            {
                var pkColumns = pkConditions.Select(t => t.ColumnName).ToList();

                var sb = new SqlBuilder<dynamic>(db);
                sb.Append("SELECT ");
                foreach (var column in oldDataColumns)
                {
                    sb.Append(column.ColumnName + ", ");
                }

                sb.Append($"CONCAT({pkColumns.Join(",'|',")}) as _id");

                sb.Append(" FROM " + table.TableName);
                sb.Append(" WHERE 1=1");
                sb.Append("AND (" + pkColumns.Join(",") + ")");
                sb.Append("in (");

                var count = 0;
                foreach (var tableInfo in updateTableInfos)
                {
                    if (count > 0)
                    {
                        sb.Append(",");
                    }

                    sb.Append("(" + tableInfo.PkConditions.Select(t => t.Value).Join("','") + ")");

                    count++;
                }
                sb.Append(")");

                var dic = await sb.GetDictionaryAsync(t => t._id, t => t).ConfigureAwait(false);
                foreach (var tableInfo in updateTableInfos)
                {
                    var pkKey = tableInfo.PkConditions.Select(t => t.Value.ToString()).Join("|");
                    if (!(dic.TryGetValue(pkKey, out var value) && value is IDictionary<string, object> oldData))
                    {
                        // 如果没有找到对应的旧数据，记录警告日志
                        await this.Context.SqlLog.Warn($"未找到主键为 {pkKey} 的旧数据，currentRow 将为空", true);
                        continue;
                    }

                    // 清空之前的旧数据，确保不会残留上一行的数据
                    tableInfo.OldColumnData.Clear();

                    foreach (var column in oldData)
                    {
                        if (column.Key == "_id") continue;
                        //tableInfo.ColumnData[column.Key] = column.Value;
                        tableInfo.OldColumnData.Add(column.Key, column.Value);

                        if (!(hasUpdateSource && sourceUpdateInfo.TryGetValue(column.Key, out var sourceFields))) continue;

                        foreach (var sourceField in sourceFields)
                        {
                            tableInfo.SourceItemDict[sourceField] = column.Value;
                        }
                    }
                }
            }
        }

        private void UpdateData(DataSourceTableData table, SqlList sqlList, DbContext db, List<DataSaveTable> saveTableList, ref int updateCount, bool hasUpdateSource, Dictionary<string, string[]> sourceUpdateInfo, bool skipException)
        {
            var updateFields = table.Columns.Where(t => t.IsUpdate).ToList();
            if (updateFields.Count == 0)
            {
                throw new CustomException("更新字段不能为空");
            }

            var sb = new SqlBuilder<int>(db);

            sb.Append("UPDATE ");
            sb.Append(table.TableName);
            sb.Append(" SET ");
            bool isFirst = true;

            foreach (var field in updateFields)
            {
                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sb.Append(", ");
                }
                sb.Append(field.ColumnName);
                sb.Append(" = ");
                sb.Append(db.DbProvider.GetParameterName("v_set_" + field.ColumnName));
            }
            sb.Append(" WHERE 1=1");
            var sql = sb.ToString();

            // 预编译脚本
            var preparedScripts = new Dictionary<string, Prepared<Script>>();

            var repeatKeys = new HashSet<int>();
            Dictionary<int, List<Tuple<string[], object>>> repeatUpdateSourceValues = [];

            for (int i = saveTableList.Count - 1; i >= 0; i--)
            {
                var item = saveTableList[i].SourceItemDict;
                var condition = saveTableList[i];
                if (!condition.IsExists) continue; // 数据库不存在, 不更新

                List<Tuple<string[], object>> repeatList = [];
                if (HasRepeat(repeatKeys, repeatList, repeatUpdateSourceValues, hasUpdateSource, i, condition, saveTableList))
                {
                    continue;
                }

                var setParamDic = new Dictionary<string, object>();
                // 确保每次都重新设置正确的变量，避免使用之前行的数据
                var currentVariables = new Dictionary<string, object>() {
                    { _itemKey, item },
                    { _currentRowKey, condition.OldColumnData }
                };
                // 使用安全的变量设置方法，确保不会有状态残留
                SetEngineVariablesSafely(condition.ScriptEngine, currentVariables);

                bool continueOuterLoop = false;
                foreach (var field in updateFields)
                {
                    try
                    {
                        if (!condition.ColumnData.TryGetValue(field.ColumnName, out var value))
                        {
                            if (field.ColumnValue?.Type == "script")
                            {
                                if (!preparedScripts.TryGetValue(field.ColumnName, out var preparedScript))
                                {
                                    preparedScripts[field.ColumnName] = preparedScript = Engine.PrepareScript(field.ColumnValue.ScriptValue);
                                }
                                var jsValue = condition.ScriptEngine.Evaluate(preparedScript);
                                value = jsValue.GetObject();
                            }
                            else
                            {
                                value = this.GetDataValue(field.ColumnValue, condition.ScriptEngine, currentVariables);
                            }
                            condition.ColumnData[field.ColumnName] = value;

                            if (hasUpdateSource && sourceUpdateInfo.TryGetValue(field.ColumnName, out var sourceFields))
                            {
                                repeatList.Add(Tuple.Create(sourceFields, value));
                            }
                        }
                        if (field.Required && value == null)
                        {
                            throw new CustomException($"字段 {field.ColumnName} 不能为空：" + condition.ColumnData.Join(", "));
                        }

                        setParamDic.Add("v_set_" + field.ColumnName, value);
                    }
                    catch (Exception ex)
                    {
                        if (skipException)
                        {
                            _ = this.Context.SqlLog.Warn($"{this.Context.Current.StepName} 表 {table.TableName} 字段 {field.ColumnName} 跳过异常：{ex.Message}，数据：{condition.ColumnData.Join(", ")}", true);
                            continueOuterLoop = true;
                            break;
                        }
                        else
                        {
                            throw;
                        }
                    }
                }
                if (continueOuterLoop) continue;

                if (repeatList is { Count: > 0 })
                {
                    UpdateSourceData(repeatList, condition);
                }

                sb = new SqlBuilder<int>(db);
                sb.Init(sql, setParamDic);

                foreach (var dicItem in condition.PkConditions)
                {
                    if (dicItem.Value is ICollection valueList)
                    {
                        sb.AndIn(dicItem.Key, valueList);
                        updateCount += valueList.Count - 1;
                    }
                    else
                    {
                        sb.Append("AND " + dicItem.Key + " = {0}", dicItem.Value);
                    }
                }
                updateCount++;
                sqlList.AddUpdate(sb);
            }
        }

        private void InsertData(DataSourceTableData table, SqlList sqlList, DbContext db, List<DataSaveTable> saveTableList, ref int insertCount, bool hasUpdateSource, Dictionary<string, string[]> sourceUpdateInfo, bool skipException)
        {
            var insertFields = table.Columns.Where(t => t.IsInsert).ToList();
            if (insertFields.Count == 0)
            {
                throw new CustomException("新增字段不能为空");
            }

            var sb = new SqlBuilder<int>(db);

            var sbColumnList = new StringBuilder(null);
            var sbParameterList = new StringBuilder(null);
            bool isFirst = true;
            foreach (var field in insertFields)
            {
                if (isFirst)
                {
                    isFirst = false;
                }
                else
                {
                    sbColumnList.Append(", ");
                    sbParameterList.Append(", ");
                }
                sbColumnList.Append(field.ColumnName);
                sbParameterList.Append(db.DbProvider.GetParameterName(field.ColumnName));
            }

            var sql = $"INSERT INTO {table.TableName} ({sbColumnList}) VALUES ({sbParameterList})";

            // 预编译所有脚本
            var preparedScripts = new Dictionary<string, Prepared<Script>>();

            Dictionary<int, List<Tuple<string[], object>>> repeatUpdateSourceValues = [];
            var repeatKeys = new HashSet<int>();

            for (int i = saveTableList.Count - 1; i >= 0; i--)
            {
                var item = saveTableList[i].SourceItemDict;
                var condition = saveTableList[i];
                if (condition.IsExists) continue; // 存在, 不插入

                List<Tuple<string[], object>> repeatList = [];
                if (HasRepeat(repeatKeys, repeatList, repeatUpdateSourceValues, hasUpdateSource, i, condition, saveTableList))
                {
                    continue;
                }

                var paramDic = new Dictionary<string, object>();
                // 确保每次都重新设置正确的变量，避免使用之前行的数据
                var currentVariables = new Dictionary<string, object>() { { _itemKey, item } };
                // 使用安全的变量设置方法，确保不会有状态残留
                SetEngineVariablesSafely(condition.ScriptEngine, currentVariables);

                bool continueOuterLoop = false;
                foreach (var field in insertFields)
                {
                    try
                    {
                        if (!condition.ColumnData.TryGetValue(field.ColumnName, out var value))
                        {
                            if (field.ColumnValue?.Type == "script")
                            {
                                if (!preparedScripts.TryGetValue(field.ColumnName, out var preparedScript))
                                {
                                    preparedScripts[field.ColumnName] = preparedScript = Engine.PrepareScript(field.ColumnValue.ScriptValue);
                                }
                                var jsValue = condition.ScriptEngine.Evaluate(preparedScript);
                                value = jsValue.GetObject();
                            }
                            else
                            {
                                value = this.GetDataValue(field.ColumnValue, condition.ScriptEngine, currentVariables);
                            }
                            condition.ColumnData[field.ColumnName] = value;

                            if (hasUpdateSource && sourceUpdateInfo.TryGetValue(field.ColumnName, out var sourceFields))
                            {
                                repeatList.Add(Tuple.Create(sourceFields, value));
                            }
                        }
                        if (field.Required && value == null)
                        {
                            throw new CustomException($"字段 {field.ColumnName} 不能为空：" + condition.ColumnData.Join(", "));
                        }
                        paramDic.Add(field.ColumnName, value);
                    }
                    catch (Exception ex)
                    {
                        if (skipException)
                        {
                            _ = this.Context.SqlLog.Warn($"{this.Context.Current.StepName} 表 {table.TableName} 字段 {field.ColumnName} 跳过异常：{ex.Message}，数据：{condition.ColumnData.Join(", ")}", true);
                            continueOuterLoop = true;
                            break;
                        }
                        else
                        {
                            throw;
                        }
                    }
                }
                if (continueOuterLoop) continue;

                if (repeatList is { Count: > 0 })
                {
                    UpdateSourceData(repeatList, condition);
                }

                sb = new SqlBuilder<int>(db);
                sb.Init(sql, paramDic);
                insertCount++;
                sqlList.AddInsert(sb);
            }
        }

        private bool HasRepeat(HashSet<int> repeatKeys, List<Tuple<string[], object>> repeatList, Dictionary<int, List<Tuple<string[], object>>> repeatUpdateSourceValues, bool hasUpdateSource, int i, DataSaveTable condition, List<DataSaveTable> saveTableList)
        {
            var hasRepeat = !repeatKeys.Add(condition.PkHashCode);
            if (hasRepeat)
            {
                if (hasUpdateSource)
                {
                    if (repeatUpdateSourceValues.TryGetValue(condition.PkHashCode, out var tmpRepeatList) && tmpRepeatList?.Count > 0)
                    {
                        UpdateSourceData(tmpRepeatList, condition);
                    }
                }

                saveTableList.RemoveAt(i);
                // 数据重复则跳过
                return true;
            }
            else
            {
                if (hasUpdateSource)
                {
                    repeatUpdateSourceValues.Add(condition.PkHashCode, repeatList);
                }
            }
            return false;
        }

        private void UpdateSourceData(List<Tuple<string[], object>> repeatList, DataSaveTable saveTableInfo)
        {
            foreach (var item in repeatList)
            {
                foreach (var sourceField in item.Item1)
                {
                    saveTableInfo.SourceItemDict[sourceField] = item.Item2;
                }
            }
        }

        private DataTable InsertData(DataSourceTableData table, DbContext db, List<DataSaveTable> saveTableList, ref int insertCount, bool hasUpdateSource, Dictionary<string, string[]> sourceUpdateInfo, bool skipException)
        {
            var repeatKeys = new HashSet<int>();

            DataTable dt = new DataTable();
            dt.TableName = table.TableName;
            var insertFields = table.Columns.Where(t => t.IsInsert).ToList();
            if (insertFields.Count == 0) return dt;

            foreach (var field in insertFields)
                dt.Columns.Add(field.ColumnName);

            // 预编译所有脚本
            var preparedScripts = new Dictionary<string, Prepared<Script>>();

            Dictionary<int, List<Tuple<string[], object>>> repeatUpdateSourceValues = [];
            for (int i = 0; i < saveTableList.Count; i++)
            {
                var item = saveTableList[i].SourceItemDict;
                var condition = saveTableList[i];
                if (condition.IsExists) continue; // 存在, 不插入

                List<Tuple<string[], object>> repeatList = [];
                if (HasRepeat(repeatKeys, repeatList, repeatUpdateSourceValues, hasUpdateSource, i, condition, saveTableList))
                {
                    continue;
                }

                var sb = new SqlBuilder<int>(db);
                // 确保每次都重新设置正确的变量，避免使用之前行的数据
                var currentVariables = new Dictionary<string, object>() { { _itemKey, item } };
                // 使用安全的变量设置方法，确保不会有状态残留
                SetEngineVariablesSafely(condition.ScriptEngine, currentVariables);

                var row = dt.NewRow();
                bool continueOuterLoop = false;
                foreach (var field in insertFields)
                {
                    try
                    {
                        if (!condition.ColumnData.TryGetValue(field.ColumnName, out var value))
                        {
                            if (field.ColumnValue?.Type == "script")
                            {
                                if (!preparedScripts.TryGetValue(field.ColumnName, out var preparedScript))
                                {
                                    preparedScripts[field.ColumnName] = preparedScript = Engine.PrepareScript(field.ColumnValue.ScriptValue);
                                }
                                var jsValue = condition.ScriptEngine.Evaluate(preparedScript);
                                value = jsValue.GetObject();
                            }
                            else
                            {
                                value = this.GetDataValue(field.ColumnValue, condition.ScriptEngine, currentVariables);
                            }
                            condition.ColumnData[field.ColumnName] = value;

                            if (hasUpdateSource && sourceUpdateInfo.TryGetValue(field.ColumnName, out var sourceFields))
                            {
                                repeatList.Add(Tuple.Create(sourceFields, value));
                            }
                        }
                        if (field.Required && value == null)
                        {
                            throw new CustomException($"字段 {field.ColumnName} 不能为空：" + condition.ColumnData.Join(", "));
                        }
                        row[field.ColumnName] = value;
                    }
                    catch (Exception ex)
                    {
                        if (skipException)
                        {
                            _ = this.Context.SqlLog.Warn($"{this.Context.Current.StepName} 表 {table.TableName} 字段 {field.ColumnName} 跳过异常：{ex.Message}，数据：{condition.ColumnData.Join(", ")}", true);
                            continueOuterLoop = true;
                            break;
                        }
                        else
                        {
                            throw;
                        }
                    }
                }

                if (continueOuterLoop) continue;

                if (repeatList is { Count: > 0 })
                {
                    UpdateSourceData(repeatList, condition);
                }

                insertCount++;
                dt.Rows.Add(row);
            }
            return dt;
        }

        /// <summary>
        /// 根据输出配置处理保存结果
        /// </summary>
        private static object ProcessSaveOutputResult(List<Dictionary<string, object>> result, DataSaveOutputConfig outputConfig)
        {
            if (outputConfig == null || outputConfig.Type == DataSaveOutputType.None)
            {
                // 无输出，返回空列表
                return new List<Dictionary<string, object>>();
            }

            if (outputConfig.Type == DataSaveOutputType.List)
            {
                // 列表输出，返回原始结果
                return result;
            }

            if (outputConfig.Type == DataSaveOutputType.Dictionary)
            {
                // 字典输出，构建字典
                return BuildSaveDictionary(result, outputConfig.DictionaryConfig);
            }

            // 默认返回列表
            return result;
        }

        /// <summary>
        /// 构建保存结果字典
        /// </summary>
        private static Dictionary<string, object> BuildSaveDictionary(List<Dictionary<string, object>> result, DataSaveDictionaryConfig dictionaryConfig)
        {
            var dictionary = new Dictionary<string, object>();

            if (dictionaryConfig == null || dictionaryConfig.KeyColumns == null || dictionaryConfig.KeyColumns.Count == 0)
            {
                return dictionary;
            }

            foreach (var item in result)
            {
                try
                {
                    // 构建键
                    var keyParts = new List<string>();
                    foreach (var keyColumn in dictionaryConfig.KeyColumns)
                    {
                        if (item.ContainsKey(keyColumn))
                        {
                            keyParts.Add(item[keyColumn]?.ToString() ?? "");
                        }
                        else
                        {
                            keyParts.Add("");
                        }
                    }
                    var key = string.Join(dictionaryConfig.KeySeparator ?? "|", keyParts);

                    // 构建值
                    object value;
                    if (dictionaryConfig.UseFullObjectAsValue)
                    {
                        value = item;
                    }
                    else if (!string.IsNullOrEmpty(dictionaryConfig.ValueColumn) && item.ContainsKey(dictionaryConfig.ValueColumn))
                    {
                        value = item[dictionaryConfig.ValueColumn];
                    }
                    else
                    {
                        value = item;
                    }

                    dictionary[key] = value;
                }
                catch
                {
                    // 忽略构建失败的项
                }
            }

            return dictionary;
        }
    }
}
