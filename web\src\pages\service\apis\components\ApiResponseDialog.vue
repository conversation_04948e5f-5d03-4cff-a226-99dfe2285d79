<template>
  <t-dialog
    v-model:visible="visible"
    header="API响应体管理"
    width="1400px"
    height="800px"
    :footer="false"
    @close="onClose"
  >
    <div class="api-response-dialog">
      <div class="dialog-content">
        <!-- 左侧：响应体列表 -->
        <div class="left-panel">
          <div class="response-list">
            <div class="response-list-header">
              <t-space>
                <span>响应体列表</span>
                <t-button size="small" @click="onAddResponse">
                  <template #icon><t-icon name="add" /></template>
                  新增
                </t-button>
              </t-space>
            </div>
            <div class="response-list-content">
              <div
                v-for="item in responseList"
                :key="item.id"
                :class="['response-item', { active: currentResponse?.id === item.id }]"
                @click="onSelectResponse(item)"
              >
                <div class="response-content">
                  <div class="response-name">{{ item.responseName }}</div>
                  <div class="response-code">{{ item.responseCode }}</div>
                </div>
                <div class="response-actions">
                  <t-tooltip content="删除">
                    <t-button
                      size="small"
                      shape="square"
                      variant="text"
                      style="color: var(--td-error-color)"
                      @click.stop="onDeleteResponse(item)"
                    >
                      <template #icon><t-icon name="delete" /></template>
                    </t-button>
                  </t-tooltip>
                </div>
              </div>
              <div v-if="responseList.length === 0" class="empty-list">
                <t-empty description="暂无响应体配置" />
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：响应体管理 -->
        <div class="right-panel">
          <div class="response-config-container">
            <div v-if="currentResponse" class="response-config">
              <!-- 基本信息 -->
              <div class="basic-info">
                <action-form-title title="基本信息" />
                <t-form :data="currentResponse" label-width="100px">
                  <t-row :gutter="[16, 16]">
                    <t-col :span="12">
                      <t-form-item label="响应体编码" name="responseCode">
                        <t-input v-model="currentResponse.responseCode" placeholder="请输入响应体编码" />
                      </t-form-item>
                    </t-col>
                    <t-col :span="12">
                      <t-form-item label="响应体名称" name="responseName">
                        <t-input v-model="currentResponse.responseName" placeholder="请输入响应体名称" />
                      </t-form-item>
                    </t-col>
                    <t-col :span="12">
                      <t-form-item label="描述" name="description">
                        <t-input v-model="currentResponse.description" placeholder="请输入描述" />
                      </t-form-item>
                    </t-col>
                  </t-row>
                </t-form>
              </div>

              <!-- 响应体结构定义 -->
              <div class="response-structure">
                <action-form-title title="响应体结构" />
                <variable-list
                  v-model:data="responseStructure"
                  show-root-node
                  @update:data="onResponseStructureChange"
                />
              </div>

              <!-- 特殊字段配置 -->
              <div class="special-fields">
                <action-form-title title="特殊字段配置" />
                <t-row :gutter="[16, 16]">
                  <t-col :span="8">
                    <div class="field-config-section">
                      <h4>成功标识</h4>
                      <p class="help-text">配置哪个字段等于什么值表示成功</p>
                      <div v-for="(flag, index) in successFlags" :key="index" class="success-flag-item">
                        <t-space direction="vertical" style="width: 100%">
                          <field-selector v-model="flag.field" :tree-data="responseStructure" placeholder="选择字段" />
                          <t-input v-model="flag.value" placeholder="期望值" />
                          <t-button size="small" variant="text" @click="removeSuccessFlag(index)">
                            <template #icon><t-icon name="remove" /></template>
                            删除
                          </t-button>
                        </t-space>
                      </div>
                      <t-button
                        size="small"
                        variant="dashed"
                        @click="addSuccessFlag"
                        style="width: 100%; margin-top: 8px"
                      >
                        <template #icon><t-icon name="add" /></template>
                        添加成功标识
                      </t-button>
                    </div>
                  </t-col>
                  <t-col :span="8">
                    <div class="field-config-section">
                      <h4>错误消息字段</h4>
                      <p class="help-text">指定包含错误信息的字段</p>
                      <field-selector
                        v-model="errorMessageField"
                        :tree-data="responseStructure"
                        placeholder="选择错误消息字段"
                      />
                    </div>
                  </t-col>
                  <t-col :span="8">
                    <div class="field-config-section">
                      <h4>数据字段</h4>
                      <p class="help-text">指定包含实际数据的字段</p>
                      <field-selector v-model="resultField" :tree-data="responseStructure" placeholder="选择数据字段" />
                    </div>
                  </t-col>
                </t-row>
              </div>

              <!-- 操作按钮 -->
              <div class="response-actions-bar">
                <t-space>
                  <t-button theme="primary" @click="onSaveResponse">保存</t-button>
                  <t-button variant="outline" @click="onSelectForApi">选择此响应体</t-button>
                </t-space>
              </div>
            </div>
            <div v-else class="no-response">
              <t-empty description="请选择或新增一个响应体" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import VariableList from '@/components/action-panel/VariableList.vue';
import FieldSelector from './FieldSelector.vue';
import { FlowData } from '@/components/action-panel/model';

interface ApiResponse {
  id?: string;
  responseCode: string;
  responseName: string;
  responseType: string;
  description: string;
  responseData: string;
  successFlag: string;
  errorMessage: string;
  result: string;
}

interface SuccessFlag {
  field: string;
  value: string;
}

const props = defineProps<{
  modelValue: boolean;
  apiId?: string;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  select: [response: ApiResponse];
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const responseList = ref<ApiResponse[]>([]);
const currentResponse = ref<ApiResponse | null>(null);
const responseStructure = ref<FlowData[]>([]);
const successFlags = ref<SuccessFlag[]>([]);
const errorMessageField = ref('');
const resultField = ref('');

// 监听当前响应体变化，同步特殊字段配置
watch(
  currentResponse,
  (newResponse) => {
    if (newResponse) {
      // 解析响应体结构
      try {
        const parsedData = newResponse.responseData ? JSON.parse(newResponse.responseData) : [];
        // 如果没有数据或者数据为空，创建一个基本的ROOT结构
        if (!parsedData || parsedData.length === 0) {
          responseStructure.value = [
            {
              id: 'ROOT',
              key: 'ROOT',
              type: 'object',
              value: { type: 'text', textValue: '' },
              description: '根节点',
              required: false,
              children: [],
            },
          ];
        } else {
          responseStructure.value = parsedData;
        }
        console.log('解析的响应体结构:', responseStructure.value);
      } catch {
        // 解析失败时创建基本ROOT结构
        responseStructure.value = [
          {
            id: 'ROOT',
            key: 'ROOT',
            type: 'object',
            value: { type: 'text', textValue: '' },
            description: '根节点',
            required: false,
            children: [],
          },
        ];
      }

      // 解析成功标识
      try {
        const flags = newResponse.successFlag ? JSON.parse(newResponse.successFlag) : {};
        successFlags.value = Object.entries(flags).map(([field, value]) => ({ field, value: String(value) }));
      } catch {
        successFlags.value = [];
      }

      // 解析错误消息字段
      try {
        const errorConfig = newResponse.errorMessage ? JSON.parse(newResponse.errorMessage) : {};
        errorMessageField.value = (Object.values(errorConfig)[0] as string) || '';
      } catch {
        errorMessageField.value = newResponse.errorMessage || '';
      }

      // 解析数据字段
      try {
        const resultConfig = newResponse.result ? JSON.parse(newResponse.result) : {};
        resultField.value = (Object.values(resultConfig)[0] as string) || '';
      } catch {
        resultField.value = newResponse.result || '';
      }
    }
  },
  { immediate: true },
);

const fetchResponseList = async () => {
  try {
    const data = await api.run(Services.apiGetResponseList);
    responseList.value = data;
  } catch (error) {
    MessagePlugin.error('获取响应体列表失败');
  }
};

const onAddResponse = () => {
  const newResponse: ApiResponse = {
    responseCode: '',
    responseName: '新响应体',
    responseType: 'json', // 固定为JSON
    description: '',
    responseData: JSON.stringify([
      {
        id: 'ROOT',
        key: 'ROOT',
        type: 'object',
        value: { type: 'text', textValue: '' },
        description: '根节点',
        required: false,
        children: [],
      },
    ]),
    successFlag: '{}',
    errorMessage: '{}',
    result: '{}',
  };
  currentResponse.value = newResponse;
};

const onSelectResponse = (response: ApiResponse) => {
  currentResponse.value = { ...response };
};

const onDeleteResponse = async (response: ApiResponse) => {
  if (!response.id) return;

  try {
    await api.run(Services.apiDeleteResponse, { id: response.id });
    MessagePlugin.success('删除成功');
    await fetchResponseList();
    if (currentResponse.value?.id === response.id) {
      currentResponse.value = null;
    }
  } catch (error) {
    MessagePlugin.error('删除失败');
  }
};

const addSuccessFlag = () => {
  successFlags.value.push({ field: '', value: '' });
};

const removeSuccessFlag = (index: number) => {
  successFlags.value.splice(index, 1);
};

const onResponseStructureChange = (newData: FlowData[]) => {
  console.log('响应体结构数据变化:', newData);
  responseStructure.value = newData;
};

const onSaveResponse = async () => {
  if (!currentResponse.value) return;

  // 验证必填字段
  if (!currentResponse.value.responseCode?.trim()) {
    MessagePlugin.error('请输入响应体编码');
    return;
  }
  if (!currentResponse.value.responseName?.trim()) {
    MessagePlugin.error('请输入响应体名称');
    return;
  }

  // 检查响应体结构数据
  console.log('当前响应体结构:', responseStructure.value);
  console.log('响应体结构长度:', responseStructure.value?.length);

  // 确保响应体结构不为空（至少有ROOT节点）
  if (!responseStructure.value || responseStructure.value.length === 0) {
    MessagePlugin.warning('请先定义响应体结构');
    return;
  }

  // 检查是否只有空的ROOT节点，如果是则提示用户添加字段
  const hasOnlyEmptyRoot =
    responseStructure.value.length === 1 &&
    responseStructure.value[0].key === 'ROOT' &&
    (!responseStructure.value[0].children || responseStructure.value[0].children.length === 0);

  if (hasOnlyEmptyRoot) {
    MessagePlugin.warning('请在响应体结构中添加字段定义');
    return;
  }

  // 构建保存数据
  const saveData = {
    ...currentResponse.value,
    responseData: JSON.stringify(responseStructure.value),
    successFlag: JSON.stringify(
      successFlags.value.reduce(
        (acc, flag) => {
          if (flag.field && flag.value) {
            acc[flag.field] = flag.value;
          }
          return acc;
        },
        {} as Record<string, string>,
      ),
    ),
    errorMessage: errorMessageField.value ? JSON.stringify({ errorMessage: errorMessageField.value }) : '{}',
    result: resultField.value ? JSON.stringify({ result: resultField.value }) : '{}',
  };

  console.log('保存数据:', saveData);
  console.log('responseData内容:', saveData.responseData);

  try {
    await api.run(Services.apiSaveResponse, saveData);
    MessagePlugin.success('保存成功');
    await fetchResponseList();
  } catch (error) {
    console.error('保存失败详细错误:', error);
    MessagePlugin.error(`保存失败: ${error?.message || '未知错误'}`);
  }
};

const onSelectForApi = () => {
  if (currentResponse.value) {
    emit('select', currentResponse.value);
    visible.value = false;
  }
};

const onClose = () => {
  currentResponse.value = null;
  responseStructure.value = [];
  successFlags.value = [];
  errorMessageField.value = '';
  resultField.value = '';
};

// 监听响应体结构变化
watch(
  responseStructure,
  (newStructure) => {
    console.log('响应体结构变化:', newStructure);
  },
  { deep: true },
);

// 初始化
watch(visible, (newVisible) => {
  if (newVisible) {
    fetchResponseList();
  }
});
</script>

<style lang="less" scoped>
.api-response-dialog {
  height: 700px;

  .dialog-content {
    display: flex;
    height: 100%;
    gap: 16px;
  }

  .left-panel {
    width: 300px;
    flex-shrink: 0;
  }

  .right-panel {
    flex: 1;
    min-width: 0;
  }

  .response-list {
    border: 1px solid var(--td-border-level-1-color);
    border-radius: var(--td-radius-default);
    height: 100%;
    display: flex;
    flex-direction: column;

    .response-list-header {
      padding: 16px;
      border-bottom: 1px solid var(--td-border-level-1-color);
      background: var(--td-bg-color-container);
      font-weight: 500;
      flex-shrink: 0;
    }

    .response-list-content {
      flex: 1;
      overflow-y: auto;

      .response-item {
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid var(--td-border-level-2-color);
        transition: all 0.2s;
        position: relative;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        &:hover {
          background: var(--td-bg-color-container-hover);
        }

        &.active {
          background: var(--td-brand-color-1);
          border-left: 3px solid var(--td-brand-color);

          .response-name {
            color: var(--td-brand-color);
          }
        }

        .response-content {
          flex: 1;
          min-width: 0; // 防止内容溢出
        }

        .response-name {
          font-weight: 500;
          margin-bottom: 6px;
          color: var(--td-text-color-primary);
          font-size: 14px;
          word-break: break-word;
        }

        .response-code {
          font-size: 12px;
          color: var(--td-text-color-secondary);
          margin-bottom: 0;
          word-break: break-word;
        }

        .response-actions {
          opacity: 0;
          transition: opacity 0.2s;
          flex-shrink: 0;
          margin-left: 8px;
          margin-top: -2px; // 微调位置，使其更靠近右上角
        }

        &:hover .response-actions {
          opacity: 1;
        }
      }

      .empty-list {
        padding: 40px 20px;
        text-align: center;
      }
    }
  }

  .response-config-container {
    height: 100%;
    overflow-y: auto;
    padding-right: 8px;
  }

  .response-config {
    padding-left: 12px;

    .basic-info,
    .response-structure,
    .special-fields {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .field-config-section {
      padding: 12px;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: var(--td-radius-default);
      background: var(--td-bg-color-container);
      height: 100%;

      h4 {
        margin: 0 0 6px 0;
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-primary);
      }

      .help-text {
        margin: 0 0 12px 0;
        font-size: 12px;
        color: var(--td-text-color-secondary);
        line-height: 1.4;
      }
    }

    .success-flag-item {
      margin-bottom: 8px;
      padding: 8px;
      border: 1px solid var(--td-border-level-2-color);
      border-radius: var(--td-radius-default);
      background: var(--td-bg-color-page);
    }

    .response-actions-bar {
      margin-top: 16px;
      padding: 12px;
      border-top: 1px solid var(--td-border-level-1-color);
      text-align: center;
      position: sticky;
      bottom: 0;
      background: var(--td-bg-color-page);
    }
  }

  .no-response {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
