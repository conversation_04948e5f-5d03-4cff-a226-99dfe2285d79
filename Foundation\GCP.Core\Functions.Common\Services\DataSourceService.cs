﻿using GCP.Common;
using GCP.DataAccess;
using LinqToDB;
using LinqToDB.SchemaProvider;

namespace GCP.Functions.Common.Services
{
    [Function("dataSource", "数据源服务")]
    class DataSourceService : BaseService
    {
        [Function("getAll", "获取所有数据源")]
        public List<LcDataSource> GetAll()
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDataSources
                        where a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        orderby new { a.Group, a.DataSourceName }
                        select a).ToList();
            data.ForEach(a => a.ConnectionString = "");
            return data;
        }

        [Function("searchTableSchemas", "搜索表结构")]
        public List<TableSchema> SearchTableSchemas(string dataSourceId, string keyword)
        {
            if (string.IsNullOrEmpty(dataSourceId) || keyword == null)
            {
                throw new CustomException("参数错误");
            }

            var dataSource = GetRawById(dataSourceId);
            var conn = dataSource.ConnectionString;
            keyword = keyword.toLowerCase();

            var schemas = new List<string>();
            if (!string.IsNullOrEmpty(dataSource.Database) && dataSource.DataProvider == "Oracle")
            {
                schemas.Add(dataSource.Database);
            }
            var schema = schemas.FirstOrDefault();

            using var db = new DbBase(dataSource.DataProvider, conn);
            var result = db.GetSchema(includedSchemas: [.. schemas]).Tables.Where(t => t.TableName.toLowerCase().Contains(keyword) || (!string.IsNullOrEmpty(t.Description)  && t.Description.toLowerCase().Contains(keyword)))
            .Take(10).ToList();

            //return result;
            // 处理Description BUG
            var prefix = "Description:";
            return result.Select(t =>
            {
                foreach (var column in t.Columns)
                {
                    if (!string.IsNullOrEmpty(column.Description) && column.Description.StartsWith(prefix))
                    {
                        column.Description = column.Description.Substring(prefix.Length);
                    }
                }
                return t;
            }).ToList();
        }

        [Function("getById", "根据数据源ID获取信息")]
        public LcDataSource GetById(string id)
        {
            var data = GetRawById(id);
            //if (data != null)
            //{
            //    data.ConnectionString = "";
            //}
            return data;
        }

        public LcDataSource GetRawById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcDataSources.FirstOrDefault(a => a.Id == id);
            return data;
        }

        [Function("add", "添加数据源")]
        public void Add(LcDataSource dataSource)
        {
            using var db = this.GetDb();
            var data = db.LcDataSources.FirstOrDefault(a => a.DataSourceName == dataSource.DataSourceName && a.ProjectId == this.ProjectId && a.SolutionId == this.SolutionId);
            if(data != null)
            {
                if(data.State == 0)
                {
                    db.LcDataSources.Delete(a => a.Id == data.Id);
                }
                else
                {
                    throw new CustomException("数据源名称重复");
                }
            }

            dataSource.SolutionId = this.SolutionId;
            dataSource.ProjectId = this.ProjectId;
            this.InsertData(dataSource, db);
        }

        [Function("update", "更新数据源")]
        public void Update(LcDataSource dataSource)
        {
            this.UpdateData(dataSource);
        }

        [Function("move", "更新数据源")]
        public void Move(string id, string group)
        {
            using var db = this.GetDb();
            this.UpdateData(db.LcDataSources
                .Where(c => c.Id == id)
                .Set(c => c.Group, group));
        }

        [Function("changeState", "更新数据源状态")]
        public void ChangeState(string id, short state)
        {
            using var db = this.GetDb();
            this.UpdateData(db.LcDataSources
                .Where(t => t.Id == id)
                .Set(t => t.State, state));
        }

        [Function("delete", "删除数据源")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            db.LcDataSources.Delete(t => t.Id == id);
        }

        [Function("testConnection", "测试数据库连接")]
        public bool TestConnection(LcDataSource dataSource)
        {
            // 参数验证
            if (dataSource == null)
            {
                throw new CustomException("数据源参数不能为空");
            }

            if (string.IsNullOrEmpty(dataSource.DataProvider))
            {
                throw new CustomException("数据库类型不能为空");
            }

            if (string.IsNullOrEmpty(dataSource.ConnectionString))
            {
                throw new CustomException("连接字符串不能为空");
            }

            try
            {
                var db = new DbContext();
                db.SetDefaultConnection(dataSource.ConnectionString, dataSource.DataProvider);
                using var conn = db.CreateOpenConnection();
                // 尝试执行一个简单的SQL语句来验证连接
                switch (dataSource.DataProvider.ToLower())
                {
                    case "mysql":
                    case "sqlserver":
                    case "postgresql":
                        conn.Sql("SELECT 1").Execute();
                        break;
                    case "oracle":
                        conn.Sql("SELECT 1 FROM DUAL").Execute();
                        break;
                    default:
                        throw new CustomException($"不支持的数据库类型：{dataSource.DataProvider}");
                }

                return true;
            }
            catch (Exception ex)
            {
                var errorMessage = "连接测试失败：";
                if (ex.InnerException != null)
                {
                    errorMessage += ex.InnerException.Message;
                }
                else
                {
                    errorMessage += ex.Message;
                }

                // 添加更多错误信息
                if (ex is System.Data.Common.DbException dbEx)
                {
                    errorMessage += $"\n错误代码：{dbEx.ErrorCode}";
                }

                throw new CustomException(errorMessage);
            }
        }

        [Function("testConnectionById", "通过ID测试数据库连接")]
        public bool TestConnectionById(string id)
        {
            // 参数验证
            if (string.IsNullOrEmpty(id))
            {
                throw new CustomException("数据源ID不能为空");
            }

            // 获取数据源信息
            var dataSource = GetRawById(id);
            if (dataSource == null)
            {
                throw new CustomException("未找到指定的数据源");
            }

            // 调用现有的测试连接方法
            return TestConnection(dataSource);
        }

        [Function("getByGroup", "根据分组获取数据源")]
        public Dictionary<string, List<LcDataSource>> GetByGroup()
        {
            using var db = this.GetDb();
            var data = (from a in db.LcDataSources
                       where a.SolutionId == this.SolutionId &&
                       a.ProjectId == this.ProjectId
                       select a).ToList();

            var result = new Dictionary<string, List<LcDataSource>>();
            foreach (var item in data)
            {
                item.ConnectionString = "";
                var group = string.IsNullOrEmpty(item.Group) ? "其他" : item.Group;
                if (!result.ContainsKey(group))
                {
                    result[group] = new List<LcDataSource>();
                }
                result[group].Add(item);
            }
            return result;
        }

        [Function("updateState", "更新数据源状态")]
        public void UpdateState(string id, short state)
        {
            using var db = this.GetDb();
            var item = db.LcDataSources.FirstOrDefault(a => a.Id == id);
            if (item == null)
            {
                throw new CustomException("数据源不存在");
            }
            item.State = state;
            this.UpdateData(item, db);
        }

        [Function("search", "搜索数据源")]
        public Dictionary<string, List<LcDataSource>> Search(string keyword)
        {
            using var db = this.GetDb();
            var query = db.LcDataSources.Where(a => 
                a.SolutionId == this.SolutionId && 
                a.ProjectId == this.ProjectId);

            if (!string.IsNullOrEmpty(keyword))
            {
                keyword = keyword.ToLower();
                query = query.Where(a =>
                    a.DataSourceName.Contains(keyword) ||
                    a.Description.Contains(keyword) ||
                    a.ServerAddress.Contains(keyword) ||
                    a.Port.ToString().Contains(keyword) ||
                    a.DataProvider.ToLower().Contains(keyword) ||
                    a.Group.ToLower().Contains(keyword)
                );
            }

            var data = query.ToList();
            var result = new Dictionary<string, List<LcDataSource>>();
            foreach (var item in data)
            {
                item.ConnectionString = "";
                var group = string.IsNullOrEmpty(item.Group) ? "其他" : item.Group;
                if (!result.ContainsKey(group))
                {
                    result[group] = new List<LcDataSource>();
                }
                result[group].Add(item);
            }
            return result;
        }
    }
}
