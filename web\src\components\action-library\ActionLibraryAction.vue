<template>
  <div class="action-library-action">
    <t-form ref="formRef" :data="formData" layout="vertical" :rules="rules">
      <!-- 动作库选择 -->
      <t-form-item label="选择动作库" name="actionLibraryId">
        <action-library-selector
          v-model="formData.actionLibraryId"
          :show-preview="false"
          @change="onActionLibraryChange"
        />
      </t-form-item>

      <!-- 执行配置 -->
      <div v-if="selectedActionLibrary" class="execution-config">
        <t-divider>执行配置</t-divider>
        
        <t-row :gutter="24">
          <t-col :span="6">
            <t-form-item label="执行模式" name="executionMode">
              <t-select v-model="formData.executionMode" :options="executionModeOptions" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="超时时间(秒)" name="timeoutSeconds">
              <t-input-number v-model="formData.timeoutSeconds" :min="1" :max="300" />
            </t-form-item>
          </t-col>
        </t-row>

        <t-form-item v-if="formData.executionMode === 'conditional'" label="执行条件" name="condition">
          <t-input v-model="formData.condition" placeholder="例如: true, false, 或表达式" />
        </t-form-item>

        <!-- 输入参数配置 -->
        <div v-if="inputParameters.length > 0" class="input-params-config">
          <h4>输入参数配置</h4>
          <t-table
            :data="inputParameters"
            :columns="inputParamColumns"
            size="small"
            :pagination="false"
          >
            <template #required="{ row }">
              <t-tag v-if="row.required" theme="danger" size="small">必填</t-tag>
              <t-tag v-else theme="default" size="small">可选</t-tag>
            </template>
            <template #value="{ row, rowIndex }">
              <variable-selector
                v-model="inputParamValues[row.name]"
                :placeholder="`请选择或输入${row.name}`"
                :allow-input="true"
                size="small"
              />
            </template>
          </t-table>
        </div>

        <!-- 输出参数配置 -->
        <div v-if="outputParameters.length > 0" class="output-params-config">
          <h4>输出参数配置</h4>
          <t-form-item label="输出变量名" name="outputVariableName">
            <t-input
              v-model="formData.outputVariableName"
              placeholder="执行结果将保存到此变量"
            />
          </t-form-item>
          
          <div class="output-params-preview">
            <t-table
              :data="outputParameters"
              :columns="outputParamColumns"
              size="small"
              :pagination="false"
            >
              <template #required="{ row }">
                <t-tag v-if="row.required" theme="danger" size="small">必填</t-tag>
                <t-tag v-else theme="default" size="small">可选</t-tag>
              </template>
            </t-table>
          </div>
        </div>

        <!-- 错误处理配置 -->
        <div class="error-handling-config">
          <h4>错误处理</h4>
          <t-row :gutter="24">
            <t-col :span="6">
              <t-form-item label="失败时" name="onError">
                <t-select v-model="formData.onError" :options="errorHandlingOptions" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="重试次数" name="retryCount">
                <t-input-number v-model="formData.retryCount" :min="0" :max="5" />
              </t-form-item>
            </t-col>
          </t-row>
        </div>
      </div>

      <!-- 动作库信息预览 -->
      <div v-if="selectedActionLibrary" class="action-library-info">
        <t-divider>动作库信息</t-divider>
        <t-descriptions :column="3" size="small">
          <t-descriptions-item label="名称">{{ selectedActionLibrary.name }}</t-descriptions-item>
          <t-descriptions-item label="版本">v{{ selectedActionLibrary.version }}</t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag :theme="getStatusTheme(selectedActionLibrary.status)">
              {{ getStatusText(selectedActionLibrary.status) }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="分类">{{ selectedActionLibrary.category || '未分类' }}</t-descriptions-item>
          <t-descriptions-item label="执行次数">{{ selectedActionLibrary.executionCount || 0 }}</t-descriptions-item>
          <t-descriptions-item label="平均耗时">{{ selectedActionLibrary.averageExecutionTime || '-' }}ms</t-descriptions-item>
        </t-descriptions>
        
        <div v-if="selectedActionLibrary.description" class="description">
          <strong>描述：</strong>{{ selectedActionLibrary.description }}
        </div>
      </div>
    </t-form>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ActionLibraryAction',
};
</script>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import ActionLibrarySelector from './ActionLibrarySelector.vue';
import VariableSelector from '@/components/variable-selector/index.vue';

interface ActionLibraryActionData {
  actionLibraryId: string;
  executionMode: string;
  timeoutSeconds: number;
  condition?: string;
  outputVariableName: string;
  onError: string;
  retryCount: number;
}

interface ActionLibrary {
  id: string;
  name: string;
  description?: string;
  category?: string;
  status: string;
  version: number;
  executionCount: number;
  averageExecutionTime?: number;
  inputSchemaJson?: string;
  outputSchemaJson?: string;
}

interface Parameter {
  name: string;
  type: string;
  required: boolean;
  description?: string;
}

const props = defineProps<{
  modelValue: ActionLibraryActionData;
}>();

const emits = defineEmits<{
  'update:modelValue': [value: ActionLibraryActionData];
}>();

const formRef = ref();
const formData = ref<ActionLibraryActionData>({ ...props.modelValue });
const selectedActionLibrary = ref<ActionLibrary | null>(null);
const inputParamValues = ref<Record<string, any>>({});

// 表单验证规则
const rules = {
  actionLibraryId: [
    { required: true, message: '请选择动作库', trigger: 'change' },
  ],
  outputVariableName: [
    { required: true, message: '请输入输出变量名', trigger: 'blur' },
  ],
};

// 执行模式选项
const executionModeOptions = [
  { label: '普通执行', value: 'normal' },
  { label: '条件执行', value: 'conditional' },
  { label: '超时执行', value: 'timeout' },
];

// 错误处理选项
const errorHandlingOptions = [
  { label: '停止流程', value: 'stop' },
  { label: '继续执行', value: 'continue' },
  { label: '跳转到错误处理', value: 'goto-error' },
];

// 输入参数
const inputParameters = computed(() => {
  if (!selectedActionLibrary.value?.inputSchemaJson) return [];
  try {
    const schema = JSON.parse(selectedActionLibrary.value.inputSchemaJson);
    return parseParameters(schema);
  } catch {
    return [];
  }
});

// 输出参数
const outputParameters = computed(() => {
  if (!selectedActionLibrary.value?.outputSchemaJson) return [];
  try {
    const schema = JSON.parse(selectedActionLibrary.value.outputSchemaJson);
    return parseParameters(schema);
  } catch {
    return [];
  }
});

// 输入参数表格列定义
const inputParamColumns = [
  { colKey: 'name', title: '参数名', width: 120 },
  { colKey: 'type', title: '类型', width: 80 },
  { colKey: 'required', title: '必填', width: 60 },
  { colKey: 'description', title: '描述', width: 150 },
  { colKey: 'value', title: '值', width: 200 },
];

// 输出参数表格列定义
const outputParamColumns = [
  { colKey: 'name', title: '参数名', width: 120 },
  { colKey: 'type', title: '类型', width: 80 },
  { colKey: 'required', title: '必填', width: 60 },
  { colKey: 'description', title: '描述', ellipsis: true },
];

// 监听表单数据变化
watch(
  () => formData.value,
  (newValue) => {
    // 合并输入参数值
    const completeData = {
      ...newValue,
      inputParams: inputParamValues.value,
    };
    emits('update:modelValue', completeData);
  },
  { deep: true }
);

// 监听输入参数值变化
watch(
  () => inputParamValues.value,
  () => {
    const completeData = {
      ...formData.value,
      inputParams: inputParamValues.value,
    };
    emits('update:modelValue', completeData);
  },
  { deep: true }
);

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...newValue };
    if (newValue.inputParams) {
      inputParamValues.value = { ...newValue.inputParams };
    }
  },
  { deep: true }
);

// 解析参数Schema
const parseParameters = (schema: any): Parameter[] => {
  if (!schema || !schema.properties) return [];
  
  const parameters: Parameter[] = [];
  const required = schema.required || [];
  
  for (const [name, prop] of Object.entries(schema.properties)) {
    const propDef = prop as any;
    parameters.push({
      name,
      type: propDef.type || 'string',
      required: required.includes(name),
      description: propDef.description || '',
    });
  }
  
  return parameters;
};

// 动作库变化处理
const onActionLibraryChange = (actionLibrary: ActionLibrary | null) => {
  selectedActionLibrary.value = actionLibrary;
  
  // 重置输入参数值
  inputParamValues.value = {};
  
  // 如果有输入参数，初始化默认值
  if (actionLibrary?.inputSchemaJson) {
    try {
      const schema = JSON.parse(actionLibrary.inputSchemaJson);
      if (schema.properties) {
        for (const [name, prop] of Object.entries(schema.properties)) {
          const propDef = prop as any;
          if (propDef.default !== undefined) {
            inputParamValues.value[name] = propDef.default;
          }
        }
      }
    } catch {
      // 忽略解析错误
    }
  }
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  switch (status) {
    case 'active': return 'success';
    case 'inactive': return 'danger';
    case 'draft': return 'warning';
    default: return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '激活';
    case 'inactive': return '停用';
    case 'draft': return '草稿';
    default: return status;
  }
};

// 验证表单
const validate = async () => {
  try {
    await formRef.value?.validate();
    
    // 验证必填的输入参数
    const missingParams = inputParameters.value
      .filter(param => param.required && !inputParamValues.value[param.name])
      .map(param => param.name);
    
    if (missingParams.length > 0) {
      throw new Error(`缺少必填参数: ${missingParams.join(', ')}`);
    }
    
    return true;
  } catch (error) {
    return false;
  }
};

defineExpose({
  validate,
});
</script>

<style lang="less" scoped>
.action-library-action {
  .execution-config,
  .action-library-info {
    margin-top: 24px;
  }

  .input-params-config,
  .output-params-config,
  .error-handling-config {
    margin-top: 24px;

    h4 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .output-params-preview {
    margin-top: 16px;
  }

  .description {
    margin-top: 16px;
    padding: 12px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.5;
  }

  .t-divider {
    margin: 24px 0 16px 0;
  }
}
</style>
