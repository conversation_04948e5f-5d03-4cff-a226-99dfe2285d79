// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 设备变量定义
	/// </summary>
	[Table("lc_iot_equipment_variable")]
	public class LcIotEquipmentVariable : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"              , CanBeNull = false, IsPrimaryKey = true)] public string    Id              { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                             )] public DateTime  TimeCreate      { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"         , CanBeNull = false                     )] public string    Creator         { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:更新时间
		/// </summary>
		[Column("TIME_MODIFIED"                                           )] public DateTime? TimeModified    { get; set; } // datetime
		/// <summary>
		/// Description:更新数据行的用户
		/// </summary>
		[Column("MODIFIER"                                                )] public string?   Modifier        { get; set; } // varchar(80)
		/// <summary>
		/// Description:可用状态
		/// </summary>
		[Column("STATE"                                                   )] public short     State           { get; set; } // smallint
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"     , CanBeNull = false                     )] public string    SolutionId      { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"      , CanBeNull = false                     )] public string    ProjectId       { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:设备ID
		/// </summary>
		[Column("EQUIPMENT_ID"    , CanBeNull = false                     )] public string    EquipmentId     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:变量名称
		/// </summary>
		[Column("VAR_NAME"        , CanBeNull = false                     )] public string    VarName         { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:变量读写方法
		/// </summary>
		[Column("METHOD"          , CanBeNull = false                     )] public string    Method          { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:变量地址
		/// </summary>
		[Column("ADDRESS"                                                 )] public string?   Address         { get; set; } // varchar(200)
		/// <summary>
		/// Description:表达式
		/// </summary>
		[Column("EXPRESSIONS"                                             )] public string?   Expressions     { get; set; } // varchar(200)
		/// <summary>
		/// Description:数据类型
		/// </summary>
		[Column("DATA_TYPE"       , CanBeNull = false                     )] public string    DataType        { get; set; } = null!; // varchar(50)
		/// <summary>
		/// Description:数据归档周期（毫秒）
		/// </summary>
		[Column("ARCHIVE_PERIOD"                                          )] public int?      ArchivePeriod   { get; set; } // int
		/// <summary>
		/// Description:变化上传阈值（百分比，0-100）
		/// </summary>
		[Column("CHANGE_THRESHOLD"                                        )] public double?   ChangeThreshold { get; set; } // double
		/// <summary>
		/// Description:是否上传
		/// </summary>
		[Column("IS_UPLOAD"                                               )] public short     IsUpload        { get; set; } // smallint
		/// <summary>
		/// Description:描述
		/// </summary>
		[Column("DESCRIPTION"                                             )] public string?   Description     { get; set; } // varchar(200)
	}
}
