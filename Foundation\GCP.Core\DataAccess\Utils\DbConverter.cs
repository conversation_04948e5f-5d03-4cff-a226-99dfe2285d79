﻿namespace GCP.DataAccess
{
    /// <summary>
    /// DB数据转换类
    /// </summary>
    public static class DbConverter {
        static IFormatProvider provider = System.Threading.Thread.CurrentThread.CurrentCulture;
        /// <summary>
        /// 布尔类型 false 自定义字符串集合
        /// </summary>
        public static string[] FalseStringArr = { null, string.Empty, "0", "N", "False", "false" };

        /// <summary>
        /// 任意DB类型转换
        /// </summary>
        /// <param name="value"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static object ChangeType(object value, Type type) {
            if (type == value.GetType()) return value;
            var convertible = value as IConvertible;
            if (convertible != null) {
                switch (Type.GetTypeCode(type)) {
                    case TypeCode.String:
                        return convertible.ToString(provider);
                    case TypeCode.Int32:
                        if (type.IsEnum) {
                            if (value is string)
                                return Enum.Parse(type, value as string);
                            else
                                return Enum.ToObject(type, convertible.ToInt32(provider));
                        }
                        return convertible.ToInt32(provider);
                    case TypeCode.DateTime:
                        return convertible.ToDateTime(provider);
                    case TypeCode.Boolean:
                        if (value is string) {
                            return !FalseStringArr.Contains(value);
                        }
                        return convertible.ToBoolean(provider);
                    case TypeCode.Decimal:
                        return convertible.ToDecimal(provider);
                    case TypeCode.Byte:
                        return convertible.ToByte(provider);
                    case TypeCode.Int16:
                        return convertible.ToInt16(provider);
                    case TypeCode.Int64:
                        return convertible.ToInt64(provider);
                    case TypeCode.Double:
                        return convertible.ToDouble(provider);
                    case TypeCode.Char:
                        return convertible.ToChar(provider);
                    case TypeCode.Single:
                        return convertible.ToSingle(provider);
                    case TypeCode.UInt32:
                        return convertible.ToUInt32(provider);
                    case TypeCode.UInt16:
                        return convertible.ToUInt16(provider);
                    case TypeCode.UInt64:
                        return convertible.ToUInt64(provider);
                    case TypeCode.SByte:
                        return convertible.ToSByte(provider);
                    default:
                        return Convert.ChangeType(value, type);
                }
            }
            return value;
        }
    }
}
