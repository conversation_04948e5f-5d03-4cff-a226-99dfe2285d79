﻿using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20241218001000, "添加API输出体定义")]
    public class AddApiResponseDefinition : Migration
    {
        public override void Up()
        {
            Create.Table("LC_API_RESPONSE").WithDescription("API返回体定义")
                .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
                .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
                .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
                .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
                .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
                .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
                .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
                .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

                .WithColumn("RESPONSE_CODE").AsAnsiString(80).WithColumnDescription("API返回体编码")
                .WithColumn("RESPONSE_TYPE").AsAnsiString(80).WithColumnDescription("API返回体类型")
                .WithColumn("RESPONSE_NAME").AsAnsiString(80).WithColumnDescription("API返回体名称")
                .WithColumn("DESCRIPTION").AsAnsiString(500).Nullable().WithColumnDescription("描述")
                .WithColumn("RESPONSE_DATA").AsAnsiString(4000).WithColumnDescription("API返回体")
                .WithColumn("SUCCESS_FLAG").AsAnsiString(2000).Nullable().WithColumnDescription("成功标识")
                .WithColumn("ERROR_CODE").AsAnsiString(2000).Nullable().WithColumnDescription("错误码")
                .WithColumn("ERROR_MESSAGE").AsAnsiString(2000).Nullable().WithColumnDescription("错误信息")
                .WithColumn("RESULT").AsAnsiString(2000).Nullable().WithColumnDescription("结果")
                ;
        }

        public override void Down()
        {
            Delete.Table("LC_API_RESPONSE");
        }
    }
}
