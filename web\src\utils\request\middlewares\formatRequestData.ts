import { isObject, isString } from 'lodash-es';

import { HttpContext } from '../http';

const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

function formatRequestData(data: Record<string, any>) {
  for (const key in data) {
    // eslint-disable-next-line no-underscore-dangle
    if (data[key] && data[key]._isAMomentObject) {
      data[key] = data[key].format(DATE_TIME_FORMAT);
    }
    if (isString(key)) {
      const value = data[key];
      if (value) {
        try {
          data[key] = isString(value) ? value.trim() : value;
        } catch (error: any) {
          throw new Error(error);
        }
      }
    }
    if (isObject(data[key])) {
      formatRequestData(data[key]);
    }
  }
}

export default async (ctx: HttpContext, next: () => any) => {
  const params = ctx.requestOptions.body;
  if (params && Object.prototype.toString.call(params) === '[object Object]') {
    formatRequestData(params as Record<string, any>);
  }

  // 下个中间件执行前
  await next();
  // 下个中间件执行后
};
