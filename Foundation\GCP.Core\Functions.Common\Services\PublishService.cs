﻿using GCP.Common;
using GCP.DataAccess;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("publish", "发布环境服务")]
    class PublishService : BaseService
    {
        [Function("getAll", "获取所有发布环境")]
        public List<LcPublish> GetAll()
        {
            using var db = this.GetDb();
            var data = (from a in db.LcPublishes
                        where a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        select a).ToList();
            return data;
        }

        [Function("getById", "根据ID获取信息")]
        public LcPublish GetById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcPublishes.FirstOrDefault(a => a.Id == id);
            return data;
        }

        [Function("add", "添加发布环境")]
        public void Add(LcPublish publish)
        {
            using var db = this.GetDb();
            var data = db.LcPublishes.FirstOrDefault(a => a.EnvironmentName == publish.EnvironmentName && a.ProjectId == this.ProjectId && a.SolutionId == this.SolutionId);
            if (data != null)
            {
                if (data.State == 0)
                {
                    db.LcPublishes.Delete(a => a.Id == data.Id);
                }
                else
                {
                    throw new CustomException("发布环境名称重复");
                }
            }

            publish.SolutionId = this.SolutionId;
            publish.ProjectId = this.ProjectId;
            this.InsertData(publish);
        }

        [Function("update", "更新发布环境")]
        public void Update(LcPublish publish)
        {
            this.UpdateData(publish);
        }

        [Function("delete", "删除发布环境")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            db.LcPublishes.Delete(a => a.Id == id);
        }
    }
}
