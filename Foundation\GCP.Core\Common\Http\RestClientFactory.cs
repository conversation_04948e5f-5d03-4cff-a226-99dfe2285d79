using RestSharp;
using System.Collections.Concurrent;

namespace GCP.Common
{
    /// <summary>
    /// RestClient 工厂类，支持根据不同配置创建和缓存客户端
    /// </summary>
    public class RestClientFactory : IDisposable
    {
        private readonly ConcurrentDictionary<string, RestClient> _clientCache = new();
        private readonly object _lock = new object();
        private bool _disposed = false;

        /// <summary>
        /// 根据配置获取或创建 RestClient
        /// </summary>
        /// <param name="timeoutInSeconds">超时时间（秒）</param>
        /// <param name="baseUrl">基础URL（可选）</param>
        /// <param name="additionalHeaders">额外的默认头部（可选）</param>
        /// <returns>配置好的 RestClient</returns>
        public RestClient GetClient(int? timeoutInSeconds = null, string baseUrl = null, Dictionary<string, string> additionalHeaders = null)
        {
            // 创建缓存键，基于配置参数
            var cacheKey = CreateCacheKey(timeoutInSeconds, baseUrl, additionalHeaders);
            
            return _clientCache.GetOrAdd(cacheKey, _ => CreateClient(timeoutInSeconds, baseUrl, additionalHeaders));
        }

        /// <summary>
        /// 创建新的 RestClient 实例
        /// </summary>
        private RestClient CreateClient(int? timeoutInSeconds, string baseUrl, Dictionary<string, string> additionalHeaders)
        {
            var options = new RestClientOptions()
            {
                // 默认2个小时超时，如果有指定超时时间则使用指定的
                Timeout = timeoutInSeconds.HasValue && timeoutInSeconds.Value > 0
                    ? TimeSpan.FromSeconds(timeoutInSeconds.Value)
                    : TimeSpan.FromHours(2),
            };

            // 如果指定了基础URL，则设置
            if (!string.IsNullOrEmpty(baseUrl))
            {
                options.BaseUrl = new Uri(baseUrl);
            }

            var client = new RestClient(options);
            
            // 添加额外的头部
            if (additionalHeaders != null)
            {
                foreach (var header in additionalHeaders)
                {
                    client.AddDefaultHeader(header.Key, header.Value);
                }
            }

            return client;
        }

        /// <summary>
        /// 创建缓存键
        /// </summary>
        private string CreateCacheKey(int? timeoutInSeconds, string baseUrl, Dictionary<string, string> additionalHeaders)
        {
            var keyParts = new List<string>
            {
                $"timeout:{timeoutInSeconds ?? -1}",
                $"baseUrl:{baseUrl ?? "null"}"
            };

            if (additionalHeaders != null && additionalHeaders.Count > 0)
            {
                var sortedHeaders = additionalHeaders.OrderBy(h => h.Key).Select(h => $"{h.Key}:{h.Value}");
                keyParts.Add($"headers:{string.Join("|", sortedHeaders)}");
            }

            return string.Join("_", keyParts);
        }

        /// <summary>
        /// 清理指定配置的客户端缓存
        /// </summary>
        public void ClearCache(int? timeoutInSeconds = null, string baseUrl = null, Dictionary<string, string> additionalHeaders = null)
        {
            var cacheKey = CreateCacheKey(timeoutInSeconds, baseUrl, additionalHeaders);
            if (_clientCache.TryRemove(cacheKey, out var client))
            {
                client?.Dispose();
            }
        }

        /// <summary>
        /// 清理所有缓存的客户端
        /// </summary>
        public void ClearAllCache()
        {
            lock (_lock)
            {
                foreach (var client in _clientCache.Values)
                {
                    client?.Dispose();
                }
                _clientCache.Clear();
            }
        }

        /// <summary>
        /// 获取当前缓存的客户端数量
        /// </summary>
        public int CachedClientCount => _clientCache.Count;

        public void Dispose()
        {
            if (!_disposed)
            {
                ClearAllCache();
                _disposed = true;
            }
        }
    }
}
