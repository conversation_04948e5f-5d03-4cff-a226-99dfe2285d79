﻿using GCP.Common;
using GCP.Iot.Services;
using Serilog;

namespace GCP.Core.Iot
{
    class InitService: IFunctionService
    {
        [Function("/gcp/iot/init", "初始化IOT服务", startupRun: true)]
        public async Task Init()
        {
            var driverManager = ServiceLocator.Current.GetService(typeof(DriverManager)) as DriverManager;
            var equipmentInitializer = ServiceLocator.Current.GetService(typeof(EquipmentInitializer)) as EquipmentInitializer;
            var driverMetadataService = ServiceLocator.Current.GetService(typeof(DriverMetadataService)) as DriverMetadataService;

            // 获取应用程序目录
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            // 获取驱动目录
            var driverFiles = Directory.GetFiles(baseDirectory, "GCP.Iot.*.dll");
            foreach (var driverFile in driverFiles)
            {
                driverManager.LoadDriverAssembly(driverFile);
            }

            try
            {
                // 同步驱动元数据
                await driverMetadataService.SyncAllDriverMetadataAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "同步驱动元数据失败");
            }

            try
            {
                // 初始化自动启动的设备
                await equipmentInitializer.InitializeAllActiveEquipmentAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "自动初始化设备失败");
            }
        }
    }
}
