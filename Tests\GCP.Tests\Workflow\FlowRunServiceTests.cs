using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.Functions.Common.Services;
using GCP.DataAccess;
using LinqToDB;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 工作流运行服务测试类
    /// </summary>
    public class FlowRunServiceTests : DatabaseTestBase
    {
        public FlowRunServiceTests(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<FlowRunService>();
        }

        [Fact]
        public async Task GetAll_ShouldReturnFlowRunInstances()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            // Act
            var result = flowRunService.GetAll();

            // Assert
            result.Should().NotBeNull("应该返回分页数据");
            result.List.Should().NotBeNull("应该包含数据列表");
            Output.WriteLine($"返回的工作流实例数量: {result.List.Count()}");
        }

        [Fact]
        public async Task GetAll_WithKeywordFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            // Act
            var result = flowRunService.GetAll(keyword: "测试");

            // Assert
            result.Should().NotBeNull("应该返回分页数据");
            result.List.Should().NotBeNull("应该包含数据列表");

            if (result.List.Any())
            {
                result.List.All(p => p.FunctionName.Contains("测试")).Should().BeTrue("所有结果都应该包含关键词");
            }
        }

        [Fact]
        public async Task GetAll_WithTriggerTypeFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            // Act
            var result = flowRunService.GetAll(triggerType: "API");

            // Assert
            result.Should().NotBeNull("应该返回分页数据");
            result.List.Should().NotBeNull("应该包含数据列表");

            if (result.List.Any())
            {
                result.List.All(p => p.TriggerType == "API").Should().BeTrue("所有结果都应该是API触发类型");
            }
        }

        [Fact]
        public async Task GetAll_WithTimeRangeFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var beginTime = DateTime.Now.AddDays(-1);
            var endTime = DateTime.Now.AddDays(1);

            // Act
            var result = flowRunService.GetAll(beginTime: beginTime, endTime: endTime);

            // Assert
            result.Should().NotBeNull("应该返回分页数据");
            result.List.Should().NotBeNull("应该包含数据列表");

            if (result.List.Any())
            {
                result.List.All(p => p.BeginTime >= beginTime && p.BeginTime <= endTime)
                    .Should().BeTrue("所有结果都应该在指定时间范围内");
            }
        }

        [Fact]
        public async Task GetSteps_ShouldReturnFlowSteps()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var testProcId = "test-proc-001";

            // Act
            var result = flowRunService.GetSteps(testProcId);

            // Assert
            result.Should().NotBeNull("应该返回步骤列表");
            
            if (result.Any())
            {
                result.All(s => s.ProcId == testProcId).Should().BeTrue("所有步骤都应该属于指定的流程实例");
                result.Should().BeInAscendingOrder(s => s.SeqNo, "步骤应该按序号排序");
            }
        }

        [Fact]
        public async Task GetStepData_ShouldReturnFlowStepData()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var testProcId = "test-proc-001";
            var testStepId = "test-step-001";

            // Act
            var result = flowRunService.GetStepData(testProcId, testStepId);

            // Assert
            result.Should().NotBeNull("应该返回步骤数据");
            result.Should().NotBeEmpty("步骤数据不应该为空");
            Output.WriteLine($"步骤数据: {result}");
        }

        [Fact]
        public async Task GetFlowLogs_ShouldReturnFlowLogs()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var testProcId = "test-proc-001";

            // Act
            var result = flowRunService.GetFlowLogs(testProcId);

            // Assert
            result.Should().NotBeNull("应该返回日志字符串");
            Output.WriteLine($"流程日志: {result}");
        }

        [Fact]
        public async Task DeleteErrorLogs_ShouldDeleteErrorFlows()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var testProcIds = new[] { "test-proc-001" };

            // Act
            var action = async () => await flowRunService.DeleteErrorLogs(testProcIds);

            // Assert
            await action.Should().NotThrowAsync("删除错误日志不应该抛出异常");
        }

        [Fact]
        public async Task FlowRetry_WithValidId_ShouldRetryFlow()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var testProcId = "test-proc-001";

            // Act & Assert
            // 由于重试涉及复杂的工作流执行逻辑，这里主要测试方法不抛出异常
            var action = async () => await flowRunService.FlowRetry(testProcId);
            
            // 可能会抛出业务异常（如流程不存在），但不应该是系统异常
            await action.Should().NotThrowAsync<NullReferenceException>("不应该抛出空引用异常");
            await action.Should().NotThrowAsync<InvalidOperationException>("不应该抛出无效操作异常");
        }

        [Fact]
        public async Task FlowRetry_WithInvalidId_ShouldThrowException()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var invalidProcId = "invalid-proc-id";

            // Act & Assert
            var action = async () => await flowRunService.FlowRetry(invalidProcId);
            await action.Should().ThrowAsync<Exception>()
                .WithMessage("*流程实例不存在*", "应该抛出流程不存在的异常");
        }

        [Fact]
        public async Task FlowContinue_ShouldNotThrow()
        {
            // Arrange
            await InitializeTestDataAsync();
            var flowRunService = GetService<FlowRunService>();
            SetTestContext(flowRunService);

            var testProcId = "test-proc-001";

            // Act & Assert
            var action = () => flowRunService.FlowContinue(testProcId);
            action.Should().NotThrow("继续执行工作流不应该抛出异常");
        }

        /// <summary>
        /// 设置测试上下文
        /// </summary>
        private void SetTestContext(FlowRunService flowRunService)
        {
            // 通过反射设置私有字段或属性
            var solutionIdProperty = typeof(FlowRunService).BaseType?.GetProperty("SolutionId");
            var projectIdProperty = typeof(FlowRunService).BaseType?.GetProperty("ProjectId");

            solutionIdProperty?.SetValue(flowRunService, "test-solution-001");
            projectIdProperty?.SetValue(flowRunService, "test-project-001");
        }

        protected override async Task SeedTestDataAsync(GcpDb db)
        {
            try
            {
                // 创建基础测试数据 - 使用唯一ID避免冲突
                var timestamp = DateTime.Now.Ticks.ToString().Substring(10); // 使用时间戳后8位
                var solutionId = $"test-sol-run-{timestamp}";
                var projectId = $"test-proj-run-{timestamp}";
                var functionId = $"test-func-run-{timestamp}";
                var procId = $"test-proc-run-{timestamp}";

                var solution = TestDataBuilder.CreateTestSolution("工作流运行测试解决方案");
                solution.Id = solutionId;
                await db.InsertAsync(solution);

                var project = TestDataBuilder.CreateTestProject("工作流运行测试项目", solutionId);
                project.Id = projectId;
                await db.InsertAsync(project);

                // 创建测试函数
                var function = TestDataBuilder.CreateTestFunction("测试工作流函数", solutionId, projectId);
                function.Id = functionId;
                await db.InsertAsync(function);

                // 使用流程数据库创建工作流运行数据
                using var flowDb = GetService<GcpFlowDb>();

                // 创建工作流运行实例
                var runProc = TestDataBuilder.CreateTestFlowRunProc(functionId, solutionId, projectId);
                runProc.Id = procId;
                await flowDb.InsertAsync(runProc);

                // 创建工作流步骤实例
                var runStep = TestDataBuilder.CreateTestFlowRunStep(procId, functionId, 0);
                await flowDb.InsertAsync(runStep);

                Output.WriteLine("工作流运行测试数据初始化完成");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"工作流运行测试数据初始化失败: {ex.Message}");
                throw;
            }
        }
    }
}
