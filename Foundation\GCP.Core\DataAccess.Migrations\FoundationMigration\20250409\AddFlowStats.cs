using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration
{
    [Migration(20250409002000, "添加流程统计数据表")]
    public class AddFlowStats : Migration
    {
        public override void Up()
        {
            // 添加流程统计数据表
            Create.Table("LC_FHI_STATS").WithDescription("流程统计数据")
                .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行ID号")
                .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
                .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")

                .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
                .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")
                .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("函数ID")
                .WithColumn("VERSION").AsInt64().WithColumnDescription("函数版本")
                .WithColumn("STAT_DATE").AsDateTime().WithColumnDescription("统计日期")
                .WithColumn("ARCHIVE_PERIOD").AsInt16().WithColumnDescription("归档周期(1:日,2:10分钟)")
                .WithColumn("TRIGGER_TYPE").AsAnsiString(36).WithColumnDescription("触发类型")
                .WithColumn("TOTAL_COUNT").AsInt32().WithColumnDescription("总调用次数")
                .WithColumn("SUCCESS_COUNT").AsInt32().WithColumnDescription("成功次数")
                .WithColumn("FAIL_COUNT").AsInt32().WithColumnDescription("失败次数")
                .WithColumn("TOTAL_DURATION").AsInt64().WithColumnDescription("总耗时(ms)")
                .WithColumn("AVG_DURATION").AsInt32().WithColumnDescription("平均耗时(ms)")
                .WithColumn("TOTAL_TRAFFIC").AsDecimal().Nullable().WithColumnDescription("总流量(MB)");


            // 为流程统计表创建索引
            Create.Index("IDX_FLOW_STATS_DATE")
                .OnTable("LC_FHI_STATS")
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending()
                .OnColumn("FUNCTION_ID").Ascending()
                .OnColumn("VERSION").Ascending()
                .OnColumn("STAT_DATE").Ascending();
        }

        public override void Down()
        {
            Delete.Table("LC_FHI_STATS");
        }
    }
} 