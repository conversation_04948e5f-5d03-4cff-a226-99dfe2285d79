<template>
  <t-row :class="'api-item' + (active ? ' active' : '')" :title="props.name + '\r\n' + props.url"
    ><t-col flex="50px" class="api-method">
      <api-method-tag :method="props.method" size="small" />
    </t-col>
    <t-col flex="auto" class="api-info">
      <div>
        <span class="api-title">{{ props.name }}</span>
      </div>
      <div class="api-url">{{ props.url }}</div>
    </t-col>
  </t-row>
</template>
<script lang="ts">
export default {
  name: 'ApiItem',
};
</script>
<script setup lang="ts">
import ApiMethodTag from './ApiMethodTag.vue';

const props = defineProps<{
  method: string;
  url: string;
  name: string;
  active?: boolean;
}>();
</script>
<style lang="less" scoped>
.api-item {
  cursor: pointer;
  padding: 5px 10px 5px 0;
  flex-wrap: nowrap;
  border-radius: 4px;

  &:hover {
    background-color: var(--td-bg-color-container-hover);
    transition: background-color 0.2s linear;
  }
  &.active {
    background-color: var(--td-bg-color-container-active);
  }

  > div {
    display: inline-block;
  }

  .api-method {
    width: 50px;
    height: 34px;

    :deep(.t-tag) {
      float: right;
      margin-top: 3px;
    }
  }

  .api-info {
    margin-left: 8px;

    .api-title {
      font-size: 14px;
    }

    .api-url {
      color: var(--td-text-color-placeholder);
      font-size: 12px;
      font-family: consolas, monospace;
    }
  }
}
</style>
