<template>
  <div class="dingtalk-config">
    <t-form-item label="Webhook地址" name="webHook">
      <t-input v-model="config.webHook" placeholder="请输入钉钉机器人Webhook地址" />
    </t-form-item>

    <t-form-item label="签名密钥" name="secret">
      <t-input v-model="config.secret" placeholder="请输入签名密钥（可选）" />
    </t-form-item>

    <t-form-item label="@所有人" name="isAtAll">
      <t-switch v-model="config.isAtAll" />
    </t-form-item>

    <t-form-item label="@指定人员" name="atMobiles">
      <div class="mobile-list">
        <div v-for="(mobile, index) in config.atMobiles" :key="index" class="mobile-item">
          <t-input v-model="config.atMobiles[index]" placeholder="请输入手机号" />
          <t-button theme="danger" variant="text" @click="removeMobile(index)">
            删除
          </t-button>
        </div>
        <t-button theme="primary" variant="text" @click="addMobile">
          添加手机号
        </t-button>
      </div>
    </t-form-item>

    <t-alert theme="info" title="配置说明">
      <p>1. Webhook地址：在钉钉群中添加自定义机器人后获得</p>
      <p>2. 签名密钥：如果机器人启用了加签验证，需要填写此项</p>
      <p>3. @功能：可以选择@所有人或@指定手机号的人员</p>
    </t-alert>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
const props = defineProps<{
  modelValue: {
    webHook: string;
    secret: string;
    isAtAll: boolean;
    atMobiles: string[];
  };
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
}>();

// 计算属性
const config = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const addMobile = () => {
  config.value.atMobiles.push('');
};

const removeMobile = (index: number) => {
  config.value.atMobiles.splice(index, 1);
};
</script>

<style scoped>
.dingtalk-config {
  padding: 10px 0;
}

.mobile-list {
  width: 100%;
}

.mobile-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.mobile-item .t-input {
  flex: 1;
}

.t-alert {
  margin-top: 20px;
}
</style>
