namespace GCP.Tests.Infrastructure
{
    /// <summary>
    /// 测试数据库配置
    /// </summary>
    public class TestDatabaseConfiguration
    {
        /// <summary>
        /// 数据源配置
        /// </summary>
        public DataSourceConfiguration DataSource { get; set; } = new();

        /// <summary>
        /// 清理策略 (ClearTables, DeleteFiles)
        /// </summary>
        public string CleanupStrategy { get; set; } = "ClearTables";
    }

    /// <summary>
    /// 数据源配置
    /// </summary>
    public class DataSourceConfiguration
    {
        /// <summary>
        /// 默认数据库配置
        /// </summary>
        public DatabaseConfig Default { get; set; } = new();
    }

    /// <summary>
    /// 数据库配置
    /// </summary>
    public class DatabaseConfig
    {
        /// <summary>
        /// 连接字符串
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// 提供程序名称 (SQLite, MySql, SqlServer, Oracle, PostgreSQL)
        /// </summary>
        public string ProviderName { get; set; } = "SQLite";
    }

    /// <summary>
    /// 数据库清理策略
    /// </summary>
    public enum DatabaseCleanupStrategy
    {
        /// <summary>
        /// 清空表内容
        /// </summary>
        ClearTables,

        /// <summary>
        /// 删除数据库文件 (仅SQLite)
        /// </summary>
        DeleteFiles
    }
}
