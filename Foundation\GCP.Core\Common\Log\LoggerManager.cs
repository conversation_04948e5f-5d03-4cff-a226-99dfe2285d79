﻿using System.Collections.Concurrent;

namespace GCP.Common
{
    public static class LoggerManager
    {
        static ConcurrentDictionary<string, Logger> clientLoggerDic { get; set; } = new ConcurrentDictionary<string, Logger>();

        private static Logger DefaultLogger { get; set; } = new Logger();
        internal static SqlLogger SqlLogger { get; set; } = new SqlLogger();

        public static Logger GetLogger(string clientId, int capacity = 320)
        {
            if (string.IsNullOrEmpty(clientId))
            {
                return DefaultLogger;
            }
            return clientLoggerDic.GetOrAdd(clientId, (key) => new Logger(capacity));
        }

        public static SqlLogger GetSqlLogger()
        {
            return SqlLogger;
        }

        public static SqlContextLogger GetFuncSqlLogger(this FunctionContext context)
        {
            return new SqlContextLogger(context);
        }

        public static void CloseAndFlush()
        {
            DefaultLogger.Dispose();
            SqlLogger.Dispose();
            foreach (var logger in clientLoggerDic.Values)
            {
                logger.Dispose();
            }
        }
    }
}
